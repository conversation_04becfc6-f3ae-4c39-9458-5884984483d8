{"root": ["./src/App.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/common-components/accordion/accordion.tsx", "./src/common-components/age-calculator/age-calculator.tsx", "./src/common-components/auth/auth-image.tsx", "./src/common-components/chip-multi-select/Chip-Multi-Select.tsx", "./src/common-components/confirmation-pop-up/confirmation-pop-up.tsx", "./src/common-components/custom-auto-complete/custom-auto-complete.tsx", "./src/common-components/custom-auto-complete/custom-auto-complete.widgets.ts", "./src/common-components/custom-check-box/custom-check-box.tsx", "./src/common-components/custom-dialog/custom-dialog.tsx", "./src/common-components/custom-dialog/widgets/custom-dialog-styles.ts", "./src/common-components/custom-drawer/custom-drawer.tsx", "./src/common-components/custom-drawer/custom-drawer.widgets.ts", "./src/common-components/custom-input/custom-input.tsx", "./src/common-components/custom-input/widgets/custom-input-styles.ts", "./src/common-components/custom-label/custom-label.tsx", "./src/common-components/custom-label/widgets/custom-label-styles.tsx", "./src/common-components/custom-otp/custom-otp.tsx", "./src/common-components/custom-select/customSelect.tsx", "./src/common-components/custom-select/widgets/customSelectStyles.tsx", "./src/common-components/custom-select-vk/Custom-select.tsx", "./src/common-components/custom-selector-sq/custom-selector-sq.tsx", "./src/common-components/custom-switch/custom-switch-button.tsx", "./src/common-components/custom-switch/custom-switcher-button.tsx", "./src/common-components/custom-switch/widgets/custom-switch-widgets.tsx", "./src/common-components/custom-tab/custom-tab.tsx", "./src/common-components/custom-text-area/custom-textarea.tsx", "./src/common-components/custom-text-area/widgets/custom-textarea-widgets.tsx", "./src/common-components/date-calender/date-calender.tsx", "./src/common-components/date-picker-field/date-picker-field.tsx", "./src/common-components/date-picker-field/month-picker.tsx", "./src/common-components/image-upload/custom-image-upload.tsx", "./src/common-components/loader/loader.tsx", "./src/common-components/multiple-files-upload/multiple-files-upload.tsx", "./src/common-components/navbar/navbar.tsx", "./src/common-components/navbar/top-menu.tsx", "./src/common-components/paginator/paginator.tsx", "./src/common-components/range-slider/Range-slider.tsx", "./src/common-components/snackbar-alert/snackbar-alert.tsx", "./src/common-components/status/status.tsx", "./src/common-components/status/status.widgets.ts", "./src/common-components/table/common-table-widgets.ts", "./src/common-components/table/table-models.ts", "./src/common-components/time-picker/TimePicker.tsx", "./src/common-components/time-picker-field/time-picker-field.tsx", "./src/common-components/toggle/toggle.tsx", "./src/common-components/vital-reference-range/VitalReferenceRange.tsx", "./src/components/layouts/AuthLayout.tsx", "./src/components/provider-portal/Devices/DevicesTab.tsx", "./src/components/provider-portal/Roles/Roles.tsx", "./src/components/provider-portal/billing/BillingTab.tsx", "./src/components/provider-portal/carePlan/care-plan-schema.ts", "./src/components/provider-portal/carePlan/care-plan-tab.tsx", "./src/components/provider-portal/carePlan/careplan-form.tsx", "./src/components/provider-portal/carePlan/view-careplan.tsx", "./src/components/provider-portal/consent-forms/add-consent-form.tsx", "./src/components/provider-portal/consent-forms/appointment-list.tsx", "./src/components/provider-portal/consent-forms/consent-forms-list.tsx", "./src/components/provider-portal/locations/location-form.tsx", "./src/components/provider-portal/locations/location-list.tsx", "./src/components/provider-portal/locations/location-schema.tsx", "./src/components/provider-portal/medical-codes/medical-codes-tab.tsx", "./src/components/provider-portal/patients/assign-care-plan.tsx", "./src/components/provider-portal/patients/invite-patient-form-schema.ts", "./src/components/provider-portal/patients/invite-patient-form.tsx", "./src/components/provider-portal/patients/mock-data.ts", "./src/components/provider-portal/patients/patients-list.tsx", "./src/components/provider-portal/patients/upload-csv-dialog.tsx", "./src/components/provider-portal/patients/enroll-Ehr-patient/ehrPatient-enroll-dialog.tsx", "./src/components/provider-portal/patients/enroll-Ehr-patient/patient-enroll-schema.ts", "./src/components/provider-portal/patients/patient-charting/patient-charting-tabs.tsx", "./src/components/provider-portal/patients/patient-charting/patient-email-drawer.tsx", "./src/components/provider-portal/patients/patient-charting/patient-message-drawer.tsx", "./src/components/provider-portal/patients/patient-charting/patient-profile.tsx", "./src/components/provider-portal/patients/patient-charting/vitals/ecg-grapgh.tsx", "./src/components/provider-portal/patients/patient-charting/vitals/vital-graph-apex.tsx", "./src/components/provider-portal/patients/patient-charting/vitals/vital-graph.tsx", "./src/components/provider-portal/patients/patient-charting/vitals/vital-note-form.tsx", "./src/components/provider-portal/patients/patient-charting/vitals/vitals.tsx", "./src/components/provider-portal/provider-dashboard/provider-dashboard.tsx", "./src/components/provider-portal/scheduling/calender-view-appointment.tsx", "./src/components/provider-portal/scheduling/custom-toolbar.tsx", "./src/components/provider-portal/scheduling/patient-details-in-schedule-appt.tsx", "./src/components/provider-portal/scheduling/schedule-appointment-dialog.tsx", "./src/components/provider-portal/scheduling/schedule-appointment.tsx", "./src/components/provider-portal/scheduling/scheduling-list-table.tsx", "./src/components/provider-portal/scheduling/dialoge/appointment-details-drawer.tsx", "./src/components/provider-portal/scheduling/dialoge/appointment-details-tabs.tsx", "./src/components/provider-portal/scheduling/dialoge/cancel-appointment-dialog.tsx", "./src/components/provider-portal/scheduling/dialoge/infusion-details.tsx", "./src/components/provider-portal/scheduling/dialoge/reassign-nurse-dialog.tsx", "./src/components/provider-portal/tasks/tasksTab.tsx", "./src/components/provider-portal/users/users-list.tsx", "./src/components/provider-portal/users/users-tabs.tsx", "./src/components/provider-portal/users/nurse/nurse-form.tsx", "./src/components/provider-portal/users/nurse/nurse-schema.ts", "./src/components/provider-portal/users/provider/provider-form.tsx", "./src/components/provider-portal/users/provider/provider-schema.tsx", "./src/components/provider-portal/users/staff-users/staff-details.tsx", "./src/components/provider-portal/users/staff-users/staff-form.tsx", "./src/components/provider-portal/users/staff-users/staff-list.tsx", "./src/components/provider-portal/users/staff-users/staff-schema.ts", "./src/components/providers/DrawerProvider.tsx", "./src/components/refresh-token/refresh-token.tsx", "./src/components/settings/profile-provider/edit-profile.tsx", "./src/components/settings/profile-provider/profile-schema.ts", "./src/components/settings/profile-provider/profile.tsx", "./src/components/settings/profile-staff/edit-profile.tsx", "./src/components/settings/profile-staff/profile-schema.ts", "./src/components/settings/profile-staff/profile.tsx", "./src/components/settings/tabs/settings-tabs.tsx", "./src/components/ui/DrawerBody.tsx", "./src/components/ui/DrawerFooter.tsx", "./src/components/ui/MainDialog.tsx", "./src/components/ui/MainDrawer.tsx", "./src/components/ui/Text.tsx", "./src/components/ui/Atom/ChipButton.tsx", "./src/components/ui/Atom/IconButton.tsx", "./src/components/ui/Atom/LineProgress.tsx", "./src/components/ui/Container/FillContainer.tsx", "./src/components/ui/Form/Autocomplete.tsx", "./src/components/ui/Form/AutocompleteMultiSelect.tsx", "./src/components/ui/Form/Datepicker.tsx", "./src/components/ui/Form/Input.tsx", "./src/components/ui/Form/InputPhoneNumber.tsx", "./src/components/ui/Form/Select.tsx", "./src/components/ui/Form/Textarea.tsx", "./src/components/ui/Form/UploadImage.tsx", "./src/components/ui/Profile/ProfileNurse.tsx", "./src/components/ui/Profile/ProfileStaff.tsx", "./src/constants/appointments-const.ts", "./src/constants/collections.ts", "./src/constants/config.ts", "./src/constants/constants.ts", "./src/constants/environments.ts", "./src/constants/error-messages.ts", "./src/constants/options.ts", "./src/constants/portals.ts", "./src/constants/provider.ts", "./src/constants/roles.ts", "./src/constants/status.ts", "./src/constants/styles.ts", "./src/constants/auth/environments.ts", "./src/constants/auth/login-page-constants.ts", "./src/hooks/use-authority.tsx", "./src/hooks/use-logout.tsx", "./src/hooks/use-menu.tsx", "./src/hooks/use-store-login-data.tsx", "./src/hooks/useApiFeedback.tsx", "./src/layouts/auth-layout.tsx", "./src/layouts/main-layout.tsx", "./src/models/auth/demo.tsx", "./src/models/auth/login-model.ts", "./src/models/auth/reset-linktype.ts", "./src/models/auth/token-payload.ts", "./src/models/provider/provider-modal.ts", "./src/models/response/error-response.ts", "./src/models/response/response-content-entity.ts", "./src/pages/auth/login.tsx", "./src/pages/auth/set-new-password.tsx", "./src/pages/auth/verify-email.tsx", "./src/pages/auth/verify-otp.tsx", "./src/pages/errors/not-authorised.tsx", "./src/pages/errors/not-found.tsx", "./src/pages/provider-portal/commonFiles/staticOptionsData.ts", "./src/pages/provider-portal/patients/provider-patients-page.tsx", "./src/pages/provider-portal/patients/patients-profile/index.tsx", "./src/pages/provider-portal/patients/patients-profile/components/ProfileHeader.tsx", "./src/pages/provider-portal/patients/patients-profile/components/VitalsTab.tsx", "./src/pages/provider-portal/patients/patients-profile/components/DevicesTab/DevicesTab.tsx", "./src/pages/provider-portal/patients/patients-profile/components/allergiesTab/AllergiesForm.tsx", "./src/pages/provider-portal/patients/patients-profile/components/allergiesTab/AllergiesTab.tsx", "./src/pages/provider-portal/patients/patients-profile/components/allergiesTab/allergiesSchema.ts", "./src/pages/provider-portal/patients/patients-profile/components/appointmentTab/AppointmentTab.tsx", "./src/pages/provider-portal/patients/patients-profile/components/carePlan/BPChart.tsx", "./src/pages/provider-portal/patients/patients-profile/components/carePlan/CareplanForm.tsx", "./src/pages/provider-portal/patients/patients-profile/components/carePlan/CareplanTab.tsx", "./src/pages/provider-portal/patients/patients-profile/components/carePlan/ProgressBar.tsx", "./src/pages/provider-portal/patients/patients-profile/components/carePlan/ViewCarePlan.tsx", "./src/pages/provider-portal/patients/patients-profile/components/consentTab/ConsentTab.tsx", "./src/pages/provider-portal/patients/patients-profile/components/diagnosisTab/DiagnosisForm.tsx", "./src/pages/provider-portal/patients/patients-profile/components/diagnosisTab/DiagnosisTab.tsx", "./src/pages/provider-portal/patients/patients-profile/components/diagnosisTab/diagnosisSchema.tsx", "./src/pages/provider-portal/patients/patients-profile/components/forms/NoteForm.tsx", "./src/pages/provider-portal/patients/patients-profile/components/forms/SendEmailForm.tsx", "./src/pages/provider-portal/patients/patients-profile/components/forms/SendMessageForm.tsx", "./src/pages/provider-portal/patients/patients-profile/components/medicationTab/MedicationForm.tsx", "./src/pages/provider-portal/patients/patients-profile/components/medicationTab/MedicationsTab.tsx", "./src/pages/provider-portal/patients/patients-profile/components/shared/DateRangeFilter.tsx", "./src/pages/provider-portal/patients/patients-profile/components/shared/EcgChart.tsx", "./src/pages/provider-portal/patients/patients-profile/components/shared/ListVitalsData.tsx", "./src/pages/provider-portal/patients/patients-profile/components/shared/VitalsChart.tsx", "./src/pages/provider-portal/patients/patients-profile/components/timeLogsTab/TimeLogTab.tsx", "./src/pages/provider-portal/settings/profile-page.tsx", "./src/pages/provider-portal/settings/provider-settings-master.tsx", "./src/pages/provider-portal/users/provider-list-page.tsx", "./src/redux/action-type.ts", "./src/redux/store.ts", "./src/redux/actions/loader-action.ts", "./src/redux/actions/patient-profile.tsx", "./src/redux/actions/profile-async-actions.ts", "./src/redux/actions/snackbar-action.ts", "./src/redux/reducer/get-patients-profile-data.tsx", "./src/redux/reducer/get-profile.ts", "./src/redux/reducer/loader-reducer.ts", "./src/redux/reducer/root-reducer.ts", "./src/redux/reducer/snackbar-reducer.ts", "./src/routes/private-route.tsx", "./src/routes/public-route.tsx", "./src/routes/routes.tsx", "./src/sdk/queries/common.ts", "./src/sdk/queries/index.ts", "./src/sdk/queries/prefetch.ts", "./src/sdk/queries/queries.ts", "./src/sdk/queries/suspense.ts", "./src/sdk/requests/index.ts", "./src/sdk/requests/schemas.gen.ts", "./src/sdk/requests/services.gen.ts", "./src/sdk/requests/types.gen.ts", "./src/sdk/requests/core/ApiError.ts", "./src/sdk/requests/core/ApiRequestOptions.ts", "./src/sdk/requests/core/ApiResult.ts", "./src/sdk/requests/core/CancelablePromise.ts", "./src/sdk/requests/core/OpenAPI.ts", "./src/sdk/requests/core/request.ts", "./src/services/common/date-formatter.ts", "./src/services/common/get-tenant-id.ts", "./src/services/common/logger.service.ts", "./src/services/common/phone-formatter.ts", "./src/services/core/cookie-service.ts", "./src/services/core/storage-service.ts", "./src/services/core/TimeLogService/StoreActions.ts", "./src/services/core/TimeLogService/TimerStore.ts", "./src/services/core/TimeLogService/timeTrackingService.ts", "./src/services/core/TimeLogService/useBrowserEvents.ts", "./src/utils/StateList.ts", "./src/utils/otp-countdown.ts", "./src/utils/regex.ts", "./src/utils/statesList.ts", "./src/utils/theme.ts", "./src/utils/time-date-formatter.ts", "./src/utils/toCamelCase.ts", "./src/utils/format/date.ts", "./src/utils/format/string.ts"], "version": "5.7.2"}