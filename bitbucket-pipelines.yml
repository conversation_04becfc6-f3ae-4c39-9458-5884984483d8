image: node:16

pipelines:
  branches:
    develop:
      - parallel:
          - step:
              name: Build And Prepare Distribution
              caches:
                - node
              script:
                - npm install --legacy-peer-deps
                - npm run build
              artifacts:
                - dist/**
          - step:
              name: Security Scan
              script:
                - pipe: atlassian/git-secrets-scan:0.5.1
      - step:
          name: Deploy to Development Environment
          deployment: Development
          clone:
            enabled: false
          script:
            # sync your files to S3
            - pipe: atlassian/aws-s3-deploy:1.6.1
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: $AWS_DEV_S3_BUCKET
                LOCAL_PATH: "dist"
            # triggering a distribution invalidation to refresh the CDN caches
            - pipe: atlassian/aws-cloudfront-invalidate:0.6.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                DISTRIBUTION_ID: $AWS_DEV_CLOULDFRONT_DISTRIBUTION
    qa:
      - parallel:
          - step:
              name: Build And Prepare Distribution
              caches:
                - node
              script:
                - npm install --legacy-peer-deps
                - npm run build:qa
              artifacts:
                - dist/**
          - step:
              name: Security Scan
              script:
                - pipe: atlassian/git-secrets-scan:0.5.1
      - step:
          name: Deploy to QA Environment
          deployment: QA
          clone:
            enabled: false
          script:
            # sync your files to S3
            - pipe: atlassian/aws-s3-deploy:1.6.1
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: $AWS_QA_S3_BUCKET
                LOCAL_PATH: "dist"
            # triggering a distribution invalidation to refresh the CDN caches
            - pipe: atlassian/aws-cloudfront-invalidate:0.6.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                DISTRIBUTION_ID: $AWS_QA_CLOULDFRONT_DISTRIBUTION
    UAT:
      - parallel:
          - step:
              name: Build And Prepare Distribution
              caches:
                - node
              script:
                - npm install --legacy-peer-deps
                - npm run build:UAT
              artifacts:
                - dist/**
          - step:
              name: Security Scan
              script:
                - pipe: atlassian/git-secrets-scan:0.5.1
      - step:
          name: Deploy to UAT Environment
          deployment: UAT
          clone:
            enabled: false
          script:
            # sync your files to S3
            - pipe: atlassian/aws-s3-deploy:1.6.1
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: $AWS_UAT_S3_BUCKET
                LOCAL_PATH: "dist"
            # triggering a distribution invalidation to refresh the CDN caches
            - pipe: atlassian/aws-cloudfront-invalidate:0.6.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                DISTRIBUTION_ID: $AWS_UAT_CLOULDFRONT_DISTRIBUTION
