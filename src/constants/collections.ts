import StressSVG from "@/assets/image_svg/icons/cognition.svg?react";
import EcgHeart from "@/assets/image_svg/icons/ecg.svg?react";
import HeartSVG from "@/assets/image_svg/icons/ecg_heart.svg?react";
import GlucoseSVG from "@/assets/image_svg/icons/glucose.svg?react";
import HrvSVG from "@/assets/image_svg/icons/monitor_heart.svg?react";
import WeightSVG from "@/assets/image_svg/icons/monitor_weight.svg?react";
import SpoSVG from "@/assets/image_svg/icons/spo2.svg?react";
import WaterDropSVG from "@/assets/image_svg/icons/water_drop.svg?react";

export const VITAL_TYPES = [
  {
    value: "Blood Pressure",
    label: "Blood Pressure",
    unit: "mmHg",
    IconComponent: WaterDropSVG,
    thresholds: {
      normal: { min: 100, max: 130 },
      warning: { min: 130, max: 150 },
      critical: { min: 150 },
    },
    yMin: 0,
    yMax: 180,
  },
  {
    value: "Blood Glucose",
    label: "Blood Glucose",
    unit: "mg/dL",
    IconComponent: GlucoseSVG,
    thresholds: {
      normal: { min: 70, max: 140 },
      warning: { min: 140, max: 199 },
      critical: { max: 70, upper: 200 },
    },
    yMin: 0,
    yMax: 200,
  },
  {
    value: "Heart Rate",
    label: "Heart Rate",
    unit: "bpm",
    IconComponent: HeartSVG,
    thresholds: {
      normal: { min: 60, max: 100 },
      warning: { min: 50, max: 60 },
      critical: { max: 50, min: 120 },
    },
    yMin: 0,
    yMax: 140,
  },
  {
    value: "Weight",
    label: "Weight",
    unit: "kg",
    IconComponent: WeightSVG,
    thresholds: {
      normal: { min: 50, max: 75 },
      warning: { min: 75, max: 90 },
      critical: { max: 50, min: 90 },
    },
    yMin: 0,
    yMax: 150,
  },
  {
    value: "ECG",
    label: "ECG",
    unit: "avg bpm",
    IconComponent: EcgHeart,
    thresholds: {
      normal: { min: 60, max: 100 },
      warning: { min: 50, max: 110 },
      critical: { min: 40, max: 130 },
    },
    yMin: 40,
    yMax: 130,
  },
  {
    value: "HRV",
    label: "HRV",
    unit: "ms",
    IconComponent: HrvSVG,
    thresholds: {
      normal: { min: 20, max: 100 },
      warning: { min: 10, max: 120 },
      critical: { min: 5, max: 150 },
    },
    yMin: 0,
    yMax: 150,
  },
  {
    value: "Oxygen Saturation",
    label: "Oxygen Saturation",
    unit: "%",
    IconComponent: SpoSVG,
    thresholds: {
      normal: { min: 95, max: 100 },
      warning: { min: 90, max: 94 },
      critical: { min: 85, max: 89 },
    },
    yMin: 80,
    yMax: 100,
  },
  {
    value: "Stress",
    label: "Stress",
    unit: "/ 100",
    IconComponent: StressSVG,
    thresholds: {
      normal: { min: 0, max: 40 },
      warning: { min: 41, max: 70 },
      critical: { min: 71, max: 100 },
    },
    yMin: 0,
    yMax: 100,
  },
];
