import { alpha } from "@mui/system";

import { theme } from "@/utils/theme";

export const PATIENT_LOWER = "patient";
export const PROVIDER_LOWER = "provider";

export type StringMap = {
  [key: string]: string;
};

export const RolesPortalMap: StringMap = {
  PROVIDER: "provider",
  SUPER_ADMIN: "provider",
  FRONTDESK: "provider",
  NURSE: "provider",
  BILLER: "provider",
  SITE_ADMIN: "provider",
  PROVIDER_GROUP_ADMIN: "provider",
};

export const StatusColorMap: { [key: string]: string } = {
  ACTIVE: theme.palette.success.main,
  INACTIVE: theme.palette.error.light,
  ENABLE: theme.palette.success.main,
  ENABLED: theme.palette.success.main,
  DISABLE: theme.palette.error.light,
  DISABLED: theme.palette.error.light,
  READY: theme.palette.success.light,
  PENDING: theme.palette.warning.light,
  APPROVED: theme.palette.success.main,
  REJECTED: theme.palette.error.light,
  // CANCELLED: theme.palette.error.light,
  CANCELLED: "#880000",
  // COMPLETED: theme.palette.success.main,
  SCHEDULED: theme.palette.success.main,
  DISCARDED: alpha(theme.palette.primary.main, 0.5),
  RESCHEDULED: theme.palette.info.main,
  // NO_SHOW: alpha(theme.palette.common.black, 0.5),
  NO_SHOW: "#805FC7",
  // SCHEDULED: theme.palette.info.main,
  COMPLETED: theme.palette.info.main,
  // CHECKED_IN: theme.palette.success.main,
  IN_EXAM: theme.palette.warning.light,
  CHECK_IN: theme.palette.success.light,
  CHECKED_IN: "#013153",
  CONFIRMED: theme.palette.success.main,
  PAY_NOW: theme.palette.success.main,
  CONFIRMATION_PENDING: theme.palette.warning.light,
  INTAKE_PENDING: theme.palette.warning.light,
  SCREENERS_PENDING: theme.palette.warning.light,
  PAYMENT_PENDING: theme.palette.warning.light,
  ABLE_TO_CHECK_IN: theme.palette.success.main,
  FEES_NOT_SET: theme.palette.warning.light,
  NO_SHOW_FROM_PROVIDER: alpha(theme.palette.common.black, 0.5),
  SIGN_OFF_PENDING: theme.palette.warning.light,
  HIGH: theme.palette.error.light,
  MEDIUM: theme.palette.warning.main,
  ACTIVE_COVERAGE: theme.palette.success.main,
  LOW: theme.palette.warning.light,
  YES: theme.palette.primary.light,
  NO: theme.palette.primary.light,
  INITIATED: theme.palette.primary.light,
  SIGN_OFF_REQUIRED: theme.palette.error.dark,
  READY_FOR_BILL: theme.palette.success.main,
  READY_FOR_PAYMENT: theme.palette.primary.light,
  READY_FOR_CLAIM: theme.palette.success.main,
  READY_FOR_SELF_PAY: theme.palette.warning.light,
  CLAIM_IN_PROGRESS: theme.palette.warning.light,
  HOLD: theme.palette.primary.light,
  SETTLED: theme.palette.success.main,
  SIGNED_OFF: theme.palette.success.main,
  IN_PROGRESS: theme.palette.warning.light,
  // IN_SESSION: theme.palette.warning.light,
  IN_SESSION: "#007A8B",
  ADULT: alpha(theme.palette.secondary.light, 0.2),
  DOT: alpha(theme.palette.error.light, 0.3),
  COVID: "#edcdec ",
  PEDIATRIC: alpha(theme.palette.warning.light, 0.3),
  REVIEW_AND_SIGNED: theme.palette.success.light,
  NOT_SINGED: theme.palette.warning.light,
  ASSIGNED: theme.palette.primary.light,
  NOT_ASSIGNED: theme.palette.warning.light,
  OPEN: theme.palette.secondary.main,
};

export const ServiceStatusTypeColorMap: { [key: string]: string } = {
  ADULT: alpha(theme.palette.secondary.light, 0.2),
  DOT: alpha(theme.palette.error.light, 0.3),
  COVID: "#E7EBFC",
  PEDIATRIC: alpha(theme.palette.warning.light, 0.3),
};
