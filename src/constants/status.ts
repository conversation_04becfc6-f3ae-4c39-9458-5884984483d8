import { alpha } from "@mui/material";

import { theme } from "../utils/theme";

export const Status = {
  ACTIVE: "ACTIVE",
  INACTIVE: "INACTIVE",
  ENABLE: "ENABLE",
  ENABLED: "ENABLED",
  DISABLE: "DISABLE",
  DISABLED: "DISABLED",
  READY: "READY",
  PENDING: "PENDING",
  APPROVED: "APPROVED",
  REJECTED: "REJECTED",
  CANCELLED: "CANCELLED",
  COMPLETED: "COMPLETED",
  DISCARDED: "DISCARDED",
  RESCHEDULED: "RESCHEDULED",
  NO_SHOW: "NO_SHOW",
  SCHEDULED: "SCHEDULED",
  CHECKED_IN: "CHECKED_IN",
  IN_EXAM: "IN_EXAM",
  CHECK_IN: "CHECK_IN",
  CONFIRMED: "CONFIRMED",
  PAY_NOW: "PAY_NOW",
  CONFIRMATION_PENDING: "CONFIRMATION_PENDING",
  INTAKE_PENDING: "INTAKE_PENDING",
  SCREENERS_PENDING: "SCREENERS_PENDING",
  PAYMENT_PENDING: "PAYMENT_PENDING",
  ABLE_TO_CHECK_IN: "ABLE_TO_CHECK_IN",
  FEES_NOT_SET: "FEES_NOT_SET",
  NO_SHOW_FROM_PROVIDER: "NO_SHOW_FROM_PROVIDER",
  SIGN_OFF_PENDING: "SIGN_OFF_PENDING",
  HIGH: "HIGH",
  MEDIUM: "MEDIUM",
  LOW: "LOW",
  ACTIVE_COVERAGE: "Active Coverage",
  YES: "YES",
  NO: "NO",
  INITIATED: "INITIATED",
  SIGN_OFF_REQUIRED: "SIGN_OFF_REQUIRED",
  READY_FOR_BILL: "READY_FOR_BILL",
  READY_FOR_PAYMENT: "READY_FOR_PAYMENT",
  READY_FOR_CLAIM: "READY_FOR_CLAIM",
  READY_FOR_SELF_PAY: "READY_FOR_SELF_PAY",
  CLAIM_IN_PROGRESS: "CLAIM_IN_PROGRESS",
  HOLD: "HOLD",
  SETTLED: "SETTLED",
  SIGNED_OFF: "SIGNED_OFF",
  BROADCASTED: "BROADCASTED",
  TRAINED: "Trained",
  NOT_TRAINED: "Not Trained",
  SIGNED: "Signed",
};

export const StatusColorMap: { [key: string]: string } = {
  REQUESTED: "#1E90FF",
  ACTIVE: theme.palette.success.light,
  INACTIVE: theme.palette.error.light,
  ENABLE: theme.palette.success.main,
  ENABLED: theme.palette.success.main,
  DISABLE: theme.palette.error.light,
  DISABLED: theme.palette.error.light,
  READY: theme.palette.success.light,
  PENDING: "#943C00",
  APPROVED: theme.palette.success.main,
  REJECTED: theme.palette.error.light,
  CANCELLED: "#8D000C",
  COMPLETED: "#016A1C",
  DISCARDED: alpha(theme.palette.primary.main, 0.5),
  RESCHEDULED: "#943C00",
  NO_SHOW: "#525E6F",
  SCHEDULED: "#004AB1",
  CHECKED_IN: "#004AB1",
  IN_EXAM: theme.palette.warning.light,
  CHECK_IN: "#004AB1",
  CONFIRMED: theme.palette.success.main,
  PAY_NOW: theme.palette.success.main,
  CONFIRMATION_PENDING: theme.palette.warning.light,
  INTAKE_PENDING: theme.palette.warning.light,
  SCREENERS_PENDING: theme.palette.warning.light,
  PAYMENT_PENDING: theme.palette.warning.light,
  ABLE_TO_CHECK_IN: theme.palette.success.main,
  FEES_NOT_SET: theme.palette.warning.light,
  NO_SHOW_FROM_PROVIDER: alpha(theme.palette.common.black, 0.5),
  SIGN_OFF_PENDING: theme.palette.warning.light,
  HIGH: theme.palette.error.light,
  MEDIUM: theme.palette.info.main,
  ACTIVE_COVERAGE: theme.palette.success.main,
  LOW: theme.palette.warning.light,
  YES: theme.palette.primary.light,
  NO: theme.palette.primary.light,
  INITIATED: theme.palette.primary.light,
  SIGN_OFF_REQUIRED: theme.palette.error.dark,
  READY_FOR_BILL: theme.palette.success.main,
  READY_FOR_PAYMENT: theme.palette.primary.light,
  READY_FOR_CLAIM: theme.palette.success.main,
  READY_FOR_SELF_PAY: theme.palette.warning.light,
  CLAIM_IN_PROGRESS: theme.palette.warning.light,
  HOLD: theme.palette.primary.light,
  SETTLED: theme.palette.success.main,
  SIGNED_OFF: theme.palette.success.main,
  IN_PROGRESS: "#004AB1",
  BROADCASTED: "#B1000F",
  BROADCAST: "#B1000F",
  BROADCAST_EVENT: "#006D8F",
  BROADCAST_ACCEPTED: "#364144",
  TRAINED: "#049B22",
  NOT_TRAINED: "#943C00",
  SIGNED: theme.palette.success.light,
  UNSIGNED: theme.palette.error.light,
};

export const StatusBackgroundColorMap: { [key: string]: string } = {
  SCHEDULED: "#EFF8FF",
  RESCHEDULED: "#FFF2D2",
  IN_PROGRESS: "#EFF8FF",
  CANCELLED: "#FFD4D8",
  COMPLETED: "#E1FCDE",
  NO_SHOW: "#ECEFF4",
  CHECK_IN: "#EFF8FF",
  PENDING: "#FFF2D2",
  BROADCASTED: "#FFD4D8",
  TRAINED: "#E1FCDE",
  NOT_TRAINED: "#FFF2D2",
};
