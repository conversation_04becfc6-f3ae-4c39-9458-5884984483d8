import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

import { ArrowBackIosNew } from "@mui/icons-material";
import { Grid2 as Grid, Tab, Tabs } from "@mui/material";

import IconButton from "@/components/ui/Atom/IconButton";
import FillContainer from "@/components/ui/Container/FillContainer";
import { Patient } from "@/sdk/requests";

import DevicesTab from "./components/DevicesTab/DevicesTab";
import ProfileHeader from "./components/ProfileHeader";
import VitalsTab from "./components/VitalsTab";
import AllergiesTab from "./components/allergiesTab/AllergiesTab";
import AppointmentTab from "./components/appointmentTab/AppointmentTab";
import CarePlanTab from "./components/carePlan/CareplanTab";
import ConsentTab from "./components/consentTab/ConsentTab";
import DiagnosisTab from "./components/diagnosisTab/DiagnosisTab";
import MedicationsTab from "./components/medicationTab/MedicationsTab";
import TimeLogsTab from "./components/timeLogsTab/TimeLogTab";

const PatientsProfile = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [patientProfileData, setPatientProfileData] = useState<Patient>();

  const activeTab = searchParams.get("tab") || "vitals";

  useEffect(() => {
    if (!searchParams.get("tab")) {
      setSearchParams({ tab: "vitals" }, { replace: true });
    }
  }, [searchParams, setSearchParams]);

  const handleTabChange = (_: unknown, value: string) => {
    setSearchParams({ tab: value });
  };

  return (
    <FillContainer>
      <Grid
        container
        spacing={2}
        sx={{
          flexWrap: "nowrap",
          height: "100%",
          overflow: "hidden",
        }}
      >
        <Grid size="auto" sx={{ pt: 1 }}>
          <IconButton icon={<ArrowBackIosNew sx={{ fontSize: 14 }} />} onClick={() => navigate("/provider/patients")} />
        </Grid>
        <Grid
          container
          size="grow"
          direction="column"
          spacing={2}
          sx={{
            pb: 2,
            height: "100%",
            display: "flex",
            flexDirection: "column",
            overflow: "hidden",
          }}
          className="bordered-box"
        >
          <Grid size="auto" sx={{ flexShrink: 0 }}>
            <ProfileHeader setPatientProfileData={setPatientProfileData} />
          </Grid>
          <Grid size="auto" sx={{ px: 2, flexShrink: 0 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              sx={{
                borderBottom: "1px solid #DEE4ED",
                "& .MuiTab-root": {
                  fontSize: 14,
                  textTransform: "none",
                  letterSpacing: "normal",
                  "&.Mui-selected": {
                    backgroundColor: "#EEFBFF",
                    color: "#006D8F",
                  },
                },
              }}
            >
              <Tab value="vitals" label="Vitals" wrapped />
              {/* <Tab value="alerts" label="Alerts" /> */}
              <Tab value="carePlans" label="Care Plans" />
              <Tab value="allergies" label="Allergies" />
              <Tab value="medications" label="Medications" />
              <Tab value="diagnoses" label="Diagnoses" />
              <Tab value="appointments" label="Appointments" />
              <Tab value="timeLogs" label="Time Logs" />
              <Tab value="devices" label="Devices" />
              <Tab value="ConsentForms" label="Consent Forms" />
            </Tabs>
          </Grid>
          <Grid
            size="grow"
            sx={{
              px: 2,
              overflow: "auto",
              display: "flex",
              flexDirection: "column",
              flexGrow: 1,
            }}
          >
            {activeTab === "vitals" && <VitalsTab patientProfileData={patientProfileData} />}
            {activeTab === "allergies" && <AllergiesTab patientProfileData={patientProfileData} />}
            {activeTab === "appointments" && <AppointmentTab />}
            {activeTab === "timeLogs" && <TimeLogsTab />}
            {activeTab === "devices" && <DevicesTab patientProfileData={patientProfileData} />}
            {activeTab === "diagnoses" && <DiagnosisTab patientProfileData={patientProfileData} />}
            {activeTab === "medications" && <MedicationsTab patientProfileData={patientProfileData} />}
            {activeTab === "carePlans" && <CarePlanTab patientProfileData={patientProfileData} />}
            {activeTab === "ConsentForms" && <ConsentTab />}
          </Grid>
        </Grid>
      </Grid>
    </FillContainer>
  );
};

export default PatientsProfile;
