import { ChangeEvent, useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import RestoreIcon from "@mui/icons-material/Restore";
import SyncIcon from "@mui/icons-material/Sync";
import {
  Button,
  Chip,
  IconButton,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid, keyframes } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomInput from "@/common-components/custom-input/custom-input";
import Paginator from "@/common-components/paginator/paginator";
import { heading, iconStyles, tableCellCss, typographyCss } from "@/common-components/table/common-table-widgets";

import useApiFeedback from "@/hooks/useApiFeedback";
import {
  usePatientAllergyControllerServiceSyncPatientAllergy,
  usePatientAllergyControllerServiceUpdatePatientAllergyArchiveStatus,
} from "@/sdk/queries";
import { Patient, PatientAllergy, PatientAllergyControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { toCamelCase } from "@/utils/toCamelCase";

import AllergiesForm from "./AllergiesForm";

interface PageInfo {
  totalElements: number;
  totalPages: number;
}

interface AllergiesResponse {
  content: PatientAllergy[];
  page: PageInfo;
}

const headerArr = ["Name", "Type", "Reaction", "Severity", "Onset Date", "Action"];
interface AllergiedProps {
  patientProfileData: Patient | undefined;
}

function AllergiesTab(props: AllergiedProps) {
  const { patientProfileData } = props;
  const { patientId } = useParams();
  const xTenantId = GetTenantId();

  const [searchAllergiesText, setSearchAllergiesText] = useState("");
  const [allergiesDate, setAllergiesDate] = useState<PatientAllergy[]>();
  const [openAddAllergiesDialog, setOpenAddAllergiesDialog] = useState(false);
  const [editDate, setEditDate] = useState<PatientAllergy>();
  const [isEdit, setIsEdit] = useState(false);
  const [archiveRestoreData, setArchiveRestoreDate] = useState<PatientAllergy>();
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);
  //pagination
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [totalElement, setTotalElements] = useState<number>(0);
  const [totalPages, setTotalPages] = useState(0);

  const handlePageChange = (event: ChangeEvent<unknown> | null, page: number) => {
    event;
    setPage(page);
  };

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const {
    data: AllergiesDate,
    isSuccess,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: ["allergies", searchAllergiesText],
    queryFn: () =>
      PatientAllergyControllerService.getPatientAllergy({
        patientUuid: patientId as string,
        searchString: searchAllergiesText,
        page: page,
        size: size,
        xTenantId: xTenantId,
      }),
  });
  useEffect(() => {
    if (isSuccess) {
      const response = AllergiesDate?.data as unknown as AllergiesResponse;
      setAllergiesDate(response.content);
      setTotalElements(response.page?.totalElements ?? 0);
      setTotalPages(response.page?.totalPages ?? 0);
    }
  }, [isSuccess, AllergiesDate]);

  const handleEditDate = (data: PatientAllergy) => {
    setIsEdit(true);
    setOpenAddAllergiesDialog(true);
    setEditDate(data);
  };

  // restoreApi
  const {
    mutateAsync: mutateAsyncArchive,
    isError: isErrorArchive,
    error: errorArchive,
    isSuccess: isSuccessArchive,
    data: dataArchive,
  } = usePatientAllergyControllerServiceUpdatePatientAllergyArchiveStatus();

  useApiFeedback(
    isErrorArchive,
    errorArchive,
    isSuccessArchive,
    (dataArchive?.message || "User archive status updated!") as string
  );

  const confirmDelete = async () => {
    await mutateAsyncArchive({
      patientAllergyId: archiveRestoreData?.uuid || "",
      status: true,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmDeletePopUp(false);
  };

  const confirmRestore = async () => {
    await mutateAsyncArchive({
      patientAllergyId: archiveRestoreData?.uuid || "",
      status: false,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmRestorePopUp(false);
  };

  const {
    mutateAsync: mutateAsyncSync,
    isSuccess: isSuccessSync,
    data: dataSync,
    error: errorSync,
    isError: isErrorSync,
    isPending: isPendingSync,
  } = usePatientAllergyControllerServiceSyncPatientAllergy();

  useApiFeedback(isErrorSync, errorSync, isSuccessSync, (dataSync?.message || "Allergies Sync Successfully") as string);

  const renderTableCell = (data: string | number) => {
    return (
      <TableCell sx={{ ...heading }} align="left">
        <Grid container flexDirection={"column"}>
          <Typography sx={typographyCss} variant="bodySmall">
            {data}
          </Typography>
        </Grid>
      </TableCell>
    );
  };

  const severityStyle = {
    MILD: { bgcolor: "#E1FCDE", color: "#004AB1" },
    MODERATE: { bgcolor: "#E1FCDE", color: "#049B22" },
    HIGH: { bgcolor: "#E1FCDE", color: "#B1000F" },
    UNDEFINED: { bgcolor: "#E1FCDE", color: "black" },
  };
  severityStyle;

  const allergiesTypeColor = {
    FOOD: { bgcolor: "#DBF1BC", color: "#355902" },
    DRUG: { bgcolor: "#FFD4D8", color: "#8D000C" },
    ENVIRONMENT: { bgcolor: "#FFF2D2", color: "#943C00" },
    OTHER: { bgcolor: "#E1FCDE", color: "black" },
  };

  const rotateIcon = keyframes`
  from {
    transform: rotate(0deg)
  }
    to{
    transform : rotate(360deg)
    }
  `;

  const handleAllergiesSyncCall = async () => {
    await mutateAsyncSync({ patientEhrId: patientProfileData?.ehrId as string, xTenantId: xTenantId });
    refetch();
  };

  return (
    <Grid>
      <Grid container justifyContent={"space-between"} mb={1}>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          <Grid>
            <CustomInput
              placeholder="Search Allergies"
              name="alleries"
              hasStartSearchIcon={true}
              value={searchAllergiesText}
              onDebounceCall={(searchString) => setSearchAllergiesText(searchString)}
              onInputEmpty={() => setSearchAllergiesText("")}
            />
          </Grid>
        </Grid>
        <Grid>
          <Button
            startIcon={
              <SyncIcon
                sx={{
                  animation: isPendingSync ? `${rotateIcon} 1s linear infinite` : "none",
                }}
              />
            }
            // loading={isPendingSync}
            variant="outlined"
            sx={{ borderRadius: "12px", height: "30px", fontWeight: 500 }}
            onClick={handleAllergiesSyncCall}
            disabled={patientProfileData?.ehrId ? false : true}
          >
            Sync Allergies
          </Button>
        </Grid>
      </Grid>

      {/* table */}
      <Grid width={"100%"}>
        <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerArr.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>

            <TableBody>
              {isLoading ? (
                Array.from({ length: 5 }).map((_, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {headerArr.map((_, cellIndex) => (
                      <TableCell key={cellIndex} align="left">
                        <Skeleton variant="text" width="100%" height={40} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : allergiesDate && allergiesDate?.length > 0 ? (
                allergiesDate?.map((data, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {renderTableCell(data?.name || "")}
                    <TableCell>
                      <Grid container flexDirection={"column"}>
                        <Typography sx={typographyCss} variant="bodySmall">
                          <Chip
                            label={toCamelCase(data?.allergyType || "")}
                            sx={{
                              minWidth: "auto",
                              height: "22px",
                              fontWeight: 550,
                              ...(allergiesTypeColor[data?.allergyType || "FOOD"] || allergiesTypeColor.OTHER),
                            }}
                          />
                        </Typography>
                      </Grid>
                    </TableCell>
                    {renderTableCell(toCamelCase(data?.reaction || ""))}
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <Typography sx={typographyCss} variant="bodySmall">
                          <Chip
                            label={toCamelCase(data?.severity || "")}
                            sx={{
                              minWidth: "auto",
                              height: "22px",
                              fontWeight: 550,
                              ...(severityStyle[data?.severity || "MILD"] || severityStyle.UNDEFINED),
                            }}
                          />
                        </Typography>
                      </Grid>
                    </TableCell>
                    {renderTableCell(format(new Date(data.onSetDate || ""), "dd-MM-yyyy"))}
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexWrap={"nowrap"}>
                        <IconButton sx={{ padding: "0px 5px" }} aria-label="edit" onClick={() => handleEditDate(data)}>
                          <EditOutlinedIcon sx={iconStyles} />
                        </IconButton>
                        {!data.archive ? (
                          <IconButton
                            aria-label="delete"
                            sx={{ padding: "0px" }}
                            onClick={() => {
                              setArchiveRestoreDate(data), setOpenConfirmDeletePopUp(true);
                            }}
                          >
                            <ArchiveOutlinedIcon sx={iconStyles} />
                          </IconButton>
                        ) : (
                          <IconButton
                            aria-label="delete"
                            sx={{ padding: "0px" }}
                            onClick={() => {
                              setArchiveRestoreDate(data), setOpenConfirmRestorePopUp(true);
                            }}
                          >
                            <RestoreIcon sx={iconStyles} />
                          </IconButton>
                        )}
                      </Grid>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={headerArr.length} align="center">
                    <Typography variant="bodySmall" fontWeight={550}>
                      No records found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Grid container>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElement}
            onRecordsPerPageChange={handleRecordsPerPageChange}
            onPageChange={handlePageChange}
            defaultSize={size}
          />
        </Grid>
      </Grid>
      <Grid>
        <CustomDialog
          title={"Add Allergies"}
          open={openAddAllergiesDialog}
          onClose={() => setOpenAddAllergiesDialog(false)}
          buttonName={[]}
          borderRadius="8px"
        >
          <AllergiesForm
            patientId={patientId}
            isEdit={isEdit}
            editDate={editDate}
            handleClose={() => setOpenAddAllergiesDialog(false)}
            refetch={refetch}
          />
        </CustomDialog>
      </Grid>

      <ConfirmationPopUp
        open={openConfirmDeletePopUp}
        confirmButtonName="Archive"
        onClose={() => setOpenConfirmDeletePopUp(false)}
        onConfirm={() => confirmDelete()}
        message={`Do you really want to archive ${archiveRestoreData?.name || "this Allergy"} ?`}
        title={`Archive Item`}
        subtitle={"Are you sure you want to archive the following item?"}
        rowData={[archiveRestoreData?.name || ""]}
        header={[{ header: "Name" }]}
      />

      <ConfirmationPopUp
        open={openConfirmRestorePopUp}
        onClose={() => setOpenConfirmRestorePopUp(false)}
        onConfirm={() => confirmRestore()}
        message={`Do you really want to restore ${archiveRestoreData?.name || "this Allergy"} ?`}
        title={`Restore Item`}
        subtitle={"Are you sure you want to restore the following item?"}
        confirmButtonName="Restore"
        rowData={[archiveRestoreData?.name || ""]}
        header={[{ header: "Name" }]}
      />
    </Grid>
  );
}

export default AllergiesTab;
