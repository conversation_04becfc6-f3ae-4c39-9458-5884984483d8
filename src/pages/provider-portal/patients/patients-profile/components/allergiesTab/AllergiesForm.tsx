import { useState } from "react";
import { <PERSON>, FormProvider, useForm } from "react-hook-form";

import { Button, FormControl, FormControlLabel, Radio, RadioGroup } from "@mui/material";
import { Grid } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { parseISO } from "date-fns";

import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import Select from "@/components/ui/Form/Select";
import useApiFeedback from "@/hooks/useApiFeedback";
import {
  usePatientAllergyControllerServiceCreatePatientAllergy,
  usePatientAllergyControllerServiceUpdatePatientAllergy,
} from "@/sdk/queries";
import { PatientAllergy } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

import { allergiesSchema } from "./allergiesSchema";

const reactions = [
  { value: "PAIN", label: "pain" },
  { value: "RUNNY_NOSE", label: "runny_nose" },
  { value: "SWELLING", label: "swelling" },
  { value: "BLOATING", label: "bloating" },
  { value: "VOMITING", label: "vomiting" },
  { value: "RASHES", label: "rashes" },
  { value: "ITCHY_NOSE", label: "itchy_nose" },
  { value: "THROAT_CLOSING", label: "throat_closing" },
  { value: "COUGH", label: "cough" },
  { value: "REDNESS", label: "redness" },
];

interface AllergiesProps {
  patientId: string | undefined;
  isEdit: boolean;
  editDate: PatientAllergy | undefined;
  handleClose: () => void;
  refetch: () => void;
}

function AllergiesForm(props: AllergiesProps) {
  const { patientId, isEdit, editDate, handleClose, refetch } = props;
  const xTenantId = GetTenantId();
  const [isloadingForm, setIsLoadingForm] = useState(false);

  const initialValue = {
    allergiesType: isEdit ? editDate?.allergyType || "" : "",
    allergiesName: isEdit ? editDate?.name || "" : "",
    reaction: isEdit ? editDate?.reaction || "" : "",
    severity: isEdit ? editDate?.severity || "" : "",
    onsetDate: isEdit && editDate?.onSetDate ? parseISO(editDate?.onSetDate) : null,
    recordedDate: isEdit && editDate?.recordedDate ? parseISO(editDate?.recordedDate) : null,
  };

  const formMethods = useForm({
    defaultValues: initialValue,
    resolver: yupResolver(allergiesSchema),
  });

  const { mutateAsync, isError, error, isSuccess, data } = usePatientAllergyControllerServiceCreatePatientAllergy();

  const {
    mutateAsync: mutateAsyncEdit,
    isSuccess: isSuccessEdit,
    isError: isErrorEdit,
    data: dateEdit,
    error: errorEdit,
  } = usePatientAllergyControllerServiceUpdatePatientAllergy();

  useApiFeedback(isError, error, isSuccess, (data?.message || "Allergies form added successfully") as string);

  useApiFeedback(
    isErrorEdit,
    errorEdit,
    isSuccessEdit,
    (dateEdit?.message || "Allergies form updated successfully") as string
  );

  const onsubmit = async () => {
    const onsetDateValue = formMethods.getValues("onsetDate");
    const recordedDate = formMethods.getValues("recordedDate");
    setIsLoadingForm(true);
    try {
      const payload = {
        patientId: patientId,
        allergyType: formMethods.getValues("allergiesType") || "",
        name: formMethods.getValues("allergiesName") || "",
        reaction: formMethods.getValues("reaction") || "",
        severity: formMethods.getValues("severity") || "",
        onSetDate: onsetDateValue ? new Date(onsetDateValue).toISOString() : "",
        recordedDate: recordedDate ? new Date(recordedDate).toISOString() : "",
      };
      if (!isEdit) {
        await mutateAsync({ requestBody: payload as unknown as PatientAllergy, xTenantId: xTenantId });
      } else {
        await mutateAsyncEdit({
          requestBody: { ...payload, uuid: editDate?.uuid } as unknown as PatientAllergy,
          xTenantId: xTenantId,
        });
      }
      refetch();
      handleClose();
    } finally {
      setIsLoadingForm(false);
    }
  };

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onsubmit)}>
        <Grid
          container
          flexDirection={"column"}
          sx={{ minWidth: "692px", minHeight: "400px" }}
          rowGap={3}
          paddingTop={2}
        >
          <Grid>
            <FormControl>
              <Controller
                name="allergiesType"
                control={formMethods.control}
                render={({ field }) => (
                  <RadioGroup
                    aria-labelledby="demo-radio-buttons-group-label"
                    defaultValue="drug"
                    sx={{ display: "flex", flexDirection: "row", gap: "10px" }}
                    {...field}
                  >
                    <FormControlLabel value="DRUG" control={<Radio />} label="Drug" />
                    <FormControlLabel value="FOOD" control={<Radio />} label="Food" />
                    <FormControlLabel value="ENVIRONMENT" control={<Radio />} label="Environment" />
                  </RadioGroup>
                )}
              />
            </FormControl>
          </Grid>

          <Grid>
            <Input name="allergiesName" isRequired />
          </Grid>
          <Grid container size={12} justifyContent={"space-between"}>
            <Grid container size={5.9}>
              <Select name="reaction" options={reactions} placeholder="Select Reaction" width="100%" />
              {/* <CustomLabel label={"Reaction"} />
              <Controller
                name={"reaction"}
                control={formMethods.control}
                render={({ field }) => (
                  <CustomSelect
                    items={reactions}
                    placeholder="Select Reaction"
                    {...field}
                    name="reaction"
                    value={field.value || ""}
                  />
                )}
              /> */}
            </Grid>

            <Grid container size={5.9}>
              <Select
                name={"severity"}
                options={[
                  { label: "Mild", value: "MILD" },
                  { label: "high", value: "HIGH" },
                  { label: "Moderate", value: "MODERATE" },
                ]}
                placeholder="Select Severity"
                width={"100%"}
                isRequired={true}
              />
            </Grid>
          </Grid>
          <Grid container size={12} justifyContent={"space-between"}>
            <Grid container size={5.9}>
              <Datepicker name={"onsetDate"} label={"Select Onset Date"} />
            </Grid>

            <Grid container size={5.9}>
              <Datepicker name={"recordedDate"} label={"Select Recorded Date"} />
            </Grid>
          </Grid>
          <Grid container justifyContent={"end"}>
            <Button type="submit" variant="contained" loading={isloadingForm}>
              Save
            </Button>
          </Grid>
        </Grid>
      </form>
    </FormProvider>
  );
}

export default AllergiesForm;
