import { ChangeEvent, useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import RestoreIcon from "@mui/icons-material/Restore";
import SyncIcon from "@mui/icons-material/Sync";
import {
  Button,
  IconButton,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid, keyframes } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSwitch from "@/common-components/custom-switch/custom-switch-button";
import Paginator from "@/common-components/paginator/paginator";
import { heading, iconStyles, tableCellCss, typographyCss } from "@/common-components/table/common-table-widgets";

import useApiFeedback from "@/hooks/useApiFeedback";
import { ContentObject } from "@/models/response/response-content-entity";
// import RestoreIcon from "@mui/icons-material/Restore";
import {
  usePatientDiagnosisControllerServiceSyncPatientDiagnosis,
  usePatientDiagnosisControllerServiceUpdatePatientDiagnosisArchiveStatus,
} from "@/sdk/queries";
import { Patient, PatientDiagnosis, PatientDiagnosisControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { toCamelCase } from "@/utils/toCamelCase";

import DiagnosisForm from "./DiagnosisForm";

const headerArr = [
  "Condition Name",
  "Diagnosis Code",
  "Type",
  "Onset Date",
  "Last Occurrence",
  "Update At",
  "Update By",
  "Status",
  "Action",
];

interface DiagnosisProps {
  patientProfileData: Patient | undefined;
}

function DiagnosisTab(props: DiagnosisProps) {
  const { patientProfileData } = props;

  // console.log("patientProfileData", patientProfileData)
  const { patientId } = useParams();
  const xTenantId = GetTenantId();
  const [searchDiagnosisText, setSearchDiagnosisText] = useState("");
  const [openDiagnosisDialog, setopenDiagnosisDialog] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [diagnosisTableData, setDiagnosisTableData] = useState<PatientDiagnosis[]>();
  const [statusValue, setStatusValue] = useState<{ [key: string]: boolean }>({});
  const [editData, setEditdata] = useState<PatientDiagnosis>();

  const [archiveRestoreData, setArchiveRestoreDate] = useState<PatientDiagnosis>();
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);

  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [totalElement, setTotalElements] = useState<number>(0);
  const [totalPages, setTotalPages] = useState(0);

  const handlePageChange = (_event: ChangeEvent<unknown> | null, page: number) => {
    setPage(page);
  };

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  // Get Diagnosis
  const { data, isSuccess, isLoading, refetch } = useQuery({
    queryKey: [searchDiagnosisText, page, size],
    queryFn: () =>
      PatientDiagnosisControllerService.getPatientDiagnosis({
        patientUuid: patientId as string,
        xTenantId: xTenantId,
        searchString: searchDiagnosisText,
      }),
  });
  useEffect(() => {
    if (isSuccess) {
      const res = (data as unknown as AxiosResponse).data as ContentObject<PatientDiagnosis[]>;
      setDiagnosisTableData(res.content);
      setTotalElements(res?.page?.totalElements as number);
      setTotalPages(res?.page?.totalPages as number);
    }
  }, [isSuccess, data]);

  const handleSwitcherChange = (_checked: boolean, uuid: string) => {
    setStatusValue((prev) => ({
      ...prev,
      [uuid]: !prev[uuid],
    }));
    // refetch()
    // handleStatus(uuid, checked);
  };

  // Archive Update
  const {
    mutateAsync: mutateAsyncArchive,
    isError: isErrorStatus,
    error: errorStatus,
    isSuccess: isSuccessStatus,
    data: dataStatus,
  } = usePatientDiagnosisControllerServiceUpdatePatientDiagnosisArchiveStatus();

  useApiFeedback(
    isErrorStatus,
    errorStatus,
    isSuccessStatus,
    (dataStatus?.message || "Diagnosis status updated successfully") as string
  );

  const confirmDelete = async () => {
    await mutateAsyncArchive({
      patientDiagnosisId: archiveRestoreData?.uuid || "",
      status: true,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmDeletePopUp(false);
  };
  const confirmRestore = async () => {
    await mutateAsyncArchive({
      patientDiagnosisId: archiveRestoreData?.uuid || "",
      status: false,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmRestorePopUp(false);
  };

  // const handleStatus = (uuid: string, checked: boolean) => {
  //   if (uuid) {
  //     mutateAsyncStatus({
  //       patientDiagnosisId: uuid || "",
  //       status: checked,
  //       xTenantId: xTenantId,
  //     });
  //   }
  // };

  const handleEditDiagnosis = (data: PatientDiagnosis) => {
    setEditdata(data);
    setIsEdit(true);
    setopenDiagnosisDialog(true);
  };

  const renderTableCell = (data: string | number) => {
    return (
      <TableCell sx={{ ...heading }} align="left">
        <Grid container flexDirection={"column"}>
          <Typography sx={typographyCss} variant="bodySmall">
            {data}
          </Typography>
        </Grid>
      </TableCell>
    );
  };

  const {
    mutateAsync: mutateAsyncSync,
    isSuccess: isSuccessSync,
    data: dataSync,
    error: errorSync,
    isError: isErrorSync,
    isPending: isPendingSync,
  } = usePatientDiagnosisControllerServiceSyncPatientDiagnosis();

  useApiFeedback(isErrorSync, errorSync, isSuccessSync, (dataSync?.message || "Diagnosis Sync Successfully") as string);

  const rotateSyncIcon = keyframes`
   from {
   transform : rotate(0deg)
   }
   to{
   transform : rotate(360deg)
   }
  `;

  const handleSyncCall = async () => {
    await mutateAsyncSync({ patientEhrId: patientProfileData?.ehrId as string, xTenantId: xTenantId });
    refetch();
  };

  return (
    <Grid>
      <Grid container justifyContent={"flex-end"} gap={2} marginBottom={"10px"}>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          <Grid>
            <CustomInput
              placeholder="Search Diagnosis"
              name="diagnosis"
              hasStartSearchIcon={true}
              value={searchDiagnosisText}
              onDebounceCall={(searchString) => setSearchDiagnosisText(searchString)}
              onInputEmpty={() => setSearchDiagnosisText("")}
            />
          </Grid>
        </Grid>
        <Grid>
          <Button
            startIcon={
              <SyncIcon
                sx={{
                  animation: isPendingSync ? `${rotateSyncIcon} 1s linear infinite` : "none",
                }}
              />
            }
            variant="outlined"
            sx={{ borderRadius: "12px", height: "30px", fontWeight: 500 }}
            onClick={handleSyncCall}
            disabled={patientProfileData?.ehrId ? false : true}
          >
            Sync Diagnosis
          </Button>
        </Grid>
      </Grid>

      {/* table */}
      <Grid width={"100%"}>
        <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerArr.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>

            <TableBody>
              {isLoading ? (
                Array.from({ length: 5 }).map((_, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {headerArr.map((_, cellIndex) => (
                      <TableCell key={cellIndex} align="left">
                        <Skeleton variant="text" width="100%" height={40} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : diagnosisTableData && diagnosisTableData?.length > 0 ? (
                diagnosisTableData?.map((data, index) => (
                  <TableRow key={index}>
                    {renderTableCell(data?.name || "-")}
                    {renderTableCell(data?.medicalCode || "-")}
                    {renderTableCell(data?.medicalCodeType || "")}
                    {renderTableCell(data?.startDate ? format(new Date(data?.startDate ?? "-"), "dd-MM-yyyy") : "")}
                    {renderTableCell(toCamelCase(data?.lastOccurrence || "-"))}
                    {renderTableCell(format(new Date(data.modified || "-"), "dd-MM-yyyy"))}
                    {renderTableCell(data.modifiedBy ?? "-")}
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <CustomSwitch
                          onStatusChange={(checked: boolean) => handleSwitcherChange(checked, data?.uuid || "")}
                          status={data.active ? data.active : data.uuid ? statusValue[data?.uuid] : false}
                        />
                      </Grid>
                    </TableCell>
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexWrap={"nowrap"}>
                        <IconButton
                          sx={{ padding: "0px 5px" }}
                          aria-label="edit"
                          onClick={() => handleEditDiagnosis(data)}
                        >
                          <EditOutlinedIcon sx={iconStyles} />
                        </IconButton>
                        {!data.archive ? (
                          <IconButton
                            aria-label="delete"
                            sx={{ padding: "0px" }}
                            onClick={() => {
                              setArchiveRestoreDate(data), setOpenConfirmDeletePopUp(true);
                            }}
                          >
                            <ArchiveOutlinedIcon sx={iconStyles} />
                          </IconButton>
                        ) : (
                          <IconButton
                            aria-label="delete"
                            sx={{ padding: "0px" }}
                            onClick={() => {
                              setArchiveRestoreDate(data), setOpenConfirmRestorePopUp(true);
                            }}
                          >
                            <RestoreIcon sx={iconStyles} />
                          </IconButton>
                        )}
                      </Grid>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={headerArr.length} align="center">
                    <Typography variant="bodySmall" fontWeight={550}>
                      No records found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Grid container>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElement}
            onRecordsPerPageChange={handleRecordsPerPageChange}
            onPageChange={handlePageChange}
            defaultSize={size}
          />
        </Grid>
      </Grid>
      <Grid>
        <CustomDialog
          title={"Add Diagnosis"}
          open={openDiagnosisDialog}
          onClose={() => setopenDiagnosisDialog(false)}
          buttonName={[]}
          borderRadius="8px"
        >
          <DiagnosisForm
            patientId={patientId}
            isEdit={isEdit}
            editDate={editData}
            handleClose={() => setopenDiagnosisDialog(false)}
            refetch={refetch}
          />
        </CustomDialog>
      </Grid>

      <ConfirmationPopUp
        open={openConfirmDeletePopUp}
        confirmButtonName="Archive"
        onClose={() => setOpenConfirmDeletePopUp(false)}
        onConfirm={() => confirmDelete()}
        message={`Do you really want to archive ${archiveRestoreData?.name || "this Diagnosis"} ?`}
        title={`Archive Item`}
        subtitle={"Are you sure you want to archive the following item?"}
        rowData={[archiveRestoreData?.name || ""]}
        header={[{ header: "Name" }]}
      />

      <ConfirmationPopUp
        open={openConfirmRestorePopUp}
        onClose={() => setOpenConfirmRestorePopUp(false)}
        onConfirm={() => confirmRestore()}
        message={`Do you really want to restore ${archiveRestoreData?.name || "this Diagnosis"} ?`}
        title={`Restore Item`}
        subtitle={"Are you sure you want to restore the following item?"}
        confirmButtonName="Restore"
        rowData={[archiveRestoreData?.name || ""]}
        header={[{ header: "Name" }]}
      />
    </Grid>
  );
}

export default DiagnosisTab;
