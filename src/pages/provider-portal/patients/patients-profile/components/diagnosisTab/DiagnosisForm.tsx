import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { Button } from "@mui/material";
import { Grid } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQuery } from "@tanstack/react-query";

import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import Select from "@/components/ui/Form/Select";
import useApiFeedback from "@/hooks/useApiFeedback";
import {
  usePatientDiagnosisControllerServiceCreatePatientDiagnosis,
  usePatientDiagnosisControllerServiceUpdatePatientDiagnosis,
} from "@/sdk/queries";
import { MedicalCode, MedicalCodeControllerService, PatientDiagnosis } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

import { diagnosisSchema } from "./diagnosisSchema";

interface AllergiesProps {
  patientId: string | undefined;
  isEdit: boolean;
  editDate: PatientDiagnosis | undefined;
  handleClose: () => void;
  refetch: () => void;
}
const diagnosisTypes = [
  { value: "CHRONIC", label: "chronic" },
  { value: "ACUTE", label: "acute" },
  { value: "PRIMARY", label: "primary" },
  { value: "SECONDARY", label: "secondary" },
  { value: "DIFFERENTIAL", label: "differential" },
  { value: "PROVISIONAL", label: "provisional" },
  { value: "RESOLVED", label: "resolved" },
];

function DiagnosisForm(props: AllergiesProps) {
  const { patientId, editDate, refetch, isEdit, handleClose } = props;
  const xTenantId = GetTenantId();
  const [icdCodeOptions, setICDCodeOptions] = useState<{ value: string; label: string }[]>([]);
  const initialValue = {
    diagnosis: editDate?.name || "",
    ICD_Code: "",
    status: editDate?.active ?? false,
    type: editDate?.type || "",
    startDate: editDate?.startDate || "",
    note: editDate?.note || "",
  };

  const formMethods = useForm({
    defaultValues: initialValue,
    resolver: yupResolver(diagnosisSchema),
  });

  //getICD-medicalCode
  const { data: IcdData, isSuccess: isSuccessICDApi } = useQuery({
    queryKey: ["CPT"],
    queryFn: () => MedicalCodeControllerService.getMedicalCodes({ type: "CPT", xTenantId: xTenantId }),
  });
  useEffect(() => {
    if (isSuccessICDApi && IcdData) {
      let res = IcdData?.data?.content as MedicalCode[];
      const allData = res.map((codes) => ({
        value: codes.uuid || "",
        label: codes.code || "",
      }));
      setICDCodeOptions(allData);
    }
  }, [isSuccessICDApi, IcdData]);

  //postapi
  const { mutateAsync, isError, error, isSuccess, data } = usePatientDiagnosisControllerServiceCreatePatientDiagnosis();
  //Edit Diagnosis
  const {
    mutateAsync: mutateAsyncEdit,
    isError: isErrorEdit,
    error: errorEdit,
    isSuccess: isSuccessEdit,
    data: dataEdit,
  } = usePatientDiagnosisControllerServiceUpdatePatientDiagnosis();

  useApiFeedback(
    isErrorEdit,
    errorEdit,
    isSuccessEdit,
    (dataEdit?.message || "Diagnosis form added successfully") as string
  );

  useApiFeedback(isError, error, isSuccess, (data?.message || "Diagnosis form updated successfully") as string);

  const onSubmit = async () => {
    const startDateUTC = formMethods.getValues("startDate");
    const payload = {
      patientId: patientId,
      name: formMethods.getValues("diagnosis"),
      medicalCode: { uuid: formMethods.getValues("ICD_Code") },
      status: formMethods.getValues("status"),
      type: formMethods.getValues("type"),
      startDate: startDateUTC ? new Date(startDateUTC).toISOString() : "",
      note: formMethods.getValues("note"),
    };
    if (!isEdit) {
      await mutateAsync({ requestBody: payload as unknown as PatientDiagnosis, xTenantId: xTenantId });
    } else {
      await mutateAsyncEdit({
        requestBody: { ...payload, uuid: editDate?.uuid } as unknown as PatientDiagnosis,
        xTenantId: xTenantId,
      });
    }
    handleClose();
    refetch();
  };

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSubmit)}>
        <Grid
          container
          flexDirection={"column"}
          sx={{ minWidth: "692px", minHeight: "400px" }}
          paddingTop={"20px"}
          borderBottom={"1px solid #E8EBEC"}
          rowGap={3}
        >
          <Grid display={"flex"} justifyContent={"space-between"} gap={2}>
            <Grid container size={6}>
              <Input name="diagnosis" placeholder="Enter Diagnosis Name" />
            </Grid>
            <Grid container size={6}>
              <Select
                width={"100%"}
                name="ICD_Code"
                label="ICD Code"
                options={icdCodeOptions}
                placeholder="Search and Select ICD Code"
                isRequired
              />
            </Grid>
          </Grid>
          <Grid display={"flex"} justifyContent={"space-between"} gap={2}>
            <Grid container size={6}>
              <Autocomplete
                name="status"
                options={[
                  { value: "true", label: "Active" },
                  { value: "false", label: "InActive" },
                ]}
                placeholder="Search Status "
              />
            </Grid>
            <Grid container size={6}>
              <Select
                width={"100%"}
                name="type"
                label="Type"
                options={diagnosisTypes}
                placeholder="Select Type"
                isRequired
              />
            </Grid>
          </Grid>
          <Grid>
            <Datepicker name="startDate" label="Start Date" />
          </Grid>
          <Grid>
            <Input name="note" label="Enter Note" />
          </Grid>
        </Grid>
        <Grid container justifyContent={"flex-end"} paddingTop={"10px"}>
          <Button type="submit" variant="contained">
            Save
          </Button>
        </Grid>
      </form>
    </FormProvider>
  );
}

export default DiagnosisForm;
