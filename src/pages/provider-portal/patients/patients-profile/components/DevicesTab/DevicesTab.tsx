import { ChangeEvent, useEffect, useState } from "react";

import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid2 as Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";

import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelector from "@/common-components/custom-selector-sq/custom-selector-sq";
import Paginator from "@/common-components/paginator/paginator";
import Status from "@/common-components/status/status";
import { heading, tableCellCss } from "@/common-components/table/common-table-widgets";

import { ContentObject } from "@/models/response/response-content-entity";
import { Patient, PatientControllerService, PatientTrainingControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

const typographyCss = {
  fontFamily: "Roboto",
  fontWeight: 400,
  fontSize: "14px",
  lineHeight: "20px",
  letterSpacing: "0%",
  color: "#212D30",
};

interface DeviceProps {
  patientProfileData: Patient | undefined;
}

export type RPMDevice = {
  deviceModelId: string;
  name: string;
  deviceType: "RPM" | "Infusion"; // Add other types if needed
  description: string;
  guideLink: string;
  active: boolean;
  archive: boolean;
  trainingConfirmed: boolean;
  assignedDate: string;
};

const DevicesTab = (props: DeviceProps) => {
  const { patientProfileData } = props;
  const xTenantId = GetTenantId();
  const patientId = patientProfileData?.uuid;

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDeviceType, setSelectedDeviceType] = useState<string>("RPM");

  const [devices, setDevices] = useState<RPMDevice[]>([]);
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [totalElements, setTotalElements] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Fetch RPM devices only when RPM is selected
  const {
    data: devicesData,
    isLoading,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: ["rpm-devices", xTenantId, patientId, page, size],
    queryFn: () =>
      PatientControllerService.getAssignedDevices({
        xTenantId,
        patientId: patientId!,
        page,
        size,
      }),
    enabled: selectedDeviceType === "RPM" && !!patientId,
  });

  useEffect(() => {
    if (isSuccess && selectedDeviceType === "RPM") {
      const response = (devicesData as unknown as AxiosResponse).data as ContentObject<RPMDevice[]>;
      const deviceList = response?.content ?? [];

      setDevices(deviceList);
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);
    }
  }, [devicesData, selectedDeviceType]);

  const handleFilterChange = (value: string) => {
    setSelectedDeviceType(value);
    setPage(0);
    setSize(10);
    setTotalElements(0);
    setTotalPages(0);
    setDevices([]);
  };

  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null);

  const updateTrainingStatus = useMutation({
    mutationKey: ["update-training-status"],
    mutationFn: async (deviceModelId: string) => {
      return PatientTrainingControllerService.addUpdateTrainedDevice({
        requestBody: {
          deviceModelId,
          patientId: patientProfileData?.uuid, // from props
          trainingConfirmed: true,
        },
      });
    },
    onSuccess: () => {
      refetch();
    },
  });

  const handlePageChange = (event: ChangeEvent<unknown> | null, page: number) => {
    event;
    setPage(page);
  };

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const filteredDevices = devices.filter((device) =>
    Object.values(device).some((value) => String(value).toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const headers =
    selectedDeviceType === "RPM"
      ? ["Device Name", "Device ID", "Serial ID", "Date of assignment", "RPM Onboarding & Training Status", "Action"]
      : ["Device Name", "Device ID", "Serial ID", "Date of assignment"];

  return (
    <Grid container direction="column">
      {/* Header section */}
      <Grid container spacing={0} alignItems="center" sx={{ padding: "16px", justifyContent: "space-between" }}>
        <Grid>
          <CustomSelector
            options={["Infusion", "RPM"]}
            onSelect={handleFilterChange}
            selectedValue={selectedDeviceType}
            widthOfBtn="100px"
          />
        </Grid>
        <Grid>
          <CustomInput
            placeholder={`Search ${selectedDeviceType} Devices`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            name="searchDevices"
            hasStartSearchIcon={true}
          />
        </Grid>
      </Grid>

      {/* Table section */}
      <Grid width="100%">
        <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="devices table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headers.map((header, index) => (
                  <TableCell key={index} sx={{ ...heading }} align="left">
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={headers.length} align="center">
                    <Typography>Loading...</Typography>
                  </TableCell>
                </TableRow>
              ) : filteredDevices.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={headers.length} align="center">
                    <Typography>No devices assigned</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredDevices.map((device, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography sx={typographyCss}>{device.name}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>-</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>-</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>
                        {device?.assignedDate ? format(new Date(device?.assignedDate), "dd/MM/yyyy") : "-"}
                      </Typography>
                    </TableCell>
                    {selectedDeviceType === "RPM" && (
                      <>
                        <TableCell>
                          <Status status={device.trainingConfirmed ? "TRAINED" : "NOT_TRAINED"} width="100px" />
                        </TableCell>
                        <TableCell>
                          {!device.trainingConfirmed ? (
                            <Button
                              size="small"
                              sx={{
                                height: "fit-content",
                                width: "100px",
                                backgroundColor: "#006D8F",
                                color: "white",
                              }}
                              onClick={() => {
                                setSelectedDeviceId(device.deviceModelId);
                                setConfirmModalOpen(true);
                              }}
                              variant="contained"
                              type="submit"
                            >
                              <Typography variant="bodySmall">Complete</Typography>
                            </Button>
                          ) : (
                            "-"
                          )}
                        </TableCell>
                      </>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Grid>

      {selectedDeviceType === "RPM" && (
        <Grid container justifyContent={"flex-end"} pt={2} pb={2}>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElements}
            onPageChange={handlePageChange}
            onRecordsPerPageChange={handleRecordsPerPageChange}
          />
        </Grid>
      )}
      <Dialog
        open={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        maxWidth="sm"
        PaperProps={{
          sx: {
            borderRadius: "16px",
            padding: "24px",
            minWidth: "420px",
          },
        }}
      >
        <DialogTitle sx={{ p: 0 }} width="448px">
          <Typography fontSize={20} fontWeight={500} color="#1C2427">
            Confirm Training Completion
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ p: 0, mt: 1 }}>
          <Typography fontSize={16} fontWeight={400} color="#364144">
            Are you sure you want to mark this device’s training as complete?
          </Typography>
        </DialogContent>

        <DialogActions sx={{ p: 0, mt: 3, justifyContent: "flex-end" }}>
          <Box display="flex" gap={2}>
            <Button
              onClick={() => setConfirmModalOpen(false)}
              variant="outlined"
              sx={{ borderRadius: 2, textTransform: "none", px: 3 }}
            >
              Cancel
            </Button>
            <Button
              loading={isLoading}
              onClick={async () => {
                if (selectedDeviceId) {
                  await updateTrainingStatus.mutateAsync(selectedDeviceId);
                  setConfirmModalOpen(false);
                }
              }}
              variant="contained"
              sx={{ borderRadius: 2, textTransform: "none", px: 3 }}
            >
              Confirm
            </Button>
          </Box>
        </DialogActions>
      </Dialog>
    </Grid>
  );
};

export default DevicesTab;
