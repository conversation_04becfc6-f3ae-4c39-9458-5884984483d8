import { ChangeEvent, useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import RestoreIcon from "@mui/icons-material/Restore";
import SyncIcon from "@mui/icons-material/Sync";
import {
  Button,
  IconButton,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Box, Grid, keyframes } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelectorSq from "@/common-components/custom-selector-sq/custom-selector-sq";
import Paginator from "@/common-components/paginator/paginator";
import { heading, iconStyles, tableCellCss, typographyCss } from "@/common-components/table/common-table-widgets";

import useApiFeedback from "@/hooks/useApiFeedback";
import { ContentObject } from "@/models/response/response-content-entity";
import {
  usePatientMedicationControllerServiceDeletePatientMedicationId,
  usePatientMedicationControllerServiceSyncPatientMedication,
} from "@/sdk/queries";
import { Patient, PatientMedication, PatientMedicationControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

import MedicationForm from "./MedicationForm";

const headerArr = [
  "Medicines",
  "Directions",
  "Start Date",
  "Duration",
  "Note",
  "Est. End Date",
  "Remaining Duration",
  "Action",
];

interface MedicationProps {
  patientProfileData: Patient | undefined;
}

function MedicationsTab(props: MedicationProps) {
  const { patientProfileData } = props;
  const xTenantId = GetTenantId();
  const [selectedfilterStatus, setSelectedFilterStatus] = useState("CURRENT");
  const [searchMedication, setSearchMedication] = useState("");

  const [openMedicationFormDialog, setMedicationFormDialog] = useState(false);
  const [editMedicationDate, setEditMedicationDate] = useState<PatientMedication>();

  const { patientId } = useParams();
  const [isMedicationDate, setIsMedicationDate] = useState<PatientMedication[]>();
  const [isEdit, setIsEdit] = useState(false);

  const [archiveRestoreData, setArchiveRestoreDate] = useState<PatientMedication>();
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);

  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [totalElement, setTotalElements] = useState<number>(0);
  const [totalPages, setTotalPages] = useState(0);

  const handlePageChange = (_event: ChangeEvent<unknown> | null, page: number) => {
    setPage(page);
  };

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handleEditDate = (medi: PatientMedication) => {
    setMedicationFormDialog(true);
    setEditMedicationDate(medi);
    setIsEdit(true);
  };

  const {
    data: medicationDate,
    isSuccess,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["medication", searchMedication, page, size, selectedfilterStatus],
    queryFn: () =>
      PatientMedicationControllerService.getPatientMedication({
        patientUuid: patientId as string,
        searchString: searchMedication,
        timeFilter: selectedfilterStatus as "CURRENT" | "PAST",
        xTenantId: xTenantId,
      }),
  });
  useEffect(() => {
    if (isSuccess) {
      const res = (medicationDate as unknown as AxiosResponse)?.data as ContentObject<PatientMedication[]>;
      setIsMedicationDate(res.content);
      setTotalElements(res.page?.totalElements as number);
      setTotalPages(res.page?.totalPages as number);
    }
  }, [medicationDate, isSuccess]);

  // restoreApi
  const {
    mutateAsync: mutateAsyncArchive,
    isError: isErrorArchive,
    error: errorArchive,
    isSuccess: isSuccessArchive,
    data: dataArchive,
  } = usePatientMedicationControllerServiceDeletePatientMedicationId();

  useApiFeedback(
    isErrorArchive,
    errorArchive,
    isSuccessArchive,
    (dataArchive?.message || "User archive status updated!") as string
  );

  const confirmDelete = async () => {
    await mutateAsyncArchive({
      patientMedicationId: archiveRestoreData?.uuid || "",
      status: true,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmDeletePopUp(false);
  };

  const confirmRestore = async () => {
    await mutateAsyncArchive({
      patientMedicationId: archiveRestoreData?.uuid || "",
      status: false,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmRestorePopUp(false);
  };

  const {
    mutateAsync: mutateAsyncSync,
    isSuccess: isSuccessSync,
    data: dataSync,
    error: errorSync,
    isError: isErrorSync,
    isPending: isPendingSync,
  } = usePatientMedicationControllerServiceSyncPatientMedication();

  useApiFeedback(isErrorSync, errorSync, isSuccessSync, (dataSync?.data || "Medication Sync Successfully") as string);

  const handleAllergiesSyncCall = async () => {
    await mutateAsyncSync({ patientEhrId: patientProfileData?.ehrId as string, xTenantId: xTenantId });
    refetch();
  };
  // usePatientMedicationControllerServiceSyncPatientMedication

  const renderTableRowCell = (celldata: string | number) => {
    return (
      <TableCell sx={{ maxHeight: "10px !important" }} align="left">
        <Grid container flexDirection={"column"}>
          <Typography sx={typographyCss} variant="bodySmall">
            {celldata}
          </Typography>
        </Grid>
      </TableCell>
    );
  };

  const rotateIcon = keyframes`
  from {
    transform: rotate(0deg)
  }
    to{
    transform : rotate(360deg)
    }
  `;

  return (
    <Grid
      height={"100%"}
      overflow={"auto"}
      sx={{
        "&::-webkit-scrollbar": {
          height: "0px",
          width: "0px",
        },
        "&::-webkit-scrollbar-button": {
          display: "none",
        },
      }}
    >
      <Grid container justifyContent="space-between" alignItems="center" pb={1}>
        <Grid>
          <CustomSelectorSq
            options={["CURRENT", "PAST"]}
            onSelect={(filterField) => setSelectedFilterStatus(filterField)}
            selectedValue={selectedfilterStatus || ""}
            widthOfBtn="100px"
          />
        </Grid>

        <Grid display="flex" alignItems="center" gap={1}>
          <Box width="220px">
            <CustomInput
              placeholder="Search Medication"
              name="medication"
              hasStartSearchIcon={true}
              value={searchMedication}
              onDebounceCall={(value) => setSearchMedication(value)}
              onInputEmpty={() => setSearchMedication("")}
            />
          </Box>

          {/* Add Medication Button */}
          <Button
            startIcon={
              <SyncIcon
                sx={{
                  animation: isPendingSync ? `${rotateIcon} 1s linear infinite` : "none",
                }}
              />
            }
            variant="outlined"
            onClick={handleAllergiesSyncCall}
            sx={{ borderRadius: "12px", height: "30px", fontWeight: 500 }}
            disabled={patientProfileData?.ehrId ? false : true}
          >
            Sync Medications
          </Button>
        </Grid>
      </Grid>

      {/* table */}
      <Grid width={"100%"}>
        <TableContainer sx={{ maxHeight: "58vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerArr.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>

            <TableBody>
              {isLoading
                ? Array.from({ length: 5 }).map((_, rowIndex) => (
                    <TableRow key={rowIndex}>
                      {headerArr.map((_, cellIndex) => (
                        <TableCell key={cellIndex} align="left">
                          <Skeleton variant="text" width="100%" height={40} />
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                : isMedicationDate?.map((data, rowIndex) => (
                    <TableRow key={rowIndex}>
                      {renderTableRowCell(data.medicineName || "")}
                      {renderTableRowCell("")}
                      {renderTableRowCell(
                        data?.startDate ? format(new Date(data.startDate ?? "-"), "dd MMM yyyy") : "-"
                      )}
                      {renderTableRowCell("-")}
                      {renderTableRowCell(data.note ?? "-")}

                      {renderTableRowCell(data?.endDate ? format(new Date(data?.endDate ?? "-"), "dd MMM yyyy") : "-")}
                      {renderTableRowCell("-")}

                      <TableCell sx={{ ...heading }} align="left">
                        <Grid container flexWrap={"nowrap"}>
                          <IconButton
                            sx={{ padding: "0px 5px" }}
                            aria-label="edit"
                            onClick={() => handleEditDate(data)}
                          >
                            <EditOutlinedIcon sx={iconStyles} />
                          </IconButton>
                          {!data.archive ? (
                            <IconButton
                              aria-label="delete"
                              sx={{ padding: "0px" }}
                              onClick={() => {
                                setArchiveRestoreDate(data), setOpenConfirmDeletePopUp(true);
                              }}
                            >
                              <ArchiveOutlinedIcon sx={iconStyles} />
                            </IconButton>
                          ) : (
                            <IconButton
                              aria-label="delete"
                              sx={{ padding: "0px" }}
                              onClick={() => {
                                setArchiveRestoreDate(data), setOpenConfirmRestorePopUp(true);
                              }}
                            >
                              <RestoreIcon sx={iconStyles} />
                            </IconButton>
                          )}
                        </Grid>
                      </TableCell>
                    </TableRow>
                  ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Grid container>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElement}
            onRecordsPerPageChange={handleRecordsPerPageChange}
            onPageChange={handlePageChange}
            defaultSize={size}
          />
        </Grid>
      </Grid>
      <Grid>
        <CustomDialog
          title={"Add Medication"}
          open={openMedicationFormDialog}
          onClose={() => setMedicationFormDialog(false)}
          buttonName={[]}
          borderRadius="8px"
        >
          <MedicationForm
            patientId={patientId}
            isEdit={isEdit}
            editDate={editMedicationDate}
            handleClose={() => setMedicationFormDialog(false)}
            refetch={refetch}
          />
        </CustomDialog>
      </Grid>
      <ConfirmationPopUp
        open={openConfirmDeletePopUp}
        confirmButtonName="Archive"
        onClose={() => setOpenConfirmDeletePopUp(false)}
        onConfirm={() => confirmDelete()}
        message={`Do you really want to archive ${archiveRestoreData?.medicineName || "this medication"} ?`}
        title={`Archive Item`}
        subtitle={"Are you sure you want to archive this medication details?"}
        rowData={[archiveRestoreData?.medicineName || ""]}
        header={[{ header: "Name" }]}
      />

      <ConfirmationPopUp
        open={openConfirmRestorePopUp}
        onClose={() => setOpenConfirmRestorePopUp(false)}
        onConfirm={() => confirmRestore()}
        message={`Do you really want to restore ${archiveRestoreData?.medicineName || "this medication"} ?`}
        title={`Restore Item`}
        subtitle={"Are you sure you want to restore this medication details?"}
        confirmButtonName="Restore"
        rowData={[archiveRestoreData?.medicineName || ""]}
        header={[{ header: "Name" }]}
      />
    </Grid>
  );
}

export default MedicationsTab;
