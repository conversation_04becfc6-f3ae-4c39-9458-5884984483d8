import { FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { Button } from "@mui/material";
import { Grid } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { parseISO } from "date-fns";
import * as yup from "yup";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { PatientMedication, PatientMedicationControllerService } from "@/sdk/requests";

const medicineList = [
  { value: "paracetamol", label: "Paracetamol" },
  { value: "ibuprofen", label: "Ibuprofen" },
  { value: "amoxicillin", label: "Amoxicillin" },
  { value: "cetirizine", label: "Cetirizine" },
  { value: "azithromycin", label: "Azithromycin" },
  { value: "loratadine", label: "Loratadine" },
  { value: "metformin", label: "Metformin" },
  { value: "atorvastatin", label: "Atorvastatin" },
  { value: "omeprazole", label: "Omeprazole" },
  { value: "salbutamol", label: "Salbutamol" },
  { value: "diclofenac", label: "Diclofenac" },
  { value: "ranitidine", label: "Ranitidine" },
  { value: "doxycycline", label: "Doxycycline" },
  { value: "clindamycin", label: "Clindamycin" },
  { value: "prednisolone", label: "Prednisolone" },
];

const medicationSchema = yup.object().shape({
  medicineName: yup.string(),
  // direction: yup.string(),
  startDate: yup.date().nullable(),
  endDate: yup.date().nullable(),
});

interface MedicationProps {
  patientId: string | undefined;
  isEdit: boolean;
  editDate: PatientMedication | undefined;
  handleClose: () => void;
  refetch: () => void;
}

function MedicationForm(props: MedicationProps) {
  const { patientId, editDate, isEdit, handleClose, refetch } = props;
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const initialValue = {
    medicineName: isEdit ? editDate?.medicineName : "",
    // direction: isEdit ? editDate?.active : "",
    startDate: isEdit && editDate?.startDate ? parseISO(editDate?.startDate) : null,
    endDate: isEdit && editDate?.endDate ? parseISO(editDate?.endDate) : null,
  };

  const formMethods = useForm({
    defaultValues: initialValue,
    resolver: yupResolver(medicationSchema),
  });

  const postMedication = useMutation({
    mutationFn: (data: PatientMedication) =>
      PatientMedicationControllerService.createPatientMedication({ requestBody: data }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["medication"] });
      dispatch(setSnackbarOn({ severity: AlertSeverity.SUCCESS, message: "Medication Added successfully" }));
    },
  });

  const updateMedication = useMutation({
    mutationFn: (postData: PatientMedication) =>
      PatientMedicationControllerService.updatePatientMedication({ requestBody: postData }),
    onSuccess: () => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.SUCCESS, message: "Medication Updated successfully" }));
    },
  });

  const onSubmit = () => {
    const startDate = formMethods.getValues("startDate");
    const endDate = formMethods.getValues("endDate");

    const payload: PatientMedication = {
      patientId: patientId || "",
      medicineName: formMethods.getValues("medicineName") || "",
      // direction: formMethods.getValues("direction") || "",
      startDate: startDate ? new Date(startDate).toISOString() : "",
      endDate: endDate ? new Date(endDate).toISOString() : "",
    };
    if (!isEdit) {
      postMedication.mutate(payload);
    } else {
      updateMedication.mutate({ ...payload, uuid: editDate?.uuid });
    }
    refetch();
    handleClose();
  };

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSubmit)}>
        <Grid container flexDirection={"column"} sx={{ minWidth: "692px", minHeight: "300px" }} rowGap={3} mt={2}>
          <Grid>
            <Autocomplete name="medicineName" options={medicineList} placeholder="Search and Select Medicine Name" />
          </Grid>
          <Grid>
            <Input name="direction" placeholder="Enter Medicine direction" />
          </Grid>
          <Grid display={"flex"} justifyContent={"space-between"} gap={2}>
            <Grid container size={6}>
              <Datepicker name="startDate" placeholder="Select Start Date" label="Start Date" />
            </Grid>
            <Grid container size={6}>
              <Datepicker name="endDate" placeholder="Select End Date" label="End Date" />
            </Grid>
          </Grid>
          <Grid container justifyContent={"end"} pt={2}>
            <Button type="submit" variant="contained">
              Save
            </Button>
          </Grid>
        </Grid>
      </form>
    </FormProvider>
  );
}

export default MedicationForm;
