import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";

import {
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import CustomInput from "@/common-components/custom-input/custom-input";
import Paginator from "@/common-components/paginator/paginator";
import Status from "@/common-components/status/status";
import { heading, tableCellCss } from "@/common-components/table/common-table-widgets";

import { ContentObject } from "@/models/response/response-content-entity";
import { setIsLoading } from "@/redux/actions/loader-action";
import { ConsentFormControllerService, PatientConsentForm } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";

// Extended type to include the additional fields from API response
interface ExtendedPatientConsentForm extends PatientConsentForm {
  modified?: string;
  date?: string;
  created?: string;
}

// headers
const headerName = ["Forms", "Date of consent", "Status"];

function ConsentTab() {
  const dispatch = useDispatch();
  const { patientId } = useParams();

  // Pagination state
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState<number>(0);
  const [size, setSize] = useState(10);
  const [searchConsentForm, setSearchConsentForm] = useState("");

  // Dialog state
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedForm, setSelectedForm] = useState<ExtendedPatientConsentForm | null>(null);
  const [documentUrl, setDocumentUrl] = useState<string>("");
  const [isLoadingContent, setIsLoadingContent] = useState(false);

  // CSS styles
  const typographyCss = {
    fontFamily: "Roboto",
    fontWeight: 400,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "0%",
    color: "#212D30",
  };

  // API call to get consent forms
  const {
    data: consentForms,
    isLoading,
    isRefetching,
  } = useQuery({
    queryKey: ["consent-forms", page, size, searchConsentForm],
    queryFn: () =>
      ConsentFormControllerService.getAllPatientConsentForm({
        xTenantId: GetTenantId(),
        page,
        size,
        patientUuid: patientId as string,
        sortBy: "created",
        sortDirection: "desc",
        searchString: searchConsentForm,
      }),
  });

  let content: ExtendedPatientConsentForm[] = [];
  if (consentForms) {
    const response = (consentForms as unknown as AxiosResponse).data as ContentObject<ExtendedPatientConsentForm[]>;
    content = response?.content || [];
    if (response?.page) {
      if (totalPages !== response.page.totalPages) setTotalPages(response.page.totalPages as number);
      if (totalElements !== response.page.totalElements) setTotalElements(response.page.totalElements as number);
    }
  }

  useEffect(() => {
    dispatch(setIsLoading(isLoading || isRefetching));
  }, [dispatch, isLoading, isRefetching]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handlePageChange = (_event: ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage);
  };

  // Format date function to display in a readable format
  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    const date = new Date(dateString);

    const day = date.getDate().toString().padStart(2, "0");
    const month = date.toLocaleString("en-US", { month: "short" });
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
  };

  const handleRowClick = async (form: ExtendedPatientConsentForm) => {
    if (!form.uuid) return;

    setSelectedForm(form);
    setDialogOpen(true);
    setIsLoadingContent(true);

    try {
      const response = await ConsentFormControllerService.getPatientConsentFormById({
        patientConsentFormUuid: form.uuid,
        xTenantId: GetTenantId(),
      });

      const formData = (response as unknown as AxiosResponse).data;
      if (formData && formData.consentFormTemplate) {
        const documentLink = formData.consentFormTemplate.document;

        if (documentLink) {
          setDocumentUrl(documentLink);
        } else {
          setDocumentUrl("");
        }
      } else {
        setDocumentUrl("");
      }
    } catch (error) {
      setDocumentUrl("");
    } finally {
      setIsLoadingContent(false);
    }
  };

  // Handle dialog close
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedForm(null);
    setDocumentUrl("");
  };

  return (
    <>
      <Grid>
        <Grid display={"flex"} flexDirection={"row"}>
          <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}></Grid>
          <Grid container mb={2} px={2} pt={2}>
            <Grid>
              <CustomInput
                placeholder={`Search consent form`}
                name="code"
                hasStartSearchIcon={true}
                value={searchConsentForm}
                onDebounceCall={(searchString) => setSearchConsentForm(searchString)}
                onInputEmpty={() => setSearchConsentForm("")}
              />
            </Grid>
          </Grid>
        </Grid>
        <Grid>
          <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
            <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
              <TableHead>
                <TableRow>
                  {headerName.map((header, index) => (
                    <TableCell
                      sx={{
                        ...heading,
                      }}
                      align="left"
                      key={index}
                    >
                      <Typography fontWeight={550} variant="bodySmall" color="#667085" sx={{ fontStyle: "Roboto" }}>
                        {header}
                      </Typography>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading || isRefetching ? (
                  [...Array(5)].map((_, index) => (
                    <TableRow key={index}>
                      {[...Array(3)].map((_, cellIndex) => (
                        <TableCell key={cellIndex}>
                          <Skeleton variant="text" width={100} />
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : content.length > 0 ? (
                  content.map((item: ExtendedPatientConsentForm, index: number) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Typography
                          onClick={() => handleRowClick(item)}
                          sx={{
                            cursor: "pointer",
                            color: theme.palette.primary.main,
                            fontWeight: 550,
                            fontSize: "14px",
                          }}
                          variant="bodySmall"
                        >
                          {item.consentFormTemplate?.name || "-"}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography sx={typographyCss}>{formatDate(item.modified)}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography>
                          <Status status={item?.consentFormTemplate?.signed ? "SIGNED" : "UNSIGNED"} width="100px" />
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={3} align="center">
                      <Typography>No consent forms found</Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Grid container justifyContent={"flex-end"} p={2}>
            <Paginator
              page={page}
              totalPages={totalPages}
              totalRecord={totalElements}
              onPageChange={handlePageChange}
              onRecordsPerPageChange={handleRecordsPerPageChange}
            />
          </Grid>
        </Grid>
      </Grid>

      {/* Consent Form Detail Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{selectedForm?.consentFormTemplate?.name || "Consent Form"}</DialogTitle>
        <DialogContent dividers>
          {isLoadingContent ? (
            <div style={{ display: "flex", justifyContent: "center", alignItems: "center", minHeight: 400 }}>
              <CircularProgress />
            </div>
          ) : documentUrl ? (
            <iframe
              src={documentUrl}
              width="100%"
              height="600px"
              style={{ border: "none" }}
              title="Consent Form Document"
            />
          ) : (
            <Typography color="error">No document available.</Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default ConsentTab;
