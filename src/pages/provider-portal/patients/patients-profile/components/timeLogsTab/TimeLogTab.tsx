import { ChangeEvent, useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";

import AddIcon from "@mui/icons-material/Add";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import CloseIcon from "@mui/icons-material/Close";
import EditIcon from "@mui/icons-material/Edit";
import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import {
  Box,
  Button,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid2 as Grid,
  IconButton,
  Link,
  Paper,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import * as yup from "yup";

import CustomAutoComplete from "@/common-components/custom-auto-complete/custom-auto-complete";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import DatePickerField from "@/common-components/date-picker-field/date-picker-field";
import Paginator from "@/common-components/paginator/paginator";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import { heading, iconStyles, tableCellCss } from "@/common-components/table/common-table-widgets";
import { Timepicker } from "@/common-components/time-picker/TimePicker";

import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import useAuthority from "@/hooks/use-authority";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { LogType, logTitleOptions } from "@/pages/provider-portal/commonFiles/staticOptionsData";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { RootState } from "@/redux/store";
import { TimeLogRequest } from "@/sdk/requests";
import { TimeLogControllerService } from "@/sdk/requests/services.gen";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { DateNewFormat, convertSecondsToHMS, formatTime, formatTimeNewFormat, formatDateNewFormat } from "@/utils/format/date";
import { isSameDay } from "date-fns";

interface ExtendedTimeLogRequest extends TimeLogRequest {
  uuid: string;
  activityName: string;
  logStartTime: string;
  role: string;
  logEndTime: string;
  duration: string;
  loggedByName: string;
  logEntryType: "AUTOMATIC" | "MANUAL";
  created: string;
}

type TimeLogViewData = {
  activityName: string;
  billingCycle: string;
  created: string;
  logEndTime: string;
  logEntryType: string;
  logIdentifier: string;
  logStartTime: string;
  logTimeDuration: number;
  loggedBy: string;
  loggedByExternal: boolean;
  loggedByName: string;
  modified: string;
  note: string | null;
  patientId: string;
  patientName: string;
  uuid: string;
};

const TimeLogsTab = () => {
  const { isProvider } = useAuthority();

  const headers = [
    "Log ID",
    "Log Name",
    "Date",
    "Logged By",
    "Duration",
    "Start Time",
    "End Time",
    "Log Type",
    ...(isProvider ? ["Actions"] : []),
  ];

  // Form validation schema
  interface TimeLogFormData {
    logTitle: string;
    date: Date;
    startTime: Date;
    endTime: Date;
    note?: string;
  }

  const formSchema = yup.object({
    logTitle: yup.string().required("Log Title is required"),
    date: yup.date().required("Date is required"),
    startTime: yup
      .date()
      .required("Start time is required")
      .when("date", (date, schema) => {
        const actualDate = Array.isArray(date) ? date[0] : date;
        if (!actualDate) return schema;
        const now = new Date();
        const selectedDate = new Date(actualDate);
        const isSameDay = now.toDateString() === selectedDate.toDateString();
        return isSameDay ? schema.max(now, "Start time cannot be in the future") : schema;
      }),
    endTime: yup
      .date()
      .required("End time is required")
      .when("date", (date, schema) => {
        const actualDate = Array.isArray(date) ? date[0] : date;
        if (!actualDate) return schema;
        const now = new Date();
        const selectedDate = new Date(actualDate);
        const isSameDay = now.toDateString() === selectedDate.toDateString();
        return isSameDay ? schema.max(now, "End time cannot be in the future") : schema;
      }),
    note: yup.string(),
  });

  // Form
  const methods = useForm<TimeLogFormData>({
    resolver: yupResolver(formSchema),
  });
  const { handleSubmit, setValue, reset, watch } = methods;

  const patientId = useParams<{ patientId: string }>().patientId;

  // const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [totalElements, setTotalElements] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTimeLog, setSelectedTimeLog] = useState<ExtendedTimeLogRequest | null>(null);
  const [selectedViewTimeLog, setSelectedViewTimeLog] = useState<string | null>(null);
  const [timeLogViewData, setTimeLogViewData] = useState<TimeLogViewData | null>(null);

  const [openTimeLogDialog, setOpenTimeLogDialog] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const [timeLogsData, setTimeLogsData] = useState<ExtendedTimeLogRequest[]>([]);

  const [viewFilters, setViewFilters] = useState(false);
  const [filterLogTitle, setFilterLogTitle] = useState<string | null>(null);
  const [filterLogType, setFilterLogType] = useState<string | null>(null);
  const [filterDate, setFilterDate] = useState<string | null>(null);

  const [showViewDialog, setShowViewDialog] = useState(false);
  const [sortDirection, setSortDirection] = useState("desc");
  const [sortBy, setSortBy] = useState("logStartTime");

  const handlePageChange = (_event: ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage);
  };

  // Get the current logged-in user profile data based on role
  const { data: currentUserData } = useSelector((state: RootState) =>
    isProvider ? state.providerProfileReducer : state.profileReducer
  );

  const {
    data: timeLogDataResponse,
    isLoading,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: [GetTenantId(), page, size, filterDate, filterLogType, filterLogTitle, sortBy, sortDirection],
    queryFn: () =>
      TimeLogControllerService.getAllPatientTimeLogs({
        xTenantId: GetTenantId(),
        patientId: patientId || "",
        page,
        size,
        loggedBy: currentUserData?.userId,
        sort: sortDirection,
        sortBy: sortBy,
        month: `${new Date().toLocaleString("default", { month: "short" }).toUpperCase()}-${new Date().getFullYear()}`,
        activityName: filterLogTitle || undefined,
        loggedEntryType: filterLogType || undefined,
      }),
    enabled: !!currentUserData?.userId,
  });

  useEffect(() => {
    if (isSuccess) {
      const responseData = (timeLogDataResponse as unknown as AxiosResponse).data;
      const timeLogs = responseData?.timeLogs || [];
      setTimeLogsData(timeLogs);

      setTotalPages((responseData?.pagination?.totalPages as number) ?? 0);
      setTotalElements((responseData?.totalCount as number) ?? 0);
    }
  }, [timeLogDataResponse]);

  // Handle records per page change
  const handleRecordsPerPageChange = (newSize: number) => {
    setSize(newSize);
    setPage(0);
  };

  const HandleEdit = (log: ExtendedTimeLogRequest) => {
    setIsEdit(true);
    setSelectedTimeLog(log);
    setOpenTimeLogDialog(true);

    setValue("logTitle", log?.activityName);
    setValue("date", new Date(log.logStartTime));
    setValue("startTime", new Date(log.logStartTime));
    setValue("endTime", new Date(log.logEndTime));
    setValue("note", log?.note);
  };

  const dispatch = useDispatch();

  const createTimeLogMutation = useMutation({
    mutationFn: ({
      patientId,
      activityName,
      logStartTime,
      logEndTime,
      note,
      logEntryType,
    }: {
      patientId: string;
      activityName: string;
      logStartTime: string;
      logEndTime: string;
      note: string;
      logEntryType: string;
    }) =>
      TimeLogControllerService.createTimeLogAsync({
        xTenantId: GetTenantId(),
        requestBody: {
          patientId: patientId,
          activityName: activityName,
          logStartTime: logStartTime,
          logEndTime: logEndTime,
          note: note,
          logEntryType: logEntryType as "MANUAL" | "AUTOMATIC",
        },
      }),
    onSuccess: (response) => {
      refetch();
      const message = (response as unknown as AxiosResponse)?.data?.message || "Time log created successfully!";
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
      refetch();
      window.location.reload();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({
      patientId,
      activityName,
      uuid,
      logStartTime,
      logEndTime,
      note,
      logEntryType,
    }: {
      patientId: string;
      activityName: string;
      uuid: string;
      logStartTime: string;
      logEndTime: string;
      note: string;
      logEntryType: string;
    }) =>
      TimeLogControllerService.updateTimeLog({
        xTenantId: GetTenantId(),
        requestBody: {
          uuid: uuid,
          patientId: patientId,
          activityName: activityName,
          logStartTime: logStartTime,
          logEndTime: logEndTime,
          note: note,
          logEntryType: logEntryType as "MANUAL" | "AUTOMATIC",
        },
      }),
    onSuccess: (response) => {
      refetch();
      const message = (response as unknown as AxiosResponse)?.data?.message || "Time log updated successfully!";
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
      refetch();
      window.location.reload();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const handleFormSubmit = async (formData: TimeLogFormData) => {
    setIsSubmitting(true);

    // Format the date and time for the API

    const date = new Date(formData.date);
    const startTimeObj = new Date(formData.startTime);
    const finalStartTime = new Date(date);
    finalStartTime.setHours(
      startTimeObj.getHours(),
      startTimeObj.getMinutes(),
      startTimeObj.getSeconds(),
      startTimeObj.getMilliseconds()
    );
    const startTime = finalStartTime.toISOString();

    const endTimeObj = new Date(formData.endTime);
    const finalEndTime = new Date(date);
    finalEndTime.setHours(
      endTimeObj.getHours(),
      endTimeObj.getMinutes(),
      endTimeObj.getSeconds(),
      endTimeObj.getMilliseconds()
    );
    const endTime = finalEndTime.toISOString();

    if (isEdit) {
      updateMutation.mutate({
        uuid: selectedTimeLog?.uuid || "",
        patientId: patientId || "",
        activityName: formData.logTitle,
        logStartTime: startTime,
        logEndTime: endTime,
        note: formData.note || "",
        logEntryType: "MANUAL",
      });
    } else {
      createTimeLogMutation.mutate({
        patientId: patientId || "",
        activityName: formData.logTitle,
        logStartTime: startTime,
        logEndTime: endTime,
        note: formData.note || "",
        logEntryType: "MANUAL",
      });
    }
    setIsSubmitting(false);
  };

  // CSS styles
  const typographyCss = {
    fontFamily: "Roboto",
    fontWeight: 400,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "0%",
    color: "#212D30",
  };

  // set page 0
  useEffect(() => {
    setPage(0);
  }, [filterLogTitle, filterLogType, filterDate]);

  // Handle filter button click
  const handleOnClickFilters = () => {
    setViewFilters(!viewFilters);
  };

  const handleClearFilters = () => {
    setPage(0);
    setSize(10);
    setFilterDate(null);
    setFilterLogTitle(null);
    setFilterLogType(null);
  };

  const { isLoading: isLoadingTimeLogs } = useQuery({
    queryKey: ["timeLogView", selectedViewTimeLog],
    queryFn: async () => {
      const response = await TimeLogControllerService.getTimeLogById({
        xTenantId: GetTenantId(),
        timeLogId: selectedViewTimeLog || "",
      });
      setTimeLogViewData(response.data as TimeLogViewData);
      return response;
    },
    enabled: !!selectedViewTimeLog,
  });

  // View Time Log
  const handleViewTimeLog = (timeLogId: string) => {
    setShowViewDialog(true);
    setSelectedViewTimeLog(timeLogId);
  };

  const handleViewTimeLogClose = () => {
    setShowViewDialog(false);
    setSelectedViewTimeLog(null);
  };

  // handle sorting
  const handleSorting = (header: string) => {
    if (header == "Date") {
      setSortBy("logStartTime");
      setSortDirection((prev) => (prev == "desc" ? "asc" : "desc"));
    }
  };

  const capitalize = (str: string = "") => (str ? str.charAt(0).toUpperCase() + str.slice(1).toLowerCase() : "");

  return (
    <>
      <Box component={Paper} boxShadow={"none"} sx={{ marginBottom: "10px" }}>
        <Grid container justifyContent="space-between" alignItems="center" p={2}>
          <Grid>
            <Typography variant="h6" fontWeight={600}>
              Time Logs ({`${new Date().toLocaleString("default", { month: "short" })} ${new Date().getFullYear()}`})
            </Typography>
          </Grid>
          <Grid display="flex" alignItems="center" gap={2}>
            <Grid container alignItems={"center"} columnGap={1} rowGap={2}>
              {!viewFilters && (
                <IconButton onClick={() => setViewFilters(true)}>
                  <Grid container border={"1px solid #B6C1C4"} p={1} borderRadius={2}>
                    <FilterAltOutlinedIcon />
                  </Grid>
                </IconButton>
              )}
              {viewFilters && (
                <>
                  <Grid container width={"fit-content"} justifyContent={"flex-end"}>
                    <Button
                      variant="outlined"
                      onClick={() => {
                        handleClearFilters();
                      }}
                    >
                      <Typography fontWeight={550} variant="bodySmall">
                        Clear Filters
                      </Typography>
                    </Button>
                  </Grid>

                  <Grid alignContent={"flex-end"} p={1} columnGap={2} rowGap={2} container justifyContent={"flex-end"}>
                    <Button onClick={handleOnClickFilters} variant="outlined">
                      <Typography fontWeight={550} variant="bodySmall" sx={{ padding: "6px 0px" }}>
                        {viewFilters ? "Hide Filters" : "Filters"}
                      </Typography>
                    </Button>
                  </Grid>
                </>
              )}
            </Grid>

            {isProvider && (
              <Button
                startIcon={<AddIcon />}
                variant="contained"
                onClick={() => {
                  setOpenTimeLogDialog(true);
                  reset();
                  setIsEdit(false);
                }}
                sx={{
                  minWidth: "180px",
                  height: "40px",
                  padding: "8px 16px",
                  borderRadius: "8px",
                  bgcolor: "#006D8F",
                  "&:hover": {
                    bgcolor: "#00596c",
                  },
                }}
              >
                <Typography variant="bodySmall" fontWeight={500} sx={{ whiteSpace: "nowrap" }}>
                  Add Time Log
                </Typography>
              </Button>
            )}
          </Grid>
        </Grid>

        <Collapse in={viewFilters} timeout={500}>
          <Grid
            sx={{
              margin: "20px",
              width: "auto",
              backgroundColor: "#F2F7F9",
              borderRadius: "8px",
            }}
            container
            size={12}
            columnGap={2}
            padding={2}
          >
            <Grid size={3}>
              <CustomLabel label="Log Title" />

              <CustomAutoComplete
                placeholder="Filter by Log Title"
                options={logTitleOptions}
                value={filterLogTitle || undefined}
                bgWhite={true}
                onChange={(newValue: string) => {
                  setFilterLogTitle(newValue);
                }}
              />
            </Grid>

            <Grid size={3}>
              <CustomLabel label="Log Type" />
              <CustomAutoComplete
                placeholder="Filter by Log Type"
                options={LogType}
                value={filterLogType || undefined}
                bgWhite={true}
                onChange={(newValue: string) => {
                  setFilterLogType(newValue);
                }}
              />
            </Grid>

            <Grid size={3}>
              <CustomLabel label="Date" />
              <DatePickerField
                bgWhite
                disableFuture={true}
                value={filterDate || undefined}
                onDateChange={function (selectedDate: string): void {
                  setFilterDate(selectedDate);
                }}
              />
            </Grid>
          </Grid>
        </Collapse>

        <TableContainer sx={{ maxHeight: "calc(100vh - 250px)", overflow: "auto" }}>
          <Table stickyHeader aria-label="time logs table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headers.map((header, index) => (
                  <TableCell
                    key={index}
                    sx={{
                      ...heading,
                    }}
                    align="left"
                  >
                    <Typography fontWeight={550} variant="bodySmall" color="#667085">
                      {header == "Date" ? (
                        <Link
                          style={{
                            color: "#667085",
                            textDecoration: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => handleSorting(header)}
                        >
                          <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                            {header}
                            <Typography>
                              {sortDirection == "asc" ? (
                                <ArrowUpwardIcon fontSize="small" />
                              ) : (
                                <ArrowDownwardIcon fontSize="small" />
                              )}
                            </Typography>
                          </Typography>
                        </Link>
                      ) : (
                        header
                      )}
                    </Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                Array.from({ length: 3 }).map((_, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {headers.map((_, cellIndex) => (
                      <TableCell key={cellIndex}>
                        <Skeleton variant="text" width={100} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : timeLogsData.length > 0 ? (
                timeLogsData.map((log, index) => (
                  <TableRow key={index} hover>
                    <TableCell>
                      <Typography sx={typographyCss}>{log?.logIdentifier}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography
                        sx={{
                          ...typographyCss,
                          color: "#0078D7",
                          fontWeight: 550,
                          cursor: "pointer",
                        }}
                        onClick={() => handleViewTimeLog(log?.uuid)}
                      >
                        {log?.activityName ?? "-"}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{DateNewFormat(log?.logStartTime)}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>
                        {(log?.loggedByName || "") + (log?.role ? ` (${capitalize(log.role)})` : "")}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{convertSecondsToHMS(log?.logTimeDuration ?? 0)}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>
                        {log?.logStartTime && log?.logEndTime
                          ? (() => {
                              const startDate = new Date(log.logStartTime);
                              const endDate = new Date(log.logEndTime);
                              const isSameDate = isSameDay(startDate, endDate);

                              if (isSameDate) {
                                // Same day: show only time
                                return formatTimeNewFormat(startDate);
                              } else {
                                // Different days: show date and time
                                return formatDateNewFormat(startDate);
                              }
                            })()
                          : "-"}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>
                        {log?.logStartTime && log?.logEndTime
                          ? (() => {
                              const startDate = new Date(log.logStartTime);
                              const endDate = new Date(log.logEndTime);
                              const isSameDate = isSameDay(startDate, endDate);

                              if (isSameDate) {
                                // Same day: show only time
                                return formatTimeNewFormat(endDate);
                              } else {
                                // Different days: show date and time
                                return formatDateNewFormat(endDate);
                              }
                            })()
                          : "-"}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>
                        {log?.logEntryType ? capitalize(log.logEntryType) : "-"}
                      </Typography>
                    </TableCell>
                    {isProvider && (
                      <TableCell>
                        {log?.logEntryType === "MANUAL" && log?.loggedBy === currentUserData?.userId ? (
                          <Grid container spacing={1}>
                            <Grid>
                              <Tooltip title="Edit" placement="top">
                                <IconButton onClick={() => HandleEdit(log)} size="small">
                                  <EditIcon sx={iconStyles} />
                                </IconButton>
                              </Tooltip>
                            </Grid>
                          </Grid>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                    )}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={headers.length} align="center">
                    <Typography variant="body1">No time logs found</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <Grid container m={2} justifyContent="flex-end">
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElements}
            onPageChange={handlePageChange}
            onRecordsPerPageChange={handleRecordsPerPageChange}
          />
        </Grid>
      </Box>

      <Dialog
        open={openTimeLogDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            width: "500px",
            height: "650px",
            maxWidth: "90vw",
            margin: 0,
            borderRadius: "8px",
          },
        }}
        sx={{
          "& .MuiDialog-container": {
            alignItems: "center",
            justifyContent: "center",
          },
        }}
      >
        {/* Header */}
        <DialogTitle sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h6" fontWeight={600}>
            {isEdit ? "Edit Time Log" : "Add Time Log"}
          </Typography>
          <IconButton onClick={() => setOpenTimeLogDialog(false)}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <Divider />

        {/* Form Content */}
        <DialogContent>
          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(handleFormSubmit)}>
              <Grid container spacing={2} sx={{ p: 0 }}>
                {/* Log Title */}
                <Grid size={12}>
                  <Autocomplete
                    name="logTitle"
                    label="Log Title"
                    options={logTitleOptions.map((option) => ({
                      label: option.key,
                      value: option.value,
                    }))}
                    placeholder="Select Log Title"
                    isRequired
                  />
                </Grid>

                {/* Date */}
                <Grid size={12}>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: 1,
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#515C5F",
                    }}
                  >
                    Date
                    <span style={{ color: "#D32F2F" }}>*</span>
                  </Typography>
                  <Datepicker name="date" maxDate={new Date()} isRequired placeholder="Select Date" />
                </Grid>

                {/* Time Fields */}
                <Grid container spacing={2} size={12}>
                  <Grid size={6}>
                    <Timepicker label="Start Time" name="startTime" isRequired placeholder="Select Start Time" />
                  </Grid>

                  <Grid size={6}>
                    <Timepicker label="End Time" name="endTime" isRequired placeholder="Select End Time" />
                  </Grid>
                </Grid>

                {/* Note */}
                <Grid size={12}>
                  <CustomLabel label="Note" />
                  <CustomInput
                    name="note"
                    value={watch("note") || ""}
                    placeholder="Enter Note"
                    multiline
                    rows={4}
                    paddingTop="10px"
                    onChange={(e) => setValue("note", e.target.value)}
                  />
                </Grid>
              </Grid>
            </form>
          </FormProvider>
        </DialogContent>

        {/* Actions */}
        <Divider />
        <DialogActions sx={{ p: 2, justifyContent: "flex-end" }}>
          <Button variant="contained" onClick={handleSubmit(handleFormSubmit)} disabled={isSubmitting}>
            {"Save"}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={showViewDialog}
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "8px",
            boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
          },
        }}
      >
        <DialogTitle
          sx={{
            fontFamily: "Roboto",
            fontWeight: 600,
            fontSize: "24px",
            lineHeight: "100%",
            letterSpacing: "0%",
            padding: "24px",
            borderBottom: "1px solid #E8EBEC",
          }}
        >
          {isLoadingTimeLogs ? "Loading..." : timeLogViewData?.logIdentifier + " " + timeLogViewData?.activityName}
        </DialogTitle>
        <IconButton
          size="medium"
          aria-label="close"
          onClick={handleViewTimeLogClose}
          sx={(theme) => ({
            position: "absolute",
            right: 8,
            top: 12,
            color: theme.palette.grey[500],
          })}
        >
          <CloseIcon />
        </IconButton>
        <DialogContent sx={{ padding: "24px" }}>
          {isLoadingTimeLogs ? (
            <>
              <Box display="flex" justifyContent="center" alignItems="center" minHeight="120px">
                <CircularProgress />
              </Box>{" "}
            </>
          ) : (
            <>
              <Box
                sx={{
                  backgroundColor: "#F2F7F9",
                  borderRadius: "4px",
                  padding: "24px",
                }}
              >
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Roboto", fontWeight: 500, fontSize: 16, lineHeight: "120%", letterSpacing: "0%" }}
                >
                  {timeLogViewData?.activityName} (
                  {timeLogViewData?.logEntryType
                    ? timeLogViewData.logEntryType.charAt(0).toUpperCase() +
                      timeLogViewData.logEntryType.slice(1).toLowerCase()
                    : ""}
                  )
                </Typography>
                <Grid container spacing={2} sx={{ mb: 2, mt: 2 }}>
                  <Grid size={4}>
                    <Typography variant="caption" color="textSecondary" sx={{ fontWeight: 500 }}>
                      Date
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 400 }}>
                      {DateNewFormat(timeLogViewData?.created)}
                    </Typography>
                  </Grid>
                  <Grid size={4}>
                    <Typography variant="caption" color="textSecondary" sx={{ fontWeight: 500 }}>
                      Time
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 400 }}>
                      {formatTimeNewFormat(timeLogViewData?.logStartTime)} -{" "}
                      {formatTimeNewFormat(timeLogViewData?.logEndTime)}
                    </Typography>
                  </Grid>
                  <Grid size={4}>
                    <Typography variant="caption" color="textSecondary" sx={{ fontWeight: 500 }}>
                      Duration
                    </Typography>
                    <Typography variant="body1">
                      {convertSecondsToHMS(timeLogViewData?.logTimeDuration || 0)}
                    </Typography>
                  </Grid>
                </Grid>
                <Typography variant="caption" color="textSecondary" sx={{ fontWeight: 500 }}>
                  Notes
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    mt: 1,
                    width: "100%",
                    wordWrap: "break-word",
                    overflowWrap: "break-word",
                    whiteSpace: "pre-wrap",
                    maxWidth: "100%",
                  }}
                >
                  {timeLogViewData?.note ?? "-"}
                </Typography>
              </Box>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TimeLogsTab;
