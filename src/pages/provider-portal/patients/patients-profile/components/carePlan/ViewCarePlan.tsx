import { useEffect, useState } from "react";

import { Circle } from "@mui/icons-material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { Box, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";

import VitalReferenceRange from "@/common-components/vital-reference-range/VitalReferenceRange";

import CP_BMI from "@/assets/image_svg/care-plan/cp-bmi.svg";
import CP_DIET from "@/assets/image_svg/care-plan/cp-diet.svg";
import CP_EXERCISE from "@/assets/image_svg/care-plan/cp-exercise.svg";
import { transformVitalRanges } from "@/components/provider-portal/carePlan/careplan-form";
import { ContentObject } from "@/models/response/response-content-entity";
import {
  <PERSON><PERSON>,
  <PERSON>ient,
  PatientCarePlanControllerService,
  PatientVital,
  PatientVitalControllerService,
  ProgramGoal,
  Protocol,
  VitalReference,
} from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";

import BPChart from "./BPChart";
import PercentageProgressBar from "./ProgressBar";

// Define vital range type to match what VitalReferenceRange expects
interface VitalRange {
  rangeType?:
    | "NORMAL"
    | "LOW_MODERATE"
    | "HIGH_MODERATE"
    | "CRITICAL"
    | "NORMAL_SYSTOLIC"
    | "NORMAL_DIASTOLIC"
    | "LOW_MODERATE_SYSTOLIC"
    | "HIGH_MODERATE_SYSTOLIC"
    | "LOW_MODERATE_DIASTOLIC"
    | "HIGH_MODERATE_DIASTOLIC"
    | "CRITICAL_SYSTOLIC"
    | "CRITICAL_DIASTOLIC";
  min?: number;
  max?: number;
}

interface ViewCarePlanProps {
  carePlanId?: string;
  patientId?: string;
  patientProfileData?: Patient | undefined;
}
interface CarePlan {
  uuid?: string;
  title?: string;
  duration?: number;
  durationUnit?: "DAY" | "WEEK" | "MONTH" | "YEAR";
  overview?: string;
  gender?: "MALE" | "FEMALE" | "UNISEX";
  ageCriteria?: string;
  age?: string;
  deviceName?: string[];
  devices?: Device[];
  routineCheckup?: string;
  programGoals?: ProgramGoal[];
  vitalReference?: VitalReference[];
  globalCarePlan?: boolean;
  external?: boolean;
  protocolType?: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
  active?: boolean;
  startDate?: string;
  endDate?: string;
  completedDate?: string;
  archive?: boolean;
  trackedVitals?: string[];
  modified?: string;
  diagnosisCodes?: string[];
  protocol?: Protocol[];
  carePlanStatus?: string;
}

const ViewCarePlan = ({ carePlanId, patientId, patientProfileData }: ViewCarePlanProps) => {
  const [carePlanDetails, setCarePlanDetails] = useState<CarePlan | null>(null);
  const [loading, setLoading] = useState(true);

  // Status indicators for vital reference ranges
  const StatusIndicator = ({ color, label }: { color: string; label: string }) => (
    <Box style={{ display: "flex", alignItems: "center", gap: "8px" }}>
      <Box style={{ width: 16, height: 16, backgroundColor: color, borderRadius: 4 }}></Box>
      <Typography>{label}</Typography>
    </Box>
  );

  const { data: weightData } = useQuery({
    queryKey: ["list-of-vitals-in-graph", patientId],
    queryFn: async () => {
      const response = (await PatientVitalControllerService.getPatientVitals1({
        patientUuid: patientId || "",
        xTenantId: GetTenantId(),
        vitalName: "Weight",
        size: 2,
      })) as unknown as AxiosResponse<ContentObject<PatientVital[]>>;
      return response;
    },
  });

  interface BMIType {
    bmi: string | null;
    date: string | undefined;
    trend: string | null;
  }

  const [BMI, setBMI] = useState<BMIType>({
    bmi: null,
    date: undefined,
    trend: null,
  });

  useEffect(() => {
    const measureBMI = () => {
      const height = Number(patientProfileData?.height) || (0 as number);
      const latestWeight = weightData?.data?.content[0]?.value1;
      const previousWeight = weightData?.data?.content[1]?.value1;
      const heightInMeters = height / 100;

      const latestBMI =
        latestWeight && heightInMeters ? (latestWeight / (heightInMeters * heightInMeters)).toFixed(2) : "0";

      let bmiTrend = null;

      if (latestWeight && previousWeight) {
        if (latestWeight > previousWeight) {
          bmiTrend = "gain";
        } else if (latestWeight < previousWeight) {
          bmiTrend = "loss";
        } else {
          bmiTrend = "Maintained";
        }
      }

      // setBMI(bmi);

      setBMI({
        bmi: latestBMI,
        date: weightData?.data?.content?.[0]?.recordedDate,
        trend: bmiTrend,
      });
    };

    measureBMI();
  }, [patientProfileData?.height, weightData]);

  // Fetch care plan details
  const { data, isLoading } = useQuery({
    queryKey: ["get-patient-care-plan", carePlanId],
    queryFn: async () => {
      if (!carePlanId) return null;
      try {
        const response = await PatientCarePlanControllerService.getPatientCarePlanById({
          patientCarePlanId: carePlanId,
          xTenantId: GetTenantId(),
        });
        return response.data;
      } catch (error) {
        return null;
      }
    },
    enabled: !!carePlanId,
  });

  const { data: graphData } = useQuery({
    queryKey: ["list-of-vitals-in-graph", patientId, carePlanDetails],
    queryFn: async () => {
      const startDate = carePlanDetails?.startDate;
      const endDate =
        carePlanDetails?.carePlanStatus === "COMPLETED" ? carePlanDetails?.completedDate : carePlanDetails?.endDate;

      const formatToISO = (date?: string) =>
        date ? new Date(new Date(date).setHours(14, 17, 26, 964)).toISOString() : "";

      const response = (await PatientVitalControllerService.getPatientVitals1({
        patientUuid: patientId || "",
        xTenantId: GetTenantId(),
        vitalName: "Blood Pressure",
        size: 5,
        startDate: formatToISO(startDate),
        endDate: formatToISO(endDate),
      })) as unknown as AxiosResponse<ContentObject<PatientVital[]>>;

      return response.data.content.sort(
        (a, b) => new Date(b.recordedDate).getTime() - new Date(a.recordedDate).getTime()
      );
    },
  });

  useEffect(() => {
    if (data) {
      setCarePlanDetails(data);
      setLoading(false);
    } else if (!isLoading) {
      setLoading(false);
    }
  }, [data, isLoading]);

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }).format(date);
  };

  if (loading) {
    return (
      <Box p={3}>
        <Typography>Loading care plan details...</Typography>
      </Box>
    );
  }

  if (!carePlanDetails) {
    return (
      <Box p={3}>
        <Typography>No care plan details found</Typography>
      </Box>
    );
  }

  // Transform gender for display
  const rawGender = carePlanDetails.gender;
  const selectedGender =
    rawGender === "UNISEX"
      ? ["Male", "Female"]
      : rawGender
        ? [rawGender.charAt(0) + rawGender.slice(1).toLowerCase()]
        : [];

  return (
    <Grid
      sx={{
        width: "100%",
        height: "100%",
        padding: "30px 25px",
        overflowY: "auto",
      }}
    >
      {/* Care Plan Title and Date Header */}
      <Grid container mb={3}>
        <Grid>
          <Typography variant="h5" fontWeight="bold" color="#333">
            {carePlanDetails.title}
          </Typography>
          <Box display="flex" alignItems="center" mt={1}>
            <Box component="span" display="flex" alignItems="center" mr={1}>
              <CalendarMonthIcon style={{ fontSize: 20, color: "#667085" }} />
            </Box>
            <Typography color="#667085">
              {formatDate(carePlanDetails.startDate)} - {formatDate(carePlanDetails.endDate)}
            </Typography>
          </Box>
          {carePlanDetails.overview && (
            <Typography mt={2} color="#515C5F">
              {carePlanDetails.overview}
            </Typography>
          )}
        </Grid>
      </Grid>
      <Grid container mb={2}>
        <Box display="flex" alignItems="center" mt={2}>
          <Typography color="#515C5F" mr={1}>
            Applicable for:
          </Typography>
          <Box display="flex" gap={1}>
            {selectedGender.map((gender, index) => (
              <Box
                key={index}
                component="span"
                bgcolor="#E0F2FF"
                color="#007BFF"
                px={1}
                py={0.5}
                borderRadius={1}
                fontSize="14px"
              >
                {gender}
              </Box>
            ))}
            <Box component="span" bgcolor="#E0F2FF" color="#007BFF" px={1} py={0.5} borderRadius={1} fontSize="14px">
              {carePlanDetails.ageCriteria && (
                <Box
                  component="span"
                  bgcolor="#E0F2FF"
                  color="#007BFF"
                  px={1}
                  py={0.5}
                  borderRadius={1}
                  fontSize="14px"
                >
                  {carePlanDetails.ageCriteria + " " + carePlanDetails.age + " yrs"}
                </Box>
              )}
            </Box>
          </Box>
        </Box>
      </Grid>

      <Grid container mb={3}>
        <Box display="flex" alignItems="center">
          <Typography color="#515C5F" mr={1}>
            Diagnosis Codes:
          </Typography>
          <Box display="flex" gap={1}>
            {carePlanDetails?.diagnosisCodes?.map((item, index) => (
              <Box
                key={index}
                component="span"
                bgcolor="#E0F2FF"
                color="#007BFF"
                px={1}
                py={0.5}
                borderRadius={1}
                fontSize="14px"
              >
                {item}
              </Box>
            ))}
          </Box>
        </Box>
      </Grid>

      <Grid container spacing={2} alignItems="flex-start">
        <Grid size={12} mt={2} mb={2}>
          <Typography
            variant="medium"
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              lineHeight: "19.2px",
              letterSpacing: "0%",
            }}
          >
            Program Goals
          </Typography>
        </Grid>

        {Array.isArray(carePlanDetails?.programGoals) &&
          carePlanDetails.programGoals.map((item, index: number) => {
            const categoryIcons: Record<string, string> = {
              weight: CP_BMI,
              exercise: CP_EXERCISE,
              diet: CP_DIET,
              bloodPressure: CP_BMI,
              // Add more mappings...
            };
            return (
              <Grid size={item?.category === "bloodPressure" ? 12 : 6} border={0} key={index} mb={3}>
                <Grid
                  border={1}
                  borderRadius={4}
                  borderColor={"#E8EBEC"}
                  p={1.5}
                  pb={item?.category === "bloodPressure" ? 5 : item?.category === "weight" ? 3 : 0}
                >
                  <Grid
                    container
                    size={12}
                    sx={{
                      borderBottom: `1px solid ${theme.palette.divider}`,
                      alignItems: "center",
                      justifyContent: "space-between",
                      paddingBottom: 0.5,
                    }}
                  >
                    <Grid container sx={{ alignItems: "center" }}>
                      <Grid>
                        {item.category && categoryIcons[item.category] && (
                          <img
                            src={categoryIcons[item.category]}
                            width="30px"
                            height="30px"
                            alt={`${item.category} Icon`}
                          />
                        )}
                      </Grid>
                      <Grid ml={1}>
                        <Typography
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: "500",
                            fontSize: 16,
                          }}
                        >
                          {item.title}
                        </Typography>
                      </Grid>
                    </Grid>
                    <Grid border={0}>
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: "400",
                          fontSize: 14,
                          color: "#515C5F",
                        }}
                      >
                        {item?.targetValue} {item?.unit}
                      </Typography>
                    </Grid>
                  </Grid>
                  {item?.category === "bloodPressure" && <BPChart vitalsData={graphData ? graphData : []} />}
                  {item.category !== "weight" && item?.category !== "bloodPressure" && (
                    <Grid mt={1}>
                      <Grid container mb={1}>
                        <Typography sx={{ fontFamily: "Roboto", fontWeight: "500", marginRight: 1, fontSize: 14 }}>
                          Daily Check-in Tracking
                        </Typography>
                        <Typography sx={{ fontFamily: "Roboto", fontWeight: "400", fontSize: 14 }}>
                          {format(new Date(carePlanDetails?.startDate ?? ""), "dd MMM yyyy")} -{" "}
                          {format(new Date(), "dd MMM yyyy")}
                        </Typography>
                      </Grid>
                      <PercentageProgressBar completed={item?.percentage ?? "0"} />
                      <Grid container mb={1} mt={1} border={0} alignItems={"center"}>
                        <Circle sx={{ color: "#078EB9", fontSize: 10, marginRight: 0.5 }} />{" "}
                        <Typography sx={{ fontFamily: "Roboto", fontWeight: "400", fontSize: 14 }}>
                          Checked-in
                        </Typography>
                        <Circle sx={{ color: "#E8EBEC", fontSize: 10, marginLeft: 1, marginRight: 0.5 }} />
                        <Typography sx={{ fontFamily: "Roboto", fontWeight: "400", fontSize: 14 }}>
                          Skipped days
                        </Typography>
                      </Grid>
                    </Grid>
                  )}

                  {item?.category === "weight" && (
                    <Grid ml={1} borderColor={"#E8EBEC"} pb={1}>
                      {carePlanDetails?.carePlanStatus === "COMPLETED" ? (
                        <Grid>
                          <Grid>
                            <Typography>Current BMI : {item?.bmi || "0"} kg/m²</Typography>
                          </Grid>
                        </Grid>
                      ) : (
                        <Grid container justifyContent={"space-between"}>
                          <Grid>
                            <Typography
                              style={{
                                fontFamily: "Roboto",
                                fontWeight: "500",
                                fontSize: 15,
                                letterSpacing: 0,
                                marginTop: 5,
                                marginBottom: 5,
                              }}
                            >
                              Current BMI :
                            </Typography>
                            <Typography
                              style={{
                                fontFamily: "Roboto",
                                fontWeight: "400",
                                fontSize: 14,
                                letterSpacing: 0,
                              }}
                            >
                              {BMI.date ? format(new Date(BMI.date), "dd MMM yyyy, hh:mm a") : "-"}
                            </Typography>
                          </Grid>
                          <Grid>
                            <Grid container alignItems={"baseline"}>
                              <Typography
                                style={{
                                  fontFamily: "Roboto",
                                  fontWeight: "500",
                                  fontSize: 18,
                                  letterSpacing: 0,
                                  textAlign: "center",
                                  color: "#006D8F",
                                  flexDirection: "row",
                                  marginTop: 5,
                                  marginBottom: 5,
                                }}
                              >
                                {BMI?.bmi}&nbsp;
                              </Typography>
                              <Typography
                                style={{
                                  fontFamily: "Roboto",
                                  fontWeight: "500",
                                  fontSize: 14,
                                  letterSpacing: 0,
                                  textAlign: "center",
                                  color: "#006D8F",
                                  flexDirection: "row",
                                  marginTop: 5,
                                  marginBottom: 5,
                                }}
                              >
                                kg/m²
                              </Typography>
                            </Grid>
                            <Typography
                              style={{
                                fontFamily: "Roboto",
                                fontWeight: "400",
                                fontSize: 14,
                                letterSpacing: 0,
                                textAlign: "center",
                                marginTop: 5,
                                marginBottom: 5,
                              }}
                            >
                              {" "}
                              ({BMI.trend})
                            </Typography>
                          </Grid>
                        </Grid>
                      )}
                    </Grid>
                  )}
                </Grid>
              </Grid>
            );
          })}
      </Grid>

      <Grid
        padding={"20px 20px"}
        bgcolor={"#F2F7F9"}
        display={"flex"}
        pt={2}
        mt={2}
        container
        flexDirection={"column"}
        gap={2.5}
        borderRadius={2}
      >
        <Grid container display="flex" justifyContent="space-between" alignItems="center"></Grid>
        <Grid container display="flex" justifyContent="space-between" alignItems="center">
          <Grid
            container
            size={12}
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={3}
            // mt={3}
          >
            <Grid>
              <Typography
                fontFamily="Roboto"
                fontWeight={500}
                fontSize="18px"
                lineHeight="21.6px"
                letterSpacing="0%"
                color="#515C5F"
              >
                Vital Reference Range
              </Typography>
            </Grid>
            <Grid style={{ display: "flex", alignItems: "center", gap: "16px" }}>
              <StatusIndicator color="#7FD067" label="Normal" />
              <StatusIndicator color="#FCB33B" label="Abnormal" />
              <StatusIndicator color="#CE0718" label="Critical" />
            </Grid>
          </Grid>
          <Grid container spacing={2} size={12}>
            {carePlanDetails?.vitalReference?.map((vital, index) => (
              <Grid
                key={index}
                size={6}
                mb={2}
                sx={{
                  ...(carePlanDetails?.vitalReference &&
                    index === carePlanDetails.vitalReference.length - 1 &&
                    carePlanDetails.vitalReference.length > 3 && {
                      marginLeft: 0,
                    }),
                }}
              >
                <VitalReferenceRange
                  title={vital.vitalType || ""}
                  unit={
                    vital.vitalType === "Blood Glucose"
                      ? "mg/dL"
                      : vital.vitalType === "Heart Rate"
                        ? "bpm"
                        : vital.vitalType === "Weight"
                          ? "kg"
                          : vital.vitalType === "ECG"
                            ? "bpm"
                            : vital.vitalType === "HRV"
                              ? "ms"
                              : vital.vitalType === "Oxygen Saturation"
                                ? "%"
                                : vital.vitalType === "Stress"
                                  ? "%"
                                  : ""
                  }
                  ranges={transformVitalRanges(vital.vitalRanges as VitalRange[])}
                />
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default ViewCarePlan;
