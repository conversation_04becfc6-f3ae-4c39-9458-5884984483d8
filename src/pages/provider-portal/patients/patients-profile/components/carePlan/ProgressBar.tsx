import { Box, Typography } from "@mui/material";

const PercentageProgressBar = ({ completed }: { completed: string }) => {
  const num = completed ? Number(completed || "0") : 0;

  const remaining = 100 - num;

  return (
    <Box
      display="flex"
      width="100%"
      height={25}
      borderRadius={1.5}
      overflow="hidden"
      boxShadow={1}
      position="relative"
      bgcolor="#E8EBEC"
      sx={{
        boxShadow: "none",
      }}
    >
      <Box
        position="absolute"
        left={0}
        top={0}
        bottom={0}
        bgcolor="#078EB9"
        width={`${completed}%`}
        display="flex"
        alignItems="center"
        justifyContent="center"
        sx={{
          transition: "width 1s ease",
          boxShadow: "none",
        }}
      >
        <Typography sx={{ fontFamily: "Roboto", fontSize: 14 }} ml={3} color="common.white">
          {completed !== "0.00" ? `${completed}%` : ""}
        </Typography>
      </Box>
      {remaining !== 0 && (
        <Box
          position="absolute"
          right={0}
          top={0}
          bottom={0}
          width={`${remaining}%`}
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          <Typography sx={{ fontFamily: "Roboto", fontSize: 14 }} color="text.primary">
            {remaining !== 100 ? `${remaining}%` : "0%"}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default PercentageProgressBar;
