import { Line } from "react-chartjs-2";

import { Circle, Square } from "@mui/icons-material";
import { Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { CategoryScale, Chart as ChartJS, Legend, LineElement, LinearScale, PointElement, Tooltip } from "chart.js";
import { format } from "date-fns";

import { PatientVital } from "@/sdk/requests";

ChartJS.register(LineElement, PointElement, CategoryScale, LinearScale, Tooltip, Legend);

interface BPChartProps {
  vitalsData: PatientVital[];
}

const BPChart = ({ vitalsData }: BPChartProps) => {
  // Sort the vitals by recordedDate (latest first or oldest first as needed)
  const sortedData = [...vitalsData].sort(
    (a, b) => new Date(a.recordedDate).getTime() - new Date(b.recordedDate).getTime()
  );

  // Prepare labels (time) and datasets (value1 = Systolic, value2 = Diastolic)

  const systolicValues = sortedData.map((item) => item.value1);
  const diastolicValues = sortedData.map((item) => item.value2);

  const firstDate = sortedData[0]?.recordedDate;
  const lastDate = sortedData[sortedData.length - 1]?.recordedDate;

  const formattedFirstDate = firstDate ? format(new Date(firstDate), "dd MMM yyyy") : "";
  const formattedLastDate = lastDate ? format(new Date(lastDate), "dd MMM yyyy") : "";
  const isFirstLastSimilar =
    firstDate && lastDate
      ? format(new Date(firstDate), "yyyy-MM-dd") === format(new Date(lastDate), "yyyy-MM-dd")
      : false;

  const getColorBySeverity = (severity: string | undefined) => {
    switch (severity) {
      case "CRITICAL":
        return "#EF4444"; // Red
      case "MODERATE":
        return "#FACC15"; // Yellow
      case "NORMAL":
        return "#22C55E"; // Green
      default:
        return "#9CA3AF"; // Gray (fallback)
    }
  };

  const systolicPointColors = sortedData.map((item) => getColorBySeverity(item.severity));
  const diastolicPointColors = sortedData.map((item) => getColorBySeverity(item.severity));

  const labels = sortedData.map((item) => {
    const date = new Date(item.recordedDate);
    return isFirstLastSimilar ? format(date, "HH:mm aa") : format(date, "(dd/MM) HH:mm aa");
  });
  const chartData = {
    labels: labels,
    datasets: [
      {
        label: "Diastolic",
        data: diastolicValues,
        borderColor: "#D946EF",
        // backgroundColor: '#3B82F6',
        tension: 0.4,
        pointRadius: 5,
        pointBackgroundColor: diastolicPointColors,
        pointBorderWidth: 0,
        borderWidth: 2,
      },
      {
        label: "Systolic",
        data: systolicValues,
        borderColor: "#D946EF",
        // backgroundColor: '#D946EF',
        tension: 0.4,
        pointRadius: 5,
        pointBackgroundColor: systolicPointColors,
        pointBorderWidth: 0,
        borderWidth: 2,
        pointStyle: "rect",
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
        position: "top" as const,
        labels: {
          usePointStyle: true,
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: true,
          borderDash: [5, 5],
        },
      },
      y: {
        min: 0,
        max: 200,
        grid: {
          display: false,
          borderDash: [5, 5],
        },
      },
    },
  };

  return (
    <div style={{ width: "100%", height: "150px" }}>
      <Grid mt={1} mb={1} container justifyContent={"space-between"}>
        <Grid container>
          <Typography sx={{ fontFamily: "Roboto", fontWeight: "500", fontSize: 14, marginRight: 0.9 }}>
            BP Reading{" "}
          </Typography>
          <Typography sx={{ fontFamily: "Roboto", fontWeight: "400", fontSize: 14 }}>
            ({isFirstLastSimilar ? formattedFirstDate : `${formattedFirstDate} - ${formattedLastDate}`})
          </Typography>
        </Grid>
        <Grid container alignItems={"center"}>
          <Square sx={{ color: "#D946EF", fontSize: 12, marginRight: 0.5 }} />
          <Typography sx={{ fontFamily: "Roboto", fontWeight: "400", fontSize: 14, marginRight: 1 }}>
            Systolic
          </Typography>
          <Circle sx={{ color: "#D946EF", fontSize: 12, marginRight: 0.5 }} />{" "}
          <Typography sx={{ fontFamily: "Roboto", fontWeight: "400", fontSize: 14 }}>Diastolic</Typography>
        </Grid>
      </Grid>
      <Line data={chartData} options={options} />
    </div>
  );
};

export default BPChart;
