import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";

import CheckIcon from "@mui/icons-material/Check";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Box, Button, Checkbox, Collapse, FormControlLabel, Switch, Typography } from "@mui/material";
import { Stack } from "@mui/material";
import { Divider } from "@mui/material";
import { TextField } from "@mui/material";
import { Grid2 as Grid } from "@mui/material";
import Accordion from "@mui/material/Accordion";
import AccordionDetails from "@mui/material/AccordionDetails";
import AccordionSummary from "@mui/material/AccordionSummary";
import { styled } from "@mui/material/styles";

import { useQuery } from "@tanstack/react-query";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import CP_BMI from "@/assets/image_svg/care-plan/cp-bmi.svg";
import CP_DIET from "@/assets/image_svg/care-plan/cp-diet.svg";
import CP_EXERCISE from "@/assets/image_svg/care-plan/cp-exercise.svg";
import { useDrawer } from "@/components/providers/DrawerProvider";
import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { CarePlanControllerService, PatientCarePlanControllerService } from "@/sdk/requests";
import { Device, ProgramGoal, Protocol, VitalRange, VitalReference } from "@/sdk/requests/types.gen";
import { GetTenantId } from "@/services/common/get-tenant-id";

import StaticSeletionShow from "../../../../../../common-components/custom-select-vk/Custom-select";
import CustomSelect from "../../../../../../common-components/custom-select/customSelect";
import RangeSlider from "../../../../../../common-components/range-slider/Range-slider";

const categoryIcons: Record<string, string> = {
  weight: CP_BMI,
  exercise: CP_EXERCISE,
  diet: CP_DIET,
  bloodPressure: CP_BMI,
  // Add more mappings...
};

interface CareplanFormValues {
  carePlan: string;
  startDate: string;
  gender: string;
  ageCriteria: string;
  diagnosis: string;
  programOverview: string;
  selectedDevices: string[];
  protocolType: ("OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG")[];
  programTitle: string;
  programDuration: number;
  programDurationUnit: "DAY" | "WEEK" | "MONTH" | "YEAR";
  applicableforGender: string[];
  applicableforAgeCriteria: string;
  applicableforAge: number;
  applicableforConditions: string[];
}

interface CarePlan {
  uuid: string;
  title: string;
  startDate: string;
  gender: string;
  ageCriteria: string;
  diagnosis: string[];
  overview: string;
  vitalReferences?: VitalReference[];
  devices?: Device[];
  external?: boolean;
}

interface CarePlanResponse {
  content: CarePlan[];
}

interface CarePlanDetails {
  vitalReference?: VitalReference[];
  vitalReferences?: VitalReference[];
  gender?: string;
  duration?: number;
  durationUnit?: "DAY" | "WEEK" | "MONTH" | "YEAR";
  ageCriteria?: string;
  age?: string;
  diagnosisCodes?: string[];
  overview?: string;
  devices?: Device[];
  programGoals?: {
    title: string;
    category: string;
    targetValue: number;
    unit: string;
    programGoalTasks?: {
      title: string;
      details: string;
    }[];
  }[];
}

type PatientCarePlan = {
  uuid?: string;
  title?: string;
  duration?: number;
  durationUnit?: "DAY" | "WEEK" | "MONTH" | "YEAR";
  overview?: string;
  gender?: "MALE" | "FEMALE" | "UNISEX";
  ageCriteria?: string;
  startDate?: string;
  diagnosis?: string[];
  age?: string;
  deviceName?: string[];
  devices?: Device[];
  routineCheckup?: string;
  carePlanStatus?: "REQUESTED" | "ASSIGNED" | "CANCELLED" | "IN_PROGRESS" | "PENDING" | "COMPLETED";
  programGoals?: ProgramGoal[];
  vitalReferences?: VitalReference[];
  globalCarePlan?: boolean;
  external?: boolean;
  protocolType?: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
  active?: boolean;
  archive?: boolean;
  trackedVitals?: string[];
  modified?: string;
  diagnosisCodes?: string[];
  protocol?: Protocol[];
};
interface CareplanFormProps {
  editData?: CarePlan;
  refetchCarePlans: () => void;
  isEdit?: boolean;
  careplanId?: PatientCarePlan;
}

const IOSSwitch = styled((props) => <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />)(
  ({ theme }) => ({
    width: 42,
    height: 26,
    padding: 0,
    "& .MuiSwitch-switchBase": {
      padding: 0,
      margin: 2,
      transitionDuration: "300ms",
      "&.Mui-checked": {
        transform: "translateX(16px)",
        color: "#fff",
        "& + .MuiSwitch-track": {
          backgroundColor: theme.palette.mode === "dark" ? "#2ECA45" : "#65C466",
          opacity: 1,
          border: 0,
        },
        "&.Mui-disabled + .MuiSwitch-track": {
          opacity: 0.5,
        },
      },
      "&.Mui-focusVisible .MuiSwitch-thumb": {
        color: "#33cf4d",
        border: "6px solid #fff",
      },
      "&.Mui-disabled .MuiSwitch-thumb": {
        color: theme.palette.mode === "light" ? theme.palette.grey[100] : theme.palette.grey[600],
      },
      "&.Mui-disabled + .MuiSwitch-track": {
        opacity: theme.palette.mode === "light" ? 0.7 : 0.3,
      },
    },
    "& .MuiSwitch-thumb": {
      boxSizing: "border-box",
      width: 22,
      height: 22,
    },
    "& .MuiSwitch-track": {
      borderRadius: 26 / 2,
      backgroundColor: theme.palette.mode === "light" ? "#E9E9EA" : "#39393D",
      opacity: 1,
      transition: theme.transitions.create(["background-color"], {
        duration: 500,
      }),
    },
  })
);

const CareplanForm = (props: CareplanFormProps) => {
  const { patientId } = useParams();
  const { editData, isEdit, careplanId } = props;
  // const navigate = useNavigate();
  const dispatch = useDispatch();
  const { close: closeDrawer } = useDrawer();

  const [isEditMode] = useState(isEdit || !!editData);

  const [dateError, setDateError] = useState<string | null>(null);

  const formMethods = useForm<CareplanFormValues>({
    defaultValues: {
      carePlan: editData?.uuid || "",
      startDate: editData?.startDate || "",
      gender: editData?.gender || "",
      ageCriteria: editData?.ageCriteria || "",
      diagnosis: Array.isArray(editData?.diagnosis) ? editData.diagnosis[0] || "" : editData?.diagnosis || "",
      programOverview: editData?.overview || "",
      selectedDevices: [],
      protocolType: [],
      programTitle: "",
      programDuration: 0,
      programDurationUnit: "DAY",
      applicableforGender: [],
      applicableforAgeCriteria: "",
      applicableforAge: 0,
      applicableforConditions: [],
    },
  });

  const [nurseType, setNurseType] = useState(editData?.external ? "EXTERNAL" : "INTERNAL");
  const [carePlanOptions, setCarePlanOptions] = useState<{ value: string; label: string }[]>([]);
  const [selectedCarePlanId, setSelectedCarePlanId] = useState(careplanId?.uuid);
  const showFullForm = !!nurseType && !!formMethods.watch("carePlan");

  const { data: carePlansData, isSuccess: isCarePlansSuccess } = useQuery<CarePlanResponse, Error>({
    queryKey: ["get-care-plans", nurseType],
    queryFn: async () => {
      const response = await CarePlanControllerService.getAllCarePlans1({
        status: true,
        archive: false,
        xTenantId: nurseType === "EXTERNAL" ? "eAmata" : GetTenantId(),
      });

      if (response.data && Array.isArray(response.data.content)) {
        return { content: response.data.content as CarePlan[] };
      }
      return { content: [] };
    },
  });

  useEffect(() => {
    if (careplanId) {
      formMethods.setValue("carePlan", careplanId?.uuid || "");
    }
  }, [careplanId]);

  const { data: selectedCarePlanDetails } = useQuery<CarePlanDetails | undefined>({
    queryKey: ["get-care-plan-by-id", selectedCarePlanId],
    queryFn: async () => {
      if (!selectedCarePlanId) {
        return undefined;
      }
      if (isEdit && selectedCarePlanId) {
        const response = await PatientCarePlanControllerService.getPatientCarePlanById({
          patientCarePlanId: selectedCarePlanId,
          xTenantId: GetTenantId(),
        });
        return response.data;
      } else if (selectedCarePlanId) {
        const response = await CarePlanControllerService.getCarePlanById({
          carePlanId: careplanId?.uuid || selectedCarePlanId,
          xTenantId: nurseType === "EXTERNAL" ? "eAmata" : GetTenantId(),
          globalCarePlan: nurseType === "EXTERNAL",
        });
        return response.data;
      }
      return undefined;
    },
    enabled: !!selectedCarePlanId,
  });

  useEffect(() => {
    if (selectedCarePlanDetails) {
      const rawGender = selectedCarePlanDetails.gender;
      const gender =
        rawGender === "UNISEX"
          ? ["Male", "Female"]
          : rawGender
            ? [rawGender.charAt(0) + rawGender.slice(1).toLowerCase()]
            : [];

      const diagnosisCodes = selectedCarePlanDetails.diagnosisCodes || [];

      formMethods.setValue("programOverview", selectedCarePlanDetails.overview || "");
      formMethods.setValue("gender", String(rawGender || ""));
      formMethods.setValue("ageCriteria", selectedCarePlanDetails.ageCriteria || "");
      formMethods.setValue("applicableforGender", gender);
      formMethods.setValue("applicableforAgeCriteria", selectedCarePlanDetails.ageCriteria || "");

      if (selectedCarePlanDetails.ageCriteria && selectedCarePlanDetails.ageCriteria.includes("Older than")) {
        formMethods.setValue(
          "applicableforAge",
          selectedCarePlanDetails.ageCriteria.split(" ")[2]
            ? parseInt(selectedCarePlanDetails.ageCriteria.split(" ")[2])
            : 0
        );
      }
      formMethods.setValue("applicableforConditions", diagnosisCodes);
      formMethods.setValue(
        "diagnosis",
        diagnosisCodes && Array.isArray(diagnosisCodes) && diagnosisCodes.length > 0 ? String(diagnosisCodes[0]) : ""
      );
    }
  }, [selectedCarePlanDetails, formMethods]);

  useEffect(() => {
    if (isCarePlansSuccess && carePlansData) {
      const plans = carePlansData.content.map((plan: CarePlan) => ({
        value: plan.uuid,
        label: plan.title,
      }));
      setCarePlanOptions(plans);
    }
  }, [isCarePlansSuccess, carePlansData]);

  useEffect(() => {
    const carePlanId = formMethods.watch("carePlan") || "";
    formMethods.setValue("carePlan", carePlanId);
    setSelectedCarePlanId(carePlanId);
  }, [formMethods.watch("carePlan")]);

  useEffect(() => {
    if (isEdit && selectedCarePlanDetails?.vitalReference) {
      setUpdatedVitalReferences(selectedCarePlanDetails.vitalReference);
    } else if (!isEdit && selectedCarePlanDetails?.vitalReferences) {
      setUpdatedVitalReferences(selectedCarePlanDetails.vitalReferences);
    }
  }, [isEdit, selectedCarePlanDetails]);

  const [updatedVitalReferences, setUpdatedVitalReferences] = useState<VitalReference[] | null>();
  const handleSliderChange = (values: { [key: string]: VitalRange[] | number[] }) => {
    setUpdatedVitalReferences((prevReferences) => {
      if (!prevReferences) return null;

      const newReferences = prevReferences.map((vital) => {
        if (values[vital.vitalType || ""]) {
          return {
            ...vital,
            vitalRanges: values[vital.vitalType || ""] as VitalRange[],
          };
        }
        return vital;
      });

      return newReferences;
    });
  };

  const onSubmit = async (data: unknown) => {
    const formValues = data as CareplanFormValues;

    let payload;
    if (isEdit) {
      payload = {
        carePlanId: formValues.carePlan,
        protocolType: "OUT_OF_RANGE_BP" as "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG",
        globalCarePlan: nurseType === "EXTERNAL" ? true : false,
        vitalReferences: updatedVitalReferences, // Ensure this is the latest state
      };
    } else {
      if (!formValues.startDate) {
        setDateError("Start Date is required");
        return;
      }
      setDateError(null);
      payload = {
        startDate: formValues.startDate
          ? typeof formValues.startDate === "object" && formValues.startDate !== null
            ? `${(formValues.startDate as Date).getFullYear()}-${((formValues.startDate as Date).getMonth() + 1).toString().padStart(2, "0")}-${(formValues.startDate as Date).getDate().toString().padStart(2, "0")}`
            : `${formValues.startDate.split("-")[2]}-${formValues.startDate.split("-")[0]}-${formValues.startDate.split("-")[1]}`
          : undefined,
        carePlanId: formValues.carePlan,
        vitalReferences: updatedVitalReferences,
        protocolType: "OUT_OF_RANGE_BP" as "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG",
        globalCarePlan: nurseType === "EXTERNAL" ? true : false,
      };
    }

    try {
      if (patientId && !isEdit) {
        await PatientCarePlanControllerService.assignCarePlan({
          patientId: patientId,
          xTenantId: GetTenantId(),
          requestBody: {
            ...payload,
            vitalReferences: updatedVitalReferences ?? [],
          },
        });

        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.SUCCESS,
            message: "Care plan assigned successfully",
          })
        );
        closeDrawer();
        props.refetchCarePlans();
      } else {
        await PatientCarePlanControllerService.updatePatientCarePlan({
          xTenantId: GetTenantId(),
          requestBody: {
            uuid: selectedCarePlanId,
            vitalReference: updatedVitalReferences ?? [],
          },
        });

        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.SUCCESS,
            message: "Care plan updated successfully",
          })
        );
        closeDrawer();
        props.refetchCarePlans();
      }
    } catch (error: unknown) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body?.message || "Failed to assign care plan",
        })
      );
    }
  };

  return (
    <DrawerBody padding={3} offset={80} sx={{ fontFamily: "Roboto, sans-serif", backgroundColor: "#FFFFFF" }}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            {!isEditMode && (
              <Grid container spacing={1}>
                <Grid size={2}>
                  <Typography
                    mb={1}
                    variant="body2"
                    sx={{
                      mb: 1,
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#515C5F",
                    }}
                  >
                    Care Plan Type
                  </Typography>
                  <CustomSelect
                    placeholder="Select Provider Group"
                    name="nurseType"
                    value={nurseType}
                    items={[
                      { value: "INTERNAL", label: "Provider Group" },
                      { value: "EXTERNAL", label: "eAmata" },
                    ]}
                    onChange={(e) => setNurseType(e.target.value)}
                  />
                </Grid>

                <Grid size={5}>
                  <Autocomplete
                    name="carePlan"
                    label="Care Plan"
                    options={
                      carePlanOptions.length > 0
                        ? carePlanOptions
                        : [{ value: "", label: "No Care Plans Available", disabled: true }]
                    }
                    placeholder="Select Care Plan"
                    isRequired
                  />
                </Grid>

                <Grid size={2.5}>
                  <Typography
                    mb={1}
                    variant="body2"
                    sx={{
                      mb: 1,
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#515C5F",
                    }}
                  >
                    Start Date
                    <span style={{ color: "#D32F2F" }}>*</span>
                  </Typography>
                  <Datepicker name="startDate" minDate={new Date()} isRequired placeholder="Select Start Date" />
                  {dateError && (
                    <Typography color="error" variant="caption">
                      {dateError}
                    </Typography>
                  )}
                </Grid>
                <Grid size={2.5}>
                  <Input
                    placeholder="End Date"
                    name="EndDate"
                    value={
                      formMethods.watch("startDate") &&
                      selectedCarePlanDetails?.duration &&
                      selectedCarePlanDetails?.durationUnit
                        ? (() => {
                            const startDate = new Date(formMethods.watch("startDate"));
                            const duration = selectedCarePlanDetails?.duration ?? 0;
                            let endDate = new Date(startDate);

                            if (selectedCarePlanDetails?.durationUnit === "DAY") {
                              endDate.setDate(startDate.getDate() + duration);
                            } else if (selectedCarePlanDetails?.durationUnit === "WEEK") {
                              endDate.setDate(startDate.getDate() + duration * 7);
                            } else if (selectedCarePlanDetails?.durationUnit === "MONTH") {
                              endDate.setMonth(startDate.getMonth() + duration);
                            } else if (selectedCarePlanDetails?.durationUnit === "YEAR") {
                              endDate.setFullYear(startDate.getFullYear() + duration);
                            }

                            return endDate.toLocaleDateString("en-GB", {
                              day: "2-digit",
                              month: "2-digit",
                              year: "numeric",
                            });
                          })()
                        : ""
                    }
                    label="End Date"
                  />
                </Grid>
                <Divider sx={{ my: 2 }} />
              </Grid>
            )}

            <Collapse in={showFullForm && !isEdit} timeout={300} sx={{ marginBottom: "10px" }}>
              <>
                <Grid container spacing={1} mb={3}>
                  <Grid size={12}>
                    <Typography
                      mb={1}
                      mt={2}
                      variant="body2"
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "16px",
                        lineHeight: "19.2px",
                        letterSpacing: "0%",
                      }}
                    >
                      Applicable for
                    </Typography>
                  </Grid>
                  <Grid size={3}>
                    <StaticSeletionShow
                      label={"Gender"}
                      options={
                        selectedCarePlanDetails?.gender === "MALE"
                          ? ["Male"]
                          : selectedCarePlanDetails?.gender === "FEMALE"
                            ? ["Female"]
                            : ["Male", "Female"]
                      }
                      selectedValue={
                        selectedCarePlanDetails?.gender === "MALE"
                          ? ["Male"]
                          : selectedCarePlanDetails?.gender === "FEMALE"
                            ? ["Female"]
                            : ["Male", "Female"]
                      }
                      onSelect={() => {}}
                    />
                  </Grid>

                  <Grid size={3}>
                    <StaticSeletionShow
                      label={"Age Criteria"}
                      options={[
                        selectedCarePlanDetails?.ageCriteria + " " + selectedCarePlanDetails?.age + " yrs" || "",
                      ]}
                      selectedValue={[
                        selectedCarePlanDetails?.ageCriteria + " " + selectedCarePlanDetails?.age + " yrs" || "",
                      ]}
                      onSelect={() => {}}
                    />
                  </Grid>

                  <Grid size={6}>
                    <StaticSeletionShow
                      label={"Diagnosis Codes"}
                      options={selectedCarePlanDetails?.diagnosisCodes || []}
                      selectedValue={selectedCarePlanDetails?.diagnosisCodes || []}
                      onSelect={() => {}}
                    />
                  </Grid>
                </Grid>

                <Divider sx={{ my: 2 }} />

                <Grid container spacing={1} mt={2}>
                  <Grid size={12}>
                    <Typography
                      variant="medium"
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "16px",
                        lineHeight: "19.2px",
                        letterSpacing: "0%",
                      }}
                    >
                      Program Overview
                    </Typography>
                    <Box mt={2}>
                      <TextField
                        disabled
                        multiline
                        rows={2}
                        value={formMethods.watch("programOverview") || ""}
                        onChange={(e) => formMethods.setValue("programOverview", e.target.value)}
                        sx={{
                          width: "100%",
                          fontFamily: "Roboto",
                          fontWeight: 400,
                          fontSize: "14px",
                          lineHeight: "22.4px",
                          letterSpacing: "0%",
                        }}
                        InputProps={{
                          "aria-label": "program overview",
                        }}
                      />
                    </Box>
                    <Grid container justifyContent="flex-end" mt={2}>
                      <Typography variant="body2" color="textSecondary">
                        {formMethods.watch("programOverview")?.length || 0}/180 characters
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>

                <Divider sx={{ my: 2 }} />
              </>
            </Collapse>

            {showFullForm && !isEdit && (
              <>
                <Grid container spacing={2} alignItems="flex-start">
                  <Grid size={12} mb={1}>
                    <Typography
                      variant="medium"
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "16px",
                        lineHeight: "19.2px",
                        letterSpacing: "0%",
                      }}
                    >
                      Program Goals
                    </Typography>
                  </Grid>
                </Grid>
                <Grid size={12} container spacing={1}>
                  {Array.isArray(selectedCarePlanDetails?.programGoals) &&
                    selectedCarePlanDetails.programGoals.map((goal, index) => (
                      <Grid size={6} key={index}>
                        <Accordion
                          defaultExpanded={false}
                          sx={{
                            background: "inherit",
                            height: "fit-content",
                            boxShadow: "none",
                            "&:before": {
                              display: "none",
                            },
                            border: "1px solid #DDDDDD",
                            borderRadius: "8px",
                          }}
                        >
                          <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            aria-controls={`panel-${index}-content`}
                            id={`panel-${index}-header`}
                          >
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                width: "100%",
                              }}
                            >
                              {goal.category && categoryIcons[goal.category] && (
                                <Box
                                  component="span"
                                  sx={{
                                    mr: 2,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: "36px",
                                    height: "36px",
                                  }}
                                >
                                  <img
                                    src={categoryIcons[goal.category]}
                                    width="24px"
                                    height="24px"
                                    alt={`${goal.category} Icon`}
                                  />
                                </Box>
                              )}
                              <Typography
                                variant="subtitle1"
                                fontWeight={500}
                                sx={{
                                  flexGrow: 1,
                                  fontFamily: "Roboto",
                                  fontSize: "14px",
                                  lineHeight: "120%",
                                  letterSpacing: "0%",
                                }}
                              >
                                {goal.title || "Program Goal"}
                              </Typography>
                            </Box>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Box sx={{ p: 1 }}>
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  mb: 2,
                                  pb: 2,
                                  borderBottom: "1px solid #f0f0f0",
                                }}
                              >
                                <Typography ml={2} variant="body2" color="text.secondary">
                                  {goal.category ? goal.category.charAt(0).toUpperCase() + goal.category.slice(1) : ""}{" "}
                                  Intake
                                </Typography>
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    border: "1px solid #e0e0e0",
                                    borderRadius: "5px",
                                    padding: "8px 16px",
                                  }}
                                >
                                  <Typography
                                    variant="body2"
                                    color="#1C2427"
                                    sx={{
                                      mr: 1,
                                      fontFamily: "Roboto",
                                      fontWeight: 400,
                                      fontSize: "14px",
                                      lineHeight: "160%",
                                      letterSpacing: "0%",
                                    }}
                                  >
                                    &lt; {goal.targetValue}
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontFamily: "Roboto",
                                      fontWeight: 500,
                                      fontSize: "14px",
                                      lineHeight: "120%",
                                      letterSpacing: "0%",
                                      color: "#1C2427",
                                    }}
                                  >
                                    {goal.unit}
                                  </Typography>
                                </Box>
                              </Box>

                              {goal.programGoalTasks && goal.programGoalTasks.length > 0 && (
                                <Box>
                                  {goal.programGoalTasks?.map((task, taskIndex) => (
                                    <Box
                                      key={taskIndex}
                                      sx={{
                                        display: "flex",
                                        mb: 1.5,
                                      }}
                                    >
                                      <Box
                                        component="span"
                                        sx={{
                                          minWidth: "6px",
                                          height: "6px",
                                          borderRadius: "50%",
                                          backgroundColor: "#666",
                                          mt: 0.8,
                                          mr: 1.5,
                                        }}
                                      />
                                      <Typography variant="body2">
                                        {task.title}
                                        {task.details && (
                                          <Box component="span" sx={{ display: "block", color: "text.secondary" }}>
                                            {task.details}
                                          </Box>
                                        )}
                                      </Typography>
                                    </Box>
                                  ))}
                                </Box>
                              )}
                            </Box>
                          </AccordionDetails>
                        </Accordion>
                      </Grid>
                    ))}
                </Grid>
                <Divider sx={{ my: 2 }} />
                <Grid container spacing={2} alignItems="flex-start">
                  <Grid size={12}>
                    <Typography
                      variant="medium"
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "16px",
                        lineHeight: "19.2px",
                        letterSpacing: "0%",
                      }}
                    >
                      Assigned Device
                    </Typography>
                  </Grid>
                  <Grid size={12}>
                    <Box sx={{ mt: 3 }}>
                      <Grid container spacing={2}>
                        {selectedCarePlanDetails?.devices?.map((device, index) => (
                          <Grid size={4} key={index}>
                            <Box
                              mb={2}
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                                border: "1px solid #DDDDDD",
                                borderRadius: "8px",
                                padding: "12px",
                                backgroundColor: "#FFFFFF",
                              }}
                            >
                              <Typography
                                variant="body1"
                                sx={{
                                  fontFamily: "Roboto",
                                  fontWeight: 500,
                                  fontSize: "16px",
                                  color: "#101828",
                                }}
                              >
                                {device.name}
                              </Typography>
                              <IOSSwitch />
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  </Grid>
                  <FormControlLabel
                    control={<Checkbox defaultChecked sx={{ "& .MuiSvgIcon-root": { fontSize: 30 } }} />}
                    label="Allow manual inputs"
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontSize: "14px",
                      lineHeight: "16px",
                      letterSpacing: "0%",
                      color: "#515C5F",
                    }}
                  />
                </Grid>
                {/* </Grid> */}

                <Divider sx={{ my: 2 }} />
              </>
            )}

            <>
              <Grid container spacing={2} alignItems="flex-start">
                {(showFullForm || isEdit) && (
                  <>
                    <Grid size={12} mb={2}>
                      <Typography
                        variant="medium"
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 500,
                          fontSize: "16px",
                          lineHeight: "19.2px",
                          letterSpacing: "0%",
                        }}
                      >
                        Vital Reference Range
                      </Typography>
                    </Grid>
                  </>
                )}
                <Grid size={12} container spacing={2}>
                  {(isEdit ? selectedCarePlanDetails?.vitalReference : selectedCarePlanDetails?.vitalReferences)?.map(
                    (vital: VitalReference, index: number) => (
                      <Grid size={12} key={index} borderBottom={"1px solid #DDDDDD"} paddingBottom={"12px"}>
                        <Typography
                          variant="medium"
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 500,
                            fontSize: "16px",
                            lineHeight: "19.2px",
                            letterSpacing: "0%",
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          {vital.vitalType}
                        </Typography>
                        <RangeSlider
                          title={vital.vitalType || "default"}
                          vitalReference={vital}
                          name={vital.vitalType || "default"}
                          onChange={handleSliderChange}
                        />
                      </Grid>
                    )
                  )}
                </Grid>
              </Grid>
            </>

            <DrawerFooter>
              <Button
                variant="contained"
                type="submit"
                sx={{
                  marginLeft: 1,
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: "14px",
                  lineHeight: "17.5px",
                  letterSpacing: "0%",
                }}
              >
                <CheckIcon fontSize="small" sx={{ mr: 1 }} />
                {isEditMode ? "Update" : "Assign"}
              </Button>
            </DrawerFooter>
          </Stack>
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default CareplanForm;
