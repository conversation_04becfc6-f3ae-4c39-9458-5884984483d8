import { useEffect, useState } from "react";
import React from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";

// This file has been improved with features from AppointmentTab, including:
// 1. Enhanced filtering capabilities (status filter, date range filter)
// 2. Sorting functionality for columns like Start Date and Status
// 3. Proper pagination implementation
// 4. Better UI for filter display and controls
// 5. Improved response data handling

// Note: CURRENT/PAST filtering works by setting the timeFilter parameter in the API call.
// For PAST view, we need to ensure that the status filter is cleared and only completed care plans are shown.
// For CURRENT view, we allow filtering by status (IN_PROGRESS, PENDING, etc.)
// We send COMPLETED status directly to the API when marking a care plan as complete.
// For backward compatibility, we still convert any CANCELLED status from the API to COMPLETED in the UI.

import AddIcon from "@mui/icons-material/Add";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  <PERSON><PERSON>,
  IconButton,
  Link,
  MenuItem,
  MenuList,
  Popover,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelectorSq from "@/common-components/custom-selector-sq/custom-selector-sq";
import Paginator from "@/common-components/paginator/paginator";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import Status from "@/common-components/status/status";
import { heading, tableCellCss, typographyCss } from "@/common-components/table/common-table-widgets";

import { useDrawer } from "@/components/providers/DrawerProvider";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import {
  PatientCarePlanControllerService,
  PatientVitalControllerService,
  ProviderControllerService,
} from "@/sdk/requests";
import {
  CarePlan,
  Device,
  NotifyPatientData,
  Patient,
  PatientVital,
  ProgramGoal,
  Protocol,
  VitalReference,
} from "@/sdk/requests/types.gen";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";

import CareplanForm from "./CareplanForm";
import ViewCarePlan from "./ViewCarePlan";

// Define header names for both CURRENT and PAST filters
const getCurrentHeaderNames = () => ["Title", "Track", "Duration", "Assigned On", "Status", "Action"];

const getPastHeaderNames = () => ["Title", "Track", "Duration", "Assigned On", "Completed On"];

type filterStatus = "CURRENT" | "PAST";

type PatientCarePlan = {
  uuid?: string;
  title?: string;
  duration?: number;
  durationUnit?: "DAY" | "WEEK" | "MONTH" | "YEAR";
  overview?: string;
  gender?: "MALE" | "FEMALE" | "UNISEX";
  ageCriteria?: string;
  startDate?: string;
  diagnosis?: string[];
  age?: string;
  deviceName?: string[];
  devices?: Device[];
  routineCheckup?: string;
  carePlanStatus?: "REQUESTED" | "ASSIGNED" | "CANCELLED" | "IN_PROGRESS" | "PENDING" | "COMPLETED";
  programGoals?: ProgramGoal[];
  vitalReferences?: VitalReference[];
  globalCarePlan?: boolean;
  external?: boolean;
  protocolType?: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
  active?: boolean;
  archive?: boolean;
  trackedVitals?: string[];
  modified?: string;
  diagnosisCodes?: string[];
  protocol?: Protocol[];
};

interface CarePlanProps {
  patientProfileData: Patient | undefined;
}

function CarePlanTab({ patientProfileData }: CarePlanProps) {
  const { patientId } = useParams();
  const [searchAssignCarePlan, setSearchAssignCarePlan] = useState("");
  const [statusFilter, setStatusFilter] = useState<filterStatus>("CURRENT");
  const [carePlanStatusFilter, setCarePlanStatusFilter] = useState<string>("ALL");
  const { open: openDrawer } = useDrawer();
  const [carePlans, setCarePlans] = useState<CarePlan[]>([]);
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [totalElement, setTotalElements] = useState<number>(0);
  const [totalPages, setTotalPages] = useState(0);
  const [sortDirection, setSortDirection] = useState("asc");
  const [sortDirectionHeader, setSortDirectionHeader] = useState("asc");
  const dispatch = useDispatch();

  // Action menu states
  const [anchorEl, setAnchorEl] = useState<Element | null>(null);
  const [selectedAction, setSelectedAction] = useState<"Edit" | "Mark as Complete" | "Mark as In Progress">("Edit");
  const [selectedCarePlan, setSelectedCarePlan] = useState<PatientCarePlan | null>(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  const [searchParams, setSearchParams] = useState({
    page: page,
    size: size,
    sortBy: "completedDate",
    sortDirection: "desc",
    searchString: "",
    status: undefined as boolean | undefined,
    archive: false,
    timeFilter: "",
    patientId: patientId,
    xTenantId: GetTenantId(),
  });

  const [headerNames, setHeaderNames] = useState<string[]>(getCurrentHeaderNames());
  const [shouldFetchData, setShouldFetchData] = useState(true);

  useEffect(() => {
    if (statusFilter === "CURRENT") {
      setHeaderNames(getCurrentHeaderNames());
    } else {
      setHeaderNames(getPastHeaderNames());
    }
  }, [statusFilter]);

  const [BMI, setBMI] = useState<string>("");

  const { data: weightData } = useQuery({
    queryKey: ["list-of-vitals-in-graph", patientId],
    queryFn: async () => {
      const response = (await PatientVitalControllerService.getPatientVitals1({
        patientUuid: patientId || "",
        xTenantId: GetTenantId(),
        vitalName: "Weight",
        size: 2,
      })) as unknown as AxiosResponse<ContentObject<PatientVital[]>>;
      return response;
    },
  });

  useEffect(() => {
    const measureBMI = () => {
      const height = Number(patientProfileData?.height) || (0 as number);
      const weight = weightData?.data?.content[0]?.value1;
      const heightInMeters = height / 100;
      const bmi = weight && heightInMeters ? (weight / heightInMeters ** 2).toFixed(1) : "0";
      setBMI(bmi);
    };

    measureBMI();
  }, [patientProfileData?.height, weightData]);

  const {
    data: carePlanData,
    isLoading,
    isFetching,
    refetch: refetchCarePlans,
  } = useQuery({
    queryKey: ["get-care-plan", searchParams],
    queryFn: () => {
      const apiParams = {
        ...searchParams,
        page: page,
        size: size,
        sortBy: "modified",
        sortDirection: sortDirectionHeader,
        timeFilter: searchParams.timeFilter as "CURRENT" | "PAST" | undefined,
        patientId: searchParams.patientId as string,
        carePlanStatus: carePlanStatusFilter === "ALL" ? "PENDING,IN_PROGRESS" : carePlanStatusFilter,
        xTenantId: GetTenantId(),
      };
      return PatientCarePlanControllerService.getAllCarePlans(apiParams);
    },
    enabled: shouldFetchData,
  });

  const handleNotifyClick = (appointmentId: string) => {
    notifyPatient({
      appointmentId: appointmentId,
      notifyType: "CARE_PLAN_REMINDER",
      xTenantId: GetTenantId(),
    });
  };

  const { mutate: notifyPatient } = useMutation({
    mutationFn: (payload: NotifyPatientData) => {
      return ProviderControllerService.notifyPatient(payload);
    },
    onSuccess: (response) => {
      const message = (response as unknown as AxiosResponse)?.data?.message || `Notification sent successfully!`;
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: error.body?.message || "Failed to send notification",
        })
      );
    },
  });

  useEffect(() => {
    if (carePlanData) {
      const res = (carePlanData as unknown as AxiosResponse).data as ContentObject<CarePlan[]>;
      if (res.content) {
        setCarePlans(res.content);
      }
      if (res.page) {
        setTotalElements(res.page.totalElements || 0);
        setTotalPages(res.page.totalPages || 0);
      }
    }
  }, [carePlanData]);

  useEffect(() => {
    setShouldFetchData(false);
    const newParams = { ...searchParams };

    if (searchAssignCarePlan) {
      newParams.searchString = searchAssignCarePlan;
    } else {
      newParams.searchString = "";
    }
    newParams.timeFilter = statusFilter;

    if (statusFilter === "CURRENT" && carePlanStatusFilter !== "ALL") {
      if (carePlanStatusFilter === "ACTIVE" || carePlanStatusFilter === "IN_PROGRESS") {
        newParams.status = true;
      } else if (
        carePlanStatusFilter === "PENDING" ||
        carePlanStatusFilter === "REQUESTED" ||
        carePlanStatusFilter === "COMPLETED"
      ) {
        newParams.status = false;
      }
    } else {
      newParams.status = undefined;
    }

    newParams.page = page;
    newParams.size = size;

    setSearchParams(newParams);
    setShouldFetchData(true);
  }, [searchAssignCarePlan, statusFilter, page, size, carePlanStatusFilter, searchParams]);

  useEffect(() => {
    if (!isLoading && !isFetching) {
      refetchCarePlans();
    }
  }, []);

  const getStatusText = (plan: PatientCarePlan): string => {
    if (plan.carePlanStatus) {
      if (plan.carePlanStatus === "CANCELLED") {
        return "COMPLETED";
      }
      return plan.carePlanStatus;
    }
    return plan.active ? "ACTIVE" : "PENDING";
  };

  const handleDrawer = {
    carePlanform: (title: string, selectedCarePlan: PatientCarePlan, isEdit?: boolean) => {
      openDrawer(
        {
          title: title,
          component: (
            <CareplanForm careplanId={selectedCarePlan} refetchCarePlans={refetchCarePlans} isEdit={!!isEdit} />
          ),
        },
        "60%"
      );
    },
    viewDetails: (carePlan: CarePlan, patientId: string | undefined) => {
      openDrawer(
        {
          title: `Care Plan Details`,
          component: (
            <ViewCarePlan carePlanId={carePlan.uuid} patientId={patientId} patientProfileData={patientProfileData} />
          ),
        },
        "60%"
      );
    },
  };

  const renderTableCell = (data: string | number | React.ReactNode) => {
    return (
      <TableCell sx={{ ...heading }} align="left">
        <Grid container flexDirection={"column"}>
          <Typography sx={typographyCss} variant="bodySmall">
            {data}
          </Typography>
        </Grid>
      </TableCell>
    );
  };

  const handleSorting = (header: string) => {
    if (header === "Completed On") {
      setSortDirection((prev) => (prev === "desc" ? "asc" : "desc"));
    }
  };

  useEffect(() => {
    setSortDirectionHeader(sortDirection);
  }, [sortDirection]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown> | null, page: number) => {
    setPage(page);
  };

  const handleFilterChange = (filterField: string) => {
    const newFilter = filterField as filterStatus;
    setPage(0);
    setSize(10);
    setStatusFilter(newFilter);

    if (newFilter === "CURRENT") {
      setCarePlanStatusFilter("ALL");
      setHeaderNames(getCurrentHeaderNames());
    } else {
      setCarePlanStatusFilter("ALL");
      const newParams = { ...searchParams };
      newParams.timeFilter = "PAST";
      newParams.status = undefined;
      setSearchParams(newParams);
      setHeaderNames(getPastHeaderNames());

      setTimeout(() => {
        setShouldFetchData(true);
        refetchCarePlans();
      }, 0);
    }
  };

  const toggleMoreMenu = (el?: Element) => {
    setAnchorEl(el || null);
  };

  // Get the latest blood pressure reading before completing the care plan
  const { data: bpData } = useQuery({
    queryKey: ["latest-bp-reading", patientId],
    queryFn: async () => {
      const response = await PatientVitalControllerService.getPatientVitals1({
        patientUuid: patientId || "",
        xTenantId: GetTenantId(),
        vitalName: "Blood Pressure",
        size: 1,
      });

      return response.data || null;
    },
    enabled: openConfirmDialog && selectedAction === "Mark as Complete",
  });

  // Type-safe access to BP data
  const bpValues = bpData?.content as PatientVital[] | undefined;
  const bpValue1 = bpValues?.[0]?.value1;
  const bpValue2 = bpValues?.[0]?.value2;

  const { mutate: updateCarePlanStatus } = useMutation({
    mutationFn: ({ patientCarePlanId, status }: { patientCarePlanId: string; status: string }) => {
      return PatientCarePlanControllerService.updatePatientCarePlanStatus({
        requestBody: {
          uuid: patientCarePlanId,
          carePlanStatus: status as "COMPLETED" | undefined,
          bmi: BMI,
          bloodPressure: bpValue1 && bpValue2 ? `${bpValue1}/${bpValue2}` : undefined,
        },
        xTenantId: GetTenantId(),
      });
    },
    onSuccess: (response) => {
      const message = (response as unknown as AxiosResponse)?.data?.message || `Care Plan status updated successfully!`;
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );

      refetchCarePlans();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: error.body?.message || "Failed to update care plan status",
        })
      );
    },
  });

  const confirmStatusChange = () => {
    if (selectedCarePlan && selectedCarePlan.uuid) {
      updateCarePlanStatus({
        patientCarePlanId: selectedCarePlan.uuid,
        status: "COMPLETED",
      });
    }
    setOpenConfirmDialog(false);
  };

  return (
    <Grid
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
      }}
    >
      {/* Fixed header section */}
      <Grid sx={{ flexShrink: 0 }}>
        <Grid container justifyContent={"space-between"} mb={1}>
          <Grid>
            <CustomSelectorSq
              options={["CURRENT", "PAST"]}
              onSelect={handleFilterChange}
              selectedValue={statusFilter || "CURRENT"}
              widthOfBtn="100px"
            />
          </Grid>

          <Grid
            container
            justifyContent="flex-end"
            alignItems={"center"}
            gap={2}
            display={"flex"}
            flexDirection={"row"}
            maxWidth="70%"
          >
            <Grid>
              <CustomInput
                placeholder="Search Care Plans"
                name="careplan"
                hasStartSearchIcon={true}
                value={searchAssignCarePlan}
                onDebounceCall={(searchString) => setSearchAssignCarePlan(searchString)}
                onInputEmpty={() => setSearchAssignCarePlan("")}
              />
            </Grid>
            <Grid>
              <Button
                startIcon={<AddIcon />}
                variant="outlined"
                sx={{ borderRadius: "8px" }}
                onClick={() => {
                  handleDrawer.carePlanform("Assign Care Plan", {} as PatientCarePlan);
                }}
              >
                Assign Care Plan
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Scrollable main content */}
      <Grid
        sx={{
          flexGrow: 1,
          overflow: "auto",
        }}
      >
        <TableContainer>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerNames.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    {header === "Completed On" ? (
                      <Link
                        style={{
                          color: "#667085",
                          textDecoration: "none",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSorting(header)}
                      >
                        <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                          {header}
                          <Typography>
                            {sortDirection === "asc" ? (
                              <ArrowUpwardIcon fontSize="small" />
                            ) : (
                              <ArrowDownwardIcon fontSize="small" />
                            )}
                          </Typography>
                        </Typography>
                      </Link>
                    ) : (
                      header
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading || isFetching ? (
                Array.from({ length: 5 }).map((_, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {headerNames.map((_, cellIndex) => (
                      <TableCell key={cellIndex} align="left">
                        <Skeleton variant="text" width="100%" height={40} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : carePlans.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={headerNames.length} align="center">
                    <Typography variant="bodySmall">No Care Plans Found</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                carePlans.map((plan, idx) => (
                  <TableRow key={idx}>
                    {/* Title - Make it clickable and blue */}
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <Typography
                          sx={{
                            color: theme.palette.primary.main,
                            cursor: "pointer",
                            fontWeight: 550,
                            fontSize: "14px",
                          }}
                          variant="bodySmall"
                          onClick={() => handleDrawer.viewDetails(plan, patientId)}
                        >
                          {plan.title || "Untitled Care Plan"}
                        </Typography>
                      </Grid>
                    </TableCell>

                    {/* Track - Using trackedVitals as track */}
                    {renderTableCell(
                      plan.trackedVitals && plan.trackedVitals.length > 0 ? plan.trackedVitals.join(", ") : "-"
                    )}

                    {/* Duration */}
                    {renderTableCell(
                      `${plan.duration || "-"} ${plan.durationUnit ? plan.durationUnit.toLowerCase() : ""}`
                    )}

                    {/* Assigned On - Use modified date */}
                    {renderTableCell(plan.modified ? new Date(plan.modified).toLocaleDateString() : "-")}

                    {/* Conditional columns */}
                    {statusFilter === "CURRENT" ? (
                      // Status (CURRENT only)
                      <TableCell sx={{ ...heading }} align="left">
                        <Grid container flexDirection={"column"}>
                          <Status status={getStatusText(plan)} width="100px" />
                        </Grid>
                      </TableCell>
                    ) : (
                      // Completed On (PAST only) - Using modified date for completed date
                      renderTableCell(plan.modified ? new Date(plan.modified).toLocaleDateString() : "-")
                    )}

                    {/* Action (CURRENT only) - Replace with dots menu */}
                    {statusFilter === "CURRENT" && (
                      <TableCell sx={{ ...heading }} align="left">
                        <Grid container flexWrap={"nowrap"}>
                          <IconButton
                            style={{ padding: 5 }}
                            onClick={(e) => {
                              toggleMoreMenu(e.currentTarget);
                              setSelectedCarePlan(plan);
                            }}
                          >
                            <MoreVertIcon />
                          </IconButton>
                        </Grid>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Grid container sx={{ mt: 2 }}>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElement}
            onRecordsPerPageChange={handleRecordsPerPageChange}
            onPageChange={handlePageChange}
            defaultSize={size}
          />
        </Grid>
      </Grid>

      {/* Popover Menu - Dynamic options based on status */}
      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={() => toggleMoreMenu()}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <MenuList autoFocusItem={!!anchorEl} disablePadding>
          <MenuItem
            onClick={() => {
              setSelectedAction("Edit");
              toggleMoreMenu();
              if (selectedCarePlan) {
                handleDrawer.carePlanform("Edit Care Plan", selectedCarePlan, true);
              }
            }}
          >
            Edit
          </MenuItem>
          {selectedCarePlan?.carePlanStatus === "PENDING" && (
            <MenuItem
              onClick={() => {
                handleNotifyClick(selectedCarePlan?.uuid as string);
                toggleMoreMenu();
              }}
            >
              Send Reminder
            </MenuItem>
          )}

          {/* Show "Mark as Complete" only when status is IN_PROGRESS */}
          {selectedCarePlan?.carePlanStatus === "IN_PROGRESS" && (
            <MenuItem
              onClick={() => {
                setSelectedAction("Mark as Complete");
                toggleMoreMenu();
                setOpenConfirmDialog(true);
              }}
            >
              Mark as Complete
            </MenuItem>
          )}
        </MenuList>
      </Popover>

      {/* Confirmation Dialog - Dynamic text based on selected action */}
      <ConfirmationPopUp
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        onConfirm={confirmStatusChange}
        message={`Are you sure you want to ${selectedAction.toLowerCase()} this care plan?`}
        title={selectedAction}
        subtitle="This action will update the status of the care plan."
        confirmButtonName="Confirm"
        rowData={[
          selectedCarePlan?.title || "",
          selectedCarePlan?.trackedVitals && selectedCarePlan?.trackedVitals.length > 0
            ? selectedCarePlan?.trackedVitals.join(", ")
            : "-",
          `${selectedCarePlan?.duration || "-"} ${selectedCarePlan?.durationUnit ? selectedCarePlan?.durationUnit.toLowerCase() : ""}`,
          selectedCarePlan?.modified ? new Date(selectedCarePlan?.modified).toLocaleDateString() : "-",
        ]}
        header={[{ header: "Title" }, { header: "Track" }, { header: "Duration" }, { header: "Assigned On" }]}
      />
    </Grid>
  );
}

export default CarePlanTab;
