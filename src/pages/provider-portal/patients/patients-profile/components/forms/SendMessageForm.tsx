import { useRef } from "react";
import React from "react";
import { Form<PERSON>rovider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { Send } from "@mui/icons-material";
import { <PERSON><PERSON>, Grid2 as Grid } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import * as yup from "yup";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
import { messageRequiredErrorMsg } from "@/constants/error-messages";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { NotificationControllerService, Patient, SendDirectMessageRequest } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

const SendSmsForm = ({ patientData, closeDrawer }: { patientData: Patient; closeDrawer: () => void }) => {
  const footerRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();

  const composeSmsSchema = yup.object().shape({
    message: yup.string().required(messageRequiredErrorMsg),
  });

  const formMethods = useForm({
    resolver: yupResolver(composeSmsSchema),
    defaultValues: {
      message: "",
    },
  });

  const {
    formState: { errors },
    setValue,
  } = formMethods;

  const { mutate: sendSmsMutation } = useMutation({
    mutationFn: (payload: SendDirectMessageRequest) => {
      return NotificationControllerService.sendDirectedMessage({
        xTenantId: GetTenantId(),
        requestBody: payload,
      });
    },
    onSuccess: (response) => {
      const message = (response as AxiosResponse)?.data?.message || "SMS sent successfully!";
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
      closeDrawer();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: error.body?.message || "Failed to send SMS",
        })
      );
    },
  });

  const onSubmit = (value: { message: string }) => {
    const payload: SendDirectMessageRequest = {
      phone: patientData?.mobileNumber,
      content: value.message,
      type: "SMS",
    };
    sendSmsMutation(payload);
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue("message", e.target.value, { shouldValidate: true });
  };

  return (
    <DrawerBody padding={3} offset={footerRef?.current?.offsetHeight}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <Grid container direction="column" spacing={0}>
            <CustomLabel label="Message" isRequired />
            <Grid>
              <CustomInput
                name="message"
                multiline
                rows={30}
                placeholder="Enter Your Message"
                value={formMethods.watch("message") || ""}
                onChange={handleMessageChange}
                hasError={!!errors.message}
                errorMessage={errors.message?.message}
              />
            </Grid>
          </Grid>

          <DrawerFooter footerRef={footerRef}>
            <Button type="submit" variant="contained" endIcon={<Send fontSize="small" />}>
              Send
            </Button>
          </DrawerFooter>
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default SendSmsForm;
