import { FormProvider, useForm } from "react-hook-form";

import { <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON> } from "@mui/lab";
import { Box, Grid2 as Grid, Stack, Typography } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQueryClient } from "@tanstack/react-query";
import * as yup from "yup";

import { Textarea } from "@/components/ui/Form/Textarea";
import MainDialog from "@/components/ui/MainDialog";
import useApiFeedback from "@/hooks/useApiFeedback";
import { usePatientVitalControllerServiceUpdatePatientVital } from "@/sdk/queries";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { formatDateNewFormat } from "@/utils/format/date";

import { SelectedNoteState } from "../VitalsTab";

interface NoteFormProps {
  config: {
    selectedNote: SelectedNoteState | null;
    setSelectedNote: (noteForm: SelectedNoteState | null) => void;
  };
}

const noteSchema = yup.object().shape({
  note: yup.string(),
});

const NoteForm = ({ config }: NoteFormProps) => {
  const { selectedNote: vital, setSelectedNote } = config;
  const isEdit = Boolean(vital?.note?.uuid);
  const isReadOnly = Boolean(vital?.readOnly);
  const queryClient = useQueryClient();

  const isAlert =
    (vital?.severity === "CRITICAL" || vital?.severity === "OVER_WEIGHT" || vital?.severity === "UNDER_WEIGHT") &&
    vital?.alertStatus === "NOT_RESOLVED";

  const isResolved = isAlert && vital?.note?.goalProgress !== "RESOLVED";

  const formMethods = useForm({
    resolver: yupResolver(noteSchema),
    defaultValues: isEdit ? { note: `${vital?.note?.name}` } : undefined,
  });

  const updateNoteMutation = usePatientVitalControllerServiceUpdatePatientVital({
    onSuccess: async () => {
      try {
        await queryClient.invalidateQueries({ queryKey: ["list-of-vitals", vital?.patientId, vital?.vitalName] });
        await queryClient.invalidateQueries({
          queryKey: ["list-of-vitals-in-graph", vital?.patientId, vital?.vitalName],
        });
      } catch (error) {
        console.error(error);
      } finally {
        handleCloseForm();
      }
    },
  });

  useApiFeedback(
    updateNoteMutation.isError,
    updateNoteMutation.error,
    updateNoteMutation.isSuccess,
    (updateNoteMutation.data?.message || `Note ${isEdit ? "updated" : "added"} successfully`) as string
  );

  const handleCloseForm = () => {
    setSelectedNote(null);
    formMethods.reset();
  };

  const onSubmit = async (values: yup.InferType<typeof noteSchema>) => {
    if (!vital) return;

    await updateNoteMutation.mutateAsync({
      requestBody: {
        ...vital,
        note: { ...vital?.note, name: values.note, goalProgress: "RESOLVED" },
        alertStatus: "RESOLVED",
      },
      xTenantId: GetTenantId(),
    });
  };

  return (
    <FormProvider {...formMethods}>
      <MainDialog
        open={Boolean(vital)}
        handleClose={handleCloseForm}
        title={isReadOnly ? "View Note" : isEdit ? "Edit Note" : isResolved ? "Resolve Alert" : "Add Note"}
        actionButtons={
          <>
            {isReadOnly ? (
              ""
            ) : (
              <Button
                type="submit"
                disabled={!formMethods.watch("note")}
                variant="contained"
                // startIcon={<CheckOutlined />}
                onClick={formMethods.handleSubmit(onSubmit)}
                loading={updateNoteMutation.isPending}
              >
                {isEdit ? "Update Note" : isResolved ? "Save & Resolve" : "Add Note"}
              </Button>
            )}
          </>
        }
      >
        <Stack spacing={2.5}>
          <Box sx={{ padding: 1.5, borderRadius: "8px", backgroundColor: isResolved ? "#FFF2F3" : "#F2F7F9" }}>
            <Grid container spacing={1} rowSpacing={2}>
              <Grid size={12}>
                <Typography variant="medium" color="#006D8F">
                  {vital?.vitalName}
                </Typography>
              </Grid>
              <Grid size={{ xs: 12, md: 4 }}>
                <Stack spacing={0.5}>
                  <Typography variant="smallBold" color="#7E8C8E">
                    Recorded Date
                  </Typography>
                  <Typography variant="medium">{formatDateNewFormat(vital?.recordedDate)}</Typography>
                </Stack>
              </Grid>
              {vital?.vitalName === "Blood Pressure" ? (
                <>
                  <Grid size={{ xs: 12, md: 4 }}>
                    <Stack spacing={0.5}>
                      <Typography variant="smallBold" color="#7E8C8E">
                        Systolic
                      </Typography>
                      <Typography variant="medium">
                        {vital?.value1} {vital?.unit || "mmHg"}
                      </Typography>
                    </Stack>
                  </Grid>
                  <Grid size={{ xs: 12, md: 4 }}>
                    <Stack spacing={0.5}>
                      <Typography variant="smallBold" color="#7E8C8E">
                        Diastolic
                      </Typography>
                      <Typography variant="medium">
                        {vital?.value2} {vital?.unit || "mmHg"}
                      </Typography>
                    </Stack>
                  </Grid>
                </>
              ) : (
                <Grid size={{ xs: 12, md: 4 }}>
                  <Stack spacing={0.5}>
                    <Typography variant="smallBold" color="#7E8C8E">
                      Value
                    </Typography>
                    <Typography variant="medium">
                      {vital?.value1} {vital?.unit}
                    </Typography>
                  </Stack>
                </Grid>
              )}
              {isReadOnly && (
                <Grid size={{ xs: 12, md: 4 }}>
                  <Stack spacing={0.5}>
                    <Typography variant="smallBold" color="#7E8C8E">
                      Note
                    </Typography>
                    <Typography variant="medium">{vital?.note?.name}</Typography>
                  </Stack>
                </Grid>
              )}
            </Grid>
          </Box>
          {!isReadOnly && (
            <Grid>
              <Textarea name="note" style={{ width: "100%" }} />
            </Grid>
          )}
        </Stack>
      </MainDialog>
    </FormProvider>
  );
};

export default NoteForm;
