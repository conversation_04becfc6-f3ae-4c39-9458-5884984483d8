import { useRef } from "react";
import React from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { Send } from "@mui/icons-material";
import { Button, Grid2 as Grid } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import * as yup from "yup";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
import { Input } from "@/components/ui/Form/Input";
import { messageRequiredErrorMsg, subjectRequiredErrorMsg } from "@/constants/error-messages";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { NotificationControllerService, Patient, SendDirectMessageRequest } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

const SendEmailForm = ({ patientData, closeDrawer }: { patientData: Patient; closeDrawer: () => void }) => {
  const footerRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();

  const composeEmailSchema = yup.object().shape({
    subject: yup.string().required(subjectRequiredErrorMsg),
    message: yup.string().required(messageRequiredErrorMsg),
  });

  const formMethods = useForm({
    resolver: yupResolver(composeEmailSchema),
    mode: "onSubmit",
    reValidateMode: "onSubmit",
  });

  const { mutate: sendEmailMutation } = useMutation({
    mutationFn: (payload: SendDirectMessageRequest) => {
      return NotificationControllerService.sendDirectedMessage({
        xTenantId: GetTenantId(),
        requestBody: payload,
      });
    },
    onSuccess: (response) => {
      const message = (response as AxiosResponse)?.data?.message || "Email sent successfully!";
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
      closeDrawer();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: error.body?.message || "Failed to send email",
        })
      );
    },
  });

  const onSubmit = (value: { message: string; subject: string }) => {
    const payload: SendDirectMessageRequest = {
      email: patientData.email,
      content: value.message,
      subject: value.subject,
      type: "EMAIL",
    };
    sendEmailMutation(payload);
  };

  const {
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = formMethods;

  const handleMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue("message", e.target.value, { shouldValidate: false });
  };

  const subjectValue = watch("subject");
  const messageValue = watch("message");

  const isFormEmpty = !subjectValue?.trim() || !messageValue?.trim();

  return (
    <DrawerBody padding={1} margin={2} offset={footerRef?.current?.offsetHeight}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <Grid container direction="column" spacing={3}>
            <Grid>
              <Input name="subject" isRequired />
            </Grid>

            <Grid>
              <CustomLabel label="Message" isRequired />
              <CustomInput
                name="message"
                paddingTop={"15px"}
                multiline
                rows={28}
                onChange={handleMessageChange}
                placeholder="Enter Your Message"
                value={messageValue || ""}
                hasError={!!errors.message}
                errorMessage={errors.message?.message}
              />
            </Grid>
          </Grid>

          <DrawerFooter footerRef={footerRef}>
            <Button
              type="submit"
              variant="contained"
              endIcon={<Send fontSize="small" />}
              disabled={isFormEmpty || isSubmitting}
            >
              Send
            </Button>
          </DrawerFooter>
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default SendEmailForm;
