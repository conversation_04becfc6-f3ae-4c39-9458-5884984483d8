import React, { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";

import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import SyncIcon from "@mui/icons-material/Sync";
import { Box, Button, Grid2 as Grid, IconButton, Skeleton, Stack, Typography, keyframes, styled } from "@mui/material";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import { CustomSelect } from "@/components/ui/Form/Select";
import { VITAL_TYPES } from "@/constants/collections";
import useApiFeedback from "@/hooks/useApiFeedback";
import { ContentObject } from "@/models/response/response-content-entity";
import { usePatientVitalControllerServiceSyncPatientVital } from "@/sdk/queries";
import { PatientVitalControllerService } from "@/sdk/requests/services.gen";
import { Patient, PatientVital } from "@/sdk/requests/types.gen";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { formatDate } from "@/utils/format/date";

import NoteForm from "./forms/NoteForm";
import { DateRangeFilter } from "./shared/DateRangeFilter";
import ECGGraph from "./shared/EcgChart";
import ListVitalsData from "./shared/ListVitalsData";
import VitalsChart from "./shared/VitalsChart";

export interface VitalType extends PatientVital {
  thresholds: {
    normal: { min: number; max: number };
    warning: { min: number; max: number };
    critical: { min: number; max: number };
  };
  yMin: number;
  yMax: number;
  IconComponent: React.FunctionComponent<
    React.SVGProps<SVGSVGElement> & { title?: string; titleId?: string; desc?: string; descId?: string }
  >;
}

export interface SelectedNoteState extends PatientVital {
  readOnly?: boolean;
}

interface VitalsProps {
  patientProfileData: Patient | undefined;
}

// localStorage key
const SELECTED_VITAL_TYPE_KEY = "selectedVitalType";
const SELECTED_ECG_NOTE_KEY = "selectedEcgNote";

const VitalsTab = ({ patientProfileData }: VitalsProps) => {
  const { patientId } = useParams();
  const xTenantId = GetTenantId();
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const vitalTypeBoxRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const ecgNoteListContainerRef = useRef<HTMLDivElement | null>(null);
  const initialVitalTypeSetRef = useRef<boolean>(false);

  // All useState declarations
  const [selectedVitalType, setSelectedVitalType] = useState<VitalType | undefined>(undefined);
  const [selectedNote, setSelectedNote] = useState<SelectedNoteState | null>(null);
  const [ecgData, setEcgData] = useState<PatientVital>();
  const [ecgArrayEle, setEcgArrayEle] = useState<string>("");
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);
  const [dynamicYMaxWeight, setDynamicYMaxWeight] = useState<number | null>(null);
  const [vitalTypeFilters, setVitalTypeFilters] = useState<{
    [key: string]: {
      timeFilter: "LAST_MONTH" | "LAST_WEEK" | "PAST_24_HOURS" | "DATE_RANGE";
      dateRange: { startDate: Date; endDate: Date };
    };
  }>({});

  const initialDateRange = {
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    endDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  };

  // Latest vitals for select vital type card
  const { data: latestVitalsData, isPending: isLatestVitalsLoading } = useQuery({
    queryKey: ["list-of-latest-vitals", patientId],
    queryFn: async () => {
      const response = (await PatientVitalControllerService.getPatientLatestVitals({
        patientUuid: String(patientId),
        xTenantId: GetTenantId(),
      })) as unknown as AxiosResponse;

      const vitalTypes: VitalType[] = response.data.map((vital: PatientVital) => {
        const vitalType = VITAL_TYPES.find((type) => type.value === vital.vitalName);
        return {
          ...vital,
          unit: vital.unit || vitalType?.unit || "",
          yMin: vitalType?.yMin || 0,
          yMax: vitalType?.yMax || 100,
          thresholds: vitalType?.thresholds || {
            normal: { min: 0, max: 100 },
            warning: { min: 0, max: 100 },
            critical: { min: 0, max: 100 },
          },
          IconComponent: vitalType?.IconComponent || "svg",
        };
      });

      return vitalTypes;
    },
    enabled: Boolean(patientId),
  });

  const {
    data: EcgRecord,
    isSuccess,
    isPending: isPendingEhrArrayValue,
  } = useQuery({
    queryKey: ["get-edg-value", ecgData],
    queryFn: () =>
      PatientVitalControllerService.getEcgValue({
        ecgId: ecgData?.ecgValue || "",
        xTenantId: xTenantId,
      }),
    enabled: !!ecgData?.ecgValue,
  });
  useEffect(() => {
    if (isSuccess) {
      setEcgArrayEle(EcgRecord.data as unknown as string);
    }
  }, [isSuccess, EcgRecord]);

  // Ensure we preserve ecgArrayEle during loading transitions
  useEffect(() => {
    if (selectedVitalType?.vitalName !== "ECG") {
      setEcgArrayEle("");
    }
  }, [selectedVitalType?.vitalName]);

  useEffect(() => {
    if (selectedNote && selectedNote.vitalName === "ECG" && ecgData?.uuid !== selectedNote.uuid) {
      setEcgData(selectedNote);
    }
  }, [selectedNote, ecgData?.uuid]);

  const isSelectedVitalType = (vitalType: PatientVital) => selectedVitalType?.uuid === vitalType.uuid;

  // Get current vital type's filter
  const getCurrentVitalFilter = () => {
    if (!selectedVitalType?.vitalName) return null;

    if (!vitalTypeFilters[selectedVitalType.vitalName]) {
      setVitalTypeFilters((prev) => ({
        ...prev,
        [selectedVitalType.vitalName]: {
          timeFilter: "LAST_MONTH",
          dateRange: initialDateRange,
        },
      }));
    }

    return vitalTypeFilters[selectedVitalType.vitalName];
  };

  const currentFilter = getCurrentVitalFilter();

  // Vitals data for graph
  const {
    data: graphData,
    isLoading: isGraphDataLoading,
    refetch,
  } = useQuery({
    queryKey: [
      "list-of-vitals-in-graph",
      patientId,
      selectedVitalType?.vitalName,
      currentFilter?.timeFilter,
      currentFilter?.dateRange,
    ],
    queryFn: async () => {
      const response = (await PatientVitalControllerService.getPatientVitals1({
        patientUuid: patientId || "",
        xTenantId: GetTenantId(),
        vitalName: selectedVitalType?.vitalName,
        timeFilter: currentFilter?.timeFilter === "DATE_RANGE" ? undefined : currentFilter?.timeFilter,
        startDate:
          currentFilter?.timeFilter === "DATE_RANGE"
            ? new Date(currentFilter.dateRange.startDate.setHours(0, 0, 0, 0)).toISOString()
            : undefined,
        endDate:
          currentFilter?.timeFilter === "DATE_RANGE"
            ? new Date(currentFilter.dateRange.endDate.setHours(23, 59, 59, 999)).toISOString()
            : undefined,
      })) as unknown as AxiosResponse<ContentObject<PatientVital[]>>;

      return response.data.content.sort(
        (a, b) => new Date(b.recordedDate).getTime() - new Date(a.recordedDate).getTime()
      );
    },
    enabled: Boolean(patientId) && Boolean(selectedVitalType?.vitalName),
  });

  useEffect(() => {
    if (selectedVitalType?.vitalName === "Weight" && graphData && graphData.length > 0) {
      const weightValues = graphData.map((v) => v.value1 ?? 0);
      const maxWeight = Math.max(...weightValues);
      const newYMax = Math.max(selectedVitalType.yMin || 0, maxWeight + 20);
      setDynamicYMaxWeight(newYMax);
    } else {
      setDynamicYMaxWeight(null);
    }
  }, [graphData, selectedVitalType?.vitalName, selectedVitalType?.yMin]);

  useEffect(() => {
    if (selectedVitalType?.vitalName !== "ECG") {
      setEcgData(undefined);
      setEcgArrayEle("");
    } else if (selectedVitalType?.vitalName === "ECG" && graphData && graphData.length > 0 && !ecgData) {
      setEcgData(graphData[0]);
    }
  }, [selectedVitalType?.vitalName, graphData, ecgData]);

  const {
    mutateAsync: mutateAsyncSync,
    isSuccess: isSuccessSync,
    data: dataSync,
    error: errorSync,
    isError: isErrorSync,
    isPending: isPendingSync,
  } = usePatientVitalControllerServiceSyncPatientVital();

  useApiFeedback(isErrorSync, errorSync, isSuccessSync, (dataSync?.data || "Vital Sync Successfully") as string);

  const handleVitalSyncCall = async () => {
    await mutateAsyncSync({ patientEhrId: patientProfileData?.ehrId as string, xTenantId: xTenantId });
    refetch();
  };

  const handleTimeFilterChange = (value: "LAST_MONTH" | "LAST_WEEK" | "PAST_24_HOURS" | "DATE_RANGE") => {
    if (!selectedVitalType?.vitalName) return;

    let newDateRange = currentFilter?.dateRange || initialDateRange;

    // If switching to DATE_RANGE, set the initial date range for this vital type
    if (value === "DATE_RANGE" && currentFilter?.timeFilter !== "DATE_RANGE") {
      const now = new Date();
      newDateRange = {
        startDate: new Date(now.getFullYear(), now.getMonth(), 1),
        endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0),
      };
    }

    setVitalTypeFilters((prev) => ({
      ...prev,
      [selectedVitalType.vitalName]: {
        ...prev[selectedVitalType.vitalName],
        timeFilter: value,
        dateRange: newDateRange,
      },
    }));
  };

  // Add this useEffect for scroll checks
  useEffect(() => {
    const checkScroll = () => {
      if (scrollContainerRef.current) {
        const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
        const hasScrollableContent = scrollWidth > clientWidth;

        // Show left arrow only when scrolled away from start
        setShowLeftArrow(hasScrollableContent && scrollLeft > 10);

        // Show right arrow only when there's more content to scroll to
        setShowRightArrow(hasScrollableContent && scrollLeft + clientWidth < scrollWidth - 50);
      }
    };

    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener("scroll", checkScroll);
      const resizeObserver = new ResizeObserver(checkScroll);
      resizeObserver.observe(container);

      // Initial check
      checkScroll();

      return () => {
        container.removeEventListener("scroll", checkScroll);
        resizeObserver.disconnect();
      };
    }
  }, [latestVitalsData]);

  const scroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const scrollAmount = 300; // Reduced scroll amount for smoother scrolling
      scrollContainerRef.current.scrollBy({
        left: direction === "left" ? -scrollAmount : scrollAmount,
        behavior: "smooth",
      });
    }
  };

  const rotateIcon = keyframes`
  from {
    transform: rotate(0deg)
  }
    to{
    transform : rotate(360deg)
    }
  `;

  // save the selected vital type in local storage
  useEffect(() => {
    if (latestVitalsData && latestVitalsData.length > 0 && !initialVitalTypeSetRef.current) {
      const savedVitalType = localStorage.getItem(SELECTED_VITAL_TYPE_KEY);
      let found = null;
      if (savedVitalType) {
        found = latestVitalsData.find((v) => v.vitalName === savedVitalType || v.uuid === savedVitalType);
      }
      if (!found) {
        found = latestVitalsData[0];
      }
      setSelectedVitalType(found);
      // Mark that we've set the initial vital type
      initialVitalTypeSetRef.current = true;

      setTimeout(() => {
        if (found && vitalTypeBoxRefs.current[found.uuid || found.vitalName]) {
          const box = vitalTypeBoxRefs.current[found.uuid || found.vitalName];
          const container = scrollContainerRef.current;
          if (box && container) {
            const boxRect = box.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            if (boxRect.left < containerRect.left || boxRect.right > containerRect.right) {
              container.scrollLeft += boxRect.left - containerRect.left - containerRect.width / 2 + boxRect.width / 2;
            }
          }
        }
      }, 100);
    }
  }, [latestVitalsData]);

  const handleVitalTypeBoxClick = (vitalType: VitalType) => {
    setSelectedVitalType(vitalType);
    // Store the vital name, not the uuid, for better continuity between sessions
    localStorage.setItem(SELECTED_VITAL_TYPE_KEY, vitalType.vitalName || "");
    // Ensure we don't reset this on data reload
    initialVitalTypeSetRef.current = true;

    setTimeout(() => {
      if (vitalType && vitalTypeBoxRefs.current[vitalType.uuid || vitalType.vitalName]) {
        const box = vitalTypeBoxRefs.current[vitalType.uuid || vitalType.vitalName];
        const container = scrollContainerRef.current;
        if (box && container) {
          const boxRect = box.getBoundingClientRect();
          const containerRect = container.getBoundingClientRect();
          if (boxRect.left < containerRect.left || boxRect.right > containerRect.right) {
            container.scrollLeft += boxRect.left - containerRect.left - containerRect.width / 2 + boxRect.width / 2;
          }
        }
      }
    }, 100);
  };

  // Save selected ECG note to localStorage when it changes
  useEffect(() => {
    if (ecgData && selectedVitalType?.vitalName === "ECG") {
      localStorage.setItem(SELECTED_ECG_NOTE_KEY, ecgData.uuid || "");
    }
  }, [ecgData, selectedVitalType?.vitalName]);

  // Load the saved ECG note when graph data is available
  useEffect(() => {
    if (selectedVitalType?.vitalName === "ECG" && graphData && graphData.length > 0) {
      const savedEcgNoteId = localStorage.getItem(SELECTED_ECG_NOTE_KEY);

      if (savedEcgNoteId) {
        // Try to find the saved ECG note in the current data
        const savedNote = graphData.find((note) => note.uuid === savedEcgNoteId);
        if (savedNote) {
          setEcgData(savedNote);
          // Also update selectedNote if needed
          setSelectedNote((prev) => (prev?.uuid === savedNote.uuid ? prev : null));

          // We'll scroll to this note in the ListVitalsData component
          // through the useEffect that runs after the component mounts
        } else if (!ecgData) {
          // If saved note not found, default to the first note
          setEcgData(graphData[0]);
        }
      } else if (!ecgData) {
        // No saved note, default to first one
        setEcgData(graphData[0]);
      }
    }
  }, [selectedVitalType?.vitalName, graphData, ecgData]);

  if (!isLatestVitalsLoading && !selectedVitalType) {
    return (
      <Box>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid>
            <Typography variant="medium" color="#515C5F">
              No data found
            </Typography>
          </Grid>
          <Grid>
            <Button
              startIcon={
                <SyncIcon
                  sx={{
                    animation: isPendingSync ? `${rotateIcon} 1s linear infinite` : "none",
                  }}
                />
              }
              variant="outlined"
              onClick={handleVitalSyncCall}
              sx={{ borderRadius: "12px", height: "30px", fontWeight: 500, mb: 1 }}
              disabled={patientProfileData?.ehrId ? false : true}
            >
              Sync From EHR
            </Button>
          </Grid>
        </Grid>
      </Box>
    );
  }

  // Modified VitalTypeBox section with navigation buttons
  const renderVitalsList = () => (
    <Box
      sx={{
        position: "relative",
        width: "100%",
        display: "flex",
        alignItems: "center",
        gap: 2,
      }}
    >
      {/* Left Navigation Button */}
      <Box
        sx={{
          position: "sticky",
          left: 0,
          zIndex: 10,
          visibility: showLeftArrow ? "visible" : "hidden",
          flexShrink: 0, // Prevent button from shrinking
        }}
      >
        <IconButton
          sx={{
            border: "1px solid #B6C1C4",
            backgroundColor: "#fff",
            "&:hover": { backgroundColor: "#F0F0F0" },
          }}
          onClick={() => scroll("left")}
        >
          <ChevronLeftIcon />
        </IconButton>
      </Box>

      <Grid
        container
        spacing={2}
        size={12}
        ref={scrollContainerRef}
        sx={{
          flexWrap: "nowrap",
          overflowX: "scroll",
          overflowY: "hidden",
          scrollbarWidth: "none",
          "&::-webkit-scrollbar": { display: "none" },
          flex: 1, // Take remaining space
        }}
      >
        {latestVitalsData?.map((vitalType) => (
          <Grid size={{ xs: 6, md: 3 }} minWidth={"250px"} key={vitalType.uuid}>
            <VitalTypeBox
              ref={(el) => {
                vitalTypeBoxRefs.current[vitalType.uuid || vitalType.vitalName] = el;
              }}
              spacing={{ xs: 0, md: 2 }}
              sx={{
                backgroundColor: isSelectedVitalType(vitalType) ? "#EEFBFF" : "#FFF",
                borderColor: isSelectedVitalType(vitalType) ? "#006D8F" : "#CDD7DA",
                cursor: isSelectedVitalType(vitalType) ? "default" : "pointer",
                "&:hover": {
                  opacity: isSelectedVitalType(vitalType) ? 1 : 0.8,
                },
              }}
              onClick={() => handleVitalTypeBoxClick(vitalType)}
            >
              <Grid container size={12}>
                <Grid display={"flex"} flexDirection={"column"} rowGap={0.6} size={9}>
                  <Grid display={"flex"} alignItems={"center"} gap={0.8}>
                    <Grid width={24} height={24} container alignItems={"center"}>
                      <vitalType.IconComponent
                        style={{
                          width: "100%",
                          fill: isSelectedVitalType(vitalType) ? "#006D8F" : "#212D30",
                        }}
                      />
                    </Grid>
                    <Typography variant="large">
                      {vitalType.vitalName === "Oxygen Saturation" ? "SpO2" : vitalType.vitalName}
                    </Typography>
                  </Grid>
                  <Grid container pl={0.5}>
                    <Typography variant="description">{formatDate(vitalType.recordedDate)}</Typography>
                  </Grid>
                </Grid>
                <Grid size={3} container justifyContent={"flex-end"} alignItems={"flex-end"} flexDirection={"column"}>
                  <Typography
                    fontWeight={500}
                    lineHeight={1.3}
                    sx={{ color: isSelectedVitalType(vitalType) ? "#006D8F" : "#212D30" }}
                  >
                    {vitalType.value1 ?? 0}
                    {vitalType.vitalName === "Blood Pressure" && `/${vitalType.value2 ?? 0}`}
                  </Typography>
                  <Typography fontSize="0.75rem" sx={{ color: isSelectedVitalType(vitalType) ? "#006D8F" : "#212D30" }}>
                    {vitalType.unit || "-"}
                  </Typography>
                </Grid>
              </Grid>
            </VitalTypeBox>
          </Grid>
        ))}
      </Grid>

      <Box
        sx={{
          position: "sticky",
          right: 0,
          zIndex: 10,
          visibility: showRightArrow ? "visible" : "hidden",
          flexShrink: 0, // Prevent button from shrinking
        }}
      >
        <IconButton
          sx={{
            border: "1px solid #B6C1C4",
            backgroundColor: "#fff",
            "&:hover": { backgroundColor: "#F0F0F0" },
          }}
          onClick={() => scroll("right")}
        >
          <ChevronRightIcon />
        </IconButton>
      </Box>
    </Box>
  );

  return (
    <>
      <Grid container spacing={2} sx={{ height: "100%" }}>
        <Grid size={2} sx={{ height: "100%" }}>
          {isLatestVitalsLoading ? (
            <Skeleton variant="rectangular" sx={{ height: "100%", borderRadius: 2 }} />
          ) : (
            <ListVitalsData
              vitalType={selectedVitalType}
              handleNoteForm={(note) => {
                setSelectedNote(note);
                // If the note is for ECG, update ecgData as well
                if (note && note.vitalName === "ECG") {
                  setEcgData(note);
                  // Save to localStorage
                  localStorage.setItem(SELECTED_ECG_NOTE_KEY, note.uuid || "");
                }
              }}
              setEcgData={(note) => {
                setEcgData(note);
                // Save to localStorage when ECG data is set
                if (note) {
                  localStorage.setItem(SELECTED_ECG_NOTE_KEY, note.uuid || "");
                }
              }}
              ecgData={ecgData}
              selectedEcgNoteId={localStorage.getItem(SELECTED_ECG_NOTE_KEY) || undefined}
              listContainerRef={ecgNoteListContainerRef}
            />
          )}
        </Grid>

        <Grid size={10}>
          <Grid container justifyContent={"end"}>
            <Grid>
              <Button
                startIcon={
                  <SyncIcon
                    sx={{
                      animation: isPendingSync ? `${rotateIcon} 1s linear infinite` : "none",
                    }}
                  />
                }
                variant="outlined"
                onClick={handleVitalSyncCall}
                sx={{ borderRadius: "12px", height: "30px", fontWeight: 500, mb: 1 }}
                disabled={patientProfileData?.ehrId ? false : true}
              >
                Sync From EHR
              </Button>
            </Grid>
          </Grid>
          {isLatestVitalsLoading ? (
            <Skeleton variant="rectangular" sx={{ height: "100%", borderRadius: 2 }} />
          ) : (
            <Stack
              spacing={1.5}
              sx={{
                height: "100%",
                border: "1px solid #DEE4ED",
                borderRadius: 2,
                padding: 2,
              }}
            >
              <Grid border={0} display={"flex"} alignItems={"center"} justifyContent={"space-evenly"}>
                <Grid width={"100%"}>{renderVitalsList()}</Grid>
              </Grid>

              <Grid size="grow" sx={{ maxHeight: "1000px" }}>
                <Stack spacing={1.5} sx={{ height: "100%" }}>
                  {selectedVitalType?.vitalName !== "ECG" && (
                    <Grid size="auto" container spacing={2} justifyContent="flex-end" alignItems="center">
                      <Grid container spacing={1}>
                        {currentFilter?.timeFilter === "DATE_RANGE" && (
                          <DateRangeFilter
                            handleDateRange={{
                              selectedDateRange: currentFilter.dateRange,
                              setSelectedDateRange: (newDateRange) => {
                                if (!selectedVitalType?.vitalName) return;
                                setVitalTypeFilters((prev) => ({
                                  ...prev,
                                  [selectedVitalType.vitalName]: {
                                    ...prev[selectedVitalType.vitalName],
                                    dateRange: newDateRange,
                                  },
                                }));
                              },
                            }}
                          />
                        )}
                        <Box width="150px">
                          <CustomSelect
                            noLabel
                            options={[
                              { value: "LAST_WEEK", label: "Last Week" },
                              { value: "LAST_MONTH", label: "Last Month" },
                              { value: "PAST_24_HOURS", label: "Last 24 Hours" },
                              { value: "DATE_RANGE", label: "Date Range" },
                            ]}
                            value={currentFilter?.timeFilter || "LAST_MONTH"}
                            onChange={(value) =>
                              handleTimeFilterChange(
                                value as "LAST_MONTH" | "LAST_WEEK" | "PAST_24_HOURS" | "DATE_RANGE"
                              )
                            }
                          />
                        </Box>
                      </Grid>
                      <Grid container alignItems="center" spacing={0.75}>
                        <Typography variant="medium" color="#212D30">
                          {selectedVitalType?.vitalName === "Blood Pressure"
                            ? "Systolic"
                            : selectedVitalType?.vitalName}
                        </Typography>
                        <Box sx={{ backgroundColor: "#E257FF", borderRadius: 0, width: 16, height: 16 }} />
                      </Grid>
                      {selectedVitalType?.vitalName === "Blood Pressure" && (
                        <Grid container alignItems="center" spacing={0.75}>
                          <Typography variant="medium" color="#212D30">
                            Diastolic
                          </Typography>
                          <Box sx={{ backgroundColor: "#B445CC", borderRadius: "50%", width: 16, height: 16 }} />
                        </Grid>
                      )}
                    </Grid>
                  )}

                  <Grid size="grow">
                    {selectedVitalType?.vitalName === "ECG" ? (
                      isPendingEhrArrayValue ? (
                        <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                          <Typography>Loading ...</Typography>
                        </Box>
                      ) : ecgArrayEle ? (
                        <ECGGraph
                          key={`ecg-${ecgData?.uuid}`}
                          ecgData={ecgArrayEle}
                          isPendingEhrArrayValue={isPendingEhrArrayValue}
                        />
                      ) : (
                        <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                          <Typography>No ECG data available. Please select an ECG reading.</Typography>
                        </Box>
                      )
                    ) : (
                      <VitalsChart
                        vitals={graphData}
                        vitalType={
                          selectedVitalType
                            ? {
                                ...selectedVitalType,
                                yMax:
                                  dynamicYMaxWeight !== null && selectedVitalType.vitalName === "Weight"
                                    ? dynamicYMaxWeight
                                    : selectedVitalType.yMax,
                              }
                            : undefined
                        }
                        // handleNoteForm={setSelectedNote}
                        isLoading={isGraphDataLoading}
                      />
                    )}
                  </Grid>

                  {selectedVitalType?.vitalName !== "ECG" && (
                    <Grid container size="auto" justifyContent="center" spacing={3} sx={{ padding: 1.5 }}>
                      <Grid container alignItems="center" spacing={0.75}>
                        <Typography variant="small" color="#212D30">
                          Normal Reading
                        </Typography>
                        <ReadingCircle sx={{ backgroundColor: "#02B966" }} />
                      </Grid>
                      <Grid container alignItems="center" spacing={0.75}>
                        <Typography variant="small" color="#212D30">
                          Warning Reading
                        </Typography>
                        <ReadingCircle sx={{ backgroundColor: "#F2930D" }} />
                      </Grid>
                      <Grid container alignItems="center" spacing={0.75}>
                        <Typography variant="small" color="#212D30">
                          Critical Reading
                        </Typography>
                        <ReadingCircle sx={{ backgroundColor: "#F21B0D" }} />
                      </Grid>
                    </Grid>
                  )}
                </Stack>
              </Grid>
            </Stack>
          )}
        </Grid>
      </Grid>

      {selectedNote && <NoteForm config={{ selectedNote, setSelectedNote }} />}
    </>
  );
};

const VitalTypeBox = styled(Stack)({
  backgroundColor: "#FFF",
  borderColor: "#CDD7DA",
  borderWidth: "1px",
  borderStyle: "solid",
  borderRadius: "8px",
  padding: "8px",
  // border: "1px solid black"
});

const ReadingCircle = styled(Box)({
  width: "14px",
  height: "14px",
  borderRadius: "50%",
});

export default VitalsTab;
