import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";

import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import PodcastsIcon from "@mui/icons-material/Podcasts";
import {
  Button,
  ButtonBase,
  Collapse,
  IconButton,
  Link,
  MenuItem,
  MenuList,
  Popover,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { endOfDay, format, isAfter, isBefore, isSameDay, startOfDay } from "date-fns";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomAutoComplete from "@/common-components/custom-auto-complete/custom-auto-complete";
import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomDrawer from "@/common-components/custom-drawer/custom-drawer";
import CustomLabel from "@/common-components/custom-label/custom-label";
import CustomSelect from "@/common-components/custom-select/customSelect";
import CustomSelectorSq from "@/common-components/custom-selector-sq/custom-selector-sq";
import DatePicker from "@/common-components/date-picker-field/date-picker-field";
import Paginator from "@/common-components/paginator/paginator";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import Status from "@/common-components/status/status";
import { heading, tableCellCss, typographyCss } from "@/common-components/table/common-table-widgets";

import AppointmentDetailsTabs from "@/components/provider-portal/scheduling/dialoge/appointment-details-tabs";
import CancelAppointmentDialog from "@/components/provider-portal/scheduling/dialoge/cancel-appointment-dialog";
import ReassignNurseDialog from "@/components/provider-portal/scheduling/dialoge/reassign-nurse-dialog";
import useAuthority from "@/hooks/use-authority";
import { ProviderRole } from "@/models/provider/provider-modal";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { RootState } from "@/redux/store";
import { useAppointmentControllerServiceUpdateAppointmentStatus } from "@/sdk/queries";
import { Appointment, AppointmentControllerService, Provider, ProviderControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";
import { toCamelCase } from "@/utils/toCamelCase";

import { Appointment as AppointmentType } from "../../../../../../components/provider-portal/scheduling/scheduling-list-table";

const headerArr = ["Purpose", "Nurse Name", "Type", "Date", "Time", "Duration in mins", "Status", "Action"];
type filterStatus = "ALL" | "UPCOMING" | "PAST" | "REQUESTED";

function AppointmentTab() {
  const { patientId } = useParams();
  const xTenantId = GetTenantId();
  // const [clearFilteroptions, setClearAllFilterOptions] = useState(false);
  const [viewFilter, setViewFilters] = useState(false);
  const [searchNurseString, setSearchNurseString] = useState("");
  const [selectedNurse, setSelectedNurse] = useState("");
  const [getAppointmentData, setGetAppointmentData] = useState<Appointment[]>();
  const [selectedfilterStatus, setSelectedFilterStatus] = useState("UPCOMING");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [nurseType, setNurseType] = useState("INTERNAL");
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [totalElement, setTotalElements] = useState<number>(0);
  const [totalPages, setTotalPages] = useState(0);
  const dispatch = useDispatch();
  const [openApptDetailsDrawer, setOpenApptDetailsDrawer] = useState(false);
  const [selectedAppt, setSelectedAppt] = useState<Appointment | null>({} as Appointment);
  const [actionMenu, setactionMenu] = useState(["Reschedule", "Cancel Appointment", "Mark As No Show"]);

  const [selectedAction, setSelectedAction] = useState<"Reschedule" | "Cancel Appointment" | "Mark As No Show">(
    "Reschedule"
  );
  const [anchorElNested, setAnchorElNested] = useState<Element | null>(null);
  const [markAsNoShowBy, setMarkAsNoShow] = useState<"PATIENT" | "NURSE">("PATIENT");

  const [openCancelAppointment, setOpenCancelAppointment] = useState(false);
  const [openReassignAppt, setOpenReassignAppt] = useState(false);
  const [anchorEl, setAnchorEl] = useState<Element | null>(null);
  const [sortDirection, setSortDirection] = useState("asc");
  const [sortBy, setSortBy] = useState("");
  const [sortDirectionHeader, setSortDirectionHeader] = useState("asc");
  const [sortDirectionByStatus, setSortDirectionByStatus] = useState("asc");
  const role = useAuthority();

  const [nurseOptions, setNurseOptions] = useState<{ key: string; value: string }[] | undefined>([]);

  const toggleMoreMenu = (el?: Element) => {
    setAnchorEl(el || null);
  };
  const toggleMoreMenuNested = (el?: Element) => {
    setAnchorElNested(el || null);
  };

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handlePageChange = (event: ChangeEvent<unknown> | null, page: number) => {
    event;
    setPage(page);
  };

  const { data: ProviderUuid } = useSelector((state: RootState) => state.providerProfileReducer);

  //getApi
  const {
    data,
    isSuccess,
    isLoading,
    isFetching,
    refetch: refetchAppointment,
  } = useQuery({
    queryKey: ["patient-appointments", patientId, page, size, sortBy, sortDirectionHeader, selectedfilterStatus],
    queryFn: () =>
      AppointmentControllerService.getAllAppointments({
        patientId: patientId,
        filter: selectedfilterStatus as filterStatus,
        page: page,
        size: size,
        nurseId: selectedNurse,
        startDate: startDate
          ? isSameDay(startDate, new Date())
            ? new Date().toISOString()
            : startOfDay(startDate).toISOString()
          : "",
        endDate: endDate ? endOfDay(endDate).toISOString() : "",
        xTenantId: xTenantId,
        sortBy: sortBy,
        sortDirection: sortDirectionHeader,
        providerId: role.isProvider ? ProviderUuid?.uuid : "",
      }),
  });

  useEffect(() => {
    if (isSuccess) {
      const res = (data as unknown as AxiosResponse).data as ContentObject<Appointment[]>;
      setGetAppointmentData(res.content);

      setTotalElements(res.page?.totalElements as number);
      setTotalPages(res.page?.totalPages as number);
    }
  }, [data, isSuccess]);

  useEffect(() => {
    if (startDate === "" && endDate === "") {
      refetchAppointment();
    }
  }, [startDate, endDate]);

  const handleClearFilters = () => {
    setSearchNurseString(""); // Reset search string
    setSelectedNurse("");
    setStartDate("");
    setEndDate("");
    setPage(0);

    // Refetch nurse options after clearing
    if (viewFilter) {
      refetchNurse();
    }
  };

  const fetchProvidersOrNurses = async (
    role: ProviderRole,
    nurseType: string,
    searchNurseString: string,
    xTenantId: string
  ) => {
    const xTenantIdVal =
      role === ProviderRole.PROVIDER
        ? xTenantId
        : role === ProviderRole.NURSE && nurseType === "EXTERNAL"
          ? "eamata"
          : xTenantId;

    try {
      const res = await ProviderControllerService.getAllProviders({
        page: 0,
        size: 100,
        sortBy: "modified",
        sortDirection: "desc",
        role,
        status: true,
        archive: false,
        xTenantId: xTenantIdVal,
        searchString: role === ProviderRole.NURSE ? searchNurseString : "",
      });

      const data = (res as unknown as AxiosResponse).data as ContentObject<Provider[]>;

      return data?.content.map((item) => ({
        key: item.uuid || "",
        value: `${item.firstName} ${item.lastName}`,
      }));
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body?.message || "An unexpected error occurred",
        })
      );
    }
  };

  // Usage inside the component
  const {
    data: nurseOptionsAll,
    refetch: refetchNurse,
    isSuccess: isSuccessNurse,
  } = useQuery({
    queryKey: ["nurses", nurseType, viewFilter, searchNurseString],
    queryFn: () => fetchProvidersOrNurses(ProviderRole.NURSE, nurseType, searchNurseString, xTenantId),
    enabled: viewFilter,
  });
  useEffect(() => {
    if (isSuccessNurse && nurseOptionsAll) {
      setNurseOptions(nurseOptionsAll);
    }
  }, [nurseOptionsAll, isSuccessNurse, searchNurseString]);

  const handleOnClickPurpose = (appt: Appointment) => {
    setOpenApptDetailsDrawer(true);
    setSelectedAppt(appt);
  };

  //noshow api
  const { mutateAsync } = useAppointmentControllerServiceUpdateAppointmentStatus({
    onError: (error) => {
      const message =
        (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while updating status";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: (data) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: (data?.message || `Successfully updated appointment status as Cancel.`) as string,
        })
      );
    },
  });

  const confirmMarkAsNoShow = async () => {
    if (selectedAction === "Mark As No Show") {
      if (selectedAppt?.uuid) {
        await mutateAsync({
          requestBody: { uuid: selectedAppt.uuid || "", status: "NO_SHOW", noshow: markAsNoShowBy },
        });
      }
    }
    setOpenCancelAppointment(false);
    refetchAppointment();
  };

  useEffect(() => {
    if (selectedfilterStatus === "PAST") {
      setactionMenu(["Cancel Appointment", "Mark As No Show"]);
    } else {
      setactionMenu(["Reschedule", "Cancel Appointment", "Mark As No Show"]);
    }
  }, [selectedfilterStatus, startDate]);

  const renderTableRowCell = (celldata: string | number) => {
    return (
      <TableCell sx={{ maxHeight: "10px !important" }} align="left">
        <Grid container flexDirection={"column"}>
          <Typography sx={typographyCss} variant="bodySmall">
            {celldata}
          </Typography>
        </Grid>
      </TableCell>
    );
  };

  const handleSorting = (header: string) => {
    if (header == "Date") {
      setSortBy("startDate");
      setSortDirection((prev) => (prev == "desc" ? "asc" : "desc"));
    } else if (header == "Status") {
      setSortBy("status");
      setSortDirectionByStatus((prev) => (prev == "desc" ? "asc" : "desc"));
    }
  };

  useEffect(() => {
    if (sortBy == "startDate") {
      setSortDirectionHeader(sortDirection);
    } else if (sortBy == "status") {
      setSortDirectionHeader(sortDirectionByStatus);
    }
  }, [handleSorting, sortBy, sortDirectionHeader]);

  return (
    <Grid
      height={"100%"}
      overflow={"auto"}
      sx={{
        "&::-webkit-scrollbar": {
          height: "0px",
          width: "0px",
        },
        "&::-webkit-scrollbar-button": {
          display: "none",
        },
      }}
    >
      <Grid container justifyContent={"space-between"} pb={1}>
        <Grid>
          <CustomSelectorSq
            options={["UPCOMING", "PAST"]}
            onSelect={(filterField) => {
              setPage(0);
              setSize(10);
              setSelectedFilterStatus(filterField);
              setStartDate("");
              setEndDate("");
              setSelectedNurse("");
            }}
            selectedValue={selectedfilterStatus || ""}
            widthOfBtn="100px"
          />
        </Grid>
        <Grid container alignItems={"flex-start"} gap={2} display={"flex"} flexDirection={"row"}>
          {viewFilter && (
            <>
              <Grid>
                <Button
                  fullWidth
                  loading={isFetching}
                  variant="contained"
                  // disabled={!endDate || !selectedNurse}
                  onClick={() => refetchAppointment()}
                >
                  Search
                </Button>
              </Grid>
              <Grid>
                <Button variant="outlined" onClick={handleClearFilters}>
                  Clear Filter
                </Button>
              </Grid>
            </>
          )}
          <Grid
            container
            border={"1px solid #B6C1C4"}
            p={0.9}
            borderRadius={2}
            onClick={() => {
              setViewFilters((prev) => !prev);
            }}
          >
            {viewFilter ? <ArrowUpwardIcon /> : <FilterAltOutlinedIcon />}
          </Grid>
        </Grid>
      </Grid>

      <Collapse in={viewFilter} timeout={300} sx={{ marginBottom: "10px" }}>
        <Grid sx={{ display: "flex", justifyContent: "space-between", mt: 1 }}>
          <Grid container size={3}>
            <CustomLabel isRequired label="Start Date" />
            <DatePicker
              bgWhite
              disableFuture={selectedfilterStatus === "PAST"}
              disablePast={selectedfilterStatus === "UPCOMING"}
              value={startDate}
              onDateChange={(selectedDate) => {
                setStartDate(selectedDate);
                if (endDate && isBefore(endDate, selectedDate)) {
                  setEndDate("");
                }
              }}
            />
          </Grid>
          <Grid container size={3}>
            <CustomLabel isRequired label="End Date" />
            <DatePicker
              bgWhite
              value={endDate}
              disablePast={selectedfilterStatus === "UPCOMING"}
              disableFuture={selectedfilterStatus === "PAST"}
              onDateChange={(selectedDate) => {
                setEndDate(selectedDate);
                if (startDate && isAfter(startDate, selectedDate)) {
                  setStartDate("");
                }
              }}
            />
          </Grid>
          <Grid container size={3}>
            <CustomLabel label="Select Nurse Type" />
            <CustomSelect
              placeholder="Select Nurse Type"
              items={[
                { value: "INTERNAL", label: "Provider Group Nurses" },
                { value: "EXTERNAL", label: "Eamata Nurses" },
              ]}
              onChange={(e) => setNurseType(e.target.value)}
              name="nurseType"
              value={nurseType}
            />
          </Grid>
          <Grid container size={3} flexDirection={"column"}>
            <CustomLabel label="Search By Nurse" />
            <CustomAutoComplete
              hasStartSearchIcon
              placeholder="Search Nurse"
              hideArrow
              value={selectedNurse}
              options={nurseOptions || []}
              onChange={(selectedValue) => setSelectedNurse(selectedValue)}
              onDebounceCall={(value) => setSearchNurseString(value)}
              onInputEmpty={() => setSearchNurseString("")}
            />
          </Grid>
        </Grid>
      </Collapse>

      {/* table */}
      <Grid width={"100%"}>
        <TableContainer sx={{ maxHeight: "58vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerArr.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    {header == "Date" ? (
                      <Link
                        style={{
                          color: "#667085",
                          textDecoration: "none",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSorting(header)}
                      >
                        <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                          {header}
                          <Typography>
                            {sortDirection == "asc" ? (
                              <ArrowUpwardIcon fontSize="small" />
                            ) : (
                              <ArrowDownwardIcon fontSize="small" />
                            )}
                          </Typography>
                        </Typography>
                      </Link>
                    ) : header == "Status" ? (
                      <Link
                        style={{
                          color: "#667085",
                          textDecoration: "none",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSorting(header)}
                      >
                        <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                          {header}
                          <Typography>
                            {sortDirectionByStatus == "asc" ? (
                              <ArrowUpwardIcon fontSize="small" />
                            ) : (
                              <ArrowDownwardIcon fontSize="small" />
                            )}
                          </Typography>
                        </Typography>
                      </Link>
                    ) : (
                      header
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>

            <TableBody>
              {isLoading ? (
                Array.from({ length: 5 }).map((_, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {headerArr.map((_, cellIndex) => (
                      <TableCell key={cellIndex} align="left">
                        <Skeleton variant="text" width="100%" height={40} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : getAppointmentData && getAppointmentData?.length > 0 ? (
                getAppointmentData?.map((data, rowIndex) => (
                  <TableRow key={rowIndex}>
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <ButtonBase
                          sx={{ display: "flex", justifyContent: "flex-start" }}
                          onClick={() => handleOnClickPurpose(data)}
                        >
                          <Typography color="primary" fontWeight={550} variant="bodySmall">
                            {data.purpose || "-"}
                          </Typography>

                          {data.broadcast && (
                            <Grid
                              container
                              bgcolor={"#B1000F"}
                              justifyContent={"center"}
                              width={"35px"}
                              borderRadius={"16px"}
                              ml={1}
                              pt={0.4}
                              pb={0.4}
                            >
                              <PodcastsIcon sx={{ width: "15px", height: "15px", color: theme.palette.common.white }} />
                            </Grid>
                          )}
                        </ButtonBase>
                      </Grid>
                    </TableCell>
                    {renderTableRowCell(data.nurseName || "")}
                    {renderTableRowCell(toCamelCase(data.mode || ""))}
                    {renderTableRowCell(format(new Date(data.startTime), "dd/MM/yyyy"))}
                    {renderTableRowCell(
                      `${format(new Date(data.startTime), "hh:mm a")} - ${format(new Date(data.endTime), "hh:mm a")}`
                    )}
                    {renderTableRowCell(data?.duration || "")}

                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexWrap={"nowrap"}>
                        <Status status={data?.status || ""} width="100px" />
                      </Grid>
                    </TableCell>

                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexWrap={"nowrap"}>
                        <IconButton
                          disabled={data.status == "CANCELLED" || data.status == "NO_SHOW"}
                          style={{ padding: 5 }}
                          onClick={(e) => {
                            toggleMoreMenu(e.currentTarget);
                            setSelectedAppt(data);
                          }}
                        >
                          <MoreVertIcon />
                        </IconButton>
                        <Popover
                          open={Boolean(anchorEl)}
                          anchorEl={anchorEl}
                          onClose={() => {
                            toggleMoreMenu();
                          }}
                          anchorOrigin={{
                            vertical: "bottom",
                            horizontal: "left",
                          }}
                          transformOrigin={{
                            vertical: "top",
                            horizontal: "left",
                          }}
                        >
                          <MenuList autoFocusItem={!!anchorEl} disablePadding>
                            {actionMenu
                              .filter((v) => selectedfilterStatus === "PAST" || v !== "Mark As No Show")
                              .map((v) => (
                                <MenuItem
                                  key={v}
                                  selected={v === selectedAction}
                                  onClick={(e) => {
                                    setSelectedAction(v as "Reschedule" | "Cancel Appointment" | "Mark As No Show");

                                    if (v === "Mark As No Show") {
                                      toggleMoreMenuNested(e.currentTarget);
                                    } else {
                                      setAnchorEl(null);
                                    }

                                    if (v === "Cancel Appointment") {
                                      setOpenCancelAppointment(true);
                                    }

                                    if (v === "Reschedule" || v === "Reschedule") {
                                      setOpenReassignAppt(true);
                                    }
                                  }}
                                >
                                  {v}
                                </MenuItem>
                              ))}
                          </MenuList>
                        </Popover>
                        <Popover
                          open={Boolean(anchorElNested)}
                          anchorEl={anchorElNested}
                          onClose={() => {
                            toggleMoreMenuNested();
                          }}
                          anchorOrigin={{
                            vertical: "top",
                            horizontal: "left",
                          }}
                          transformOrigin={{
                            vertical: "top",
                            horizontal: "right",
                          }}
                        >
                          <MenuList autoFocusItem={!!anchorElNested} disablePadding>
                            {["Patient", "Nurse"].map((v) => (
                              <MenuItem
                                key={v}
                                selected={v === selectedAction}
                                onClick={() => {
                                  setMarkAsNoShow(v === "Patient" ? "PATIENT" : "NURSE");
                                  setAnchorElNested(null);
                                  setAnchorEl(null);
                                  setOpenCancelAppointment(true);
                                }}
                              >
                                {v}{" "}
                              </MenuItem>
                            ))}
                          </MenuList>
                        </Popover>
                      </Grid>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={10} align="center">
                    <Typography variant="bodySmall" fontWeight={550}>
                      No records found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Grid container>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElement}
            onRecordsPerPageChange={handleRecordsPerPageChange}
            onPageChange={handlePageChange}
            defaultSize={size}
          />
        </Grid>
      </Grid>
      {selectedAppt && (
        <CustomDrawer
          anchor={"right"}
          open={openApptDetailsDrawer}
          title={"Appointment Details"}
          drawerWidth="600px"
          onClose={() => {
            setOpenApptDetailsDrawer(false);
            setSelectedAppt({} as Appointment);
          }}
        >
          <AppointmentDetailsTabs
            onClose={() => {
              setOpenApptDetailsDrawer(false);
              setSelectedAppt({} as Appointment);
            }}
            appointmentDetails={selectedAppt as AppointmentType}
          />
        </CustomDrawer>
      )}

      {selectedAppt && (
        <CustomDialog
          buttonName={["Confirm"]}
          open={selectedAction === "Cancel Appointment" && openCancelAppointment}
          title={selectedAction}
          onClose={() => setOpenCancelAppointment(false)}
        >
          <CancelAppointmentDialog
            appointmentDetails={selectedAppt}
            onClose={() => setOpenCancelAppointment(false)}
            refetch={() => {
              refetchAppointment();
              setSelectedAppt(null);
            }}
            selectedAction={selectedAction}
          />
        </CustomDialog>
      )}

      <ConfirmationPopUp
        open={selectedAction === "Mark As No Show" && openCancelAppointment}
        onClose={() => setOpenCancelAppointment(false)}
        onConfirm={() => confirmMarkAsNoShow()}
        message={`Do you really want to mark it as no show?`}
        title={`Mark As No Show By ${toCamelCase(markAsNoShowBy)}`}
        subtitle={"Are you sure you want to to mark it as no show?"}
        confirmButtonName="Confirm"
        rowData={[
          selectedAppt?.purpose || "",
          selectedAppt?.mode ? toCamelCase(selectedAppt?.mode) : "",
          selectedAppt?.patientName || "",
          selectedAppt?.startTime ? format(new Date(selectedAppt.startTime), "MM-dd-yyyy") : "-",
        ]}
        header={[{ header: "Purpose" }, { header: "Type" }, { header: "Patient" }, { header: "Date" }]}
      />

      {/* Reassign nurse dialog */}
      <CustomDialog
        buttonName={["Confirm"]}
        borderRadius="12px"
        open={openReassignAppt}
        title={selectedAction}
        onClose={() => setOpenReassignAppt(false)}
      >
        <ReassignNurseDialog
          appointmentDetails={selectedAppt as Appointment}
          action={selectedAction}
          setOpenReassignAppt={setOpenReassignAppt}
          refetch={() => refetchAppointment()}
        />
      </CustomDialog>
    </Grid>
  );
}

export default AppointmentTab;
