import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";

import { Skeleton } from "@mui/material";
import { Box } from "@mui/system";

import {
  CategoryScale,
  Legend,
  LineController,
  LineElement,
  LinearScale,
  PointElement,
  TimeScale,
  Tooltip,
} from "chart.js";
import { Chart } from "chart.js";
import annotationPlugin from "chartjs-plugin-annotation";
import zoomPlugin from "chartjs-plugin-zoom";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import { setSnackbarOn } from "@/redux/actions/snackbar-action";

Chart.register(
  CategoryScale,
  LinearScale,
  LineController,
  LineElement,
  PointElement,
  TimeScale,
  Tooltip,
  Legend,
  zoomPlugin,
  annotationPlugin
);
interface EcgGraph {
  ecgData: string | undefined;
  isPendingEhrArrayValue: boolean;
}

const ECGGraph = (props: EcgGraph) => {
  const { ecgData, isPendingEhrArrayValue } = props;
  const chartRef = useRef<HTMLCanvasElement | null>(null);
  const chartInstanceRef = useRef<Chart | null>(null);
  const dispatch = useDispatch();
  // const [zoomLevel, setZoomLevel] = useState(1);
  // zoomLevel;
  const [dataValues, setDataValues] = useState<number[]>([]);
  const [labels, setLabels] = useState<string[]>([]);
  const [minY, setMinY] = useState<number>(0);
  const [maxY, setMaxY] = useState<number>(0);

  useEffect(() => {
    if (ecgData && ecgData?.length > 1) {
      try {
        const parsedData = JSON.parse(ecgData);
        if (Array.isArray(parsedData)) {
          setDataValues(parsedData);
          setMinY(Math.min(...parsedData));
          setMaxY(Math.max(...parsedData));
          // Maped all points within 3 seconds:=>
          const totalTime = 30; // seconds
          const interval = totalTime / parsedData.length;
          const newLabels = parsedData.map((_, index) => (index * interval).toFixed(0) + "s");
          setLabels(newLabels);
        }
      } catch (error) {
        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.ERROR,
            message: "Invalid ECG data format",
          })
        );
      }
    }
  }, [ecgData]);

  const createChart = () => {
    if (chartInstanceRef.current) {
      chartInstanceRef.current.destroy();
    }

    if (!chartRef.current) return;
    const ctx = chartRef.current.getContext("2d");

    const initialViewSeconds = 30;
    // const initialDataPoints = Math.floor((initialViewSeconds / totalDuration) * labels.length);

    const gridBackgroundPlugin = {
      id: "graphPaperBackground",
      beforeDraw: (chart: Chart) => {
        const { ctx, chartArea } = chart;
        const { left, right, top, bottom } = chartArea;
        const spacingX = 5;
        const spacingY = 5;

        ctx.save();
        ctx.strokeStyle = "#ddd";
        ctx.lineWidth = 0.5;

        for (let x = left; x <= right; x += spacingX) {
          ctx.beginPath();
          ctx.moveTo(x, top);
          ctx.lineTo(x, bottom);
          ctx.stroke();
        }

        for (let y = top; y <= bottom; y += spacingY) {
          ctx.beginPath();
          ctx.moveTo(left, y);
          ctx.lineTo(right, y);
          ctx.stroke();
        }
        ctx.restore();
      },
    };

    chartInstanceRef.current = new Chart(ctx!, {
      type: "line",
      data: {
        labels: labels,
        datasets: [
          {
            label: "ECG Signal",
            data: dataValues || 0,
            borderColor: "red",
            backgroundColor: "red",
            pointRadius: 0,
            borderWidth: 1,
            tension: 0,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        elements: {
          line: {
            tension: 0,
          },
        },
        plugins: {
          legend: { display: false },
          zoom: {
            pan: {
              enabled: true,
              mode: "x",
            },
            // zoom: {
            //     wheel: {
            //         enabled: true,
            //     },
            //     pinch: {
            //         enabled: true,
            //     },
            //     mode: "x",
            //     onZoom: ({ chart }) => {
            //         const xScale = chart.scales.x;
            //         const zoomRatio = (xScale.max - xScale.min) / labels.length;
            //         setZoomLevel(1 / zoomRatio);
            //     },
            // },
          },
        },
        scales: {
          x: {
            title: { display: true, text: "Time (seconds)" },
            grid: { drawTicks: true },
            ticks: {
              autoSkip: true,
              maxTicksLimit: 3,
            },
            min: 0,
            max: labels.length * (3 / initialViewSeconds),
          },
          y: {
            title: { display: true, text: "mV" },
            display: false,
            grid: { drawTicks: false },
            ticks: {
              maxTicksLimit: 10,
            },
            min: minY,
            max: maxY,
          },
        },
      },
      plugins: [gridBackgroundPlugin],
    });
  };

  useEffect(() => {
    if (dataValues) {
      createChart();
    }
  }, [dataValues]);

  return (
    <>
      {isPendingEhrArrayValue ? (
        <Skeleton width={"800"} height={"100%"} />
      ) : (
        <Box mt={6} sx={{ width: "100%", height: 450, display: "flex", flexDirection: "column" }}>
          <canvas ref={chartRef} width={800} height={400} />
        </Box>
      )}
    </>
  );
};

export default ECGGraph;
