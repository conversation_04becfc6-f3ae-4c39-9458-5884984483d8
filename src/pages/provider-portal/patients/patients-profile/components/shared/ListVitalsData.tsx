import { useCallback, useEffect, useRef, useState } from "react";
import React from "react";
import { useParams } from "react-router-dom";

import { Add, EditOutlined, VisibilityOutlined } from "@mui/icons-material";
import { Box, Chip, CircularProgress, Grid2 as Grid, Skeleton, Stack, Typography } from "@mui/material";

import { useInfiniteQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { capitalize, groupBy } from "lodash";

import EmergencyIcon from "@/assets/image_svg/icons/emergency_home.svg";
import ChipButton from "@/components/ui/Atom/ChipButton";
import { CustomDatepicker } from "@/components/ui/Form/Datepicker";
import { ContentObject } from "@/models/response/response-content-entity";
import { PatientVital, PatientVitalControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { formatDateWithToday, formatTime } from "@/utils/format/date";

import { SelectedNoteState, VitalType } from "../VitalsTab";

interface ListVitalsDataProps {
  vitalType?: VitalType;
  handleNoteForm: (selectedNote: SelectedNoteState | null) => void;
  setEcgData?: (list: PatientVital) => void;
  ecgData?: PatientVital | undefined;
  selectedEcgNoteId?: string;
  listContainerRef?: React.RefObject<HTMLDivElement>;
}

const ListVitalsData = ({
  vitalType,
  handleNoteForm,
  setEcgData,
  ecgData,
  selectedEcgNoteId,
  listContainerRef,
}: ListVitalsDataProps) => {
  const { patientId } = useParams();

  const observerRef = useRef<IntersectionObserver>();
  const containerRef = useRef<HTMLDivElement>(null);
  const vitalItemRefs = useRef<{ [uuid: string]: HTMLDivElement | null }>({});

  const [scrollTop, setScrollTop] = useState(0);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [mergedVitalsData, setMergedVitalsData] = useState<PatientVital[]>([]);

  const handleScroll = useCallback(() => {
    if (containerRef.current) {
      setScrollTop(containerRef.current.scrollTop);
    }
  }, []);

  useEffect(() => {
    const element = containerRef.current;
    element?.addEventListener("scroll", handleScroll);

    return () => {
      element?.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll]);

  useEffect(() => {
    containerRef.current?.scrollTo({ top: 0, behavior: "smooth" });
  }, [vitalType]);

  const {
    data: vitalsData,
    isPending: loadingVitalsData,
    fetchNextPage: loadMoreVitalsData,
    hasNextPage: hasMoreVitalsData,
    isFetchingNextPage: loadingMoreVitalsData,
  } = useInfiniteQuery({
    queryKey: ["list-of-vitals", patientId, vitalType?.vitalName, selectedDate],
    initialPageParam: 0,
    queryFn: async ({ pageParam = 0 }) => {
      const response = (await PatientVitalControllerService.getPatientVitals1({
        patientUuid: patientId || "",
        xTenantId: GetTenantId(),
        page: pageParam,
        vitalName: vitalType?.vitalName,
        size: 10,
        timeFilter: undefined,
        startDate: selectedDate ? new Date(selectedDate.setHours(0, 0, 0, 0)).toISOString() : undefined,
        endDate: selectedDate ? new Date(selectedDate.setHours(23, 59, 59, 999)).toISOString() : undefined,
      })) as unknown as AxiosResponse<ContentObject<PatientVital[]>>;

      return response.data;
    },
    getNextPageParam: (lastPage, _allPages, lastPageParam) => {
      const hasMore = Number(lastPage?.page?.number) + 1 < Number(lastPage?.page?.totalPages);

      return hasMore ? lastPageParam + 1 : undefined;
    },
    enabled: !!patientId && !!vitalType?.vitalName,
  });

  useEffect(() => {
    if (vitalsData?.pages) {
      const allVitals = vitalsData.pages.reduce<PatientVital[]>((acc, page) => {
        return [...acc, ...(page.content || [])];
      }, []);
      setMergedVitalsData(allVitals);
    }
  }, [vitalsData?.pages]);

  const loadMoreRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (loadingMoreVitalsData) return;

      if (observerRef.current) observerRef.current.disconnect();

      observerRef.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMoreVitalsData) {
          loadMoreVitalsData();
        }
      });

      if (node) observerRef.current.observe(node);
    },
    [loadingMoreVitalsData, hasMoreVitalsData, loadMoreVitalsData]
  );

  const handleECGData = (listData: PatientVital) => {
    if (vitalType?.vitalName === "ECG") {
      setEcgData && setEcgData(listData);
    }
  };

  useEffect(() => {
    if (
      vitalType?.vitalName === "ECG" &&
      mergedVitalsData.length > 0 &&
      selectedEcgNoteId &&
      vitalItemRefs.current[selectedEcgNoteId] &&
      containerRef.current
    ) {
      setTimeout(() => {
        const element = vitalItemRefs.current[selectedEcgNoteId];
        if (element && containerRef.current) {
          const container = containerRef.current;
          const containerRect = container.getBoundingClientRect();
          const elementRect = element.getBoundingClientRect();

          if (elementRect.top < containerRect.top || elementRect.bottom > containerRect.bottom) {
            element.scrollIntoView({ behavior: "smooth", block: "nearest" });
          }
        }
      }, 100);
    }
  }, [mergedVitalsData, selectedEcgNoteId, vitalType?.vitalName]);

  useEffect(() => {
    if (listContainerRef && listContainerRef.current !== containerRef.current) {
      const currentContainerRef = containerRef.current;
      if (currentContainerRef) {
        const parentRefCallback = listContainerRef as React.MutableRefObject<HTMLDivElement | null>;
        parentRefCallback.current = currentContainerRef;
      }
    }
  }, [listContainerRef]);

  return (
    <Box
      sx={{
        height: "100%",
        position: "relative",
        "&::before, &::after": {
          content: '""',
          position: "absolute",
          left: 0,
          right: 0,
          height: "4px",
          zIndex: 1,
          pointerEvents: "none",
        },
        "&::before": {
          top: 0,
          background: "linear-gradient(to bottom, #FFF, transparent)",
          opacity: scrollTop > 0 ? 1 : 0,
        },
        "&::after": {
          bottom: 0,
          background: "linear-gradient(to top, #FFF, transparent)",
        },
      }}
    >
      <Box sx={{ mb: 2, backgroundColor: "white", pb: 1, pt: 1 }}>
        <CustomDatepicker
          value={selectedDate}
          format="dd/MM/yyyy"
          onChange={(value) => {
            if (value && value.toString() === "Invalid Date") {
              return;
            }

            setSelectedDate(value);
          }}
          slotProps={{
            actionBar: {
              actions: ["clear"],
            },
            field: {
              clearable: true,
            },
            textField: {
              placeholder: "Filter by Date",
            },
          }}
        />
      </Box>

      <Box
        ref={containerRef}
        sx={{
          height: "calc(100% - 70px)",
          overflow: "auto",
          "&::-webkit-scrollbar": {
            display: "none",
          },
          msOverflowStyle: "none",
          scrollbarWidth: "none",
        }}
      >
        {loadingVitalsData ? (
          <Stack direction="column" spacing={1.5}>
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton variant="rectangular" height={100} sx={{ borderRadius: "8px" }} key={index} />
            ))}
          </Stack>
        ) : mergedVitalsData.length > 0 ? (
          <>
            <Stack spacing={3}>
              {mergedVitalsData.length > 0 &&
                Object.entries(
                  groupBy(mergedVitalsData, (vital) =>
                    new Date(vital.recordedDate).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })
                  )
                ).map(([date, vitals]) => (
                  <Stack key={date} spacing={1.5}>
                    <Typography variant="medium" color="#515C5F">
                      {formatDateWithToday(date)}
                    </Typography>
                    <Stack direction="column" spacing={1.5}>
                      {vitals.map((vital) => {
                        const isResolved =
                          (vital?.severity === "CRITICAL" ||
                            vital?.severity === "OVER_WEIGHT" ||
                            vital?.severity === "UNDER_WEIGHT") &&
                          vital?.alertStatus === "NOT_RESOLVED";
                        const isSelected = ecgData?.uuid === vital.uuid;
                        return (
                          <Grid
                            key={vital.uuid}
                            ref={(el) => {
                              vitalItemRefs.current[vital.uuid || ""] = el;
                            }}
                            border={1}
                            sx={{
                              border: isResolved ? 2 : isSelected ? 2 : 0,
                              borderColor: isResolved ? "#B1000F" : isSelected ? "#006D8F" : "",
                              padding: 1.5,
                              borderRadius: "8px",
                              cursor: "pointer",
                              backgroundColor:
                                ecgData?.uuid === vital.uuid ? "#B2EBF2" : isResolved ? "#FFF2F3" : "#FAF3F1",
                              transition: "backgroundColor 0.3s ease-in-out",
                            }}
                            onClick={() => {
                              handleECGData(vital);
                            }}
                          >
                            <Grid container direction="column" spacing={0.5} alignItems="flex-start">
                              <Grid
                                container
                                sx={{
                                  width: "100%",
                                  justifyContent: "space-between",
                                }}
                              >
                                <Chip
                                  label={capitalize(vital.integrationType)}
                                  size="small"
                                  sx={{
                                    backgroundColor: "#EFF8FF",
                                    color: "#004AB1",
                                    fontWeight: "500",
                                  }}
                                />
                                {(vital?.severity === "CRITICAL" ||
                                  vital?.severity === "OVER_WEIGHT" ||
                                  vital?.severity === "UNDER_WEIGHT") &&
                                  vital?.alertStatus === "NOT_RESOLVED" &&
                                  vital.note?.goalProgress !== "RESOLVED" && (
                                    <Grid container>
                                      <img src={EmergencyIcon} style={{ height: "20px", width: "20px" }} />
                                      <Typography sx={{ fontFamily: "Roboto", color: "#B1000F", fontWeight: "500" }}>
                                        Alert
                                      </Typography>
                                    </Grid>
                                  )}
                              </Grid>
                              <Grid container justifyContent="space-between" alignItems="center" sx={{ width: "100%" }}>
                                <Grid pb={1}>
                                  <Typography variant="xl" mr={0.5}>
                                    {vital.value1 ?? 0}
                                    {vital.vitalName === "Blood Pressure" && `/${vital.value2 ?? 0}`}
                                  </Typography>
                                  <Typography variant="medium" fontWeight="400">
                                    {vital.unit || vitalType?.unit}
                                  </Typography>
                                </Grid>
                                <Typography variant="description" color="#515C5F">
                                  {formatTime(vital.recordedDate)}
                                </Typography>
                              </Grid>
                            </Grid>
                            {vital.note ? (
                              <Grid container spacing={1} justifyContent="space-between" alignItems="flex-start">
                                <Grid size={{ xs: 12, md: 6 }}>
                                  <ChipButton
                                    label="View Note"
                                    icon={<VisibilityOutlined sx={{ fontSize: "18px" }} />}
                                    sx={{
                                      width: "100%",
                                      fontWeight: 500,
                                      fontSize: "14px",
                                      padding: "6px",
                                      whiteSpace: "nowrap",
                                    }}
                                    onClick={() => handleNoteForm({ ...vital, readOnly: true })}
                                  />
                                </Grid>
                                <Grid size={{ xs: 12, md: 6 }}>
                                  <ChipButton
                                    label="Edit Note"
                                    icon={<EditOutlined sx={{ fontSize: "18px" }} />}
                                    sx={{
                                      width: "100%",
                                      fontWeight: 500,
                                      fontSize: "14px",
                                      padding: "6px",
                                      whiteSpace: "nowrap",
                                    }}
                                    onClick={() => handleNoteForm(vital)}
                                  />
                                </Grid>
                              </Grid>
                            ) : (
                              <ChipButton
                                label={"Add Note"}
                                icon={<Add sx={{ fontSize: "18px" }} />}
                                sx={{ width: "100%", fontWeight: 500, fontSize: "14px", padding: "6px" }}
                                onClick={() => handleNoteForm(vital)}
                              />
                            )}
                          </Grid>
                        );
                      })}
                    </Stack>
                  </Stack>
                ))}
            </Stack>
            <Box ref={loadMoreRef} sx={{ textAlign: "center", py: 2 }}>
              {loadingMoreVitalsData && <CircularProgress size={24} />}
            </Box>
          </>
        ) : (
          <Stack direction="column" spacing={1.5}>
            <Typography variant="medium" color="#515C5F">
              No data found
            </Typography>
          </Stack>
        )}
      </Box>
    </Box>
  );
};

export default ListVitalsData;
