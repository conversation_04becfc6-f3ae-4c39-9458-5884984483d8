import { Grid2 as Grid, Typography } from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";

import { enIN } from "date-fns/locale";

interface DateRangeFilterProps {
  handleDateRange: {
    selectedDateRange: { startDate: Date; endDate: Date };
    setSelectedDateRange: (dateRange: { startDate: Date; endDate: Date }) => void;
  };
}

export const DateRangeFilter = ({ handleDateRange }: DateRangeFilterProps) => {
  const textFieldStyle = {
    sx: {
      "& .MuiInputBase-root": {
        width: "180px",
        height: "43px",
        background: "#FFFFFF",
        border: "1px solid #E8EBEC",
        borderRadius: "8px",
      },
      "& .MuiInputBase-input": {
        padding: "0 12px",
        fontSize: "14px",
        color: "#515C5F",
      },
      "& .MuiInputAdornment-root button": {
        color: "#515C5F ",
      },
      "& .MuiOutlinedInput-notchedOutline": {
        border: "none",
      },
    },
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enIN}>
      <Grid container alignItems="center" spacing={1}>
        <DatePicker
          format="dd/MM/yyyy"
          value={handleDateRange.selectedDateRange.startDate}
          maxDate={handleDateRange.selectedDateRange.endDate}
          onChange={(value) => {
            if (value) {
              handleDateRange.setSelectedDateRange({
                startDate: value,
                endDate: handleDateRange.selectedDateRange.endDate,
              });
            }
          }}
          slotProps={{
            textField: textFieldStyle,
            field: {
              readOnly: true,
            },
          }}
        />
        <Typography variant="medium" color="#515C5F">
          —
        </Typography>
        <DatePicker
          format="dd/MM/yyyy"
          value={handleDateRange.selectedDateRange.endDate}
          minDate={handleDateRange.selectedDateRange.startDate}
          onChange={(value) => {
            if (value) {
              handleDateRange.setSelectedDateRange({
                startDate: handleDateRange.selectedDateRange.startDate,
                endDate: value,
              });
            }
          }}
          slotProps={{
            textField: textFieldStyle,
            field: {
              readOnly: true,
            },
          }}
        />
      </Grid>
    </LocalizationProvider>
  );
};
