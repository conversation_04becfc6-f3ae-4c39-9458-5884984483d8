import { useEffect, useState } from "react";
import Chart from "react-apexcharts";

import { Box } from "@mui/material";

import type { ApexOptions } from "apexcharts";
import { compact } from "lodash";

import { PatientVital } from "@/sdk/requests";
import { formatDate } from "@/utils/format/date";

import { VitalType } from "../VitalsTab";

interface VitalsChartProps extends ApexOptions {
  vitals?: PatientVital[];
  vitalType?: VitalType;
  // handleNoteForm: (vital: PatientVital) => void;
  isLoading?: boolean;
}

const VitalsChart = ({ vitals, vitalType, isLoading }: VitalsChartProps) => {
  const [series, setSeries] = useState<ApexOptions["series"]>([]);

  useEffect(() => {
    if (vitalType && vitals) {
      const isBloodPressure = vitalType?.vitalName === "Blood Pressure";

      const seriesData = compact([
        {
          name: isBloodPressure ? "Systolic" : vitalType?.vitalName,
          data: vitals.map((vital) => {
            return {
              x: new Date(vital.recordedDate),
              y: vital.value1,
              fillColor: getMarkerColor(vital?.severity || ""),
            };
          }),
          color: "#E257FF",
        },
        isBloodPressure && {
          name: "Diastolic",
          data: vitals.map((vital) => {
            return {
              x: new Date(vital.recordedDate),
              y: vital.value2,
              fillColor: getMarkerColor(vital?.severity || ""),
            };
          }),
          color: "#B445CC",
        },
      ]);

      setSeries(seriesData);
    }
  }, [vitalType, vitals]);

  const getMarkerColor = (severity: string) => {
    if (severity === "CRITICAL" || severity === "OVER_WEIGHT" || severity === "UNDER_WEIGHT") return "#F21B0D"; // Critical
    if (severity === "MODERATE") return "#F2930D"; // Moderate
    if (severity === "NORMAL") return "#02B966"; // Normal
    return "#9CA3AF"; // Gray
  };

  // calculation of dynamic max for chart yAxis
  let dynamicMax = 200;
  if (vitalType?.vitalName === "Blood Pressure" && Array.isArray(vitals) && vitals.length > 0) {
    let maxBP = 0;
    for (const v of vitals) {
      if (typeof v.value1 === "number" && v.value1 > maxBP) maxBP = v.value1;
      if (typeof v.value2 === "number" && v.value2 > maxBP) maxBP = v.value2;
    }
    dynamicMax = Math.max(maxBP, 200);
    dynamicMax = Math.ceil(dynamicMax * 1.05);
  }

  const options: ApexOptions = {
    chart: {
      id: "vitals-chart",
      toolbar: {
        show: true,
        autoSelected: "pan",
      },
      zoom: {
        type: "x",
        enabled: true,
        autoScaleYaxis: true,
      },
      events: {
        // TODO: Add marker click event
        // markerClick: (_: unknown, _chart: unknown, { dataPointIndex }: any) => {
        //   handleNoteForm(vitals?.[dataPointIndex] as PatientVital);
        // },
      },
    },
    legend: {
      show: false,
    },
    stroke: {
      curve: "straight",
      width: 2,
    },
    grid: {
      borderColor: "#DEE4ED",
    },
    markers: {
      size: 6,
      hover: {
        size: 8,
      },
      strokeWidth: 1,
      strokeColors: "#712B7F",
      shape: ["square", "circle"],
    },
    tooltip: {
      shared: false,
      intersect: true,
      x: {
        show: true,
        formatter: (value: number) => {
          return formatDate(value);
        },
      },
    },
    xaxis: {
      type: "datetime",
      axisTicks: {
        show: true,
        color: "#212D30",
      },
      axisBorder: {
        show: true,
        color: "#212D30",
      },
      tooltip: {
        enabled: false,
      },
      labels: {
        datetimeUTC: false,
      },
    },
    yaxis: {
      axisTicks: {
        show: true,
        color: "#212D30",
      },
      axisBorder: {
        show: true,
        color: "#212D30",
      },
      labels: {
        style: {
          colors: "#212D30",
        },
      },
      title: {
        text: vitalType?.unit,
        style: {
          color: "#212D30",
        },
      },
      min: Number(vitalType?.yMin),
      max: dynamicMax,
      tickAmount: 8,
    },
  };

  return (
    <Box sx={{ width: "100%", height: "100%", paddingRight: 1 }}>
      <Chart
        type="line"
        options={{
          ...options,
          noData: Number(vitals?.length) > 0 ? { text: "" } : { text: isLoading ? "Loading..." : "No readings data" },
        }}
        series={series}
        height="100%"
        style={{ opacity: isLoading ? 0.5 : 1, cursor: "default" }}
      />
    </Box>
  );
};

export default VitalsChart;
