import React from "react";

import CloseIcon from "@mui/icons-material/Close";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import { Box, Dialog, DialogContent, DialogTitle, Divider, IconButton, Typography } from "@mui/material";

interface MiniChatDialogProps {
  /**
   * Controls whether the dialog is open or closed.
   */
  open: boolean;
  /**
   * Function to handle closing the dialog.
   */
  onClose: () => void;
  /**
   * Function to handle expanding to the FullChatDialog.
   */
  onExpand: () => void;
  /**
   * The content to be displayed within the dialog, typically the RecentChatsList.
   */
  children: React.ReactNode;
}

/**
 * A small chat dialog that appears at the bottom-right of the screen.
 * It displays a list of recent chats and provides actions to expand or close the view.
 */
const MiniChatDialog: React.FC<MiniChatDialogProps> = ({ open, onClose, onExpand, children }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="mini-chat-dialog-title"
      PaperProps={{
        sx: {
          position: "fixed",
          bottom: { xs: 0, sm: 32 },
          right: { xs: 0, sm: 32 },
          m: 0,
          width: { xs: "100%", sm: 380 },
          height: { xs: "100%", sm: "70vh" },
          maxHeight: 600,
          borderRadius: { xs: 0, sm: 2 },
        },
      }}
    >
      <DialogTitle
        id="mini-chat-dialog-title"
        sx={{ p: 2, backgroundColor: "primary.main", color: "primary.contrastText" }}
      >
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h6" component="div">
            Recent Chats
          </Typography>
          <Box>
            <IconButton size="small" onClick={onExpand} sx={{ color: "primary.contrastText" }}>
              <FullscreenIcon />
            </IconButton>
            <IconButton size="small" onClick={onClose} sx={{ color: "primary.contrastText", ml: 1 }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>
      <Divider />
      <DialogContent sx={{ p: 0, overflowY: "auto" }}>{children}</DialogContent>
    </Dialog>
  );
};

export default MiniChatDialog;
