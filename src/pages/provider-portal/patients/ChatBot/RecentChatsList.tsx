import React from "react";

import { Box, List, Typography } from "@mui/material";

import ChatListItem from "./ChatListItem";

// Mock data for demonstration purposes
const mockChats = [
  {
    id: 1,
    name: "<PERSON>",
    lastMessage: "Okay, I will be there.",
    timestamp: "10:45 AM",
    unreadCount: 2,
    avatarUrl: "/path/to/avatar1.jpg",
  },
  {
    id: 2,
    name: "<PERSON>",
    lastMessage: "Thank you so much, <PERSON>!",
    timestamp: "Yesterday",
    unreadCount: 0,
    avatarUrl: "/path/to/avatar2.jpg",
  },
  {
    id: 3,
    name: "AI Assistant",
    lastMessage: "Summary: Patient reported mild...",
    timestamp: "9:00 AM",
    unreadCount: 1,
    avatarUrl: "",
  },
];

interface RecentChatsListProps {
  /**
   * Function to handle selecting a chat.
   * It receives the chat ID as an argument.
   */
  chats: Array<{
    id: number;
    name: string;
    lastMessage: string;
    timestamp: string;
    unreadCount: number;
    avatarUrl?: string;
  }>;
  onChatSelect: (chatId: number) => void;
  onChatSelect: (chatId: number) => void;
}

/**
 * Displays a list of recent chats using the ChatListItem component.
 */
const RecentChatsList: React.FC<RecentChatsListProps> = ({ chats, onChatSelect }) => {
  if (!mockChats || mockChats.length === 0) {
    return (
      <Box sx={{ p: 3, textAlign: "center" }}>
        <Typography color="text.secondary">No recent chats.</Typography>
      </Box>
    );
  }

  return (
    <List disablePadding>
      {mockChats.map((chat) => (
        <ChatListItem
          key={chat.id}
          name={chat.name}
          lastMessage={chat.lastMessage}
          timestamp={chat.timestamp}
          unreadCount={chat.unreadCount}
          avatarUrl={chat.avatarUrl}
          onClick={() => onChatSelect(chat.id)}
        />
      ))}
    </List>
  );
};

export default RecentChatsList;
