import React, { useState } from "react";

import CloseIcon from "@mui/icons-material/Close";
import { AppBar, Avatar, Box, Dialog, Divider, IconButton, Slide, Toolbar, Typography } from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";

import CustomInput from "@/common-components/custom-input/custom-input";

import { useChat } from "@/hooks/useChat";
import type { ChatSession } from "@/services/chat/chatService";

// Import your other components
import ChatWindow from "./ChatWindow";
import MessageInputBox from "./MessageInputBox";
import RecentChatsList from "./RecentChatsList";
import { FullscreenExit } from "@mui/icons-material";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & { children: React.ReactElement },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

interface FullChatDialogProps {
  /**
   * Controls whether the dialog is open or closed.
   */
  open: boolean;
  /**
   * Function to handle closing the dialog.
   */
  onClose: () => void;
  /**
   * Currently selected chat session
   */
  onShrink: () => void;
  selectedChat?: ChatSession | null;
  /**
   * Function to handle chat selection
   */
  onChatSelect?: (chat: ChatSession) => void;
}

/**
 * A full-screen dialog that provides an immersive chat experience.
 * It shows chat list on the left and selected chat on the right.
 */
const FullChatDialog: React.FC<FullChatDialogProps> = ({ open, onShrink, onClose, selectedChat, onChatSelect }) => {
  const { messages, sendMessage, recentChats } = useChat();
  const [searchValue, setSearchValue] = useState("");

  const handleSendMessage = (message: string) => {
    if (selectedChat) {
      sendMessage(message);
    }
  };

  const handleChatSelect = (chatId: number) => {
    const chat = recentChats.find((c) => c.id === chatId);
    if (chat && onChatSelect) {
      onChatSelect(chat);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      title="Recent chats"
      PaperProps={{
        sx: {
          width: "1184px",
          height: "836px",
          maxWidth: "1184px", // Prevent MUI from overriding
        },
      }}
    >
      <AppBar sx={{ position: "relative", bgcolor: "white", color: "black", boxShadow: "none" }}>
        <Toolbar>
          <Typography sx={{ flex: 1 }} variant="h6" component="div">
            Recent chats
          </Typography>
          <IconButton edge="end" color="inherit" onClick={onShrink} aria-label="shrink">
            <FullscreenExit />
          </IconButton>
          <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
            <CloseIcon />
          </IconButton>
        </Toolbar>
      </AppBar>
      <Divider />
      <Box
        sx={{
          display: "flex",
          height: "100%",
          overflow: "hidden",
        }}
      >
        {/* Left side - Chat List */}
        <Box
          sx={{
            width: 350,
            borderRight: 1,
            borderColor: "divider",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box sx={{ p: 2, borderBottom: 1, borderColor: "divider" }}>
            <CustomInput
              placeholder="Search patient"
              name="search"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
            />
          </Box>
          <Box sx={{ flex: 1, overflow: "auto" }}>
            <RecentChatsList chats={recentChats} onChatSelect={handleChatSelect} />
          </Box>
        </Box>

        {/* Right side - Chat Window */}
        <Box
          sx={{
            flex: 1,
            display: "flex",
            flexDirection: "column",
            overflow: "hidden",
          }}
        >
          {selectedChat ? (
            <>
              {/* Chat Header */}
              <Box sx={{ p: 2, borderBottom: 1, borderColor: "divider" }}>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Avatar src={selectedChat.avatarUrl} sx={{ mr: 2 }}>
                    {selectedChat.name.charAt(0)}
                  </Avatar>
                  <Typography variant="h6">{selectedChat.name}</Typography>
                </Box>
              </Box>

              {/* Chat Messages */}
              <Box sx={{ flex: 1, overflow: "hidden" }}>
                <ChatWindow messages={messages} />
              </Box>

              {/* Message Input */}
              <MessageInputBox onSendMessage={handleSendMessage} />
            </>
          ) : (
            /* No Chat Selected */
            <Box
              sx={{
                flex: 1,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography variant="h6" color="text.secondary">
                No Chat Selected
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Dialog>
  );
};

export default FullChatDialog;
