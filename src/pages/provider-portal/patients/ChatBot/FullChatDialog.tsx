import React from "react";

import CloseIcon from "@mui/icons-material/Close";
import { AppBar, Avatar, Box, Dialog, IconButton, Slide, Toolbar, Typography } from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";

import { useChat } from "@/hooks/useChat";

// Import your other components
import ChatWindow from "./ChatWindow";
import MessageInputBox from "./MessageInputBox";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & { children: React.ReactElement },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

interface FullChatDialogProps {
  /**
   * Controls whether the dialog is open or closed.
   */
  open: boolean;
  /**
   * Function to handle closing the dialog.
   */
  onClose: () => void;
  /**
   * Information about the patient currently being chatted with.
   */
  patient: {
    name: string;
    avatarUrl?: string;
  };
}

/**
 * A full-screen dialog that provides an immersive chat experience.
 * It includes a header with patient info, a scrollable message window, and an input box.
 */
const FullChatDialog: React.FC<FullChatDialogProps> = ({ open, onClose, patient }) => {
  const { messages, sendMessage } = useChat();

  const handleSendMessage = (message: string) => {
    sendMessage(message);
  };

  return (
    <Dialog fullScreen open={open} onClose={onClose} TransitionComponent={Transition}>
      <AppBar sx={{ position: "relative" }}>
        <Toolbar>
          <Avatar src={patient.avatarUrl} sx={{ mr: 2 }}>
            {patient.name.charAt(0)}
          </Avatar>
          <Typography sx={{ flex: 1 }} variant="h6" component="div">
            {patient.name}
          </Typography>
          <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
            <CloseIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          height: "100%",
          overflow: "hidden", // Parent container handles overflow
        }}
      >
        {/* The ChatWindow will be scrollable */}
        <ChatWindow messages={messages} />

        {/* The MessageInputBox will be fixed at the bottom */}
        <MessageInputBox onSendMessage={handleSendMessage} />
      </Box>
    </Dialog>
  );
};

export default FullChatDialog;
