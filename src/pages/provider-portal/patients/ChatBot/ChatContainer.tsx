import React, { useState } from "react";

import { <PERSON><PERSON>, Snac<PERSON>bar } from "@mui/material";

import { useChat } from "@/hooks/useChat";
import type { ChatSession } from "@/services/chat/chatService";

import FloatingChatButton from "./FloatingChatButton";
import FullChatDialog from "./FullChatDialog";
import MiniChatDialog from "./MiniChatDialog";
import RecentChatsList from "./RecentChatsList";

/**
 * Main container component that manages the entire chat experience.
 * Handles state for mini/full dialogs and coordinates between components.
 */
const ChatContainer: React.FC = () => {
  const [miniChatOpen, setMiniChatOpen] = useState(false);
  const [fullChatOpen, setFullChatOpen] = useState(false);
  const [selectedChatInMini, setSelectedChatInMini] = useState<ChatSession | null>(null);
  const [selectedChatInFull, setSelectedChatInFull] = useState<ChatSession | null>(null);

  const { recentChats, selectChat, error, clearError } = useChat();

  const handleFloatingButtonClick = () => {
    setMiniChatOpen(true);
  };

  const handleMiniChatClose = () => {
    setMiniChatOpen(false);
  };

  const handleMiniChatExpand = () => {
    setMiniChatOpen(false);
    setFullChatOpen(true);
    // Keep the selected chat from mini when expanding to full
    setSelectedChatInFull(selectedChatInMini);
  };

  const handleFullChatExpand = () => {
    setMiniChatOpen(true);
    setFullChatOpen();
    // Keep the selected chat from mini when expanding to full
    setSelectedChatInFull(selectedChatInMini);
  };

  const handleFullChatClose = () => {
    setFullChatOpen(false);
    setSelectedChatInFull(null);
  };

  const handleMiniChatSelect = async (chatId: number) => {
    const selectedChat = recentChats.find((chat) => chat.id === chatId);
    if (selectedChat) {
      // Select the chat and load messages
      await selectChat(selectedChat);
      // Set selected chat for mini dialog
      setSelectedChatInMini(selectedChat);
    }
  };

  const handleFullChatSelect = async (chat: ChatSession) => {
    // Select the chat and load messages
    await selectChat(chat);
    // Set selected chat for full dialog
    setSelectedChatInFull(chat);
  };

  const handleBackToMiniList = () => {
    setSelectedChatInMini(null);
  };

  const handleErrorClose = () => {
    clearError();
  };

  return (
    <>
      {/* Floating Action Button */}
      <FloatingChatButton onClick={handleFloatingButtonClick} />

      {/* Mini Chat Dialog */}
      <MiniChatDialog
        open={miniChatOpen}
        onClose={handleMiniChatClose}
        onExpand={handleMiniChatExpand}
        selectedChat={selectedChatInMini}
        onBackToList={handleBackToMiniList}
      >
        <RecentChatsList chats={recentChats} onChatSelect={handleMiniChatSelect} />
      </MiniChatDialog>

      {/* Full Chat Dialog */}
      <FullChatDialog
        open={fullChatOpen}
        onClose={handleFullChatClose}
        selectedChat={selectedChatInFull}
        onChatSelect={handleFullChatSelect}
      />

      {/* Error Snackbar */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleErrorClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
      >
        <Alert onClose={handleErrorClose} severity="error" sx={{ width: "100%" }}>
          {error}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ChatContainer;
