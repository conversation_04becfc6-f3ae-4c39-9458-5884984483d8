import React from "react";

import ChatIcon from "@mui/icons-material/Chat";
import { Fab, Tooltip } from "@mui/material";

interface FloatingChatButtonProps {
  /**
   * The function to call when the button is clicked.
   * This should handle opening the MiniChatDialog.
   */
  onClick: () => void;
}

/**
 * A Floating Action Button (FAB) that serves as the entry point to the chat feature.
 * It is positioned at the bottom-right of the screen.
 */
const FloatingChatButton: React.FC<FloatingChatButtonProps> = ({ onClick }) => {
  return (
    <Tooltip title="Open Chat" arrow>
      <Fab
        color="primary"
        aria-label="open chat"
        onClick={onClick}
        sx={{
          position: "fixed",
          bottom: 32,
          right: 32,
          zIndex: 1300, // Ensure it's above other content
        }}
      >
        <ChatIcon />
      </Fab>
    </Tooltip>
  );
};

export default FloatingChatButton;
