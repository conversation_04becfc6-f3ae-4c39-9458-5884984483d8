import React, { useState } from "react";

import AttachFileIcon from "@mui/icons-material/AttachFile";
import EmojiEmotionsIcon from "@mui/icons-material/EmojiEmotions";
import SendIcon from "@mui/icons-material/Send";
import { Box, IconButton, InputAdornment, TextField, Tooltip } from "@mui/material";

interface MessageInputBoxProps {
  /**
   * Function called when a message is sent.
   * Receives the message content as a string.
   */
  onSendMessage: (message: string) => void;
  /**
   * Placeholder text for the input field.
   */
  placeholder?: string;
  /**
   * Whether the input is disabled.
   */
  disabled?: boolean;
}

/**
 * A message input component with send button and optional attachment/emoji buttons.
 * Handles message composition and sending.
 */
const MessageInputBox: React.FC<MessageInputBoxProps> = ({
  onSendMessage,
  placeholder = "Type a message...",
  disabled = false,
}) => {
  const [message, setMessage] = useState("");

  const handleSend = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled) {
      onSendMessage(trimmedMessage);
      setMessage("");
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  const handleAttachment = () => {
    // TODO: Implement file attachment functionality
    console.log("Attachment clicked");
  };

  const handleEmoji = () => {
    // TODO: Implement emoji picker functionality
    console.log("Emoji clicked");
  };

  return (
    <Box
      sx={{
        p: 2,
        borderTop: 1,
        borderColor: "divider",
        backgroundColor: "background.paper",
      }}
    >
      <TextField
        fullWidth
        multiline
        maxRows={4}
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyPress={handleKeyPress}
        placeholder={placeholder}
        disabled={disabled}
        variant="outlined"
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Tooltip title="Attach file">
                <IconButton
                  size="small"
                  onClick={handleAttachment}
                  disabled={disabled}
                  sx={{ mr: 0.5 }}
                >
                  <AttachFileIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Add emoji">
                <IconButton
                  size="small"
                  onClick={handleEmoji}
                  disabled={disabled}
                >
                  <EmojiEmotionsIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <Tooltip title="Send message">
                <IconButton
                  onClick={handleSend}
                  disabled={disabled || !message.trim()}
                  color="primary"
                  size="small"
                >
                  <SendIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </InputAdornment>
          ),
          sx: {
            borderRadius: 3,
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: "divider",
            },
            "&:hover .MuiOutlinedInput-notchedOutline": {
              borderColor: "primary.main",
            },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderColor: "primary.main",
            },
          },
        }}
        sx={{
          "& .MuiInputBase-root": {
            paddingLeft: 1,
            paddingRight: 1,
          },
        }}
      />
    </Box>
  );
};

export default MessageInputBox;
