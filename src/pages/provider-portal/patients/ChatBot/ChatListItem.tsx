import React from "react";

import { Ava<PERSON>, Badge, Box, ListItem, ListItemAvatar, ListItemText, Typography } from "@mui/material";

interface ChatListItemProps {
  /**
   * The name of the person in the chat.
   */
  name: string;
  /**
   * The last message exchanged in the chat.
   */
  lastMessage: string;
  /**
   * The timestamp of the last message (e.g., "10:30 AM" or "Yesterday").
   */
  timestamp: string;
  /**
   * The URL for the user's avatar image.
   */
  avatarUrl?: string;
  /**
   * The number of unread messages. If 0, the badge is hidden.
   */
  unreadCount: number;
  /**
   * A function to call when the chat item is clicked.
   */
  onClick: () => void;
  /**
   * Whether this chat item is currently selected.
   */
  selected?: boolean;
}

/**
 * Renders a single item in the recent chats list.
 * It includes an avatar, name, last message, timestamp, and unread count badge.
 */
const ChatListItem: React.FC<ChatListItemProps> = ({
  name,
  lastMessage,
  timestamp,
  avatarUrl,
  unreadCount,
  onClick,
  selected = false,
}) => {
  return (
    <ListItem
      button
      onClick={onClick}
      selected={selected}
      divider
      sx={{
        "&.Mui-selected": {
          backgroundColor: "action.selected",
        },
      }}
    >
      <ListItemAvatar>
        <Avatar src={avatarUrl}>{name.charAt(0)}</Avatar>
      </ListItemAvatar>
      <ListItemText
        primary={<Typography noWrap>{name}</Typography>}
        secondary={
          <Typography noWrap color="text.secondary">
            {lastMessage}
          </Typography>
        }
      />
      <Box sx={{ textAlign: "right", ml: 2, minWidth: "fit-content" }}>
        <Typography variant="caption" color="text.secondary">
          {timestamp}
        </Typography>
        <Box sx={{ mt: 0.5, display: "flex", justifyContent: "flex-end" }}>
          {unreadCount > 0 && <Badge badgeContent={unreadCount} color="primary" />}
        </Box>
      </Box>
    </ListItem>
  );
};

export default ChatListItem;
