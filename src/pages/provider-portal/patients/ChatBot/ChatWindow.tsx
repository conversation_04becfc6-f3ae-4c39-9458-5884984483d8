import React, { useEffect, useRef } from "react";

import { Avatar, Box, Paper, Typography } from "@mui/material";

export interface ChatMessage {
  id: string;
  sender: "Patient" | "Provider" | "AI";
  senderName: string;
  content: string;
  timestamp: string;
  avatarUrl?: string;
}

interface ChatWindowProps {
  /**
   * Array of messages to display in the chat window.
   */
  messages?: ChatMessage[];
}

/**
 * A scrollable chat window that displays messages between patient and provider.
 * Messages are displayed with different styling based on the sender.
 */
const ChatWindow: React.FC<ChatWindowProps> = ({ messages = [] }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Mock messages for demonstration if no messages provided
  const mockMessages: ChatMessage[] = [
    {
      id: "1",
      sender: "Patient",
      senderName: "John Patient",
      content: "Hello, I have a question about my medication.",
      timestamp: "10:30 AM",
      avatarUrl: "",
    },
    {
      id: "2",
      sender: "Provider",
      senderName: "Dr. <PERSON>",
      content: "Hello John! I'd be happy to help. What's your question?",
      timestamp: "10:32 AM",
      avatarUrl: "",
    },
    {
      id: "3",
      sender: "Patient",
      senderName: "John Patient",
      content: "I'm experiencing some side effects from the new medication you prescribed. Should I be concerned?",
      timestamp: "10:35 AM",
      avatarUrl: "",
    },
    {
      id: "4",
      sender: "Provider",
      senderName: "Dr. Smith",
      content: "Can you describe the side effects you're experiencing? This will help me assess the situation.",
      timestamp: "10:37 AM",
      avatarUrl: "",
    },
  ];

  const displayMessages = messages.length > 0 ? messages : mockMessages;

  const getMessageAlignment = (sender: ChatMessage["sender"]) => {
    return sender === "Provider" ? "flex-end" : "flex-start";
  };

  const getMessageColor = (sender: ChatMessage["sender"]) => {
    switch (sender) {
      case "Provider":
        return "primary.main";
      case "AI":
        return "secondary.main";
      default:
        return "grey.300";
    }
  };

  const getTextColor = (sender: ChatMessage["sender"]) => {
    return sender === "Provider" ? "primary.contrastText" : "text.primary";
  };

  if (displayMessages.length === 0) {
    return (
      <Box
        sx={{
          flex: 1,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          p: 3,
        }}
      >
        <Typography color="text.secondary">No messages yet. Start a conversation!</Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        flex: 1,
        overflowY: "auto",
        p: 2,
        display: "flex",
        flexDirection: "column",
        gap: 2,
      }}
    >
      {displayMessages.map((message) => (
        <Box
          key={message.id}
          sx={{
            display: "flex",
            justifyContent: getMessageAlignment(message.sender),
            alignItems: "flex-start",
            gap: 1,
          }}
        >
          {message.sender !== "Provider" && (
            <Avatar src={message.avatarUrl} sx={{ width: 32, height: 32 }}>
              {message.senderName.charAt(0)}
            </Avatar>
          )}

          <Box sx={{ maxWidth: "70%", minWidth: "120px" }}>
            <Paper
              elevation={1}
              sx={{
                p: 2,
                backgroundColor: getMessageColor(message.sender),
                color: getTextColor(message.sender),
                borderRadius: 2,
                ...(message.sender === "Provider" && {
                  borderBottomRightRadius: 4,
                }),
                ...(message.sender !== "Provider" && {
                  borderBottomLeftRadius: 4,
                }),
              }}
            >
              <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                {message.senderName}
              </Typography>
              <Typography variant="body1">{message.content}</Typography>
            </Paper>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{
                display: "block",
                mt: 0.5,
                textAlign: message.sender === "Provider" ? "right" : "left",
              }}
            >
              {message.timestamp}
            </Typography>
          </Box>

          {message.sender === "Provider" && (
            <Avatar src={message.avatarUrl} sx={{ width: 32, height: 32 }}>
              {message.senderName.charAt(0)}
            </Avatar>
          )}
        </Box>
      ))}
      <div ref={messagesEndRef} />
    </Box>
  );
};

export default ChatWindow;
