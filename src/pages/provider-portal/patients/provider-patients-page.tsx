import { useState } from "react";

import { Box, Grid } from "@mui/material";

import ChatWindow from "../../../components/chat/ChatWindow";
// Your existing component
import PatientsList from "../../../components/provider-portal/patients/patients-list";
import MessageInputBox from "./ChatBot/";
// --- 1. IMPORT ALL THE CHAT COMPONENTS ---
import FloatingChatButton from "./ChatBot/FloatingChatButton";
import FullChatDialog from "./ChatBot/FullChatDialog";
import MiniChatDialog from "./ChatBot/MiniChatDialog";
import RecentChatsList from "./ChatBot/RecentChatsList";

// --- 2. DEFINE MOCK DATA (for demonstration) ---
// In a real app, this would come from an API
const mockRecentChats = [
  {
    id: 1,
    name: "<PERSON>",
    lastMessage: "Okay, I will be there.",
    timestamp: "10:45 AM",
    unreadCount: 2,
    avatarUrl: "/avatars/john.jpg",
  },
  {
    id: 2,
    name: "<PERSON>",
    lastMessage: "Thank you so much, <PERSON>!",
    timestamp: "Yesterday",
    unreadCount: 0,
    avatarUrl: "/avatars/jane.jpg",
  },
  {
    id: 3,
    name: "AmataBot Summary",
    lastMessage: "AI: Patient reported mild headache...",
    timestamp: "9:00 AM",
    unreadCount: 1,
    avatarUrl: "",
  },
];

const mockMessagesByChatId = {
  1: [
    // Messages for John Patient
    {
      id: "msg1",
      sender: "Patient",
      senderName: "John",
      content: "Hi Dr. Smith, I have a question about my prescription.",
      timestamp: "10:40 AM",
    },
    {
      id: "msg2",
      sender: "Provider",
      senderName: "Dr. Smith",
      content: "Of course, John. I'm here to help. What's on your mind?",
      timestamp: "10:41 AM",
    },
    {
      id: "msg3",
      sender: "AI",
      senderName: "AmataBot",
      content: "Patient sentiment appears anxious.",
      timestamp: "10:42 AM",
    },
    {
      id: "msg4",
      sender: "Patient",
      senderName: "John",
      content: "The new medication is making me feel a bit dizzy. Is that normal?",
      timestamp: "10:43 AM",
    },
    {
      id: "msg5",
      sender: "Provider",
      senderName: "Dr. Smith",
      content:
        "Some initial dizziness can occur. Let's monitor it. If it persists past tomorrow, please let me know immediately.",
      timestamp: "10:44 AM",
    },
    { id: "msg6", sender: "Patient", senderName: "John", content: "Okay, I will be there.", timestamp: "10:45 AM" },
  ],
  2: [
    // Messages for Jane Doe
    {
      id: "msg7",
      sender: "Patient",
      senderName: "Jane Doe",
      content: "Just wanted to confirm my appointment for Friday.",
      timestamp: "Yesterday",
    },
    {
      id: "msg8",
      sender: "Provider",
      senderName: "Dr. Smith",
      content: "Hi Jane, yes, you are confirmed for Friday at 2:00 PM.",
      timestamp: "Yesterday",
    },
  ],
  3: [
    // Messages for AmataBot Summary
    {
      id: "msg9",
      sender: "AI",
      senderName: "AmataBot",
      content: "AI: Patient reported mild headache and requested a follow-up. Suggested Tylenol.",
      timestamp: "9:00 AM",
    },
  ],
};

const ProviderPatientsPage = () => {
  // --- 3. ADD STATE MANAGEMENT ---
  const [isMiniChatOpen, setMiniChatOpen] = useState(false);
  const [isFullChatOpen, setFullChatOpen] = useState(false);
  const [selectedChat, setSelectedChat] = useState(null);
  const [messages, setMessages] = useState(mockMessagesByChatId);

  // --- 4. ADD HANDLER FUNCTIONS TO CONTROL THE UI ---
  const handleOpenMiniChat = () => {
    setMiniChatOpen(true);
  };

  const handleCloseMiniChat = () => {
    setMiniChatOpen(false);
  };

  const handleCloseFullChat = () => {
    setFullChatOpen(false);
    setSelectedChat(null); // Deselect chat when closing the full view
  };

  const handleSelectChat = (chatId) => {
    const chatDetails = mockRecentChats.find((chat) => chat.id === chatId);
    if (chatDetails) {
      setSelectedChat(chatDetails);
      setMiniChatOpen(false);
      setFullChatOpen(true);
    }
  };

  const handleSendMessage = (content: string) => {
    if (!selectedChat || !content.trim()) return;

    const newMessage = {
      id: `msg${Date.now()}`, // Simple unique ID
      sender: "Provider" as const,
      senderName: "Dr. Smith", // Or dynamically set provider name
      content,
      timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    };

    // Update the messages state immutably for the selected chat
    setMessages((prevMessages) => ({
      ...prevMessages,
      [selectedChat.id]: [...(prevMessages[selectedChat.id] || []), newMessage],
    }));
  };

  // --- 5. RENDER AND COMPOSE THE COMPONENTS ---
  return (
    <Grid container sx={{ width: "100%" }}>
      {/* Your original page content remains */}
      <Grid item xs={12}>
        <PatientsList />
      </Grid>

      {/* --- CHAT FEATURE IMPLEMENTATION --- */}

      {/* This button is the entry point */}
      <FloatingChatButton onClick={handleOpenMiniChat} />

      {/* This is the small window with the chat list */}
      <MiniChatDialog
        open={isMiniChatOpen}
        onClose={handleCloseMiniChat}
        onExpand={() => {
          // Handle clicking the expand icon directly
          if (mockRecentChats.length > 0) handleSelectChat(mockRecentChats[0].id);
        }}
      >
        <RecentChatsList chats={mockRecentChats} onChatSelect={handleSelectChat} />
      </MiniChatDialog>

      {/* This is the full-screen window. It only renders if a chat is selected. */}
      {selectedChat && (
        <FullChatDialog
          open={isFullChatOpen}
          onClose={handleCloseFullChat}
          patient={{ name: selectedChat.name, avatarUrl: selectedChat.avatarUrl }}
        >
          {/* We pass the ChatWindow and MessageInputBox as children */}
          <Box sx={{ display: "flex", flexDirection: "column", height: "100%", overflow: "hidden" }}>
            <ChatWindow messages={messages[selectedChat.id] || []} />
            <MessageInputBox onSendMessage={handleSendMessage} />
          </Box>
        </FullChatDialog>
      )}
    </Grid>
  );
};

export default ProviderPatientsPage;
