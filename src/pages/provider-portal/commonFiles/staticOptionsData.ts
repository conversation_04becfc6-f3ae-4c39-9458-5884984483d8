// patient time log page options

export const logNameOptions = [
  { key: "1", value: "Patient Data Review" },
  { key: "2", value: "Comprehensive Care Plan" },
  { key: "3", value: "Medication Adjustment" },
];

export const loggedByOptions = [
  { key: "1", value: "<PERSON><PERSON>" },
  { key: "2", value: "<PERSON>" },
  { key: "3", value: "Annette Black" },
];

export const logTitleOptions = [
  { key: "Patient Monitoring", value: "Patient Monitoring" },
  { key: "Care Plan Adjustments", value: "Care Plan Adjustments" },
  { key: "Clinical Review", value: "Clinical Review" },
  { key: "Documentation", value: "Documentation" },
  { key: "Device Technical Support", value: "Device Technical Support" },
];

export const DeviceCategory = [
  { key: "MECHANICAL", value: "Mechanical" },
  { key: "ELECTRICAL", value: "Electrical" },
  { key: "DIGITAL", value: "Digital" },
];

export const DeviceTypes = [
  { key: "RPM", value: "RPM" },
  { key: "infusion", value: "infusion" },
  { key: "Elastomeric Infusion Pump", value: "Elastomeric Infusion Pump" },
  { key: "Catheter Securement Device", value: "Catheter Securement Device" },
  { key: "Blood Pressure Monitor", value: "Blood Pressure Monitor" },
  { key: "Glucose Monitor", value: "Glucose Monitor" },
];

export const LogType = [
  { key: "AUTOMATIC", value: "Automatic" },
  { key: "MANUAL", value: "Manual" },
];
