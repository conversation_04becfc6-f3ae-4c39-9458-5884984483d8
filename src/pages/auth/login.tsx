import { Controller, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";

import { Button, Grid2 as Grid, Link, Typography } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { AxiosResponse } from "axios";
import * as yup from "yup";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import LoginSVG from "@/assets/image_svg/auth/Login-Image.svg";
import AuthLayout from "@/components/layouts/AuthLayout";
import {
  emailRegexErrorMsg,
  emailRequiredErrorMsg,
  passwordIsRequired,
  passwordRegexErrorMsg,
} from "@/constants/error-messages";
import { PortalStartingRoute } from "@/constants/portals";
import { RolesPortalMap } from "@/constants/roles";
import useStoreLoginData from "@/hooks/use-store-login-data";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useUserControllerServiceGetAccessToken } from "@/sdk/queries";
import { GetTenantId } from "@/services/common/get-tenant-id";
import storageService from "@/services/core/storage-service";
import { emailRegExp, passwordRegx } from "@/utils/regex";
import { theme } from "@/utils/theme";

const loginSchema = yup.object().shape({
  password: yup.string().required(passwordIsRequired).matches(passwordRegx, passwordRegexErrorMsg),
  email: yup.string().required(emailRequiredErrorMsg).matches(emailRegExp, emailRegexErrorMsg),
});

const LoginPage = () => {
  const xTenantId = GetTenantId();

  const navigate = useNavigate();
  const storeLoginDataInStore = useStoreLoginData();
  const dispatch = useDispatch();
  const location = useLocation();

  const initialValues = {
    email: location.state ? location.state?.email : "",
    password: "",
  };

  const { mutateAsync: loginMutateAsync, isPending: isLoggingIn } = useUserControllerServiceGetAccessToken({
    onError: (error) => {
      const message = (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while logging in";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: (data) => {
      const loginResponse = (data as unknown as AxiosResponse).data;

      storeLoginDataInStore(loginResponse);

      const role = storageService.getRoles() || "";
      const redirectURL = localStorage.getItem("redirectURL");

      if (redirectURL && !role) {
        navigate(redirectURL);
        localStorage.removeItem("redirectURL");
      } else if (role) {
        const portal = RolesPortalMap[role];
        navigate(PortalStartingRoute[portal]);
      }
    },
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(loginSchema),
  });

  const onSubmit = async (values: typeof initialValues) => {
    await loginMutateAsync({
      requestBody: { username: values.email, password: values.password },
      xTenantId: xTenantId,
    });
  };

  return (
    <AuthLayout illustrationSrc={LoginSVG}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid display="flex" flexDirection="column" gap="8px" paddingBottom="24px">
          <Typography fontWeight={600} fontSize="30px">
            Log in to your account
          </Typography>
          <Typography fontSize="16px" fontWeight={400} color={theme.palette.text.secondary}>
            Welcome! Please enter your details.
          </Typography>
        </Grid>

        <Grid display="flex" flexDirection="column" gap="24px">
          <Grid>
            <CustomLabel label="Email" />
            <Controller
              control={control}
              name="email"
              render={({ field }) => (
                <CustomInput
                  placeholder="Enter Your Email"
                  {...field}
                  hasError={!!errors.email}
                  errorMessage={(errors.email?.message as string) || ""}
                  onChange={(event) => {
                    setValue("email", event.target.value, {
                      shouldValidate: true,
                    });
                  }}
                />
              )}
            />
          </Grid>

          <Grid>
            <CustomLabel label="Password" />
            <Controller
              control={control}
              name="password"
              render={({ field }) => (
                <CustomInput
                  placeholder={"Enter your Password"}
                  isNumeric={false}
                  isPassword={true}
                  hasError={!!errors.password}
                  errorMessage={errors.password?.message}
                  {...field}
                  onChange={(event) => {
                    setValue("password", event.target.value, {
                      shouldValidate: false,
                    });
                  }}
                />
              )}
            />
          </Grid>

          <Grid container justifyContent={"flex-end"} alignItems={"center"}>
            <Link
              sx={{ textDecoration: "none", cursor: "pointer" }}
              onClick={() =>
                navigate("/auth/verify-email", {
                  state: {
                    isForgotPassword: true,
                  },
                })
              }
            >
              <Typography fontWeight={550} variant="bodySmall">
                {"Forgot password"}
              </Typography>
            </Link>
          </Grid>

          <Grid>
            <Button variant="contained" fullWidth type="submit" loading={isLoggingIn}>
              Login
            </Button>
          </Grid>
        </Grid>
      </form>
    </AuthLayout>
  );
};

export default LoginPage;
