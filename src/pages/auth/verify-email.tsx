import { Controller, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";

import { Button, Grid2 as Grid, Typography } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import EmailVerificationSVG from "@/assets/image_svg/auth/email_verification.svg";
import AuthLayout from "@/components/layouts/AuthLayout";
import { emailRegexErrorMsg, emailRequiredErrorMsg } from "@/constants/error-messages";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useUserControllerServiceVerifyUser } from "@/sdk/queries";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { emailRegExp } from "@/utils/regex";
import { theme } from "@/utils/theme";

export const verifySchema = yup.object().shape({
  email: yup.string().required(emailRequiredErrorMsg).matches(emailRegExp, emailRegexErrorMsg),
});

const VerifyEmailPage = () => {
  const xTenantId = GetTenantId();

  const initialValues = {
    email: "",
  };

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();

  const isForgotPassword = Boolean(location?.state?.isForgotPassword);

  const { mutateAsync, isPending } = useUserControllerServiceVerifyUser({
    onError: (error) => {
      const message =
        (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while verifying email";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: (data) => {
      if (data.data && !isForgotPassword) {
        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.SUCCESS,
            message: "Your account already activated. Please login to continue.",
          })
        );
        navigate("/auth/login", {
          state: {
            email: getValues("email"),
          },
        });

        return;
      }

      navigate("/auth/verify-otp", {
        state: {
          email: getValues("email"),
          isForgotPassword: isForgotPassword,
        },
      });
    },
  });

  const {
    control,
    handleSubmit,
    getValues,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(verifySchema),
  });

  const onSubmit = async (values: typeof initialValues) => {
    await mutateAsync({ email: values.email, xTenantId: xTenantId });
  };

  return (
    <AuthLayout illustrationSrc={EmailVerificationSVG}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid display="flex" flexDirection="column" gap="8px" paddingBottom="24px">
          <Typography fontWeight={600} fontSize="30px">
            {isForgotPassword ? "Forgot Password" : "Email Verification"}
          </Typography>
          <Typography fontSize="16px" fontWeight={400} color={theme.palette.text.secondary}>
            Please enter your email to receive verification code
          </Typography>
        </Grid>

        <Grid display="flex" flexDirection="column" gap="24px">
          <Grid>
            <CustomLabel label="Email" />

            <Controller
              control={control}
              name="email"
              render={({ field }) => (
                <CustomInput
                  placeholder={"Enter Your Email"}
                  {...field}
                  hasError={!!errors.email}
                  errorMessage={errors.email?.message}
                  onChange={(event) => {
                    setValue("email", event.target.value, {
                      shouldValidate: true,
                    });
                  }}
                />
              )}
            />
          </Grid>

          <Grid display="flex" flexDirection="column" gap="16px">
            <Button
              variant="contained"
              fullWidth
              type="submit"
              disabled={!!errors.email || getValues("email").length === 0}
              loading={isPending}
            >
              Send Verification Code
            </Button>
            <Button variant="text" fullWidth sx={{ fontWeight: 500 }} onClick={() => navigate("/auth/login")}>
              Back to Login
            </Button>
          </Grid>
        </Grid>
      </form>
    </AuthLayout>
  );
};

export default VerifyEmailPage;
