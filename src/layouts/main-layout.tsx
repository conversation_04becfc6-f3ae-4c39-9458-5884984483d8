import { PropsWithChildren } from "react";

import { Grid } from "@mui/system";

import Navbar from "@/common-components/navbar/navbar";

import { DrawerProvider } from "@/components/providers/DrawerProvider";

const MainLayout = (props: PropsWithChildren) => {
  return (
    <DrawerProvider>
      <Grid container height={"100vh"}>
        <Grid
          container
          flex={1}
          flexDirection={"column"}
          height={"100vh"}
          sx={{
            overflow: "auto",
            maxHeight: "100vh",
            transition: "all .2s",
          }}
          flexWrap={"nowrap"}
        >
          <Navbar />
          <Grid container flex={1} justifyContent={"center"}>
            {props.children}
          </Grid>
        </Grid>
      </Grid>
    </DrawerProvider>
  );
};

export default MainLayout;
