// timerStore.ts (unchanged)
interface TimeEntry {
  pageUrl: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  isActive: boolean;
  sessionId: string;
}

interface TimerState {
  currentEntry: TimeEntry | null;
  entries: TimeEntry[];
  isTracking: boolean;
  sessionId: string;
}

class TimerStore {
  private state: TimerState;
  private listeners: Set<() => void> = new Set();

  constructor() {
    this.state = {
      currentEntry: null,
      entries: [],
      isTracking: false,
      sessionId: this.generateSessionId(),
    };
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getState(): TimerState {
    return { ...this.state };
  }

  setState(newState: Partial<TimerState>): void {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  subscribe(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach((listener) => listener());
  }

  addEntry(entry: TimeEntry): void {
    this.setState({
      entries: [...this.state.entries, entry],
    });
  }

  updateCurrentEntry(updates: Partial<TimeEntry>): void {
    if (this.state.currentEntry) {
      const updatedEntry: TimeEntry = { ...this.state.currentEntry, ...updates };
      this.setState({ currentEntry: updatedEntry });
    }
  }

  clearEntries(): void {
    this.setState({ entries: [] });
  }
}

export const timerStore = new TimerStore();
export type { TimeEntry, TimerState };
