// import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { TimeLogControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { StoreActions } from "@/services/core/TimeLogService/StoreActions";

// import { useDispatch } from "react-redux";
// import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

export const AllowedTimeTrackingPages = [
  "vitals",
  "carePlans",
  "allergies",
  "medications",
  "diagnoses",
  "appointments",
  "devices",
  "ConsentForms",
];
// const dispatch = useDispatch();

interface NavigationRestrictionOptions {
  allowNavigation: boolean;
  message?: string;
  shouldShowDialog: boolean;
}

class TimeTrackingService {
  private static instance: TimeTrackingService;
  private elapsedSeconds = 0;
  private intervalId: number | null = null;
  private readonly callbacks = new Set<(elapsedSeconds: number) => void>();
  private readonly storageKey = "patientChartingTimeTracking";
  private currentPageUrl: string | null = null;
  private navigationRestrictionCallback: ((options: NavigationRestrictionOptions) => void) | null = null;
  private isManuallyPaused = false;

  private constructor() {
    this.handleUrlChange = this.handleUrlChange.bind(this);
    this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);

    this.setupNavigationListeners();
    this.restoreElapsedTime();
    this.handleUrlChange();
  }

  public static getInstance(): TimeTrackingService {
    if (!TimeTrackingService.instance) {
      TimeTrackingService.instance = new TimeTrackingService();
    }
    return TimeTrackingService.instance;
  }

  public setNavigationRestrictionCallback(callback: (options: NavigationRestrictionOptions) => void): void {
    this.navigationRestrictionCallback = callback;
  }

  public checkNavigation(newUrl: string): void {
    const options = this.checkNavigationRestriction(newUrl);
    if (this.navigationRestrictionCallback) {
      this.navigationRestrictionCallback(options);
    }
  }

  private checkNavigationRestriction(newUrl: string): NavigationRestrictionOptions {
    const isTracking = StoreActions.getIsTracking();
    const isNewPageAllowed = this.isAllowedPage(newUrl);

    if (isTracking && !isNewPageAllowed) {
      return {
        allowNavigation: false,
        message: "You cannot leave this page while time tracking is active. Please stop tracking first.",
        shouldShowDialog: true,
      };
    }

    return {
      allowNavigation: true,
      shouldShowDialog: true,
    };
  }

  private handleBeforeUnload(_event: BeforeUnloadEvent): void {
    // Try to sync time log before user leaves
    // Use sendBeacon for reliability
    this.sendPayloadToBackend();
    // Optionally, show a confirmation dialog (not reliable for async work)
    // event.preventDefault();
    // event.returnValue = '';
  }

  private handleVisibilityChange(): void {
    if (document.visibilityState === "hidden") {
      this.sendPayloadToBackend();
    }
  }

  private isAllowedPage(url: string): boolean {
    const urlParams = new URLSearchParams(url.split("?")[1] || "");
    const tab = urlParams.get("tab");
    return tab ? AllowedTimeTrackingPages.includes(tab) : false;
  }

  private notifyCallbacks(): void {
    for (const callback of this.callbacks) {
      callback(this.elapsedSeconds);
    }
  }

  private setupNavigationListeners(): void {
    window.addEventListener("popstate", this.handleUrlChange);
    window.addEventListener("pushstate", this.handleUrlChange);
    window.addEventListener("replacestate", this.handleUrlChange);
    this.patchHistoryMethod("pushState");
    this.patchHistoryMethod("replaceState");
    window.addEventListener("beforeunload", this.handleBeforeUnload);
    document.addEventListener("visibilitychange", this.handleVisibilityChange);
    // For SSR/testing, allow cleanup
    window.removeEventListener("beforeunload", this.handleBeforeUnload);
    document.removeEventListener("visibilitychange", this.handleVisibilityChange);
  }

  private patchHistoryMethod(method: "pushState" | "replaceState"): void {
    const original = history[method];
    history[method] = function (...args: Parameters<typeof original>) {
      const result = original.apply(this, args);
      window.dispatchEvent(new Event(method.toLowerCase()));
      return result;
    };
  }

  private restoreElapsedTime(): void {
    const stored = localStorage.getItem(this.storageKey);
    this.elapsedSeconds = stored ? parseInt(stored, 10) || 0 : 0;
  }

  private saveElapsedTime(): void {
    localStorage.setItem(this.storageKey, this.elapsedSeconds.toString());
  }

  private handleUrlChange(): void {
    const currentUrl = window.location.pathname + window.location.search;
    this.currentPageUrl = currentUrl;

    const isTrackingPage = this.isAllowedPage(currentUrl);

    if (isTrackingPage) {
      this.isManuallyPaused = false; // Reset manual pause on allowed page
      this.startTracking();
    } else {
      this.pauseTracking();
    }
  }

  public getIsTracking(): boolean {
    return this.intervalId !== null && StoreActions.getIsTracking();
  }

  public startTracking(): void {
    if (this.intervalId !== null) return;

    const currentUrl = window.location.pathname + window.location.search;
    if (!this.isAllowedPage(currentUrl)) return;

    this.isManuallyPaused = false;

    this.intervalId = window.setInterval(() => {
      this.elapsedSeconds += 1;
      this.saveElapsedTime();
      this.notifyCallbacks();
    }, 1000);

    let currentEntry = StoreActions.getCurrentEntry();
    if (!currentEntry || !currentEntry.isActive) {
      currentEntry = {
        pageUrl: currentUrl,
        startTime: Date.now(),
        isActive: true,
        sessionId: StoreActions.getSessionId(),
      };
      StoreActions.setCurrentEntry(currentEntry);
    } else if (currentEntry.pageUrl !== currentUrl) {
      StoreActions.updateCurrentEntry({ pageUrl: currentUrl });
    }

    StoreActions.setIsTracking(true);
    this.notifyCallbacks();
  }

  public pauseTracking(): void {
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    StoreActions.setIsTracking(false);

    const currentEntry = StoreActions.getCurrentEntry();
    if (currentEntry) {
      StoreActions.updateCurrentEntry({ isActive: false });
    }

    this.notifyCallbacks();
  }

  public stopTracking(): void {
    this.isManuallyPaused = true;

    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    this.elapsedSeconds = 0;

    StoreActions.finalizeCurrentEntry();
    this.notifyCallbacks();
  }

  public resumeTracking(): void {
    this.isManuallyPaused = false;

    const currentUrl = window.location.pathname + window.location.search;
    if (this.isAllowedPage(currentUrl)) {
      this.startTracking();
    }
  }

  public reset(): void {
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    this.elapsedSeconds = 0;
    this.saveElapsedTime();
    this.isManuallyPaused = false;

    StoreActions.setCurrentEntry(null);
    StoreActions.setIsTracking(false);

    const currentUrl = window.location.pathname + window.location.search;
    if (this.isAllowedPage(currentUrl)) {
      this.startTracking();
    }

    this.notifyCallbacks();
  }

  public getElapsedTime(): number {
    return this.getIsTracking() ? this.elapsedSeconds : 0;
  }

  public formatTime(totalSeconds: number): { hours: string; minutes: string; seconds: string } {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    return {
      hours: hours.toString().padStart(2, "0"),
      minutes: minutes.toString().padStart(2, "0"),
      seconds: seconds.toString().padStart(2, "0"),
    };
  }

  public subscribe(callback: (elapsedSeconds: number) => void): void {
    this.callbacks.add(callback);
    callback(this.elapsedSeconds);
  }

  public unsubscribe(callback: (elapsedSeconds: number) => void): void {
    this.callbacks.delete(callback);
  }

  private extractPatientIdFromUrl(url: string): string | null {
    const match = url.match(/\/patients\/([a-f0-9-]+)\//i);
    return match ? match[1] : null;
  }

  public async sendPayloadToBackend(): Promise<void> {
    const payloadArray = StoreActions.getState().entries;
    if (!Array.isArray(payloadArray) || payloadArray.length === 0) return;

    for (const payload of payloadArray) {
      await TimeLogControllerService.createTimeLogAsync({
        xTenantId: GetTenantId(),
        requestBody: {
          patientId: this.extractPatientIdFromUrl(payload.pageUrl) || "",
          activityName: "Patient Monitoring",
          logEntryType: "AUTOMATIC",
          logStartTime: payload.startTime ? new Date(payload.startTime).toISOString() : undefined,
          logEndTime: payload.endTime ? new Date(payload.endTime).toISOString() : undefined,
        },
      });
      // setSnackbarOn({ severity: AlertSeverity.SUCCESS, message: "test"});
    }
    this.flushData();
  }

  public flushData(): void {
    StoreActions.clearAllEntries();
    StoreActions.setIsTracking(false);
    StoreActions.setCurrentEntry(null);
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.elapsedSeconds = 0;
    this.saveElapsedTime();
    this.isManuallyPaused = false;
  }

  public getCurrentPageUrl(): string | null {
    return this.currentPageUrl;
  }

  public isCurrentPageAllowed(): boolean {
    return this.currentPageUrl ? this.isAllowedPage(this.currentPageUrl) : false;
  }

  public getDebugInfo(): object {
    return {
      currentUrl: this.currentPageUrl,
      isAllowed: this.isCurrentPageAllowed(),
      isTracking: this.getIsTracking(),
      elapsedSeconds: this.elapsedSeconds,
      isManuallyPaused: this.isManuallyPaused,
      hasInterval: this.intervalId !== null,
      storeState: StoreActions.getState(),
    };
  }
}

export default TimeTrackingService.getInstance();
