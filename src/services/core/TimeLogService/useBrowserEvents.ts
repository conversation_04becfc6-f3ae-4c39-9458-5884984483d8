import { useEffect, useRef } from "react";

interface BrowserEventHandlers {
  onVisibilityChange?: () => void;
  onBeforeUnload?: (event: BeforeUnloadEvent) => void;
}

export const useBrowserEvents = (handlers: BrowserEventHandlers) => {
  const savedHandlers = useRef(handlers);

  useEffect(() => {
    savedHandlers.current = handlers;
  }, [handlers]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden" && savedHandlers.current.onVisibilityChange) {
        savedHandlers.current.onVisibilityChange();
      }
    };

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (savedHandlers.current.onBeforeUnload) {
        savedHandlers.current.onBeforeUnload(event);
      }
    };

    const hasVisibilityHandler = !!savedHandlers.current.onVisibilityChange;
    const hasUnloadHandler = !!savedHandlers.current.onBeforeUnload;

    if (hasVisibilityHandler) {
      document.addEventListener("visibilitychange", handleVisibilityChange);
    }
    if (hasUnloadHandler) {
      window.addEventListener("beforeunload", handleBeforeUnload);
    }

    return () => {
      if (hasVisibilityHandler) {
        document.removeEventListener("visibilitychange", handleVisibilityChange);
      }
      if (hasUnloadHandler) {
        window.removeEventListener("beforeunload", handleBeforeUnload);
      }
    };
  }, []);
};
