// storeActions.ts
import { TimeEntry, TimerState, timerStore } from "./TimerStore";

export class StoreActions {
  // Getters
  static getCurrentEntry(): TimeEntry | null {
    return timerStore.getState().currentEntry;
  }

  static getAllEntries(): TimeEntry[] {
    return timerStore.getState().entries;
  }

  /**
   * Returns the single entry to show: the current entry if active, otherwise the most recent finalized entry.
   */
  static getSingleEntry(): TimeEntry | null {
    const state = timerStore.getState();
    if (state.currentEntry && state.currentEntry.isActive) {
      return state.currentEntry;
    }
    // Find the most recent finalized entry (by endTime)
    const finalizedEntries = state.entries.filter((e) => e.endTime);
    if (finalizedEntries.length > 0) {
      // Return the one with the latest endTime
      return finalizedEntries.reduce(
        (latest, entry) => (!latest || entry.endTime! > latest.endTime! ? entry : latest),
        null as TimeEntry | null
      );
    }
    return null;
  }

  static getIsTracking(): boolean {
    return timerStore.getState().isTracking;
  }

  static getSessionId(): string {
    return timerStore.getState().sessionId;
  }

  static getState(): TimerState {
    return timerStore.getState();
  }

  static getTotalTimeForPage(pageUrl: string): number {
    const entries = timerStore.getState().entries;
    return entries
      .filter((entry) => entry.pageUrl === pageUrl && entry.duration)
      .reduce((total, entry) => total + (entry.duration || 0), 0);
  }

  static getActiveTimeEntries(): TimeEntry[] {
    return timerStore.getState().entries.filter((entry) => entry.isActive);
  }

  // Setters
  static setCurrentEntry(entry: TimeEntry | null): void {
    timerStore.setState({ currentEntry: entry });
  }

  static setIsTracking(isTracking: boolean): void {
    timerStore.setState({ isTracking });
  }

  static addTimeEntry(entry: TimeEntry): void {
    timerStore.addEntry(entry);
  }

  static updateCurrentEntry(updates: Partial<TimeEntry>): void {
    timerStore.updateCurrentEntry(updates);
  }

  static finalizeCurrentEntry(): TimeEntry | null {
    const currentEntry = timerStore.getState().currentEntry;
    if (currentEntry && !currentEntry.endTime) {
      const now = Date.now();
      const finalizedEntry: TimeEntry = {
        ...currentEntry,
        endTime: now,
        duration: now - currentEntry.startTime,
        isActive: false,
      };

      // Overwrite entries to keep only the latest finalized entry (enforce single-entry)
      timerStore.setState({ entries: [finalizedEntry] });
      timerStore.setState({ currentEntry: null, isTracking: false });
      return finalizedEntry;
    }
    return null;
  }

  static clearAllEntries(): void {
    timerStore.clearEntries();
  }

  // Utility methods
  static subscribe(listener: () => void): () => void {
    return timerStore.subscribe(listener);
  }

  static formatDuration(durationInMs: number): string {
    const totalSeconds = Math.floor(durationInMs / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    const parts: string[] = [];
    if (hours) parts.push(`${hours}h`);
    if (minutes) parts.push(`${minutes}m`);
    if (seconds || parts.length === 0) parts.push(`${seconds}s`);

    return parts.join(" ");
  }

  static getEntriesForPayload(): {
    duration: number;
    startTime: string;
    endTime: string;
  }[] {
    return timerStore
      .getState()
      .entries.filter((entry) => entry.duration && entry.endTime)
      .map((entry) => ({
        duration: entry.duration!,
        startTime: new Date(entry.startTime).toISOString(),
        endTime: new Date(entry.endTime!).toISOString(),
      }));
  }
}
