import type { ChatMessage } from "@/pages/provider-portal/patients/ChatBot";

export interface ChatSession {
  id: number;
  name: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  avatarUrl?: string;
  patientId?: string;
  providerId?: string;
}

export interface SendMessageRequest {
  content: string;
  chatId: number;
  senderId: string;
  senderType: "Patient" | "Provider" | "AI";
}

export interface ChatHistoryResponse {
  messages: ChatMessage[];
  hasMore: boolean;
  nextCursor?: string;
}

// Mock data for testing
const mockChatSessions: ChatSession[] = [
  {
    id: 1,
    name: "<PERSON>",
    lastMessage: "Thank you for the help!",
    timestamp: "10:45 AM",
    unreadCount: 2,
    avatarUrl: "",
    patientId: "patient_1",
  },
  {
    id: 2,
    name: "<PERSON>",
    lastMessage: "When is my next appointment?",
    timestamp: "Yesterday",
    unreadCount: 0,
    avatarUrl: "",
    patientId: "patient_2",
  },
  {
    id: 3,
    name: "AI Assistant",
    lastMessage: "Patient summary updated",
    timestamp: "9:00 AM",
    unreadCount: 1,
    avatarUrl: "",
  },
];

const mockMessages: { [chatId: number]: ChatMessage[] } = {
  1: [
    {
      id: "msg1",
      sender: "Patient",
      senderName: "John Patient",
      content: "Hello, I have a question about my medication.",
      timestamp: "10:30 AM",
    },
    {
      id: "msg2",
      sender: "Provider",
      senderName: "Dr. Smith",
      content: "Hello John! I'd be happy to help. What's your question?",
      timestamp: "10:32 AM",
    },
    {
      id: "msg3",
      sender: "Patient",
      senderName: "John Patient",
      content: "I'm experiencing some side effects from the new medication you prescribed. Should I be concerned?",
      timestamp: "10:35 AM",
    },
    {
      id: "msg4",
      sender: "Provider",
      senderName: "Dr. Smith",
      content: "Can you describe the side effects you're experiencing? This will help me assess the situation.",
      timestamp: "10:37 AM",
    },
  ],
  2: [
    {
      id: "msg5",
      sender: "Patient",
      senderName: "Jane Doe",
      content: "Thank you for the treatment plan. I'm feeling much better!",
      timestamp: "Yesterday",
    },
    {
      id: "msg6",
      sender: "Provider",
      senderName: "Dr. Smith",
      content: "That's wonderful to hear, Jane! Keep following the plan and let me know if you have any concerns.",
      timestamp: "Yesterday",
    },
    {
      id: "msg7",
      sender: "Patient",
      senderName: "Jane Doe",
      content: "Will do. When is my next appointment?",
      timestamp: "Yesterday",
    },
  ],
  3: [
    {
      id: "msg8",
      sender: "AI",
      senderName: "AI Assistant",
      content: "Patient John reported mild headache and requested a follow-up. Suggested Tylenol.",
      timestamp: "9:00 AM",
    },
    {
      id: "msg9",
      sender: "AI",
      senderName: "AI Assistant",
      content: "Patient summary has been updated with latest vitals and medication changes.",
      timestamp: "9:00 AM",
    },
  ],
};

/**
 * Service class for handling chat-related API calls and operations
 */
export class ChatService {
  private static instance: ChatService;

  private constructor() {
    // No need for tenant ID with mock data
  }

  public static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  /**
   * Get chat history for a specific chat session
   */
  async getChatHistory(chatId: number): Promise<ChatHistoryResponse> {
    try {
      // Return mock messages for the specific chat
      const messages = mockMessages[chatId] || [];

      return {
        messages,
        hasMore: false,
      };
    } catch (error) {
      console.error("Error fetching chat history:", error);
      throw new Error("Failed to fetch chat history");
    }
  }

  /**
   * Send a message in a chat session
   */
  async sendMessage(request: SendMessageRequest): Promise<ChatMessage> {
    try {
      // Create a new message object with mock data
      const newMessage: ChatMessage = {
        id: `msg_${Date.now()}`,
        sender: request.senderType,
        senderName: request.senderType === "Provider" ? "Dr. Smith" :
                   request.senderType === "AI" ? "AI Assistant" : "Patient",
        content: request.content,
        timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      };

      // Add the message to mock data for persistence during session
      if (!mockMessages[request.chatId]) {
        mockMessages[request.chatId] = [];
      }
      mockMessages[request.chatId].push(newMessage);

      return newMessage;
    } catch (error) {
      console.error("Error sending message:", error);
      throw new Error("Failed to send message");
    }
  }

  /**
   * Get list of recent chat sessions
   */
  async getRecentChats(): Promise<ChatSession[]> {
    try {
      // Return mock chat sessions
      return mockChatSessions;
    } catch (error) {
      console.error("Error fetching recent chats:", error);
      throw new Error("Failed to fetch recent chats");
    }
  }

  /**
   * Mark messages as read for a specific chat
   */
  async markAsRead(chatId: number): Promise<void> {
    try {
      // This would make an API call to mark messages as read
      console.log(`Marking chat ${chatId} as read`);
    } catch (error) {
      console.error("Error marking messages as read:", error);
      throw new Error("Failed to mark messages as read");
    }
  }


}

// Export singleton instance
export const chatService = ChatService.getInstance();
