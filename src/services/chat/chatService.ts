import type { ChatMessage } from "@/pages/provider-portal/patients/ChatBot";
import { AiConnectionControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

export interface ChatSession {
  id: number;
  name: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  avatarUrl?: string;
  patientId?: string;
  providerId?: string;
}

export interface SendMessageRequest {
  content: string;
  chatId: number;
  senderId: string;
  senderType: "Patient" | "Provider" | "AI";
}

export interface ChatHistoryResponse {
  messages: ChatMessage[];
  hasMore: boolean;
  nextCursor?: string;
}

/**
 * Service class for handling chat-related API calls and operations
 */
export class ChatService {
  private static instance: ChatService;
  private tenantId: string;

  private constructor() {
    this.tenantId = GetTenantId();
  }

  public static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  /**
   * Get chat history for a specific chat session
   */
  async getChatHistory(chatId: number, cursor?: string): Promise<ChatHistoryResponse> {
    try {
      // For now, using the existing chatbot history endpoint
      // This would need to be adapted based on your actual API structure
      const response = await AiConnectionControllerService.getChatbotHistory({
        xTenantId: this.tenantId,
      });

      // Transform the response to match our ChatMessage interface
      // This is a placeholder - you'll need to adapt based on actual API response
      const mockMessages: ChatMessage[] = [
        {
          id: "1",
          sender: "Patient",
          senderName: "John Patient",
          content: "Hello, I have a question about my medication.",
          timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
        },
        {
          id: "2",
          sender: "Provider",
          senderName: "Dr. Smith",
          content: "Hello John! I'd be happy to help. What's your question?",
          timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
        },
      ];

      return {
        messages: mockMessages,
        hasMore: false,
      };
    } catch (error) {
      console.error("Error fetching chat history:", error);
      throw new Error("Failed to fetch chat history");
    }
  }

  /**
   * Send a message in a chat session
   */
  async sendMessage(request: SendMessageRequest): Promise<ChatMessage> {
    try {
      // For AI chat, use the existing AI nurse endpoint
      if (request.senderType === "AI") {
        const response = await AiConnectionControllerService.llmauChatWithMyNurse({
          providerId: request.senderId,
          requestBody: {
            content: request.content,
            role: "user",
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          },
          xTenantId: this.tenantId,
        });

        // Transform response to ChatMessage
        return {
          id: `msg_${Date.now()}`,
          sender: "AI",
          senderName: "AI Nurse",
          content: response.data || "AI response received",
          timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
        };
      }

      // For regular provider-patient messages, create a new message object
      // In a real implementation, this would make an API call to save the message
      const newMessage: ChatMessage = {
        id: `msg_${Date.now()}`,
        sender: request.senderType,
        senderName: request.senderType === "Provider" ? "Dr. Smith" : "Patient",
        content: request.content,
        timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      };

      return newMessage;
    } catch (error) {
      console.error("Error sending message:", error);
      throw new Error("Failed to send message");
    }
  }

  /**
   * Get list of recent chat sessions
   */
  async getRecentChats(): Promise<ChatSession[]> {
    try {
      // This would typically fetch from a real API endpoint
      // For now, returning mock data
      const mockChats: ChatSession[] = [
        {
          id: 1,
          name: "John Patient",
          lastMessage: "Thank you for the help!",
          timestamp: "10:45 AM",
          unreadCount: 2,
          avatarUrl: "",
          patientId: "patient_1",
        },
        {
          id: 2,
          name: "Jane Doe",
          lastMessage: "When is my next appointment?",
          timestamp: "Yesterday",
          unreadCount: 0,
          avatarUrl: "",
          patientId: "patient_2",
        },
        {
          id: 3,
          name: "AI Assistant",
          lastMessage: "Patient summary updated",
          timestamp: "9:00 AM",
          unreadCount: 1,
          avatarUrl: "",
        },
      ];

      return mockChats;
    } catch (error) {
      console.error("Error fetching recent chats:", error);
      throw new Error("Failed to fetch recent chats");
    }
  }

  /**
   * Mark messages as read for a specific chat
   */
  async markAsRead(chatId: number): Promise<void> {
    try {
      // This would make an API call to mark messages as read
      console.log(`Marking chat ${chatId} as read`);
    } catch (error) {
      console.error("Error marking messages as read:", error);
      throw new Error("Failed to mark messages as read");
    }
  }

  /**
   * Get chatbot reports for a patient
   */
  async getChatbotReports(patientUuid: string, year: number, month: number) {
    try {
      return await AiConnectionControllerService.getChatbotReports({
        patientUuid,
        year,
        month,
        xTenantId: this.tenantId,
      });
    } catch (error) {
      console.error("Error fetching chatbot reports:", error);
      throw new Error("Failed to fetch chatbot reports");
    }
  }
}

// Export singleton instance
export const chatService = ChatService.getInstance();
