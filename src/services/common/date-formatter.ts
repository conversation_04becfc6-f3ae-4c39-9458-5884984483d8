import { addDays, addHours, addMinutes, endOfDay, format, parse, startOfDay } from "date-fns";
import { fromZonedTime, toZonedTime } from "date-fns-tz";

export const BASIC_DATE_FORMAT = "yyyy-MM-dd";
export const BASIC_DATE_TIME_FORMAT = "yyyy-MM-ddTHH:mm:ss";
export const BASIC_TIME_FORMAT = "HH:mm:ss";
export const BASIC_DATE_FORMAT_MM_DD_YYYY = "MM-dd-yyyy";
export const DATE_FORMAT_DD_MM_YY = "dd/MM/yyyy";

const localTz = Intl.DateTimeFormat().resolvedOptions().timeZone;

export type AllTimezones =
  | "PST"
  | "EST"
  | "CST"
  | "MST"
  | "AST"
  | "HST"
  | "EDT"
  | "PDT"
  | "CDT"
  | "ADT"
  | "MDT"
  | "IST"
  | "AKDT"
  | "AKST"
  | "UTC"
  | "SGT"
  | undefined;

export const TimeZoneMap: { [key: string]: string } = {
  EST: "EST",
  MST: "MST",
  HST: "HST",
  PST: "America/Los_Angeles",
  CST: "America/Chicago",
  IST: "Asia/Calcutta",
  AST: "America/Anchorage",
};

export const TimeZoneMapLocalToMoment: { [key: string]: string } = {
  EST: "EST",
  MST: "MST",
  HST: "HST",
  "America/Los_Angeles": "PST",
  "America/Chicago": "CST",
  "Asia/Calcutta": "IST",
  "America/Anchorage": "AST",
};

export const formatDateMMddyyyyToUTC = (dateString: string) => {
  // eslint-disable-next-line no-constant-condition
  if (!dateString || "-") {
    return;
  }
  // dateString format : yyyy-MM-dd HH:mm:ss (return by date picker)
  const updatedDateAndTime = fromZonedTime(dateString, "UTC"); ///Return in 2024-11-08T00:00:00.000Z format
  return updatedDateAndTime;
};

export const formatUTCDateTimeInLocalTZ = (dateString: string) => {
  // eslint-disable-next-line no-constant-condition
  if (!dateString || "-") {
    return;
  }

  // dateString format is 2024-11-08T00:00:00.000Z
  const dateAndTimeInLocalTZ = toZonedTime(dateString, localTz);
  const MMDDYYDate = format(dateAndTimeInLocalTZ, BASIC_DATE_FORMAT_MM_DD_YYYY);
  return { dateAndTime: dateAndTimeInLocalTZ, MMDDYY: MMDDYYDate };
};

//** Input format : "2024-12-27T03:45:00Z" and OutPut format "Thursday, 4 Sept - 10:00 am – 11:00 am"//
export const formatInEEEE_d_MMM_h_mm_a = (dateStr: string) => {
  const date = new Date(dateStr);

  // Format the start time
  const formattedStart = format(date, "EEEE, d MMM - h:mm a");

  // Calculate the end time (1 hour later)
  const endTime = addHours(date, 1);
  const formattedEnd = format(endTime, "h:mm a");

  // Combine both parts
  const finalFormat = `${formattedStart} – ${formattedEnd}`;

  return finalFormat;
};

// Function to get start and end of day in UTC from a selected date & timezone
export const getStartAndEndOfDayUTC = (selectedDate: string, timeZone: string) => {
  // Parse the selected date from "MM-dd-yyyy" format
  const parsedDate = parse(selectedDate, "MM-dd-yyyy", new Date());

  // Get start and end of the day in the given time zone
  const startOfDayZoned = fromZonedTime(startOfDay(parsedDate), timeZone);
  const endOfDayZoned = fromZonedTime(endOfDay(parsedDate), timeZone);

  // Convert to UTC
  const startOfDayUTC = toZonedTime(startOfDayZoned, "UTC");
  const endOfDayUTC = toZonedTime(endOfDayZoned, "UTC");

  return {
    startUTC: format(startOfDayUTC, "yyyy-MM-dd'T'HH:mm:ss'X'") + "Z",
    endUTC: format(endOfDayUTC, "yyyy-MM-dd'T'HH:mm:ss'X'") + "Z",
  };
};

export const getStartAndEndOfDayUTC2 = (selectedDate: string, timeZone: string) => {
  if (!selectedDate) {
    return {
      startUTC: undefined,
      endUTC: undefined,
    };
  }

  const now = new Date(); // Current time in UTC

  const todayInTimeZone = toZonedTime(now, timeZone); // Convert current UTC time to given time zone

  // Parse selected date from "MM-dd-yyyy" format
  const parsedDate = parse(selectedDate, "MM-dd-yyyy", new Date());
  // Check if selected date is today

  const isToday = format(parsedDate, "yyyy-MM-dd") === format(todayInTimeZone, "yyyy-MM-dd");

  // If today, add 2 minutes to the current UTC time; otherwise, use start of the day in the given time zone
  const startOfDayZoned = isToday
    ? addMinutes(now, 2) // Add 2 minutes to the current UTC time
    : fromZonedTime(startOfDay(parsedDate), timeZone);

  // End of the selected day in given time zone
  // const endOfDayZoned = fromZonedTime(endOfDay(parsedDate), timeZone);
  const endOfDayDate = startOfDay(addDays(parsedDate, 1)); // Next day's midnight
  const endOfDayUTC = toZonedTime(fromZonedTime(endOfDayDate, timeZone), "UTC");

  // // Convert to UTC
  const startOfDayUTC = toZonedTime(startOfDayZoned, "UTC");
  // const endOfDayUTC = toZonedTime(endOfDayZoned, "UTC");

  return {
    startUTC: format(startOfDayUTC, "yyyy-MM-dd'T'HH:mm:ss") + "Z",
    endUTC: format(endOfDayUTC, "yyyy-MM-dd'T'HH:mm:ss") + "Z",
  };
};
