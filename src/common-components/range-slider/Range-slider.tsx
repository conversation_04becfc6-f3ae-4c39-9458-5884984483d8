import React, { useEffect, useState } from "react";

import { Tooltip } from "@mui/material";
import Box from "@mui/material/Box";
import Slider from "@mui/material/Slider";
import Typography from "@mui/material/Typography";

import { VitalRange, VitalReference } from "@/sdk/requests/types.gen";

interface RangeSliderProps {
  onChange: (values: { [key: string]: VitalRange[] | number[] }) => void;
  title?: string;
  name: string;
  vitalReference?: VitalReference;
  min?: number;
  max?: number;
  step?: number;
}

// Custom Tooltip component
const ValueLabelComponent = (props: { children: React.ReactElement; open: boolean; value: number }) => {
  const { children, open, value } = props;

  // Format the value to have only one decimal place
  const formattedValue = Number(value.toFixed(1));

  return (
    <Tooltip
      open={open}
      placement="top"
      title={formattedValue}
      PopperProps={{
        disablePortal: true,
        modifiers: [
          {
            name: "preventOverflow",
            options: {
              boundary: "scrollParent",
            },
          },
          {
            name: "offset",
            options: {
              offset: [0, 8],
            },
          },
        ],
        sx: {
          zIndex: 1,
        },
      }}
      componentsProps={{
        tooltip: {
          sx: {
            backgroundColor: "white",
            color: "black",
            fontSize: "0.875rem",
            padding: "4px 8px",
            borderRadius: "4px",
            height: "30px",
            border: "1px solid #E8EBEC",
            boxShadow: "0px 2px 6px rgba(0, 0, 0, 0.2)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          },
        },
      }}
    >
      {children}
    </Tooltip>
  );
};

const organizeBloodPressureRanges = (ranges: VitalRange[], type: "SYSTOLIC" | "DIASTOLIC") => {
  // Filter ranges based on type - only get ranges with the specific type suffix
  const filteredRanges = ranges.filter((range) => range.rangeType?.includes(`_${type}`));

  // Sort ranges based on their min values
  const sortedRanges = filteredRanges.sort((a, b) => (a.min || 0) - (b.min || 0));

  // Get the ranges in order - using specific blood pressure type suffixes
  const lowModerate = sortedRanges.find((range) => range.rangeType === `LOW_MODERATE_${type}`);
  const normal = sortedRanges.find((range) => range.rangeType === `NORMAL_${type}`);
  const highModerate = sortedRanges.find((range) => range.rangeType === `HIGH_MODERATE_${type}`);
  // Create exactly 4 pointers
  const values: number[] = [];

  // First pointer: LOW_MODERATE.min
  if (lowModerate?.min !== undefined) {
    values.push(lowModerate.min);
  }

  // Second pointer: LOW_MODERATE.max (which should be same as NORMAL.min)
  if (lowModerate?.max !== undefined) {
    values.push(lowModerate.max);
  }

  // Third pointer: NORMAL.max (which should be same as HIGH_MODERATE.min)
  if (normal?.max !== undefined) {
    values.push(normal.max);
  }

  // Fourth pointer: HIGH_MODERATE.max
  if (highModerate?.max !== undefined) {
    values.push(highModerate.max);
  }

  // Make sure we have 4 values with fallbacks if needed
  if (values.length < 4 && type === "SYSTOLIC") {
    // Default values for systolic if missing
    const defaults = [90, 100, 130, 140];
    while (values.length < 4) {
      values.push(defaults[values.length]);
    }
  } else if (values.length < 4 && type === "DIASTOLIC") {
    // Default values for diastolic if missing
    const defaults = [60, 70, 80, 90];
    while (values.length < 4) {
      values.push(defaults[values.length]);
    }
  }

  return values;
};

const organizeVitalRanges = (ranges: VitalRange[]) => {
  // Sort ranges based on their min values
  const sortedRanges = ranges.sort((a, b) => (a.min || 0) - (b.min || 0));

  // Get the ranges in order
  const lowModerate = sortedRanges.find((range) => range.rangeType === "LOW_MODERATE");
  const normal = sortedRanges.find((range) => range.rangeType === "NORMAL");
  const highModerate = sortedRanges.find((range) => range.rangeType === "HIGH_MODERATE");

  // Create exactly 4 pointers
  const values: number[] = [];

  // First pointer: LOW_MODERATE.min
  if (lowModerate?.min !== undefined) {
    values.push(lowModerate.min);
  }

  // Second pointer: LOW_MODERATE.max (which should be same as NORMAL.min)
  if (lowModerate?.max !== undefined) {
    values.push(lowModerate.max);
  }

  // Third pointer: NORMAL.max (which should be same as HIGH_MODERATE.min)
  if (normal?.max !== undefined) {
    values.push(normal.max);
  }

  // Fourth pointer: HIGH_MODERATE.max
  if (highModerate?.max !== undefined) {
    values.push(highModerate.max);
  }

  return values;
};

const getRailSections = (values: number[], maxValue: number = 200) => {
  const sortedValues = [...values].sort((a, b) => a - b);
  const sections = [];

  // Define colors for different range types
  const colors = {
    critical: "#EA483F", // Red for critical ranges
    lowModerate: "#FFA07D", // Light red for moderate ranges
    normal: "#7FD167", // Green for normal range
    highModerate: "#FFA07D", // Light red for moderate ranges
  };

  // First section: 0 to LOW_MODERATE.min (CRITICAL)
  if (sortedValues[0] > 0) {
    sections.push({
      left: "0%",
      width: `${(sortedValues[0] / maxValue) * 100}%`,
      backgroundColor: colors.critical,
    });
  }

  // Second section: LOW_MODERATE.min to NORMAL.min (LOW_MODERATE)
  if (sortedValues.length >= 2) {
    sections.push({
      left: `${(sortedValues[0] / maxValue) * 100}%`,
      width: `${((sortedValues[1] - sortedValues[0]) / maxValue) * 100}%`,
      backgroundColor: colors.lowModerate,
    });
  }

  // Third section: NORMAL.min to NORMAL.max (NORMAL)
  if (sortedValues.length >= 3) {
    sections.push({
      left: `${(sortedValues[1] / maxValue) * 100}%`,
      width: `${((sortedValues[2] - sortedValues[1]) / maxValue) * 100}%`,
      backgroundColor: colors.normal,
    });
  }

  // Fourth section: NORMAL.max to HIGH_MODERATE.max (HIGH_MODERATE)
  if (sortedValues.length >= 4) {
    sections.push({
      left: `${(sortedValues[2] / maxValue) * 100}%`,
      width: `${((sortedValues[3] - sortedValues[2]) / maxValue) * 100}%`,
      backgroundColor: colors.highModerate,
    });
  }

  // Fifth section: HIGH_MODERATE.max to max (CRITICAL)
  if (sortedValues.length >= 4 && sortedValues[3] < maxValue) {
    sections.push({
      left: `${(sortedValues[3] / maxValue) * 100}%`,
      width: `${((maxValue - sortedValues[3]) / maxValue) * 100}%`,
      backgroundColor: colors.critical,
    });
  }

  return sections;
};

const getWeightRailSections = (values: number[], minValue: number = 0, maxValue: number = 200) => {
  if (values.length !== 2) return []; // Expecting [normal.min, normal.max]

  const sortedValues = [...values].sort((a, b) => a - b);
  const normalMin = sortedValues[0];
  const normalMax = sortedValues[1];
  const sections = [];

  const colors = {
    critical: "#EA483F", // Red
    normal: "#7FD167", // Green
  };

  // 1. Critical Low section: minValue to normalMin
  if (normalMin > minValue) {
    sections.push({
      left: `${(minValue / (maxValue - minValue)) * 100}%`,
      width: `${((normalMin - minValue) / (maxValue - minValue)) * 100}%`,
      backgroundColor: colors.critical,
    });
  }

  // 2. Normal section: normalMin to normalMax
  if (normalMax > normalMin) {
    sections.push({
      left: `${((normalMin - minValue) / (maxValue - minValue)) * 100}%`,
      width: `${((normalMax - normalMin) / (maxValue - minValue)) * 100}%`,
      backgroundColor: colors.normal,
    });
  }

  // 3. Critical High section: normalMax to maxValue
  if (maxValue > normalMax) {
    sections.push({
      left: `${((normalMax - minValue) / (maxValue - minValue)) * 100}%`,
      width: `${((maxValue - normalMax) / (maxValue - minValue)) * 100}%`,
      backgroundColor: colors.critical,
    });
  }

  return sections;
};

// Transform slider values back to VitalRange format
const transformValuesToRanges = (values: number[], ranges: VitalRange[]): VitalRange[] => {
  if (!values.length || !ranges.length) return [];

  // Sort values to ensure correct order
  const sortedValues = [...values].sort((a, b) => a - b);

  // Find original ranges by rangeType
  const lowModerate = ranges.find(
    (range) => range.rangeType === "LOW_MODERATE" || range.rangeType?.includes("LOW_MODERATE")
  );
  const normal = ranges.find((range) => range.rangeType === "NORMAL" || range.rangeType?.includes("NORMAL"));
  const highModerate = ranges.find(
    (range) => range.rangeType === "HIGH_MODERATE" || range.rangeType?.includes("HIGH_MODERATE")
  );
  const critical = ranges.find((range) => range.rangeType === "CRITICAL" || range.rangeType?.includes("CRITICAL"));

  // Create new ranges with updated values
  const updatedRanges: VitalRange[] = [];

  // Assuming values are in order: lowModerate.min, lowModerate.max/normal.min, normal.max/highModerate.min, highModerate.max
  if (lowModerate && sortedValues.length >= 2) {
    updatedRanges.push({
      ...lowModerate,
      min: sortedValues[0],
      max: sortedValues[1],
    });
  }

  if (normal && sortedValues.length >= 3) {
    updatedRanges.push({
      ...normal,
      min: sortedValues[1],
      max: sortedValues[2],
    });
  }

  if (highModerate && sortedValues.length >= 4) {
    updatedRanges.push({
      ...highModerate,
      min: sortedValues[2],
      max: sortedValues[3],
    });
  }

  if (critical && sortedValues.length >= 1) {
    // For critical ranges, use min: 0 and max: lowModerate.min, or highModerate.max to maxValue
    // There might be two critical ranges (very low and very high)
    const criticalMin = critical.min || 0;
    if (criticalMin === 0 || criticalMin < sortedValues[0]) {
      updatedRanges.push({
        ...critical,
        min: 0,
        max: sortedValues[0],
      });
    } else {
      updatedRanges.push({
        ...critical,
        min: sortedValues[sortedValues.length - 1],
        max: 200, // Using the default max value
      });
    }
  }

  return updatedRanges;
};

// Transform blood pressure slider values back to VitalRange format
const transformBPValuesToRanges = (
  systolicValues: number[],
  diastolicValues: number[],
  ranges: VitalRange[]
): VitalRange[] => {
  if ((!systolicValues.length && !diastolicValues.length) || !ranges.length) return [];

  // Find the ranges by their specific type suffixes
  const systolicRanges = ranges.filter((range) => range.rangeType?.includes("_SYSTOLIC"));
  const diastolicRanges = ranges.filter((range) => range.rangeType?.includes("_DIASTOLIC"));

  // Create updated ranges by updating the respective ranges with new values
  const updatedRanges: VitalRange[] = [];

  // Update systolic ranges
  if (systolicValues.length >= 4) {
    const sortedValues = [...systolicValues].sort((a, b) => a - b);

    // Find specific systolic ranges
    const lowModerateSystolic = systolicRanges.find((range) => range.rangeType === "LOW_MODERATE_SYSTOLIC");
    const normalSystolic = systolicRanges.find((range) => range.rangeType === "NORMAL_SYSTOLIC");
    const highModerateSystolic = systolicRanges.find((range) => range.rangeType === "HIGH_MODERATE_SYSTOLIC");
    const criticalSystolic = systolicRanges.find((range) => range.rangeType === "CRITICAL_SYSTOLIC");

    // Update ranges with new values
    if (lowModerateSystolic) {
      updatedRanges.push({
        ...lowModerateSystolic,
        min: sortedValues[0],
        max: sortedValues[1],
      });
    }

    if (normalSystolic) {
      updatedRanges.push({
        ...normalSystolic,
        min: sortedValues[1],
        max: sortedValues[2],
      });
    }

    if (highModerateSystolic) {
      updatedRanges.push({
        ...highModerateSystolic,
        min: sortedValues[2],
        max: sortedValues[3],
      });
    }

    if (criticalSystolic) {
      updatedRanges.push({
        ...criticalSystolic,
        min: sortedValues[3],
        max: criticalSystolic.max || 180,
      });
    }
  }

  // Update diastolic ranges
  if (diastolicValues.length >= 4) {
    const sortedValues = [...diastolicValues].sort((a, b) => a - b);

    // Find specific diastolic ranges
    const lowModerateDiastolic = diastolicRanges.find((range) => range.rangeType === "LOW_MODERATE_DIASTOLIC");
    const normalDiastolic = diastolicRanges.find((range) => range.rangeType === "NORMAL_DIASTOLIC");
    const highModerateDiastolic = diastolicRanges.find((range) => range.rangeType === "HIGH_MODERATE_DIASTOLIC");
    const criticalDiastolic = diastolicRanges.find((range) => range.rangeType === "CRITICAL_DIASTOLIC");

    // Update ranges with new values
    if (lowModerateDiastolic) {
      updatedRanges.push({
        ...lowModerateDiastolic,
        min: sortedValues[0],
        max: sortedValues[1],
      });
    }

    if (normalDiastolic) {
      updatedRanges.push({
        ...normalDiastolic,
        min: sortedValues[1],
        max: sortedValues[2],
      });
    }

    if (highModerateDiastolic) {
      updatedRanges.push({
        ...highModerateDiastolic,
        min: sortedValues[2],
        max: sortedValues[3],
      });
    }

    if (criticalDiastolic) {
      updatedRanges.push({
        ...criticalDiastolic,
        min: sortedValues[3],
        max: criticalDiastolic.max || 120,
      });
    }
  }

  return updatedRanges;
};

export default function RangeSlider({
  onChange = () => {},
  title = "Range Slider",
  name,
  vitalReference,
  min = 0,
  max = 200,
  step = 1,
}: RangeSliderProps) {
  const [systolicValues, setSystolicValues] = useState<number[]>([]);
  const [diastolicValues, setDiastolicValues] = useState<number[]>([]);
  const [singleValues, setSingleValues] = useState<number[]>([]);
  useEffect(() => {
    if (vitalReference?.vitalRanges) {
      if (vitalReference.vitalType === "Blood Pressure") {
        // For blood pressure, organize systolic and diastolic ranges separately
        const systolicRanges = organizeBloodPressureRanges(vitalReference.vitalRanges, "SYSTOLIC");
        const diastolicRanges = organizeBloodPressureRanges(vitalReference.vitalRanges, "DIASTOLIC");

        setSystolicValues(systolicRanges);
        setDiastolicValues(diastolicRanges);
      } else if (vitalReference.vitalType === "Weight") {
        // For Weight, find the NORMAL range and use its min/max
        const normalRange = vitalReference.vitalRanges.find((range) => range.rangeType === "NORMAL");
        if (normalRange?.min !== undefined && normalRange?.max !== undefined) {
          setSingleValues([normalRange.min, normalRange.max]);
        } else {
          // Fallback if NORMAL range is missing or incomplete for Weight
          setSingleValues([40, 60]); // Example fallback
        }
      } else {
        // For other vital types, use the organizeVitalRanges function (4 pointers)
        const values = organizeVitalRanges(vitalReference.vitalRanges);
        setSingleValues(values);
      }
    }
  }, [vitalReference]);

  const handleSystolicChange = (_event: Event, newValue: number[]) => {
    setSystolicValues(newValue);
    if (vitalReference?.vitalRanges) {
      const updatedRanges = transformBPValuesToRanges(newValue, diastolicValues, vitalReference.vitalRanges);
      onChange({ [name]: updatedRanges });
    } else {
      onChange({ [`${name}_systolic`]: newValue });
    }
  };

  const handleDiastolicChange = (_event: Event, newValue: number[]) => {
    setDiastolicValues(newValue);
    if (vitalReference?.vitalRanges) {
      const updatedRanges = transformBPValuesToRanges(systolicValues, newValue, vitalReference.vitalRanges);
      onChange({ [name]: updatedRanges });
    } else {
      onChange({ [`${name}_diastolic`]: newValue });
    }
  };

  const handleSingleChange = (_event: Event, newValue: number[]) => {
    setSingleValues(newValue);
    if (vitalReference?.vitalRanges) {
      if (vitalReference.vitalType === "Weight") {
        // Handle Weight: update the NORMAL range
        const normalRange = vitalReference.vitalRanges.find((range) => range.rangeType === "NORMAL");
        const updatedRanges: VitalRange[] = [];
        if (normalRange && newValue.length === 2) {
          updatedRanges.push({
            ...normalRange,
            min: newValue[0],
            max: newValue[1],
          });
        }
        // Also include other potential ranges if they exist, unmodified
        vitalReference.vitalRanges.forEach((range) => {
          if (range.rangeType !== "NORMAL") {
            updatedRanges.push(range);
          }
        });
        onChange({ [name]: updatedRanges });
      } else {
        // Handle other vital types (4 pointers)
        const updatedRanges = transformValuesToRanges(newValue, vitalReference.vitalRanges);
        onChange({ [name]: updatedRanges });
      }
    } else {
      onChange({ [name]: newValue });
    }
  };

  const renderSlider = (
    values: number[],
    onChange: (event: Event, value: number[]) => void,
    label: string,
    sliderMin: number = min,
    sliderMax: number = max
  ) => {
    const isWeight = vitalReference?.vitalType === "Weight";
    const sections = isWeight
      ? getWeightRailSections(values, sliderMin, sliderMax)
      : getRailSections(values, sliderMax);

    return (
      <Box sx={{ width: "100%", position: "relative", paddingTop: "20px" }}>
        {vitalReference?.vitalType === "Blood Pressure" && (
          <Typography mb={3} id={`track-false-range-slider-${label}`} gutterBottom>
            {label}
          </Typography>
        )}
        <Box
          sx={{ position: "relative", height: "8px", backgroundColor: "lightgray", borderRadius: "4px", top: "20px" }}
        >
          {sections.map((section, index) => (
            <Box
              key={index}
              sx={{
                position: "absolute",
                height: "100%",
                borderRadius: "4px",
                ...section,
              }}
            />
          ))}
        </Box>
        <Slider
          step={isWeight ? 0.1 : step}
          track={false}
          aria-labelledby={`track-false-range-slider-${label}`}
          value={values}
          onChange={(event: Event, value: number | number[]) => onChange(event, value as number[])}
          valueLabelDisplay="on"
          min={sliderMin}
          max={sliderMax}
          components={{
            ValueLabel: ValueLabelComponent,
          }}
          sx={{
            "& .MuiSlider-thumb": {
              width: "16px",
              height: "16px",
              backgroundColor: "teal",
              "&:before": {
                boxShadow: "0 2px 4px rgba(185, 38, 38, 0.3)",
              },
            },
            "& .MuiSlider-rail": {
              display: "none",
            },
            "& .MuiSlider-track": {
              display: "none",
            },
          }}
        />
      </Box>
    );
  };

  return (
    <Box>
      {vitalReference?.vitalType === "Blood Pressure" ? (
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Box sx={{ flex: 1, paddingRight: "8px" }}>
            {renderSlider(systolicValues, handleSystolicChange, "Systolic")}
          </Box>
          <Box sx={{ flex: 1, paddingLeft: "8px" }}>
            {renderSlider(diastolicValues, handleDiastolicChange, "Diastolic")}
          </Box>
        </Box>
      ) : (
        renderSlider(singleValues, handleSingleChange, title)
      )}
    </Box>
  );
}
