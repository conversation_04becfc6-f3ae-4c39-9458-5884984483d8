import { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";

import { InputLabel } from "@mui/material";
import { LocalizationProvider, TimePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { TimeClock } from "@mui/x-date-pickers/TimeClock";

import { enIN } from "date-fns/locale";
import { startCase } from "lodash";

interface TimepickerProps {
  name: string;
  label?: string;
  isRequired?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export const Timepicker = forwardRef<HTMLDivElement, TimepickerProps>(
  ({ name, label, isRequired = false, disabled = false, placeholder }) => {
    const { control, setError, clearErrors } = useFormContext();

    return (
      <Controller
        name={name}
        control={control}
        defaultValue={null}
        render={({ field, fieldState: { error } }) => (
          <>
            {label && (
              <InputLabel
                sx={{
                  mb: 1,
                  fontSize: "14px",
                  fontWeight: "500",
                  color: "#515C5F",
                }}
              >
                {label || startCase(name)}&nbsp;
                {isRequired && <span style={{ color: "#D32F2F" }}>*</span>}
              </InputLabel>
            )}

            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enIN}>
              <TimePicker
                {...field}
                ampm
                onChange={(value) => {
                  if (!value || value.toString() === "Invalid Date") {
                    setError(name, { message: "Invalid time" });
                  } else {
                    clearErrors(name);
                    field.onChange(value);
                  }
                }}
                disabled={disabled}
                viewRenderers={{
                  hours: (params) => <TimeClock {...params} />,
                  minutes: (params) => <TimeClock {...params} />,
                }}
                slotProps={{
                  textField: {
                    placeholder: placeholder || `Select ${startCase(name)}`,
                    error: !!error,
                    fullWidth: true,
                    sx: {
                      "& .MuiInputBase-root": {
                        height: "43px",
                        background: "#FFFFFF",
                        border: "1px solid #E8EBEC",
                        borderRadius: "8px",
                      },
                      "& .MuiInputBase-input": {
                        padding: "0 12px",
                        fontSize: "14px",
                        color: "#515C5F",
                      },
                      "& .MuiInputAdornment-root button": {
                        color: "#515C5F",
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>

            {error && (
              <InputLabel error sx={{ mt: 0.5, fontSize: "12px", whiteSpace: "normal" }}>
                {error.message}
              </InputLabel>
            )}
          </>
        )}
      />
    );
  }
);
