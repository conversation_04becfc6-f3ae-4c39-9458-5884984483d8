/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from "react";

import { useMediaQuery } from "@mui/system";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";

import { theme } from "../../utils/theme";
import "./widgets/date-calender-widgets.css";

export interface DateCalenderProps {
  name?: string;
  styles?: React.CSSProperties;
  useCustomStyle?: boolean;
  value?: Date | null;
  maxDate?: Date;
  minDate?: Date;

  onChange: (date: Date | null) => void;
  hasError?: boolean;
  errorMessage?: string;
  disableFuture?: boolean;
  label?: string;
  disablePast?: boolean;
  bgWhite?: boolean;
}

const DateCalender = (props: DateCalenderProps) => {
  const { value, onChange, maxDate, minDate, disableFuture, disablePast } = props;

  const below1230 = useMediaQuery("(max-width:1230px)");

  const [inputValue, setInputValue] = useState<Date | null>(value ? new Date(value) : null);

  const handleChange = (date: Date | null) => {
    setInputValue(date);
    onChange(date);
  };

  useEffect(() => {
    setInputValue(value ? new Date(value) : null);
  }, [value]);

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DateCalendar
        value={inputValue}
        onChange={handleChange}
        maxDate={maxDate}
        minDate={minDate}
        disableFuture={disableFuture}
        disablePast={disablePast}
        sx={{
          position: "absolute",
          width: "100%",
          // height: "500% !important",
          paddingX: "10px",
          paddingY: "5px",
          "& .MuiPickersCalendarHeader-root": {
            borderRadius: "10px",

            border: `0.5px solid ${theme.palette.grey[400]}`,
            boxShadow: `0 1px 6px 0 ${theme.palette.grey[400]}`,
            color: theme.palette.primary.main,
            fontWeight: 700,
            width: "100%",
          },
          "& .MuiDayCalendar-root": {
            height: "260px",
            border: `0.5px solid ${theme.palette.grey[400]}`,
            borderRadius: "10px",
            boxShadow: `1px 1px 8px 0px ${theme.palette.grey[400]}`,
          },
          "& .css-1chuxo2-MuiPickersCalendarHeader-label": {
            fontSize: below1230 ? "12px" : "14px",
          },
        }}
        className="date-calendar-border"
      />
    </LocalizationProvider>
  );
};

export default DateCalender;
