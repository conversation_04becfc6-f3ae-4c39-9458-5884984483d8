import React from "react";

import { Box, Chip, Typography } from "@mui/material";

interface CustomSelectProps {
  label: string;
  options: string[];
  selectedValue: string | string[];
  onSelect: (value: string | string[]) => void;
  multiSelect?: boolean;
  isDisabled?: boolean;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  label,
  options,
  selectedValue,
  onSelect,
  multiSelect = false,
  isDisabled = false,
}) => {
  const handleSelection = (option: string) => {
    if (isDisabled) return;

    if (!multiSelect) {
      onSelect(option);
      return;
    }

    const currentSelections = Array.isArray(selectedValue) ? [...selectedValue] : [];

    if (currentSelections.includes(option)) {
      onSelect(currentSelections.filter((item) => item !== option));
    } else {
      onSelect([...currentSelections, option]);
    }
  };

  const isSelected = (option: string) => {
    if (Array.isArray(selectedValue)) {
      return selectedValue.includes(option);
    }
    return selectedValue === option;
  };

  return (
    <Box
      pl={2}
      sx={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "#FFFFFF",
        width: "100%",
        height: "auto",
        minHeight: "42px",
        gap: "8px",
        border: "1px solid #E8EBEC",
        borderRadius: "8px",
        padding: "5px 10px",
        justifyContent: "space-between",
        cursor: isDisabled ? "not-allowed" : "default",
      }}
    >
      <Typography
        sx={{
          fontFamily: "Roboto",
          fontWeight: 500,
          fontSize: "14px",
          lineHeight: "120%",
          letterSpacing: "0%",
          color: "#212D30",
        }}
      >
        {label}
      </Typography>
      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          maxWidth: "100%",
          gap: "8px",
          border: "none",
          borderRadius: "8px",
          padding: "0",
          justifyContent: "flex-end",
        }}
      >
        {options.map((option) => (
          <Chip
            key={option}
            label={option}
            onClick={() => handleSelection(option)}
            variant={isSelected(option) ? "filled" : "outlined"}
            disabled={isDisabled}
            sx={{
              background: isSelected(option) ? "#E0E7FF" : "transparent",
              color: isSelected(option) ? "#2D7AE5" : "#212D30",
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "12px",
              lineHeight: "100%",
              letterSpacing: "0%",
              borderRadius: "25px",
              height: "25px",
              padding: "0px",
              pointerEvents: isDisabled ? "none" : "auto",
              "& .MuiChip-label": {
                opacity: 1,
              },
              "&.Mui-disabled": {
                background: isSelected(option) ? "#E0E7FF" : "transparent",
                color: isSelected(option) ? "#2D7AE5" : "#212D30",
                opacity: 1,
              },
            }}
            style={{ border: "none" }}
          />
        ))}
      </Box>
    </Box>
  );
};

export default CustomSelect;
