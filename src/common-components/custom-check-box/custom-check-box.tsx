import React, { useEffect, useRef, useState } from "react";

import { Box, Button, Grid, Typography } from "@mui/material";
import Checkbox from "@mui/material/Checkbox";

// import { StatusColorMap } from "@/constants/status";
import { toCamelCase } from "@/utils/toCamelCase";

import { theme } from "../../utils/theme";

export type CheckedArray = {
  checked: boolean;
  color?: string;
  key: string;
  value?: string; // Make this optional
  colorcode?: string;
};

type CustomCheckBoxType = {
  options: CheckedArray[];
  onChange: (updatedArray: CheckedArray[]) => void;
  maxHeight?: string;
  initialVisibleItems?: number;
};

function lightenColor(hex: string, percent: number): string {
  const num = parseInt(hex.slice(1), 16);
  const r = (num >> 16) + Math.round((255 - (num >> 16)) * (percent / 100));
  const g = ((num >> 8) & 0x00ff) + Math.round((255 - ((num >> 8) & 0x00ff)) * (percent / 100));
  const b = (num & 0x0000ff) + Math.round((255 - (num & 0x0000ff)) * (percent / 100));
  const newR = r > 255 ? 255 : r;
  const newG = g > 255 ? 255 : g;
  const newB = b > 255 ? 255 : b;
  return `#${((newR << 16) | (newG << 8) | newB).toString(16).padStart(6, "0")}`;
}

const CustomCheckBox: React.FC<CustomCheckBoxType> = (props) => {
  const { options, onChange, maxHeight = "none", initialVisibleItems = 6 } = props;
  const [updatedArray, setUpdatedArray] = useState<CheckedArray[]>(options);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>, value: CheckedArray) => {
    const updatedArr = updatedArray.map((val) => {
      if (value.key === val.key) {
        return { ...val, checked: event.target.checked };
      } else {
        return { ...val };
      }
    });
    setUpdatedArray(updatedArr);
    onChange(updatedArr);
  };

  const handleClearAll = () => {
    const clearedArray = updatedArray.map((val) => ({
      ...val,
      checked: false,
    }));
    setUpdatedArray(clearedArray);
    onChange(clearedArray);
  };

  useEffect(() => {
    setUpdatedArray(options);
  }, [options]);

  return (
    <Box sx={{ position: "relative" }}>
      <Box
        ref={containerRef}
        sx={{
          maxHeight: maxHeight,
          overflow: "auto",
          transition: "max-height 0.3s ease-out",
        }}
      >
        <Grid container direction="column" spacing={0}>
          {updatedArray.map((val, index) => (
            <Grid
              item
              key={val.key}
              container
              alignItems="center"
              wrap="nowrap"
              sx={{
                py: 0.25,
                display: index < initialVisibleItems ? "flex" : "flex",
              }}
            >
              <Checkbox
                checked={val.checked}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(e, val)}
                inputProps={{ "aria-label": "controlled" }}
                sx={{ p: 0.5, mr: 1 }}
              />
              {val.colorcode && (
                <Grid
                  item
                  sx={{
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    backgroundColor: lightenColor(val.colorcode, 60),
                    mr: 1,
                  }}
                />
              )}
              <Typography sx={{ flexGrow: 1 }}>
                {val.value ? val.value : toCamelCase(val.key || "Undefined")}
              </Typography>
            </Grid>
          ))}
        </Grid>
      </Box>
      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 1 }}>
        <Button
          onClick={handleClearAll}
          variant="text"
          sx={{
            color: theme.palette.primary.main,
            p: 0,
            minWidth: "auto",
            "&:hover": {
              backgroundColor: "transparent",
              textDecoration: "underline",
            },
          }}
        >
          Clear All
        </Button>
      </Box>
    </Box>
  );
};

export default CustomCheckBox;
