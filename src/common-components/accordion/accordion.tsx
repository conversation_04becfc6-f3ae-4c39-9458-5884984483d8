import React, { useState } from "react";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Typography } from "@mui/material";
import Accordion from "@mui/material/Accordion";
import AccordionDetails from "@mui/material/AccordionDetails";
import AccordionSummary from "@mui/material/AccordionSummary";

type CustomAccordionType = {
  title: string;
  isOpen?: boolean;
};

const CustomAccordion = (props: React.PropsWithChildren<CustomAccordionType>) => {
  const { title, isOpen = false } = props;
  const [expanded, setExpanded] = useState(isOpen);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  return (
    <div>
      <Accordion
        sx={{
          background: "inherit",
          height: "fit-content",
          boxShadow: "none",
          "&:before": {
            display: "none",
          },
          border: "1px solid #DDDDDD",
          borderRadius: "8px",
          padding: "12px",
        }}
        expanded={expanded}
        onChange={handleToggle}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />} aria-controls="panel1-content" id="panel1-header">
          <Typography sx={{ fontWeight: 550, letterSpacing: "inherit" }} variant="subtitle2">
            {title}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>{props.children}</AccordionDetails>
      </Accordion>
    </div>
  );
};

export default CustomAccordion;
