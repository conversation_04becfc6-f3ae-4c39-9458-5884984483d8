// import React from "react";
import { Controller, FieldError, FieldVal<PERSON>, Path, useFormContext } from "react-hook-form";

import CancelRoundedIcon from "@mui/icons-material/CancelRounded";
import {
  Box,
  Checkbox,
  Chip,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  ListItemIcon,
  ListItemText,
  MenuItem,
  Select,
  Tooltip,
  Typography,
} from "@mui/material";
import { SelectChangeEvent } from "@mui/material/Select";

import { startCase } from "lodash";

interface Option {
  label: string;
  value: string | number;
}

interface ChipMultiSelectProps<T extends FieldValues> {
  options: Option[];
  label?: string;
  name: Path<T>;
  rules?: object;
  defaultValue?: (string | number)[];
  placeholder?: string;
  isRequired?: boolean;
  noLabel?: boolean;
  disabled?: boolean;
  width?: string;
  bgColor?: string;
}

export function ChipMultiSelect<T extends FieldValues>({
  name,
  rules,
  defaultValue = [],
  options,
  placeholder,
  label,
  isRequired,
  noLabel,
  disabled,
  width,
  bgColor,
}: ChipMultiSelectProps<T>) {
  const { control } = useFormContext();

  return (
    <Grid width={width}>
      <Controller
        name={name}
        control={control}
        rules={rules}
        defaultValue={defaultValue as unknown as Path<T>}
        render={({ field: { onChange, value }, fieldState: { error } }) => (
          <MultiSelect
            bgColor={bgColor}
            options={options}
            placeholder={placeholder}
            value={value || []}
            onChange={onChange}
            error={error}
            label={label || startCase(name)}
            isRequired={isRequired}
            noLabel={noLabel}
            disabled={disabled}
          />
        )}
      />
    </Grid>
  );
}

interface MultiSelectProps {
  options: Option[];
  label?: string;
  value?: (string | number)[];
  onChange?: (value: (string | number)[]) => void;
  error?: FieldError | undefined;
  placeholder?: string;
  isRequired?: boolean;
  noLabel?: boolean;
  disabled?: boolean;
  bgColor?: string;
}

const MultiSelect = ({
  options,
  label = "",
  value = [],
  onChange,
  isRequired,
  noLabel,
  placeholder,
  error,
  bgColor,
}: MultiSelectProps) => {
  const handleOptionChange = (event: SelectChangeEvent<(string | number)[]>) => {
    const newValue = event.target.value as (string | number)[];
    onChange?.(newValue);
  };

  const handleClearAll = () => {
    onChange?.([]);
  };

  return (
    <FormControl fullWidth sx={{ backgroundColor: bgColor || "#FFFFFF" }}>
      {!noLabel && (
        <Typography display={"flex"} mb={1} fontSize="14px" fontWeight="500" color="#515C5F" gap={"5px"}>
          {label}
          {isRequired && <span style={{ color: "#D32F2F" }}>*</span>}
        </Typography>
      )}
      <Box sx={{ position: "relative", borderRadius: "10px", bgcolor: "#FFFFFF" }}>
        <Select
          displayEmpty
          labelId="multi-select-label"
          id="multi-select"
          multiple
          value={value}
          onChange={handleOptionChange}
          renderValue={(selected) => {
            if (!selected || selected.length === 0) {
              return (
                <span style={{ color: "#B6C1C4", fontSize: "14px", marginLeft: "-10px" }}>
                  {placeholder ?? "Please Select"}
                </span>
              );
            }

            return (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  overflowX: "auto",
                  whiteSpace: "nowrap",
                  gap: 0.5,
                  "&::-webkit-scrollbar": { display: "none" },
                  msOverflowStyle: "none",
                  scrollbarWidth: "none",
                }}
              >
                {selected.map((val) => {
                  const displayLabel = options.find((opt) => opt.value === val)?.label || val;
                  return (
                    <Chip
                      key={val}
                      label={displayLabel.toString()}
                      sx={{
                        height: "24px",
                        fontWeight: "500",
                        fontSize: "14px",
                        color: "#2D7AE5",
                        gap: ".5px",
                        borderRadius: "100px",
                        paddingTop: "4px",
                        paddingRight: "12px",
                        paddingBottom: "4px",
                        paddingLeft: "12px",
                        backgroundColor: "#F2F7FF",
                      }}
                    />
                  );
                })}
              </Box>
            );
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                marginTop: "8px",
                borderRadius: "8px",
              },
            },
          }}
          sx={{
            width: "100%",
            height: "42px",
            padding: "12px",
            gap: "8px",
            borderRadius: "8px",
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: "#E8EBEC",
              borderWidth: "1px",
            },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderColor: "#E8EBEC",
              borderWidth: "1px",
            },
            "&:hover .MuiOutlinedInput-notchedOutline": {
              borderColor: "#C7D0DD",
              borderWidth: "1px",
            },
            "&:hover": {
              background: "#F3F6F9",
            },
            "& .MuiSelect-selectMenu": {
              padding: "5px",
              borderRadius: "8px",
              border: "1px solid #E8EBEC",
            },
            "& .MuiMenuItem-root": {
              padding: "8px",
              borderRadius: "8px",
              "&:hover, &.Mui-selected": {
                backgroundColor: "#E5EAF2",
                color: "#1C2025",
              },
              "&:focus-visible": {
                outline: "3px solid #99CCF3",
              },
            },
          }}
        >
          {options.map((option) => (
            <MenuItem
              sx={{
                marginLeft: "5px",
                marginRight: "5px",
                width: "98%",
                height: "38px",
                minHeight: "38px",
                borderRadius: "8px",
                marginBottom: "4px",
                "&:last-child": {
                  marginBottom: "0",
                },
              }}
              key={option.value}
              value={option.value}
            >
              <ListItemText primary={option.label.toString()} />
              <ListItemIcon sx={{ minWidth: "auto", marginRight: 0 }}>
                <Checkbox checked={value.includes(option.value)} />
              </ListItemIcon>
            </MenuItem>
          ))}
        </Select>
        {value.length > 0 && (
          <Tooltip title="Clear All">
            <IconButton
              onClick={handleClearAll}
              size="small"
              sx={{ position: "absolute", top: "50%", right: "25px", transform: "translateY(-50%)" }}
            >
              <CancelRoundedIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {error && (
        <InputLabel error sx={{ mt: 7, fontSize: "12px", whiteSpace: "normal" }}>
          {error.message}
        </InputLabel>
      )}
    </FormControl>
  );
};

export default ChipMultiSelect;
