import React, { useEffect, useState } from "react";

import {
  Button,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
} from "@mui/material";
import Switch, { SwitchProps } from "@mui/material/Switch";
import { styled } from "@mui/material/styles";

const IOSSwitch = styled((props: SwitchProps) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
  width: 42,
  height: 26,
  padding: 0,
  "& .MuiSwitch-switchBase": {
    padding: 0,
    margin: 2,
    transitionDuration: "300ms",
    "&.Mui-checked": {
      transform: "translateX(16px)",
      color: "#fff",
      "& + .MuiSwitch-track": {
        backgroundColor: "#17BF33",
        opacity: 1,
        border: 0,
        "&.Mui-disabled + .MuiSwitch-track": {
          opacity: 0.5,
        },
      },
      "&.Mui-focusVisible .Mui<PERSON>witch-thumb": {
        color: "#33cf4d",
        border: "6px solid #fff",
      },
      "&.Mui-disabled .MuiSwitch-thumb": {
        color: theme.palette.grey[100],
      },
      "&.Mui-disabled + .MuiSwitch-track": {
        opacity: 0.7,
      },
    },
  },
  "& .MuiSwitch-thumb": {
    boxSizing: "border-box",
    width: 22,
    height: 22,
  },
  "& .MuiSwitch-track": {
    borderRadius: 26 / 2,
    backgroundColor: "#E9E9EA",
    opacity: 1,
    transition: theme.transitions.create(["background-color"], {
      duration: 500,
    }),
  },
}));

interface ToggleProps {
  status: boolean;
  handleStatusChange: (checked: boolean, uuid?: string) => void;
  locationName?: string;
  isDisable?: boolean;
}

const Toggle: React.FC<ToggleProps> = (props) => {
  const { status } = props;
  const [checked, setChecked] = useState(status);

  useEffect(() => {
    setChecked(status);
  }, [status]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    event;
    handleClickOpenConfirmationDialog();
  };

  const [openConfirmationDialog, setOpenConfirmationDialog] = useState(false);

  const handleClickOpenConfirmationDialog = () => {
    setOpenConfirmationDialog(true);
  };

  //cancel
  const handleClickCloseConfirmationDialog = () => {
    setOpenConfirmationDialog(false);
  };

  //confirm change
  const handleClickCloseAndConfirmChangeStatus = () => {
    setChecked((prev) => !prev);
    props.handleStatusChange(!checked);
    setOpenConfirmationDialog(false);
  };

  return (
    <>
      <IOSSwitch onChange={handleChange} checked={props.status} disabled={props.isDisable} />
      <Dialog
        open={openConfirmationDialog}
        onClose={handleClickCloseConfirmationDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        maxWidth="sm"
      >
        <DialogTitle id="alert-dialog-title">
          <Typography variant="h6">{"Confirm"}</Typography>
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            <Typography variant="body1">
              {props.locationName
                ? `Do you really want to change the status of ${props.locationName}?`
                : `Do you really want to ${props.status ? "Inactive" : "Active"} the care plan?`}
            </Typography>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClickCloseConfirmationDialog} variant="outlined">
            <Typography variant="button">{"Cancel"}</Typography>
          </Button>
          <Button onClick={handleClickCloseAndConfirmChangeStatus} autoFocus variant="contained" color="primary">
            <Typography variant="button">{"Ok"}</Typography>
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default Toggle;
