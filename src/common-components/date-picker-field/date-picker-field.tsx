import React, { useEffect, useState } from "react";

import { Typography } from "@mui/material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { DesktopDatePicker } from "@mui/x-date-pickers/DesktopDatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DateRangeIcon } from "@mui/x-date-pickers/icons";
import { DateValidationError, PickerChangeHandlerContext } from "@mui/x-date-pickers/models";

import { format, parse } from "date-fns";
import { enIN } from "date-fns/locale";

import { customInputStyles, errorStyle } from "../custom-input/widgets/custom-input-styles";

export interface DatePickerProps {
  value?: string; // Optional initial value as a string (formatted)

  onDateChange: (selectedDate: string) => void; // Prop to return the selected date
  bgWhite: boolean;
  hasError?: boolean;
  errorMessage?: string;
  disableFuture?: boolean;
  disablePast?: boolean;
}

const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onDateChange,
  bgWhite,
  hasError,
  errorMessage,
  disableFuture,
  disablePast,
}) => {
  const [inputValue, setInputValue] = useState<Date | null>(value ? new Date(value) : null); // Manage date as a Date object

  // Parse the incoming value when the component mounts or when the value prop changes
  useEffect(() => {
    if (value) {
      const parsedDate = parse(value, "MM-dd-yyyy", new Date()); // Parse incoming value
      setInputValue(parsedDate);
    } else {
      setInputValue(null);
    }
  }, [value]);

  const handleChange = (value: Date | null, context: PickerChangeHandlerContext<DateValidationError>) => {
    if (value) {
      context;
      const formattedDate = format(value, "MM-dd-yyyy"); // Format date with date-fns
      setInputValue(value); // Update the state with the selected date
      onDateChange(formattedDate); // Return the formatted date to the parent component
    }
  };

  return (
    <>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enIN}>
        <DesktopDatePicker
          disableFuture={disableFuture}
          disablePast={disablePast}
          value={inputValue}
          closeOnSelect={true}
          onChange={handleChange}
          format="dd-MM-yyyy" // Specify the format
          slotProps={{
            openPickerIcon: DateRangeIcon,
            inputAdornment: {
              position: "start",
            },
          }}
          sx={{
            minWidth: "100% !important",
            borderRadius: "12px",
            "& .MuiOutlinedInput-root": {
              background: bgWhite ? "white" : "inherit",
              "& .MuiOutlinedInput-notchedOutline": {
                border: "none",
              },

              ...customInputStyles.textFieldInput,
              ...customInputStyles.textFieldRoot,
            },
            "& .MuiInputBase-input": {
              fontSize: "14px",
              padding: "9px 0px",
              outline: "none",
            },
          }}
        />
      </LocalizationProvider>
      <Typography textAlign={"start"} sx={errorStyle} variant="caption">
        {hasError ? errorMessage : ""}
      </Typography>
    </>
  );
};

export default DatePicker;
