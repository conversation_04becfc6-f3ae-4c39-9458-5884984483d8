import * as React from "react";

import CloseIcon from "@mui/icons-material/Close";
import { Divider, Typography } from "@mui/material";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import { Grid } from "@mui/system";

import { theme } from "../../utils/theme";
import { customDialogStyles } from "./widgets/custom-dialog-styles";

interface CustomDialogProps {
  title: React.ReactNode;
  buttonName: string[];
  open: boolean;
  onClose: () => void;
  width?: string | number;
  height?: string | number;
  sx?: object;
  overFlow?: string;
  padding?: string;
  titleAlign?: "left" | "center";
  showDivider?: boolean;
  titleFontWeight?: number | string;
  titleFontSize?: string | number;
  borderRadius?: string;
  description?: string;
}

const CustomDialog = (props: React.PropsWithChildren<CustomDialogProps>) => {
  const {
    onClose,
    borderRadius,
    open,
    title,
    width,
    height,
    sx = {},
    overFlow,
    padding,
    titleAlign = "left",
    showDivider = false,
    titleFontWeight = "bold",
    titleFontSize = "1.3rem",
    description,
  } = props;

  const titleAlignment = {
    textAlign: titleAlign,
    fontWeight: titleFontWeight,
    fontSize: titleFontSize,
  };

  const handleDialogClose = (_event: object, reason: string) => {
    if (reason === "backdropClick") {
      return;
    }
    if (onClose) {
      onClose();
    }
  };

  return (
    <Dialog
      onClose={handleDialogClose}
      aria-labelledby="customized-dialog-title"
      open={open}
      fullWidth
      sx={{ paper: customDialogStyles.dialog, ...sx }}
      PaperProps={{
        sx: {
          width: width || "auto",
          height: height || "auto",
          maxWidth: width || "auto",
          borderRadius: borderRadius ? "20px" : "0px",
        },
      }}
    >
      <DialogTitle
        id="customized-dialog-title"
        sx={{
          ...customDialogStyles.dialogTitle,
          padding: "5px 22px",
          borderColor: theme.palette.grey[300],

          // cursor: draggable ? "move" : "default",
        }}
      >
        <Grid
          container
          p={0}
          justifyContent="space-between"
          alignItems="center"
          sx={{
            marginBottom: showDivider ? "0" : "10px",
          }}
        >
          <Grid sx={titleAlignment}>{title}</Grid>
          <Grid>
            <IconButton aria-label="close" onClick={onClose} sx={customDialogStyles.closeIcon}>
              <CloseIcon />
            </IconButton>
          </Grid>
        </Grid>
        {description && (
          <Grid sx={{ mt: -2 }}>
            <Typography variant="body2" sx={{ fontSize: "14px", color: "#667085" }}>
              {" "}
              {description}
            </Typography>
          </Grid>
        )}
        {showDivider && <Divider sx={{ my: "10px" }} />}{" "}
      </DialogTitle>
      <DialogContent sx={{ overflow: overFlow, padding: padding, borderTop: "1px solid #E8EBEC" }}>
        <Grid flex={1}>{props.children}</Grid>
      </DialogContent>
    </Dialog>
  );
};

export default CustomDialog;
