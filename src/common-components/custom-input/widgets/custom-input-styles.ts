import { alpha } from "@mui/system";

import { theme } from "../../../utils/theme";

export const errorStyle = {
  color: `red`,
};

export const customInputStyles = {
  textFieldRoot: {
    border: `1px solid ${alpha(theme.palette.grey[500], 0.3)}`,
    padding: "6px, 8px, 6px, 8px",
  },
  textFieldInput: {
    color: "#333333",
    fontSize: "15px",
    fontStyle: "normal",
    fontWeight: "400",
    lineHeight: "130%",
    letterSpacing: "0.12px",
    borderRadius: "12px",
    fontFamily: "Roboto",

    "&::placeholder": {
      color: "#B6C1C4",
      fontSize: "13px",
      fontStyle: "inter sans-serif",
      fontWeight: "400",
      padding: "2.5px",
    },
  },
  textFieldActive: {},
  textFieldError: {
    border: `1px solid red`,
  },
};
