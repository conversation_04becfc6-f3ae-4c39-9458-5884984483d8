export const editTextAreaStyle = {
  textArea: {
    border: `1px solid #D2D2D2`,
    height: "40px",
    padding: "10px 12px",
    width: "100%",
    color: "black",
    fontSize: "14px",
    fontStyle: "inter sans-serif ",
    fontWeight: "400 ",
    lineHeight: "130% ",
    letterSpacing: "0.12px ",
    borderRadius: "8px ",
    // boxShadow: "0px 0px 5px rgba(0, 0, 0, 0.2)",
    "&::placeholder": {
      fontSize: "16px ",
      fontStyle: "inter sans-serif ",
      fontWeight: "400",
    },
  },
  errorMessage: {
    border: "1px solid red",
  },
};
