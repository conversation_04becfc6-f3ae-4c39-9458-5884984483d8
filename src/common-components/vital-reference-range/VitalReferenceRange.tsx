import { Box, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";

interface RangeType {
  low_critical?: { min: number; max: number };
  low_moderate?: { min: number; max: number };
  normal?: { min: number; max: number };
  high_moderate?: { min: number; max: number };
  high_critical?: { min: number; max: number };
}

interface VitalReferenceRangeProps {
  title: string;
  unit?: string;
  ranges:
    | RangeType
    | {
        systolic: RangeType;
        diastolic: RangeType;
      };
}

const RangeContainer = styled(Box)({
  backgroundColor: "#fff",
  borderRadius: "8px",
  width: "100%",
  border: "1px solid #E8EBEC",
  padding: "16px",
  height: "120px",
  display: "flex",
  flexDirection: "column",
});

const RangeBar = styled(Box)({
  display: "flex",
  height: "18px",
  borderRadius: "4px",
  overflow: "hidden",
  width: "100%",
});

const RangeSegment = styled(Box)({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  color: "#000",
  fontWeight: 500,
  fontSize: "14px",
});

const BPLabel = styled(Typography)({
  fontSize: "14px",
  fontWeight: 400,
  color: "#333",
  width: "60px",
  marginRight: "12px",
  flexShrink: 0,
});

const VitalReferenceRange = ({ title, unit = "", ranges }: VitalReferenceRangeProps) => {
  const isBloodPressure = title.toLowerCase().includes("blood pressure");

  const formatTitle = (text: string) => {
    return text
      .split("-")
      .join(" ")
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const renderRangeBar = (rangeData: RangeType, label?: string) => {
    const onlyNormalPresent =
      rangeData.normal?.min !== undefined &&
      rangeData.normal?.max !== undefined &&
      rangeData.low_moderate?.min === 0 &&
      rangeData.low_moderate?.max === 0 &&
      rangeData.high_moderate?.min === 0 &&
      rangeData.high_moderate?.max === 0;

    return (
      <Box sx={{ display: "flex", alignItems: "center", mb: label ? 1 : 0, width: "100%" }}>
        {label && <BPLabel>{label}</BPLabel>}
        <RangeBar>
          <RangeSegment
            sx={{
              backgroundColor: "#CE0718",
              color: "#fff",
              flex: 1,
            }}
          >
            {rangeData.low_moderate?.min && rangeData.low_moderate.min > 0
              ? `< ${rangeData.low_moderate.min}`
              : rangeData.normal?.min !== undefined
                ? `< ${rangeData.normal.min}`
                : "N/A"}
          </RangeSegment>

          {!onlyNormalPresent && (
            <RangeSegment
              sx={{
                backgroundColor: "#FCB33B",
                flex: 1,
              }}
            >
              {rangeData.low_moderate ? `${rangeData.low_moderate.min} - ${rangeData.low_moderate.max}` : "N/A"}
            </RangeSegment>
          )}

          <RangeSegment
            sx={{
              backgroundColor: "#7FD067",
              flex: 1,
            }}
          >
            {rangeData.normal ? `${rangeData.normal.min} - ${rangeData.normal.max}` : "N/A"}
          </RangeSegment>

          {!onlyNormalPresent && (
            <RangeSegment
              sx={{
                backgroundColor: "#FCB33B",
                flex: 1,
              }}
            >
              {rangeData.high_moderate ? `${rangeData.high_moderate.min} - ${rangeData.high_moderate.max}` : "N/A"}
            </RangeSegment>
          )}

          <RangeSegment
            sx={{
              backgroundColor: "#CE0718",
              color: "#fff",
              flex: 1,
            }}
          >
            {rangeData.high_critical?.min && rangeData.high_critical.min > 0
              ? `> ${rangeData.high_critical.min}`
              : rangeData.normal?.max !== undefined
                ? `> ${rangeData.normal.max}`
                : "N/A"}
          </RangeSegment>
        </RangeBar>
      </Box>
    );
  };

  return (
    <RangeContainer>
      <Typography
        sx={{
          mb: 2,
          fontFamily: "Roboto",
          fontWeight: 500,
          fontSize: "14px",
          lineHeight: "120%",
          color: "#333",
        }}
      >
        {formatTitle(title)} {unit && `(${unit})`}
      </Typography>

      <Box sx={{ flex: 1, display: "flex", flexDirection: "column", justifyContent: "center" }}>
        {isBloodPressure ? (
          <Box>
            {renderRangeBar((ranges as { systolic: RangeType }).systolic, "Systolic")}
            {renderRangeBar((ranges as { diastolic: RangeType }).diastolic, "Diastolic")}
          </Box>
        ) : (
          renderRangeBar(ranges as RangeType)
        )}
      </Box>
    </RangeContainer>
  );
};

export default VitalReferenceRange;
