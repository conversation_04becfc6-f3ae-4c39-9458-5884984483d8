export const switchStyles = {
  switch: {
    width: 28,
    height: 15,
    padding: 0,
    "& .<PERSON>iSwitch-switchBase": {
      padding: 0,
      margin: 2,
      bottom: "0.3px",
      transitionDuration: "300ms",
      "&.Mui-checked": {
        transform: "translateX(11px)",
        color: "#fff",
        "& + .MuiSwitch-track": {
          backgroundColor: "#65C466",
          opacity: 1,
          border: 0,
        },
        "&.Mui-disabled + .MuiSwitch-track": {
          opacity: 0.5,
        },
      },
      "&.Mui-focusVisible .MuiSwitch-thumb": {
        color: "#33cf4d",
        border: "6px solid #fff",
      },
      "&.Mui-disabled .MuiSwitch-thumb": {
        color: "#E0E0E0",
      },
      "&.Mui-disabled + .MuiSwitch-track": {
        opacity: 0.7,
      },
    },
    "& .MuiSwitch-thumb": {
      boxSizing: "border-box",
      width: 12,
      height: 12,
    },
    "& .<PERSON>iSwitch-track": {
      borderRadius: 20 / 2,
      backgroundColor: "#BFBFBF",
      opacity: 1,
      transition: "background-color 500ms",
    },
  },
};

export const chipStyles = {
  chip: {
    backgroundColor: "#E0E0E0",
    width: "100px",
    height: "25px",
    "& .MuiChip-icon": {
      color: "inherit",
    },
    "& .MuiChip-label": {
      flex: "1",
      fontSize: "0.8rem",
    },
  },
};
