import { useState } from "react";

import { Grid, Typography, styled, useMediaQuery } from "@mui/material";

import { theme } from "../../utils/theme";

type SwitcherProps = {
  option1: string;
  option2: string;
  buttonWidth: string;
  compactHeight?: boolean;
  variant: "light" | "dark";
  onChange: (option: string) => void;
};

const Switcher = (props: SwitcherProps) => {
  const { option1, option2, buttonWidth, variant, onChange } = props;
  const [option, setOption] = useState(option1);
  const below389 = useMediaQuery("(max-width:389px)");
  const below320 = useMediaQuery("(max-width:326px)");

  const handleOptionChange = (newOption: string) => {
    setOption((prevState) => {
      if (prevState === newOption) {
        return newOption;
      }
      return newOption;
    });
    onChange(newOption);
  };

  const StyledGridContainer = styled(Grid)(() => ({
    width: "fit-content",
    borderRadius: "40px",
    color: variant === "dark" ? theme.palette.common.white : theme.palette.grey[700],
    backgroundColor: variant === "dark" ? theme.palette.primary.dark : theme.palette.common.white,
    height: "40px",
    boxShadow: "0px 0px 6px rgba(0, 0, 0, 0.16)",
  }));

  const StyledGridButton = styled(Grid)(() => ({
    cursor: "pointer",
    paddingTop: "5px",
    borderRadius: "40px",
    borderColor: theme.palette.primary.dark,
    width: below320 ? "130px" : below389 ? "140px" : buttonWidth || "120px",
    height: "40px",
    justifyContent: "center",
    backgroundColor: variant === "dark" ? "transparent" : theme.palette.common.white,
    transition: "background-color 0.3s, border-color 0.3s",
    // "&:hover": {
    //   color:
    //     variant === "dark"
    //       ? theme.palette.primary.dark
    //       : theme.palette.primary.dark,
    //   backgroundColor: theme.palette.common.white,
    //   border: `1px solid ${theme.palette.primary.dark}`,
    // },
    "&:active": {
      backgroundColor: variant === "dark" ? theme.palette.common.white : theme.palette.common.white,
    },
  }));

  const optionActive = {
    backgroundColor: variant === "dark" ? theme.palette.common.white : theme.palette.common.white,
    color: theme.palette.primary.dark,
    border: `1px solid ${theme.palette.primary.dark}`,
    borderColor: theme.palette.primary.dark,
  };

  return (
    <StyledGridContainer mt={"10px"} container /* gap={below389 ? 0.5 : 1} */>
      <StyledGridButton
        item
        container
        onClick={handleOptionChange.bind(null, option1)}
        sx={option === option1 ? optionActive : {}}
      >
        <Typography
          variant="subtitle1"
          fontSize={below320 ? "13px" : below389 ? "15px" : "1rem"}
          paddingTop={below320 ? "4px" : "0px"}
        >
          {option1.length > 17 ? ` ${option1.slice(0, 17)}..` : option1}
        </Typography>
      </StyledGridButton>
      <StyledGridButton
        item
        container
        onClick={handleOptionChange.bind(null, option2)}
        sx={option === option2 ? optionActive : {}}
      >
        <Typography
          variant="subtitle1"
          fontSize={below320 ? "13px" : below389 ? "15px" : "1rem"}
          paddingTop={below320 ? "4px" : "0px"}
        >
          {option2.length > 17 ? ` ${option2.slice(0, 17)}..` : option2}
        </Typography>
      </StyledGridButton>
    </StyledGridContainer>
  );
};

export default Switcher;
