// generated with @7nohe/openapi-react-query-codegen@1.4.1
import { UseQueryResult } from "@tanstack/react-query";

import {
  ActivityControllerService,
  AiControllerService,
  AppointmentControllerService,
  AvailabilityControllerService,
  CarePlanControllerService,
  ClinicalNoteControllerService,
  ConditionControllerService,
  ConsentFormControllerService,
  DeviceControllerService,
  EhrControllerService,
  EhrProviderControllerService,
  EqrControllerService,
  FirebaseMessageControllerService,
  LicenseStateControllerService,
  LocationControllerService,
  MedicalCodeControllerService,
  NotificationControllerService,
  PatientAllergyControllerService,
  PatientCarePlanControllerService,
  PatientControllerService,
  PatientDiagnosisControllerService,
  PatientMedicationControllerService,
  PatientTrainingControllerService,
  PatientVitalControllerService,
  PatientVitalSettingControllerService,
  ProviderControllerService,
  ProviderGroupControllerService,
  RolesAndPrivilegesControllerService,
  TaskControllerService,
  TimeLogControllerService,
  UserControllerService,
  VitalControllerService,
  ZoomControllerService,
} from "../requests/services.gen";

export type UserControllerServiceGetAllUsersDefaultResponse = Awaited<
  ReturnType<typeof UserControllerService.getAllUsers>
>;
export type UserControllerServiceGetAllUsersQueryResult<
  TData = UserControllerServiceGetAllUsersDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useUserControllerServiceGetAllUsersKey = "UserControllerServiceGetAllUsers";
export const UseUserControllerServiceGetAllUsersKeyFn = (
  {
    archive,
    locationId,
    page,
    role,
    roleType,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    locationId?: string;
    page?: number;
    role?: string;
    roleType?: "PROVIDER" | "STAFF" | "PATIENT";
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useUserControllerServiceGetAllUsersKey,
  ...(queryKey ?? [
    { archive, locationId, page, role, roleType, searchString, size, sortBy, sortDirection, status, xTenantId },
  ]),
];
export type UserControllerServiceGetUserDefaultResponse = Awaited<ReturnType<typeof UserControllerService.getUser>>;
export type UserControllerServiceGetUserQueryResult<
  TData = UserControllerServiceGetUserDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useUserControllerServiceGetUserKey = "UserControllerServiceGetUser";
export const UseUserControllerServiceGetUserKeyFn = (
  {
    userId,
    xTenantId,
  }: {
    userId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useUserControllerServiceGetUserKey, ...(queryKey ?? [{ userId, xTenantId }])];
export type UserControllerServiceGetProfile1DefaultResponse = Awaited<
  ReturnType<typeof UserControllerService.getProfile1>
>;
export type UserControllerServiceGetProfile1QueryResult<
  TData = UserControllerServiceGetProfile1DefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useUserControllerServiceGetProfile1Key = "UserControllerServiceGetProfile1";
export const UseUserControllerServiceGetProfile1KeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useUserControllerServiceGetProfile1Key, ...(queryKey ?? [{ xTenantId }])];
export type PatientVitalSettingControllerServiceGetVitalSettingsDefaultResponse = Awaited<
  ReturnType<typeof PatientVitalSettingControllerService.getVitalSettings>
>;
export type PatientVitalSettingControllerServiceGetVitalSettingsQueryResult<
  TData = PatientVitalSettingControllerServiceGetVitalSettingsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientVitalSettingControllerServiceGetVitalSettingsKey =
  "PatientVitalSettingControllerServiceGetVitalSettings";
export const UsePatientVitalSettingControllerServiceGetVitalSettingsKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [usePatientVitalSettingControllerServiceGetVitalSettingsKey, ...(queryKey ?? [{ xTenantId }])];
export type PatientVitalSettingControllerServiceGetPatientVitalSettingDefaultResponse = Awaited<
  ReturnType<typeof PatientVitalSettingControllerService.getPatientVitalSetting>
>;
export type PatientVitalSettingControllerServiceGetPatientVitalSettingQueryResult<
  TData = PatientVitalSettingControllerServiceGetPatientVitalSettingDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientVitalSettingControllerServiceGetPatientVitalSettingKey =
  "PatientVitalSettingControllerServiceGetPatientVitalSetting";
export const UsePatientVitalSettingControllerServiceGetPatientVitalSettingKeyFn = (
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [usePatientVitalSettingControllerServiceGetPatientVitalSettingKey, ...(queryKey ?? [{ patientUuid, xTenantId }])];
export type TimeLogControllerServiceGetAllPatientTimeLogsDefaultResponse = Awaited<
  ReturnType<typeof TimeLogControllerService.getAllPatientTimeLogs>
>;
export type TimeLogControllerServiceGetAllPatientTimeLogsQueryResult<
  TData = TimeLogControllerServiceGetAllPatientTimeLogsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useTimeLogControllerServiceGetAllPatientTimeLogsKey = "TimeLogControllerServiceGetAllPatientTimeLogs";
export const UseTimeLogControllerServiceGetAllPatientTimeLogsKeyFn = (
  {
    activityName,
    loggedBy,
    loggedEntryType,
    month,
    page,
    patientId,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    activityName?: string;
    loggedBy?: string;
    loggedEntryType?: string;
    month: string;
    page?: number;
    patientId: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useTimeLogControllerServiceGetAllPatientTimeLogsKey,
  ...(queryKey ?? [{ activityName, loggedBy, loggedEntryType, month, page, patientId, size, sort, sortBy, xTenantId }]),
];
export type TimeLogControllerServiceGetTimeLogByIdDefaultResponse = Awaited<
  ReturnType<typeof TimeLogControllerService.getTimeLogById>
>;
export type TimeLogControllerServiceGetTimeLogByIdQueryResult<
  TData = TimeLogControllerServiceGetTimeLogByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useTimeLogControllerServiceGetTimeLogByIdKey = "TimeLogControllerServiceGetTimeLogById";
export const UseTimeLogControllerServiceGetTimeLogByIdKeyFn = (
  {
    timeLogId,
    xTenantId,
  }: {
    timeLogId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useTimeLogControllerServiceGetTimeLogByIdKey, ...(queryKey ?? [{ timeLogId, xTenantId }])];
export type TaskControllerServiceGetAllTasksDefaultResponse = Awaited<
  ReturnType<typeof TaskControllerService.getAllTasks>
>;
export type TaskControllerServiceGetAllTasksQueryResult<
  TData = TaskControllerServiceGetAllTasksDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useTaskControllerServiceGetAllTasksKey = "TaskControllerServiceGetAllTasks";
export const UseTaskControllerServiceGetAllTasksKeyFn = (
  {
    active,
    archive,
    assignedBy,
    assignedDate,
    assignedTo,
    currentUserUuid,
    dueDate,
    page,
    patientId,
    priority,
    searchAssignTo,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    type,
    xTenantId,
  }: {
    active?: boolean;
    archive?: boolean;
    assignedBy?: string;
    assignedDate?: string;
    assignedTo?: string;
    currentUserUuid?: string;
    dueDate?: string;
    page?: number;
    patientId?: string;
    priority?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    searchAssignTo?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: "PENDING" | "COMPLETED" | "DISCARDED";
    type?: string;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useTaskControllerServiceGetAllTasksKey,
  ...(queryKey ?? [
    {
      active,
      archive,
      assignedBy,
      assignedDate,
      assignedTo,
      currentUserUuid,
      dueDate,
      page,
      patientId,
      priority,
      searchAssignTo,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      type,
      xTenantId,
    },
  ]),
];
export type TaskControllerServiceGetTaskByUuidDefaultResponse = Awaited<
  ReturnType<typeof TaskControllerService.getTaskByUuid>
>;
export type TaskControllerServiceGetTaskByUuidQueryResult<
  TData = TaskControllerServiceGetTaskByUuidDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useTaskControllerServiceGetTaskByUuidKey = "TaskControllerServiceGetTaskByUuid";
export const UseTaskControllerServiceGetTaskByUuidKeyFn = (
  {
    taskUuid,
    xTenantId,
  }: {
    taskUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useTaskControllerServiceGetTaskByUuidKey, ...(queryKey ?? [{ taskUuid, xTenantId }])];
export type RolesAndPrivilegesControllerServiceGetAllRolesDefaultResponse = Awaited<
  ReturnType<typeof RolesAndPrivilegesControllerService.getAllRoles>
>;
export type RolesAndPrivilegesControllerServiceGetAllRolesQueryResult<
  TData = RolesAndPrivilegesControllerServiceGetAllRolesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useRolesAndPrivilegesControllerServiceGetAllRolesKey = "RolesAndPrivilegesControllerServiceGetAllRoles";
export const UseRolesAndPrivilegesControllerServiceGetAllRolesKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useRolesAndPrivilegesControllerServiceGetAllRolesKey, ...(queryKey ?? [{ xTenantId }])];
export type RolesAndPrivilegesControllerServiceGetAllPrivilegesDefaultResponse = Awaited<
  ReturnType<typeof RolesAndPrivilegesControllerService.getAllPrivileges>
>;
export type RolesAndPrivilegesControllerServiceGetAllPrivilegesQueryResult<
  TData = RolesAndPrivilegesControllerServiceGetAllPrivilegesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useRolesAndPrivilegesControllerServiceGetAllPrivilegesKey =
  "RolesAndPrivilegesControllerServiceGetAllPrivileges";
export const UseRolesAndPrivilegesControllerServiceGetAllPrivilegesKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useRolesAndPrivilegesControllerServiceGetAllPrivilegesKey, ...(queryKey ?? [{ xTenantId }])];
export type RolesAndPrivilegesControllerServiceGetAllRolesPermissionsDefaultResponse = Awaited<
  ReturnType<typeof RolesAndPrivilegesControllerService.getAllRolesPermissions>
>;
export type RolesAndPrivilegesControllerServiceGetAllRolesPermissionsQueryResult<
  TData = RolesAndPrivilegesControllerServiceGetAllRolesPermissionsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useRolesAndPrivilegesControllerServiceGetAllRolesPermissionsKey =
  "RolesAndPrivilegesControllerServiceGetAllRolesPermissions";
export const UseRolesAndPrivilegesControllerServiceGetAllRolesPermissionsKeyFn = (
  {
    realm,
    xTenantId,
  }: {
    realm: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useRolesAndPrivilegesControllerServiceGetAllRolesPermissionsKey, ...(queryKey ?? [{ realm, xTenantId }])];
export type ProviderControllerServiceGetAllProvidersDefaultResponse = Awaited<
  ReturnType<typeof ProviderControllerService.getAllProviders>
>;
export type ProviderControllerServiceGetAllProvidersQueryResult<
  TData = ProviderControllerServiceGetAllProvidersDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useProviderControllerServiceGetAllProvidersKey = "ProviderControllerServiceGetAllProviders";
export const UseProviderControllerServiceGetAllProvidersKeyFn = (
  {
    archive,
    page,
    role,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    role?:
      | "PROVIDER"
      | "PATIENT"
      | "SUPER_ADMIN"
      | "ADMIN"
      | "FRONTDESK"
      | "BILLER"
      | "SITE_ADMIN"
      | "PROVIDER_GROUP_ADMIN"
      | "NURSE"
      | "ANONYMOUS";
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useProviderControllerServiceGetAllProvidersKey,
  ...(queryKey ?? [{ archive, page, role, searchString, size, sortBy, sortDirection, state, status, xTenantId }]),
];
export type ProviderControllerServiceGetProviderByIdDefaultResponse = Awaited<
  ReturnType<typeof ProviderControllerService.getProviderById>
>;
export type ProviderControllerServiceGetProviderByIdQueryResult<
  TData = ProviderControllerServiceGetProviderByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useProviderControllerServiceGetProviderByIdKey = "ProviderControllerServiceGetProviderById";
export const UseProviderControllerServiceGetProviderByIdKeyFn = (
  {
    providerUuid,
    xTenantId,
  }: {
    providerUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useProviderControllerServiceGetProviderByIdKey, ...(queryKey ?? [{ providerUuid, xTenantId }])];
export type ProviderControllerServiceGetNurseReportDashboardDefaultResponse = Awaited<
  ReturnType<typeof ProviderControllerService.getNurseReportDashboard>
>;
export type ProviderControllerServiceGetNurseReportDashboardQueryResult<
  TData = ProviderControllerServiceGetNurseReportDashboardDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useProviderControllerServiceGetNurseReportDashboardKey =
  "ProviderControllerServiceGetNurseReportDashboard";
export const UseProviderControllerServiceGetNurseReportDashboardKeyFn = (
  {
    month,
    page,
    providerId,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month: number;
    page?: number;
    providerId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: unknown[]
) => [
  useProviderControllerServiceGetNurseReportDashboardKey,
  ...(queryKey ?? [{ month, page, providerId, size, sortBy, sortDirection, xTenantId, year }]),
];
export type ProviderControllerServiceGetPatientDashboardDefaultResponse = Awaited<
  ReturnType<typeof ProviderControllerService.getPatientDashboard>
>;
export type ProviderControllerServiceGetPatientDashboardQueryResult<
  TData = ProviderControllerServiceGetPatientDashboardDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useProviderControllerServiceGetPatientDashboardKey = "ProviderControllerServiceGetPatientDashboard";
export const UseProviderControllerServiceGetPatientDashboardKeyFn = (
  {
    month,
    page,
    providerId,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month: number;
    page?: number;
    providerId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: unknown[]
) => [
  useProviderControllerServiceGetPatientDashboardKey,
  ...(queryKey ?? [{ month, page, providerId, size, sortBy, sortDirection, xTenantId, year }]),
];
export type ProviderControllerServiceGetUserIdByProviderDefaultResponse = Awaited<
  ReturnType<typeof ProviderControllerService.getUserIdByProvider>
>;
export type ProviderControllerServiceGetUserIdByProviderQueryResult<
  TData = ProviderControllerServiceGetUserIdByProviderDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useProviderControllerServiceGetUserIdByProviderKey = "ProviderControllerServiceGetUserIdByProvider";
export const UseProviderControllerServiceGetUserIdByProviderKeyFn = (
  {
    userId,
    xTenantId,
  }: {
    userId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useProviderControllerServiceGetUserIdByProviderKey, ...(queryKey ?? [{ userId, xTenantId }])];
export type ProviderControllerServiceGetProfileDefaultResponse = Awaited<
  ReturnType<typeof ProviderControllerService.getProfile>
>;
export type ProviderControllerServiceGetProfileQueryResult<
  TData = ProviderControllerServiceGetProfileDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useProviderControllerServiceGetProfileKey = "ProviderControllerServiceGetProfile";
export const UseProviderControllerServiceGetProfileKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useProviderControllerServiceGetProfileKey, ...(queryKey ?? [{ xTenantId }])];
export type ProviderGroupControllerServiceGetAllProviderGroupsDefaultResponse = Awaited<
  ReturnType<typeof ProviderGroupControllerService.getAllProviderGroups>
>;
export type ProviderGroupControllerServiceGetAllProviderGroupsQueryResult<
  TData = ProviderGroupControllerServiceGetAllProviderGroupsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useProviderGroupControllerServiceGetAllProviderGroupsKey =
  "ProviderGroupControllerServiceGetAllProviderGroups";
export const UseProviderGroupControllerServiceGetAllProviderGroupsKeyFn = (
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useProviderGroupControllerServiceGetAllProviderGroupsKey,
  ...(queryKey ?? [{ archive, page, searchString, size, sortBy, sortDirection, state, status, xTenantId }]),
];
export type ProviderGroupControllerServiceGetProviderGroupByIdDefaultResponse = Awaited<
  ReturnType<typeof ProviderGroupControllerService.getProviderGroupById>
>;
export type ProviderGroupControllerServiceGetProviderGroupByIdQueryResult<
  TData = ProviderGroupControllerServiceGetProviderGroupByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useProviderGroupControllerServiceGetProviderGroupByIdKey =
  "ProviderGroupControllerServiceGetProviderGroupById";
export const UseProviderGroupControllerServiceGetProviderGroupByIdKeyFn = (
  {
    providerGroupId,
    xTenantId,
  }: {
    providerGroupId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useProviderGroupControllerServiceGetProviderGroupByIdKey, ...(queryKey ?? [{ providerGroupId, xTenantId }])];
export type ProviderGroupControllerServiceGetProviderGroupBySchemaDefaultResponse = Awaited<
  ReturnType<typeof ProviderGroupControllerService.getProviderGroupBySchema>
>;
export type ProviderGroupControllerServiceGetProviderGroupBySchemaQueryResult<
  TData = ProviderGroupControllerServiceGetProviderGroupBySchemaDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useProviderGroupControllerServiceGetProviderGroupBySchemaKey =
  "ProviderGroupControllerServiceGetProviderGroupBySchema";
export const UseProviderGroupControllerServiceGetProviderGroupBySchemaKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useProviderGroupControllerServiceGetProviderGroupBySchemaKey, ...(queryKey ?? [{ xTenantId }])];
export type PatientControllerServiceGetAllPatientDefaultResponse = Awaited<
  ReturnType<typeof PatientControllerService.getAllPatient>
>;
export type PatientControllerServiceGetAllPatientQueryResult<
  TData = PatientControllerServiceGetAllPatientDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientControllerServiceGetAllPatientKey = "PatientControllerServiceGetAllPatient";
export const UsePatientControllerServiceGetAllPatientKeyFn = (
  {
    archive,
    genderFilter,
    mrn,
    name,
    nurseId,
    page,
    providerId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    genderFilter?: "MALE" | "FEMALE" | "OTHER" | "BOTH";
    mrn?: string;
    name?: string;
    nurseId?: string;
    page?: number;
    providerId?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  usePatientControllerServiceGetAllPatientKey,
  ...(queryKey ?? [
    {
      archive,
      genderFilter,
      mrn,
      name,
      nurseId,
      page,
      providerId,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      xTenantId,
    },
  ]),
];
export type PatientControllerServiceGetPatientByIdDefaultResponse = Awaited<
  ReturnType<typeof PatientControllerService.getPatientById>
>;
export type PatientControllerServiceGetPatientByIdQueryResult<
  TData = PatientControllerServiceGetPatientByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientControllerServiceGetPatientByIdKey = "PatientControllerServiceGetPatientById";
export const UsePatientControllerServiceGetPatientByIdKeyFn = (
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [usePatientControllerServiceGetPatientByIdKey, ...(queryKey ?? [{ patientUuid, xTenantId }])];
export type PatientControllerServiceGetPatientStatisticDefaultResponse = Awaited<
  ReturnType<typeof PatientControllerService.getPatientStatistic>
>;
export type PatientControllerServiceGetPatientStatisticQueryResult<
  TData = PatientControllerServiceGetPatientStatisticDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientControllerServiceGetPatientStatisticKey = "PatientControllerServiceGetPatientStatistic";
export const UsePatientControllerServiceGetPatientStatisticKeyFn = (
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [usePatientControllerServiceGetPatientStatisticKey, ...(queryKey ?? [{ patientId, xTenantId }])];
export type PatientControllerServiceGetPatientRecordDefaultResponse = Awaited<
  ReturnType<typeof PatientControllerService.getPatientRecord>
>;
export type PatientControllerServiceGetPatientRecordQueryResult<
  TData = PatientControllerServiceGetPatientRecordDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientControllerServiceGetPatientRecordKey = "PatientControllerServiceGetPatientRecord";
export const UsePatientControllerServiceGetPatientRecordKeyFn = (
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [usePatientControllerServiceGetPatientRecordKey, ...(queryKey ?? [{ patientId, xTenantId }])];
export type PatientControllerServiceGetProfile2DefaultResponse = Awaited<
  ReturnType<typeof PatientControllerService.getProfile2>
>;
export type PatientControllerServiceGetProfile2QueryResult<
  TData = PatientControllerServiceGetProfile2DefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientControllerServiceGetProfile2Key = "PatientControllerServiceGetProfile2";
export const UsePatientControllerServiceGetProfile2KeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [usePatientControllerServiceGetProfile2Key, ...(queryKey ?? [{ xTenantId }])];
export type PatientControllerServiceDownloadTemplateDefaultResponse = Awaited<
  ReturnType<typeof PatientControllerService.downloadTemplate>
>;
export type PatientControllerServiceDownloadTemplateQueryResult<
  TData = PatientControllerServiceDownloadTemplateDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientControllerServiceDownloadTemplateKey = "PatientControllerServiceDownloadTemplate";
export const UsePatientControllerServiceDownloadTemplateKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [usePatientControllerServiceDownloadTemplateKey, ...(queryKey ?? [{ xTenantId }])];
export type PatientControllerServiceGetPatientListDefaultResponse = Awaited<
  ReturnType<typeof PatientControllerService.getPatientList>
>;
export type PatientControllerServiceGetPatientListQueryResult<
  TData = PatientControllerServiceGetPatientListDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientControllerServiceGetPatientListKey = "PatientControllerServiceGetPatientList";
export const UsePatientControllerServiceGetPatientListKeyFn = (
  {
    archive,
    nurseId,
    page,
    providerId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    nurseId?: string;
    page?: number;
    providerId?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  usePatientControllerServiceGetPatientListKey,
  ...(queryKey ?? [
    { archive, nurseId, page, providerId, searchString, size, sortBy, sortDirection, status, xTenantId },
  ]),
];
export type PatientControllerServiceGetAssignedDevicesDefaultResponse = Awaited<
  ReturnType<typeof PatientControllerService.getAssignedDevices>
>;
export type PatientControllerServiceGetAssignedDevicesQueryResult<
  TData = PatientControllerServiceGetAssignedDevicesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientControllerServiceGetAssignedDevicesKey = "PatientControllerServiceGetAssignedDevices";
export const UsePatientControllerServiceGetAssignedDevicesKeyFn = (
  {
    page,
    patientId,
    size,
    sortBy,
    sortDirection,
    type,
    xTenantId,
  }: {
    page?: number;
    patientId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    type?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientControllerServiceGetAssignedDevicesKey,
  ...(queryKey ?? [{ page, patientId, size, sortBy, sortDirection, type, xTenantId }]),
];
export type PatientVitalControllerServiceGetPatientVitals1DefaultResponse = Awaited<
  ReturnType<typeof PatientVitalControllerService.getPatientVitals1>
>;
export type PatientVitalControllerServiceGetPatientVitals1QueryResult<
  TData = PatientVitalControllerServiceGetPatientVitals1DefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientVitalControllerServiceGetPatientVitals1Key = "PatientVitalControllerServiceGetPatientVitals1";
export const UsePatientVitalControllerServiceGetPatientVitals1KeyFn = (
  {
    endDate,
    page,
    patientUuid,
    size,
    sort,
    sortBy,
    startDate,
    timeFilter,
    vitalName,
    xTenantId,
  }: {
    endDate?: string;
    page?: number;
    patientUuid: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    startDate?: string;
    timeFilter?: "LAST_MONTH" | "LAST_WEEK" | "PAST_24_HOURS" | "DATE_RANGE";
    vitalName?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientVitalControllerServiceGetPatientVitals1Key,
  ...(queryKey ?? [{ endDate, page, patientUuid, size, sort, sortBy, startDate, timeFilter, vitalName, xTenantId }]),
];
export type PatientVitalControllerServiceGetPatientVitalByIdDefaultResponse = Awaited<
  ReturnType<typeof PatientVitalControllerService.getPatientVitalById>
>;
export type PatientVitalControllerServiceGetPatientVitalByIdQueryResult<
  TData = PatientVitalControllerServiceGetPatientVitalByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientVitalControllerServiceGetPatientVitalByIdKey =
  "PatientVitalControllerServiceGetPatientVitalById";
export const UsePatientVitalControllerServiceGetPatientVitalByIdKeyFn = (
  {
    patientVitalId,
    xTenantId,
  }: {
    patientVitalId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [usePatientVitalControllerServiceGetPatientVitalByIdKey, ...(queryKey ?? [{ patientVitalId, xTenantId }])];
export type PatientVitalControllerServiceGetPatientLatestVitalsDefaultResponse = Awaited<
  ReturnType<typeof PatientVitalControllerService.getPatientLatestVitals>
>;
export type PatientVitalControllerServiceGetPatientLatestVitalsQueryResult<
  TData = PatientVitalControllerServiceGetPatientLatestVitalsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientVitalControllerServiceGetPatientLatestVitalsKey =
  "PatientVitalControllerServiceGetPatientLatestVitals";
export const UsePatientVitalControllerServiceGetPatientLatestVitalsKeyFn = (
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [usePatientVitalControllerServiceGetPatientLatestVitalsKey, ...(queryKey ?? [{ patientUuid, xTenantId }])];
export type PatientVitalControllerServiceGetEcgValueDefaultResponse = Awaited<
  ReturnType<typeof PatientVitalControllerService.getEcgValue>
>;
export type PatientVitalControllerServiceGetEcgValueQueryResult<
  TData = PatientVitalControllerServiceGetEcgValueDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientVitalControllerServiceGetEcgValueKey = "PatientVitalControllerServiceGetEcgValue";
export const UsePatientVitalControllerServiceGetEcgValueKeyFn = (
  {
    ecgId,
    xTenantId,
  }: {
    ecgId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [usePatientVitalControllerServiceGetEcgValueKey, ...(queryKey ?? [{ ecgId, xTenantId }])];
export type PatientMedicationControllerServiceGetPatientMedicationDefaultResponse = Awaited<
  ReturnType<typeof PatientMedicationControllerService.getPatientMedication>
>;
export type PatientMedicationControllerServiceGetPatientMedicationQueryResult<
  TData = PatientMedicationControllerServiceGetPatientMedicationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientMedicationControllerServiceGetPatientMedicationKey =
  "PatientMedicationControllerServiceGetPatientMedication";
export const UsePatientMedicationControllerServiceGetPatientMedicationKeyFn = (
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sort,
    sortBy,
    status,
    timeFilter,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    status?: boolean;
    timeFilter?: "CURRENT" | "PAST";
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientMedicationControllerServiceGetPatientMedicationKey,
  ...(queryKey ?? [{ archive, page, patientUuid, searchString, size, sort, sortBy, status, timeFilter, xTenantId }]),
];
export type PatientMedicationControllerServiceGetPatientMedicationByIdDefaultResponse = Awaited<
  ReturnType<typeof PatientMedicationControllerService.getPatientMedicationById>
>;
export type PatientMedicationControllerServiceGetPatientMedicationByIdQueryResult<
  TData = PatientMedicationControllerServiceGetPatientMedicationByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientMedicationControllerServiceGetPatientMedicationByIdKey =
  "PatientMedicationControllerServiceGetPatientMedicationById";
export const UsePatientMedicationControllerServiceGetPatientMedicationByIdKeyFn = (
  {
    patientMedicationId,
    xTenantId,
  }: {
    patientMedicationId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientMedicationControllerServiceGetPatientMedicationByIdKey,
  ...(queryKey ?? [{ patientMedicationId, xTenantId }]),
];
export type PatientMedicationControllerServiceSendPatientNotificationDefaultResponse = Awaited<
  ReturnType<typeof PatientMedicationControllerService.sendPatientNotification>
>;
export type PatientMedicationControllerServiceSendPatientNotificationQueryResult<
  TData = PatientMedicationControllerServiceSendPatientNotificationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientMedicationControllerServiceSendPatientNotificationKey =
  "PatientMedicationControllerServiceSendPatientNotification";
export const UsePatientMedicationControllerServiceSendPatientNotificationKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [usePatientMedicationControllerServiceSendPatientNotificationKey, ...(queryKey ?? [{ xTenantId }])];
export type PatientDiagnosisControllerServiceGetPatientDiagnosisDefaultResponse = Awaited<
  ReturnType<typeof PatientDiagnosisControllerService.getPatientDiagnosis>
>;
export type PatientDiagnosisControllerServiceGetPatientDiagnosisQueryResult<
  TData = PatientDiagnosisControllerServiceGetPatientDiagnosisDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientDiagnosisControllerServiceGetPatientDiagnosisKey =
  "PatientDiagnosisControllerServiceGetPatientDiagnosis";
export const UsePatientDiagnosisControllerServiceGetPatientDiagnosisKeyFn = (
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientDiagnosisControllerServiceGetPatientDiagnosisKey,
  ...(queryKey ?? [{ archive, page, patientUuid, searchString, size, sortBy, sortDirection, status, xTenantId }]),
];
export type PatientDiagnosisControllerServiceGetPatientDiagnosisByIdDefaultResponse = Awaited<
  ReturnType<typeof PatientDiagnosisControllerService.getPatientDiagnosisById>
>;
export type PatientDiagnosisControllerServiceGetPatientDiagnosisByIdQueryResult<
  TData = PatientDiagnosisControllerServiceGetPatientDiagnosisByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientDiagnosisControllerServiceGetPatientDiagnosisByIdKey =
  "PatientDiagnosisControllerServiceGetPatientDiagnosisById";
export const UsePatientDiagnosisControllerServiceGetPatientDiagnosisByIdKeyFn = (
  {
    patientDiagnosisId,
    xTenantId,
  }: {
    patientDiagnosisId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientDiagnosisControllerServiceGetPatientDiagnosisByIdKey,
  ...(queryKey ?? [{ patientDiagnosisId, xTenantId }]),
];
export type ConsentFormControllerServiceGetAllConsentFormTemplateDefaultResponse = Awaited<
  ReturnType<typeof ConsentFormControllerService.getAllConsentFormTemplate>
>;
export type ConsentFormControllerServiceGetAllConsentFormTemplateQueryResult<
  TData = ConsentFormControllerServiceGetAllConsentFormTemplateDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useConsentFormControllerServiceGetAllConsentFormTemplateKey =
  "ConsentFormControllerServiceGetAllConsentFormTemplate";
export const UseConsentFormControllerServiceGetAllConsentFormTemplateKeyFn = (
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useConsentFormControllerServiceGetAllConsentFormTemplateKey,
  ...(queryKey ?? [{ archive, page, searchString, size, sortBy, sortDirection, status, xTenantId }]),
];
export type ConsentFormControllerServiceGetAllPatientConsentFormDefaultResponse = Awaited<
  ReturnType<typeof ConsentFormControllerService.getAllPatientConsentForm>
>;
export type ConsentFormControllerServiceGetAllPatientConsentFormQueryResult<
  TData = ConsentFormControllerServiceGetAllPatientConsentFormDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useConsentFormControllerServiceGetAllPatientConsentFormKey =
  "ConsentFormControllerServiceGetAllPatientConsentForm";
export const UseConsentFormControllerServiceGetAllPatientConsentFormKeyFn = (
  {
    page,
    patientUuid,
    searchString,
    size,
    sortBy,
    sortDirection,
    xTenantId,
  }: {
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useConsentFormControllerServiceGetAllPatientConsentFormKey,
  ...(queryKey ?? [{ page, patientUuid, searchString, size, sortBy, sortDirection, xTenantId }]),
];
export type ConsentFormControllerServiceGetPatientConsentFormByIdDefaultResponse = Awaited<
  ReturnType<typeof ConsentFormControllerService.getPatientConsentFormById>
>;
export type ConsentFormControllerServiceGetPatientConsentFormByIdQueryResult<
  TData = ConsentFormControllerServiceGetPatientConsentFormByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useConsentFormControllerServiceGetPatientConsentFormByIdKey =
  "ConsentFormControllerServiceGetPatientConsentFormById";
export const UseConsentFormControllerServiceGetPatientConsentFormByIdKeyFn = (
  {
    patientConsentFormUuid,
    xTenantId,
  }: {
    patientConsentFormUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useConsentFormControllerServiceGetPatientConsentFormByIdKey,
  ...(queryKey ?? [{ patientConsentFormUuid, xTenantId }]),
];
export type ConsentFormControllerServiceGetConsentFormIdDefaultResponse = Awaited<
  ReturnType<typeof ConsentFormControllerService.getConsentFormId>
>;
export type ConsentFormControllerServiceGetConsentFormIdQueryResult<
  TData = ConsentFormControllerServiceGetConsentFormIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useConsentFormControllerServiceGetConsentFormIdKey = "ConsentFormControllerServiceGetConsentFormId";
export const UseConsentFormControllerServiceGetConsentFormIdKeyFn = (
  {
    consentFormId,
    xTenantId,
  }: {
    consentFormId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useConsentFormControllerServiceGetConsentFormIdKey, ...(queryKey ?? [{ consentFormId, xTenantId }])];
export type PatientCarePlanControllerServiceGetAllCarePlansDefaultResponse = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.getAllCarePlans>
>;
export type PatientCarePlanControllerServiceGetAllCarePlansQueryResult<
  TData = PatientCarePlanControllerServiceGetAllCarePlansDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientCarePlanControllerServiceGetAllCarePlansKey = "PatientCarePlanControllerServiceGetAllCarePlans";
export const UsePatientCarePlanControllerServiceGetAllCarePlansKeyFn = (
  {
    archive,
    carePlanStatus,
    page,
    patientId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    timeFilter,
    xTenantId,
  }: {
    archive?: boolean;
    carePlanStatus?: string;
    page?: number;
    patientId: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    timeFilter?: "CURRENT" | "PAST";
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientCarePlanControllerServiceGetAllCarePlansKey,
  ...(queryKey ?? [
    {
      archive,
      carePlanStatus,
      page,
      patientId,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      timeFilter,
      xTenantId,
    },
  ]),
];
export type PatientCarePlanControllerServiceGetPatientCarePlanByIdDefaultResponse = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.getPatientCarePlanById>
>;
export type PatientCarePlanControllerServiceGetPatientCarePlanByIdQueryResult<
  TData = PatientCarePlanControllerServiceGetPatientCarePlanByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientCarePlanControllerServiceGetPatientCarePlanByIdKey =
  "PatientCarePlanControllerServiceGetPatientCarePlanById";
export const UsePatientCarePlanControllerServiceGetPatientCarePlanByIdKeyFn = (
  {
    patientCarePlanId,
    xTenantId,
  }: {
    patientCarePlanId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientCarePlanControllerServiceGetPatientCarePlanByIdKey,
  ...(queryKey ?? [{ patientCarePlanId, xTenantId }]),
];
export type PatientCarePlanControllerServiceGetProgramGoalTrackDetailsDefaultResponse = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.getProgramGoalTrackDetails>
>;
export type PatientCarePlanControllerServiceGetProgramGoalTrackDetailsQueryResult<
  TData = PatientCarePlanControllerServiceGetProgramGoalTrackDetailsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientCarePlanControllerServiceGetProgramGoalTrackDetailsKey =
  "PatientCarePlanControllerServiceGetProgramGoalTrackDetails";
export const UsePatientCarePlanControllerServiceGetProgramGoalTrackDetailsKeyFn = (
  {
    programGoalId,
    xTenantId,
  }: {
    programGoalId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientCarePlanControllerServiceGetProgramGoalTrackDetailsKey,
  ...(queryKey ?? [{ programGoalId, xTenantId }]),
];
export type PatientCarePlanControllerServiceGetPatientActiveCarePlanDefaultResponse = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.getPatientActiveCarePlan>
>;
export type PatientCarePlanControllerServiceGetPatientActiveCarePlanQueryResult<
  TData = PatientCarePlanControllerServiceGetPatientActiveCarePlanDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientCarePlanControllerServiceGetPatientActiveCarePlanKey =
  "PatientCarePlanControllerServiceGetPatientActiveCarePlan";
export const UsePatientCarePlanControllerServiceGetPatientActiveCarePlanKeyFn = (
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [usePatientCarePlanControllerServiceGetPatientActiveCarePlanKey, ...(queryKey ?? [{ patientId, xTenantId }])];
export type PatientAllergyControllerServiceGetPatientAllergyDefaultResponse = Awaited<
  ReturnType<typeof PatientAllergyControllerService.getPatientAllergy>
>;
export type PatientAllergyControllerServiceGetPatientAllergyQueryResult<
  TData = PatientAllergyControllerServiceGetPatientAllergyDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientAllergyControllerServiceGetPatientAllergyKey =
  "PatientAllergyControllerServiceGetPatientAllergy";
export const UsePatientAllergyControllerServiceGetPatientAllergyKeyFn = (
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sort,
    sortBy,
    status,
    type,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    status?: boolean;
    type?: "OTHER" | "DRUG" | "FOOD" | "ENVIRONMENT";
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  usePatientAllergyControllerServiceGetPatientAllergyKey,
  ...(queryKey ?? [{ archive, page, patientUuid, searchString, size, sort, sortBy, status, type, xTenantId }]),
];
export type PatientAllergyControllerServiceGetPatientAllergyByIdDefaultResponse = Awaited<
  ReturnType<typeof PatientAllergyControllerService.getPatientAllergyById>
>;
export type PatientAllergyControllerServiceGetPatientAllergyByIdQueryResult<
  TData = PatientAllergyControllerServiceGetPatientAllergyByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const usePatientAllergyControllerServiceGetPatientAllergyByIdKey =
  "PatientAllergyControllerServiceGetPatientAllergyById";
export const UsePatientAllergyControllerServiceGetPatientAllergyByIdKeyFn = (
  {
    patientAllergyId,
    xTenantId,
  }: {
    patientAllergyId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [usePatientAllergyControllerServiceGetPatientAllergyByIdKey, ...(queryKey ?? [{ patientAllergyId, xTenantId }])];
export type MedicalCodeControllerServiceGetMedicalCodesDefaultResponse = Awaited<
  ReturnType<typeof MedicalCodeControllerService.getMedicalCodes>
>;
export type MedicalCodeControllerServiceGetMedicalCodesQueryResult<
  TData = MedicalCodeControllerServiceGetMedicalCodesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useMedicalCodeControllerServiceGetMedicalCodesKey = "MedicalCodeControllerServiceGetMedicalCodes";
export const UseMedicalCodeControllerServiceGetMedicalCodesKeyFn = (
  {
    active,
    archive,
    page,
    searchString,
    size,
    sort,
    sortBy,
    type,
    xTenantId,
  }: {
    active?: boolean;
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    type?: "ICD10" | "CPT" | "ALL";
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useMedicalCodeControllerServiceGetMedicalCodesKey,
  ...(queryKey ?? [{ active, archive, page, searchString, size, sort, sortBy, type, xTenantId }]),
];
export type MedicalCodeControllerServiceGetMedicalCodeByIdDefaultResponse = Awaited<
  ReturnType<typeof MedicalCodeControllerService.getMedicalCodeById>
>;
export type MedicalCodeControllerServiceGetMedicalCodeByIdQueryResult<
  TData = MedicalCodeControllerServiceGetMedicalCodeByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useMedicalCodeControllerServiceGetMedicalCodeByIdKey = "MedicalCodeControllerServiceGetMedicalCodeById";
export const UseMedicalCodeControllerServiceGetMedicalCodeByIdKeyFn = (
  {
    medicalCodeId,
    xTenantId,
  }: {
    medicalCodeId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useMedicalCodeControllerServiceGetMedicalCodeByIdKey, ...(queryKey ?? [{ medicalCodeId, xTenantId }])];
export type LocationControllerServiceGetAllLocationsDefaultResponse = Awaited<
  ReturnType<typeof LocationControllerService.getAllLocations>
>;
export type LocationControllerServiceGetAllLocationsQueryResult<
  TData = LocationControllerServiceGetAllLocationsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useLocationControllerServiceGetAllLocationsKey = "LocationControllerServiceGetAllLocations";
export const UseLocationControllerServiceGetAllLocationsKeyFn = (
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useLocationControllerServiceGetAllLocationsKey,
  ...(queryKey ?? [{ archive, page, searchString, size, sortBy, sortDirection, state, status, xTenantId }]),
];
export type LocationControllerServiceGetLocationByIdDefaultResponse = Awaited<
  ReturnType<typeof LocationControllerService.getLocationById>
>;
export type LocationControllerServiceGetLocationByIdQueryResult<
  TData = LocationControllerServiceGetLocationByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useLocationControllerServiceGetLocationByIdKey = "LocationControllerServiceGetLocationById";
export const UseLocationControllerServiceGetLocationByIdKeyFn = (
  {
    locationId,
    xTenantId,
  }: {
    locationId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useLocationControllerServiceGetLocationByIdKey, ...(queryKey ?? [{ locationId, xTenantId }])];
export type DeviceControllerServiceGetAllDevicesDefaultResponse = Awaited<
  ReturnType<typeof DeviceControllerService.getAllDevices>
>;
export type DeviceControllerServiceGetAllDevicesQueryResult<
  TData = DeviceControllerServiceGetAllDevicesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useDeviceControllerServiceGetAllDevicesKey = "DeviceControllerServiceGetAllDevices";
export const UseDeviceControllerServiceGetAllDevicesKeyFn = (
  {
    archive,
    category,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    type,
    xTenantId,
  }: {
    archive?: boolean;
    category?: string;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    type?: string;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useDeviceControllerServiceGetAllDevicesKey,
  ...(queryKey ?? [{ archive, category, page, searchString, size, sortBy, sortDirection, status, type, xTenantId }]),
];
export type DeviceControllerServiceGetDeviceByIdDefaultResponse = Awaited<
  ReturnType<typeof DeviceControllerService.getDeviceById>
>;
export type DeviceControllerServiceGetDeviceByIdQueryResult<
  TData = DeviceControllerServiceGetDeviceByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useDeviceControllerServiceGetDeviceByIdKey = "DeviceControllerServiceGetDeviceById";
export const UseDeviceControllerServiceGetDeviceByIdKeyFn = (
  {
    deviceUuid,
    xTenantId,
  }: {
    deviceUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useDeviceControllerServiceGetDeviceByIdKey, ...(queryKey ?? [{ deviceUuid, xTenantId }])];
export type ClinicalNoteControllerServiceGetClinicalNoteByUuidDefaultResponse = Awaited<
  ReturnType<typeof ClinicalNoteControllerService.getClinicalNoteByUuid>
>;
export type ClinicalNoteControllerServiceGetClinicalNoteByUuidQueryResult<
  TData = ClinicalNoteControllerServiceGetClinicalNoteByUuidDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useClinicalNoteControllerServiceGetClinicalNoteByUuidKey =
  "ClinicalNoteControllerServiceGetClinicalNoteByUuid";
export const UseClinicalNoteControllerServiceGetClinicalNoteByUuidKeyFn = (
  {
    clinicalNoteUuid,
    xTenantId,
  }: {
    clinicalNoteUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useClinicalNoteControllerServiceGetClinicalNoteByUuidKey, ...(queryKey ?? [{ clinicalNoteUuid, xTenantId }])];
export type ClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdDefaultResponse = Awaited<
  ReturnType<typeof ClinicalNoteControllerService.getClinicalNoteByAppointmentId>
>;
export type ClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdQueryResult<
  TData = ClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdKey =
  "ClinicalNoteControllerServiceGetClinicalNoteByAppointmentId";
export const UseClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdKeyFn = (
  {
    appointmentId,
    xTenantId,
  }: {
    appointmentId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdKey,
  ...(queryKey ?? [{ appointmentId, xTenantId }]),
];
export type CarePlanControllerServiceGetAllCarePlans1DefaultResponse = Awaited<
  ReturnType<typeof CarePlanControllerService.getAllCarePlans1>
>;
export type CarePlanControllerServiceGetAllCarePlans1QueryResult<
  TData = CarePlanControllerServiceGetAllCarePlans1DefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useCarePlanControllerServiceGetAllCarePlans1Key = "CarePlanControllerServiceGetAllCarePlans1";
export const UseCarePlanControllerServiceGetAllCarePlans1KeyFn = (
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useCarePlanControllerServiceGetAllCarePlans1Key,
  ...(queryKey ?? [{ archive, page, searchString, size, sortBy, sortDirection, status, xTenantId }]),
];
export type CarePlanControllerServiceGetCarePlanByIdDefaultResponse = Awaited<
  ReturnType<typeof CarePlanControllerService.getCarePlanById>
>;
export type CarePlanControllerServiceGetCarePlanByIdQueryResult<
  TData = CarePlanControllerServiceGetCarePlanByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useCarePlanControllerServiceGetCarePlanByIdKey = "CarePlanControllerServiceGetCarePlanById";
export const UseCarePlanControllerServiceGetCarePlanByIdKeyFn = (
  {
    carePlanId,
    globalCarePlan,
    xTenantId,
  }: {
    carePlanId: string;
    globalCarePlan?: boolean;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useCarePlanControllerServiceGetCarePlanByIdKey, ...(queryKey ?? [{ carePlanId, globalCarePlan, xTenantId }])];
export type CarePlanControllerServiceGetAllReferenceRangesDefaultResponse = Awaited<
  ReturnType<typeof CarePlanControllerService.getAllReferenceRanges>
>;
export type CarePlanControllerServiceGetAllReferenceRangesQueryResult<
  TData = CarePlanControllerServiceGetAllReferenceRangesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useCarePlanControllerServiceGetAllReferenceRangesKey = "CarePlanControllerServiceGetAllReferenceRanges";
export const UseCarePlanControllerServiceGetAllReferenceRangesKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useCarePlanControllerServiceGetAllReferenceRangesKey, ...(queryKey ?? [{ xTenantId }])];
export type CarePlanControllerServiceGetAllProtocolsDefaultResponse = Awaited<
  ReturnType<typeof CarePlanControllerService.getAllProtocols>
>;
export type CarePlanControllerServiceGetAllProtocolsQueryResult<
  TData = CarePlanControllerServiceGetAllProtocolsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useCarePlanControllerServiceGetAllProtocolsKey = "CarePlanControllerServiceGetAllProtocols";
export const UseCarePlanControllerServiceGetAllProtocolsKeyFn = (
  {
    protocolType,
    xTenantId,
  }: {
    protocolType: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useCarePlanControllerServiceGetAllProtocolsKey, ...(queryKey ?? [{ protocolType, xTenantId }])];
export type AppointmentControllerServiceGetAllAppointmentsDefaultResponse = Awaited<
  ReturnType<typeof AppointmentControllerService.getAllAppointments>
>;
export type AppointmentControllerServiceGetAllAppointmentsQueryResult<
  TData = AppointmentControllerServiceGetAllAppointmentsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAppointmentControllerServiceGetAllAppointmentsKey = "AppointmentControllerServiceGetAllAppointments";
export const UseAppointmentControllerServiceGetAllAppointmentsKeyFn = (
  {
    assigned,
    endDate,
    filter,
    mode,
    nurseId,
    page,
    patientId,
    providerId,
    size,
    sortBy,
    sortDirection,
    startDate,
    status,
    xTenantId,
  }: {
    assigned?: boolean;
    endDate?: string;
    filter?: "PAST" | "ALL" | "UPCOMING" | "REQUESTED";
    mode?: string;
    nurseId?: string;
    page?: number;
    patientId?: string;
    providerId?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    startDate?: string;
    status?: string;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useAppointmentControllerServiceGetAllAppointmentsKey,
  ...(queryKey ?? [
    {
      assigned,
      endDate,
      filter,
      mode,
      nurseId,
      page,
      patientId,
      providerId,
      size,
      sortBy,
      sortDirection,
      startDate,
      status,
      xTenantId,
    },
  ]),
];
export type AppointmentControllerServiceGetAppointmentByIdDefaultResponse = Awaited<
  ReturnType<typeof AppointmentControllerService.getAppointmentById>
>;
export type AppointmentControllerServiceGetAppointmentByIdQueryResult<
  TData = AppointmentControllerServiceGetAppointmentByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAppointmentControllerServiceGetAppointmentByIdKey = "AppointmentControllerServiceGetAppointmentById";
export const UseAppointmentControllerServiceGetAppointmentByIdKeyFn = (
  {
    appointmentId,
    xTenantId,
  }: {
    appointmentId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useAppointmentControllerServiceGetAppointmentByIdKey, ...(queryKey ?? [{ appointmentId, xTenantId }])];
export type AppointmentControllerServiceEscalateAppointmentDefaultResponse = Awaited<
  ReturnType<typeof AppointmentControllerService.escalateAppointment>
>;
export type AppointmentControllerServiceEscalateAppointmentQueryResult<
  TData = AppointmentControllerServiceEscalateAppointmentDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAppointmentControllerServiceEscalateAppointmentKey = "AppointmentControllerServiceEscalateAppointment";
export const UseAppointmentControllerServiceEscalateAppointmentKeyFn = (
  {
    email,
    schema,
    xTenantId,
  }: {
    email: string;
    schema?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useAppointmentControllerServiceEscalateAppointmentKey, ...(queryKey ?? [{ email, schema, xTenantId }])];
export type AppointmentControllerServiceGetAppointmentListDefaultResponse = Awaited<
  ReturnType<typeof AppointmentControllerService.getAppointmentList>
>;
export type AppointmentControllerServiceGetAppointmentListQueryResult<
  TData = AppointmentControllerServiceGetAppointmentListDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAppointmentControllerServiceGetAppointmentListKey = "AppointmentControllerServiceGetAppointmentList";
export const UseAppointmentControllerServiceGetAppointmentListKeyFn = (
  {
    endDate,
    filter,
    nurseId,
    patientUuid,
    providerId,
    startDate,
    status,
    type,
    xTenantId,
  }: {
    endDate: string;
    filter?: "PAST" | "ALL" | "UPCOMING" | "REQUESTED";
    nurseId?: string;
    patientUuid?: string;
    providerId?: string;
    startDate: string;
    status?: string;
    type?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useAppointmentControllerServiceGetAppointmentListKey,
  ...(queryKey ?? [{ endDate, filter, nurseId, patientUuid, providerId, startDate, status, type, xTenantId }]),
];
export type AvailabilityControllerServiceGetProviderSlotsDefaultResponse = Awaited<
  ReturnType<typeof AvailabilityControllerService.getProviderSlots>
>;
export type AvailabilityControllerServiceGetProviderSlotsQueryResult<
  TData = AvailabilityControllerServiceGetProviderSlotsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAvailabilityControllerServiceGetProviderSlotsKey = "AvailabilityControllerServiceGetProviderSlots";
export const UseAvailabilityControllerServiceGetProviderSlotsKeyFn = (
  {
    duration,
    endDate,
    page,
    providerUuid,
    size,
    startDate,
    xTenantId,
  }: {
    duration: number;
    endDate?: string;
    page?: number;
    providerUuid: string;
    size?: number;
    startDate?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useAvailabilityControllerServiceGetProviderSlotsKey,
  ...(queryKey ?? [{ duration, endDate, page, providerUuid, size, startDate, xTenantId }]),
];
export type AvailabilityControllerServiceGetProviderAvailabilitySettingDefaultResponse = Awaited<
  ReturnType<typeof AvailabilityControllerService.getProviderAvailabilitySetting>
>;
export type AvailabilityControllerServiceGetProviderAvailabilitySettingQueryResult<
  TData = AvailabilityControllerServiceGetProviderAvailabilitySettingDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAvailabilityControllerServiceGetProviderAvailabilitySettingKey =
  "AvailabilityControllerServiceGetProviderAvailabilitySetting";
export const UseAvailabilityControllerServiceGetProviderAvailabilitySettingKeyFn = (
  {
    providerUuid,
    xTenantId,
  }: {
    providerUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useAvailabilityControllerServiceGetProviderAvailabilitySettingKey,
  ...(queryKey ?? [{ providerUuid, xTenantId }]),
];
export type AiControllerServiceGetPatientReportsDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.getPatientReports>
>;
export type AiControllerServiceGetPatientReportsQueryResult<
  TData = AiControllerServiceGetPatientReportsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceGetPatientReportsKey = "AiControllerServiceGetPatientReports";
export const UseAiControllerServiceGetPatientReportsKeyFn = (
  {
    month,
    page,
    patientUuid,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month?: number;
    page?: number;
    patientUuid: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year?: number;
  },
  queryKey?: unknown[]
) => [
  useAiControllerServiceGetPatientReportsKey,
  ...(queryKey ?? [{ month, page, patientUuid, size, sortBy, sortDirection, xTenantId, year }]),
];
export type AiControllerServiceLlmauGetVoiceRecommendationDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.llmauGetVoiceRecommendation>
>;
export type AiControllerServiceLlmauGetVoiceRecommendationQueryResult<
  TData = AiControllerServiceLlmauGetVoiceRecommendationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceLlmauGetVoiceRecommendationKey = "AiControllerServiceLlmauGetVoiceRecommendation";
export const UseAiControllerServiceLlmauGetVoiceRecommendationKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useAiControllerServiceLlmauGetVoiceRecommendationKey, ...(queryKey ?? [{ xTenantId }])];
export type AiControllerServiceGetNurseReportsDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.getNurseReports>
>;
export type AiControllerServiceGetNurseReportsQueryResult<
  TData = AiControllerServiceGetNurseReportsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceGetNurseReportsKey = "AiControllerServiceGetNurseReports";
export const UseAiControllerServiceGetNurseReportsKeyFn = (
  {
    reportUuid,
    xTenantId,
  }: {
    reportUuid: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useAiControllerServiceGetNurseReportsKey, ...(queryKey ?? [{ reportUuid, xTenantId }])];
export type AiControllerServiceLlmauGetMyNurseAvatarDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.llmauGetMyNurseAvatar>
>;
export type AiControllerServiceLlmauGetMyNurseAvatarQueryResult<
  TData = AiControllerServiceLlmauGetMyNurseAvatarDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceLlmauGetMyNurseAvatarKey = "AiControllerServiceLlmauGetMyNurseAvatar";
export const UseAiControllerServiceLlmauGetMyNurseAvatarKeyFn = (
  {
    providerId,
    xTenantId,
  }: {
    providerId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useAiControllerServiceLlmauGetMyNurseAvatarKey, ...(queryKey ?? [{ providerId, xTenantId }])];
export type AiControllerServiceGetNurseActionStatisticsDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.getNurseActionStatistics>
>;
export type AiControllerServiceGetNurseActionStatisticsQueryResult<
  TData = AiControllerServiceGetNurseActionStatisticsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceGetNurseActionStatisticsKey = "AiControllerServiceGetNurseActionStatistics";
export const UseAiControllerServiceGetNurseActionStatisticsKeyFn = (
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: unknown[]
) => [useAiControllerServiceGetNurseActionStatisticsKey, ...(queryKey ?? [{ month, patientUuid, xTenantId, year }])];
export type AiControllerServiceLlmauGetAvatarDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.llmauGetAvatar>
>;
export type AiControllerServiceLlmauGetAvatarQueryResult<
  TData = AiControllerServiceLlmauGetAvatarDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceLlmauGetAvatarKey = "AiControllerServiceLlmauGetAvatar";
export const UseAiControllerServiceLlmauGetAvatarKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useAiControllerServiceLlmauGetAvatarKey, ...(queryKey ?? [{ xTenantId }])];
export type AiControllerServiceGetNurseActionInExcelDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.getNurseActionInExcel>
>;
export type AiControllerServiceGetNurseActionInExcelQueryResult<
  TData = AiControllerServiceGetNurseActionInExcelDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceGetNurseActionInExcelKey = "AiControllerServiceGetNurseActionInExcel";
export const UseAiControllerServiceGetNurseActionInExcelKeyFn = (
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: unknown[]
) => [useAiControllerServiceGetNurseActionInExcelKey, ...(queryKey ?? [{ month, patientUuid, xTenantId, year }])];
export type AiControllerServiceGetChatbotReportsDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.getChatbotReports>
>;
export type AiControllerServiceGetChatbotReportsQueryResult<
  TData = AiControllerServiceGetChatbotReportsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceGetChatbotReportsKey = "AiControllerServiceGetChatbotReports";
export const UseAiControllerServiceGetChatbotReportsKeyFn = (
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: unknown[]
) => [useAiControllerServiceGetChatbotReportsKey, ...(queryKey ?? [{ month, patientUuid, xTenantId, year }])];
export type AiControllerServiceGetChatbotHistoryDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.getChatbotHistory>
>;
export type AiControllerServiceGetChatbotHistoryQueryResult<
  TData = AiControllerServiceGetChatbotHistoryDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceGetChatbotHistoryKey = "AiControllerServiceGetChatbotHistory";
export const UseAiControllerServiceGetChatbotHistoryKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useAiControllerServiceGetChatbotHistoryKey, ...(queryKey ?? [{ xTenantId }])];
export type AiControllerServiceLlmauGetAvatarVideoByTitleDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.llmauGetAvatarVideoByTitle>
>;
export type AiControllerServiceLlmauGetAvatarVideoByTitleQueryResult<
  TData = AiControllerServiceLlmauGetAvatarVideoByTitleDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceLlmauGetAvatarVideoByTitleKey = "AiControllerServiceLlmauGetAvatarVideoByTitle";
export const UseAiControllerServiceLlmauGetAvatarVideoByTitleKeyFn = (
  {
    providerId,
    videoTitle,
    xTenantId,
  }: {
    providerId: string;
    videoTitle: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useAiControllerServiceLlmauGetAvatarVideoByTitleKey, ...(queryKey ?? [{ providerId, videoTitle, xTenantId }])];
export type AiControllerServiceAvqGetAlertAudioDefaultResponse = Awaited<
  ReturnType<typeof AiControllerService.avqGetAlertAudio>
>;
export type AiControllerServiceAvqGetAlertAudioQueryResult<
  TData = AiControllerServiceAvqGetAlertAudioDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useAiControllerServiceAvqGetAlertAudioKey = "AiControllerServiceAvqGetAlertAudio";
export const UseAiControllerServiceAvqGetAlertAudioKeyFn = (
  {
    providerId,
    videoTitle,
    xTenantId,
  }: {
    providerId: string;
    videoTitle: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useAiControllerServiceAvqGetAlertAudioKey, ...(queryKey ?? [{ providerId, videoTitle, xTenantId }])];
export type VitalControllerServiceGetPatientVitalsDefaultResponse = Awaited<
  ReturnType<typeof VitalControllerService.getPatientVitals>
>;
export type VitalControllerServiceGetPatientVitalsQueryResult<
  TData = VitalControllerServiceGetPatientVitalsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useVitalControllerServiceGetPatientVitalsKey = "VitalControllerServiceGetPatientVitals";
export const UseVitalControllerServiceGetPatientVitalsKeyFn = (
  {
    page,
    searchString,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    page?: number;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useVitalControllerServiceGetPatientVitalsKey,
  ...(queryKey ?? [{ page, searchString, size, sort, sortBy, xTenantId }]),
];
export type ZoomControllerServiceGetAuthTokenDefaultResponse = Awaited<
  ReturnType<typeof ZoomControllerService.getAuthToken>
>;
export type ZoomControllerServiceGetAuthTokenQueryResult<
  TData = ZoomControllerServiceGetAuthTokenDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useZoomControllerServiceGetAuthTokenKey = "ZoomControllerServiceGetAuthToken";
export const UseZoomControllerServiceGetAuthTokenKeyFn = (
  {
    roomId,
    xTenantId,
  }: {
    roomId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useZoomControllerServiceGetAuthTokenKey, ...(queryKey ?? [{ roomId, xTenantId }])];
export type ZoomControllerServiceSubscribeDefaultResponse = Awaited<ReturnType<typeof ZoomControllerService.subscribe>>;
export type ZoomControllerServiceSubscribeQueryResult<
  TData = ZoomControllerServiceSubscribeDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useZoomControllerServiceSubscribeKey = "ZoomControllerServiceSubscribe";
export const UseZoomControllerServiceSubscribeKeyFn = (
  {
    eventKey,
    xTenantId,
  }: {
    eventKey: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useZoomControllerServiceSubscribeKey, ...(queryKey ?? [{ eventKey, xTenantId }])];
export type ZoomControllerServiceEmitDefaultResponse = Awaited<ReturnType<typeof ZoomControllerService.emit>>;
export type ZoomControllerServiceEmitQueryResult<
  TData = ZoomControllerServiceEmitDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useZoomControllerServiceEmitKey = "ZoomControllerServiceEmit";
export const UseZoomControllerServiceEmitKeyFn = (
  {
    eventKey,
    xTenantId,
  }: {
    eventKey: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useZoomControllerServiceEmitKey, ...(queryKey ?? [{ eventKey, xTenantId }])];
export type LicenseStateControllerServiceGetAllLicensedStatesDefaultResponse = Awaited<
  ReturnType<typeof LicenseStateControllerService.getAllLicensedStates>
>;
export type LicenseStateControllerServiceGetAllLicensedStatesQueryResult<
  TData = LicenseStateControllerServiceGetAllLicensedStatesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useLicenseStateControllerServiceGetAllLicensedStatesKey =
  "LicenseStateControllerServiceGetAllLicensedStates";
export const UseLicenseStateControllerServiceGetAllLicensedStatesKeyFn = (
  {
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    xTenantId,
  }: {
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useLicenseStateControllerServiceGetAllLicensedStatesKey,
  ...(queryKey ?? [{ page, searchString, size, sortBy, sortDirection, xTenantId }]),
];
export type EqrControllerServiceGetInfusionTherapyByIdDefaultResponse = Awaited<
  ReturnType<typeof EqrControllerService.getInfusionTherapyById>
>;
export type EqrControllerServiceGetInfusionTherapyByIdQueryResult<
  TData = EqrControllerServiceGetInfusionTherapyByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEqrControllerServiceGetInfusionTherapyByIdKey = "EqrControllerServiceGetInfusionTherapyById";
export const UseEqrControllerServiceGetInfusionTherapyByIdKeyFn = (
  {
    therapyId,
    xTenantId,
  }: {
    therapyId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useEqrControllerServiceGetInfusionTherapyByIdKey, ...(queryKey ?? [{ therapyId, xTenantId }])];
export type EhrProviderControllerServiceGetAllEhrProvidersDefaultResponse = Awaited<
  ReturnType<typeof EhrProviderControllerService.getAllEhrProviders>
>;
export type EhrProviderControllerServiceGetAllEhrProvidersQueryResult<
  TData = EhrProviderControllerServiceGetAllEhrProvidersDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrProviderControllerServiceGetAllEhrProvidersKey = "EhrProviderControllerServiceGetAllEhrProviders";
export const UseEhrProviderControllerServiceGetAllEhrProvidersKeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useEhrProviderControllerServiceGetAllEhrProvidersKey, ...(queryKey ?? [{ xTenantId }])];
export type EhrControllerServiceGetPractitionerByProviderIdDefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.getPractitionerByProviderId>
>;
export type EhrControllerServiceGetPractitionerByProviderIdQueryResult<
  TData = EhrControllerServiceGetPractitionerByProviderIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceGetPractitionerByProviderIdKey = "EhrControllerServiceGetPractitionerByProviderId";
export const UseEhrControllerServiceGetPractitionerByProviderIdKeyFn = (
  {
    practitionerId,
    xTenantId,
  }: {
    practitionerId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useEhrControllerServiceGetPractitionerByProviderIdKey, ...(queryKey ?? [{ practitionerId, xTenantId }])];
export type EhrControllerServiceSearchPractitionersDefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.searchPractitioners>
>;
export type EhrControllerServiceSearchPractitionersQueryResult<
  TData = EhrControllerServiceSearchPractitionersDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceSearchPractitionersKey = "EhrControllerServiceSearchPractitioners";
export const UseEhrControllerServiceSearchPractitionersKeyFn = (
  {
    name,
    xTenantId,
  }: {
    name: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useEhrControllerServiceSearchPractitionersKey, ...(queryKey ?? [{ name, xTenantId }])];
export type EhrControllerServiceGetPatientVitals2DefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.getPatientVitals2>
>;
export type EhrControllerServiceGetPatientVitals2QueryResult<
  TData = EhrControllerServiceGetPatientVitals2DefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceGetPatientVitals2Key = "EhrControllerServiceGetPatientVitals2";
export const UseEhrControllerServiceGetPatientVitals2KeyFn = (
  {
    date,
    patientId,
    xTenantId,
  }: {
    date?: string;
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useEhrControllerServiceGetPatientVitals2Key, ...(queryKey ?? [{ date, patientId, xTenantId }])];
export type EhrControllerServiceGetPatientEncounterDiagnosisByPatientIdDefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.getPatientEncounterDiagnosisByPatientId>
>;
export type EhrControllerServiceGetPatientEncounterDiagnosisByPatientIdQueryResult<
  TData = EhrControllerServiceGetPatientEncounterDiagnosisByPatientIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceGetPatientEncounterDiagnosisByPatientIdKey =
  "EhrControllerServiceGetPatientEncounterDiagnosisByPatientId";
export const UseEhrControllerServiceGetPatientEncounterDiagnosisByPatientIdKeyFn = (
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useEhrControllerServiceGetPatientEncounterDiagnosisByPatientIdKey, ...(queryKey ?? [{ patientId, xTenantId }])];
export type EhrControllerServiceGetAllergiesByPatientIdDefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.getAllergiesByPatientId>
>;
export type EhrControllerServiceGetAllergiesByPatientIdQueryResult<
  TData = EhrControllerServiceGetAllergiesByPatientIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceGetAllergiesByPatientIdKey = "EhrControllerServiceGetAllergiesByPatientId";
export const UseEhrControllerServiceGetAllergiesByPatientIdKeyFn = (
  {
    clinicalStatus,
    patientId,
    recordedDate,
    xTenantId,
  }: {
    clinicalStatus?: string;
    patientId: string;
    recordedDate?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useEhrControllerServiceGetAllergiesByPatientIdKey,
  ...(queryKey ?? [{ clinicalStatus, patientId, recordedDate, xTenantId }]),
];
export type EhrControllerServiceSearchPatientsDefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.searchPatients>
>;
export type EhrControllerServiceSearchPatientsQueryResult<
  TData = EhrControllerServiceSearchPatientsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceSearchPatientsKey = "EhrControllerServiceSearchPatients";
export const UseEhrControllerServiceSearchPatientsKeyFn = (
  {
    birthdate,
    family,
    given,
    organisationId,
    patientId,
    xTenantId,
  }: {
    birthdate?: string;
    family?: string;
    given?: string;
    organisationId: string;
    patientId?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useEhrControllerServiceSearchPatientsKey,
  ...(queryKey ?? [{ birthdate, family, given, organisationId, patientId, xTenantId }]),
];
export type EhrControllerServiceGetOrganizationByPracticeIdDefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.getOrganizationByPracticeId>
>;
export type EhrControllerServiceGetOrganizationByPracticeIdQueryResult<
  TData = EhrControllerServiceGetOrganizationByPracticeIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceGetOrganizationByPracticeIdKey = "EhrControllerServiceGetOrganizationByPracticeId";
export const UseEhrControllerServiceGetOrganizationByPracticeIdKeyFn = (
  {
    practiceId,
    xTenantId,
  }: {
    practiceId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useEhrControllerServiceGetOrganizationByPracticeIdKey, ...(queryKey ?? [{ practiceId, xTenantId }])];
export type EhrControllerServiceGetMedicationRequestByPatientIdDefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.getMedicationRequestByPatientId>
>;
export type EhrControllerServiceGetMedicationRequestByPatientIdQueryResult<
  TData = EhrControllerServiceGetMedicationRequestByPatientIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceGetMedicationRequestByPatientIdKey =
  "EhrControllerServiceGetMedicationRequestByPatientId";
export const UseEhrControllerServiceGetMedicationRequestByPatientIdKeyFn = (
  {
    patientId,
    status,
    xTenantId,
  }: {
    patientId: string;
    status?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useEhrControllerServiceGetMedicationRequestByPatientIdKey, ...(queryKey ?? [{ patientId, status, xTenantId }])];
export type EhrControllerServiceGetMedicationDispenseByPatientIdDefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.getMedicationDispenseByPatientId>
>;
export type EhrControllerServiceGetMedicationDispenseByPatientIdQueryResult<
  TData = EhrControllerServiceGetMedicationDispenseByPatientIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceGetMedicationDispenseByPatientIdKey =
  "EhrControllerServiceGetMedicationDispenseByPatientId";
export const UseEhrControllerServiceGetMedicationDispenseByPatientIdKeyFn = (
  {
    patientId,
    status,
    whenhandedover,
    xTenantId,
  }: {
    patientId: string;
    status?: string;
    whenhandedover?: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [
  useEhrControllerServiceGetMedicationDispenseByPatientIdKey,
  ...(queryKey ?? [{ patientId, status, whenhandedover, xTenantId }]),
];
export type EhrControllerServiceGetLocationByLocationIdDefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.getLocationByLocationId>
>;
export type EhrControllerServiceGetLocationByLocationIdQueryResult<
  TData = EhrControllerServiceGetLocationByLocationIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceGetLocationByLocationIdKey = "EhrControllerServiceGetLocationByLocationId";
export const UseEhrControllerServiceGetLocationByLocationIdKeyFn = (
  {
    locationId,
    xTenantId,
  }: {
    locationId: string;
    xTenantId?: string;
  },
  queryKey?: unknown[]
) => [useEhrControllerServiceGetLocationByLocationIdKey, ...(queryKey ?? [{ locationId, xTenantId }])];
export type EhrControllerServiceGetAccessToken1DefaultResponse = Awaited<
  ReturnType<typeof EhrControllerService.getAccessToken1>
>;
export type EhrControllerServiceGetAccessToken1QueryResult<
  TData = EhrControllerServiceGetAccessToken1DefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useEhrControllerServiceGetAccessToken1Key = "EhrControllerServiceGetAccessToken1";
export const UseEhrControllerServiceGetAccessToken1KeyFn = (
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useEhrControllerServiceGetAccessToken1Key, ...(queryKey ?? [{ xTenantId }])];
export type ConditionControllerServiceGetAllConditionsDefaultResponse = Awaited<
  ReturnType<typeof ConditionControllerService.getAllConditions>
>;
export type ConditionControllerServiceGetAllConditionsQueryResult<
  TData = ConditionControllerServiceGetAllConditionsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useConditionControllerServiceGetAllConditionsKey = "ConditionControllerServiceGetAllConditions";
export const UseConditionControllerServiceGetAllConditionsKeyFn = (
  {
    name,
    page,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    name?: string;
    page?: number;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [
  useConditionControllerServiceGetAllConditionsKey,
  ...(queryKey ?? [{ name, page, size, sort, sortBy, xTenantId }]),
];
export type ActivityControllerServiceGetAllActivitiesDefaultResponse = Awaited<
  ReturnType<typeof ActivityControllerService.getAllActivities>
>;
export type ActivityControllerServiceGetAllActivitiesQueryResult<
  TData = ActivityControllerServiceGetAllActivitiesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;
export const useActivityControllerServiceGetAllActivitiesKey = "ActivityControllerServiceGetAllActivities";
export const UseActivityControllerServiceGetAllActivitiesKeyFn = (
  {
    search,
    xTenantId,
  }: {
    search?: string;
    xTenantId?: string;
  } = {},
  queryKey?: unknown[]
) => [useActivityControllerServiceGetAllActivitiesKey, ...(queryKey ?? [{ search, xTenantId }])];
export type UserControllerServiceAddUserMutationResult = Awaited<ReturnType<typeof UserControllerService.addUser>>;
export type UserControllerServiceVerifyUserMutationResult = Awaited<
  ReturnType<typeof UserControllerService.verifyUser>
>;
export type UserControllerServiceVerifyOtpMutationResult = Awaited<ReturnType<typeof UserControllerService.verifyOtp>>;
export type UserControllerServiceSetPasswordMutationResult = Awaited<
  ReturnType<typeof UserControllerService.setPassword>
>;
export type UserControllerServiceResendOtpMutationResult = Awaited<ReturnType<typeof UserControllerService.resendOtp>>;
export type UserControllerServiceLogoutMutationResult = Awaited<ReturnType<typeof UserControllerService.logout>>;
export type UserControllerServiceGetAccessTokenMutationResult = Awaited<
  ReturnType<typeof UserControllerService.getAccessToken>
>;
export type UserControllerServiceChangePasswordMutationResult = Awaited<
  ReturnType<typeof UserControllerService.changePassword>
>;
export type UserControllerServiceGetAccessTokenFromRefreshTokenMutationResult = Awaited<
  ReturnType<typeof UserControllerService.getAccessTokenFromRefreshToken>
>;
export type TimeLogControllerServiceCreateTimeLogAsyncMutationResult = Awaited<
  ReturnType<typeof TimeLogControllerService.createTimeLogAsync>
>;
export type TimeLogControllerServicePlayerTimeLogMutationResult = Awaited<
  ReturnType<typeof TimeLogControllerService.playerTimeLog>
>;
export type TaskControllerServiceAddTaskMutationResult = Awaited<ReturnType<typeof TaskControllerService.addTask>>;
export type RolesAndPrivilegesControllerServiceAddRoleMutationResult = Awaited<
  ReturnType<typeof RolesAndPrivilegesControllerService.addRole>
>;
export type RolesAndPrivilegesControllerServiceResetAllRolePrivilegesMutationResult = Awaited<
  ReturnType<typeof RolesAndPrivilegesControllerService.resetAllRolePrivileges>
>;
export type ProviderControllerServiceCreateProviderMutationResult = Awaited<
  ReturnType<typeof ProviderControllerService.createProvider>
>;
export type ProviderGroupControllerServiceCreateProviderGroupMutationResult = Awaited<
  ReturnType<typeof ProviderGroupControllerService.createProviderGroup>
>;
export type PatientControllerServiceCreatePatientMutationResult = Awaited<
  ReturnType<typeof PatientControllerService.createPatient>
>;
export type PatientControllerServiceUploadFileMutationResult = Awaited<
  ReturnType<typeof PatientControllerService.uploadFile>
>;
export type PatientVitalControllerServiceCreatePatientVitalMutationResult = Awaited<
  ReturnType<typeof PatientVitalControllerService.createPatientVital>
>;
export type PatientVitalControllerServiceCreateBulkPatientVitalMutationResult = Awaited<
  ReturnType<typeof PatientVitalControllerService.createBulkPatientVital>
>;
export type PatientMedicationControllerServiceCreatePatientMedicationMutationResult = Awaited<
  ReturnType<typeof PatientMedicationControllerService.createPatientMedication>
>;
export type PatientMedicationControllerServiceCreateBulkPatientMedicationMutationResult = Awaited<
  ReturnType<typeof PatientMedicationControllerService.createBulkPatientMedication>
>;
export type PatientMedicationControllerServiceCreateBulkPatientMedicationWithImageMutationResult = Awaited<
  ReturnType<typeof PatientMedicationControllerService.createBulkPatientMedicationWithImage>
>;
export type PatientDiagnosisControllerServiceCreatePatientDiagnosisMutationResult = Awaited<
  ReturnType<typeof PatientDiagnosisControllerService.createPatientDiagnosis>
>;
export type PatientDiagnosisControllerServiceCreateBulkPatientDiagnosisMutationResult = Awaited<
  ReturnType<typeof PatientDiagnosisControllerService.createBulkPatientDiagnosis>
>;
export type ConsentFormControllerServiceCreateConsentFormsMutationResult = Awaited<
  ReturnType<typeof ConsentFormControllerService.createConsentForms>
>;
export type ConsentFormControllerServiceAddPatientConsentMutationResult = Awaited<
  ReturnType<typeof ConsentFormControllerService.addPatientConsent>
>;
export type PatientCarePlanControllerServiceAddProgramGoalTrackMutationResult = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.addProgramGoalTrack>
>;
export type PatientAllergyControllerServiceCreatePatientAllergyMutationResult = Awaited<
  ReturnType<typeof PatientAllergyControllerService.createPatientAllergy>
>;
export type PatientAllergyControllerServiceCreateBulkPatientAllergyMutationResult = Awaited<
  ReturnType<typeof PatientAllergyControllerService.createBulkPatientAllergy>
>;
export type MedicalCodeControllerServiceCreateMedicalCodeMutationResult = Awaited<
  ReturnType<typeof MedicalCodeControllerService.createMedicalCode>
>;
export type MedicalCodeControllerServiceUploadFile1MutationResult = Awaited<
  ReturnType<typeof MedicalCodeControllerService.uploadFile1>
>;
export type LocationControllerServiceCreateLocationMutationResult = Awaited<
  ReturnType<typeof LocationControllerService.createLocation>
>;
export type DeviceControllerServiceCreateDeviceMutationResult = Awaited<
  ReturnType<typeof DeviceControllerService.createDevice>
>;
export type ClinicalNoteControllerServiceCreateClinicalNoteMutationResult = Awaited<
  ReturnType<typeof ClinicalNoteControllerService.createClinicalNote>
>;
export type ClinicalNoteControllerServiceExportAndEmailPdfMutationResult = Awaited<
  ReturnType<typeof ClinicalNoteControllerService.exportAndEmailPdf>
>;
export type CarePlanControllerServiceCreateCarePlanMutationResult = Awaited<
  ReturnType<typeof CarePlanControllerService.createCarePlan>
>;
export type AppointmentControllerServiceCreateAppointmentMutationResult = Awaited<
  ReturnType<typeof AppointmentControllerService.createAppointment>
>;
export type AppointmentControllerServiceBookAppointmentRequestMutationResult = Awaited<
  ReturnType<typeof AppointmentControllerService.bookAppointmentRequest>
>;
export type AppointmentControllerServiceSendInvitationLinkMutationResult = Awaited<
  ReturnType<typeof AppointmentControllerService.sendInvitationLink>
>;
export type AvailabilityControllerServiceSetProviderAvailabilitySettingMutationResult = Awaited<
  ReturnType<typeof AvailabilityControllerService.setProviderAvailabilitySetting>
>;
export type PatientTrainingControllerServiceAddUpdateTrainedDeviceMutationResult = Awaited<
  ReturnType<typeof PatientTrainingControllerService.addUpdateTrainedDevice>
>;
export type NotificationControllerServiceSendDirectedMessageMutationResult = Awaited<
  ReturnType<typeof NotificationControllerService.sendDirectedMessage>
>;
export type NotificationControllerServiceTestNotifMutationResult = Awaited<
  ReturnType<typeof NotificationControllerService.testNotif>
>;
export type NotificationControllerServiceTestNotifImageMutationResult = Awaited<
  ReturnType<typeof NotificationControllerService.testNotifImage>
>;
export type FirebaseMessageControllerServiceSendMulticastMessageMutationResult = Awaited<
  ReturnType<typeof FirebaseMessageControllerService.sendMulticastMessage>
>;
export type FirebaseMessageControllerServiceSaveTokenMutationResult = Awaited<
  ReturnType<typeof FirebaseMessageControllerService.saveToken>
>;
export type AiControllerServiceStoreNurseActionStatisticsMutationResult = Awaited<
  ReturnType<typeof AiControllerService.storeNurseActionStatistics>
>;
export type AiControllerServiceAvqSetupAvatarMutationResult = Awaited<
  ReturnType<typeof AiControllerService.avqSetupAvatar>
>;
export type AiControllerServiceAvqSetupAvatarEdgeMutationResult = Awaited<
  ReturnType<typeof AiControllerService.avqSetupAvatarEdge>
>;
export type AiControllerServiceSendPatientAlertMutationResult = Awaited<
  ReturnType<typeof AiControllerService.sendPatientAlert>
>;
export type AiControllerServiceLlmauGenerateMedicationAudioMutationResult = Awaited<
  ReturnType<typeof AiControllerService.llmauGenerateMedicationAudio>
>;
export type AiControllerServiceVygenGenerateAvatarMutationResult = Awaited<
  ReturnType<typeof AiControllerService.vygenGenerateAvatar>
>;
export type AiControllerServiceGenerateAudioMonthlyQuickSummaryMutationResult = Awaited<
  ReturnType<typeof AiControllerService.generateAudioMonthlyQuickSummary>
>;
export type AiControllerServiceVygenDetectFaceMutationResult = Awaited<
  ReturnType<typeof AiControllerService.vygenDetectFace>
>;
export type AiControllerServiceLlmauChatWithMyNurseMutationResult = Awaited<
  ReturnType<typeof AiControllerService.llmauChatWithMyNurse>
>;
export type AiControllerServiceGetAiCarePlanMutationResult = Awaited<
  ReturnType<typeof AiControllerService.getAiCarePlan>
>;
export type AiControllerServiceGetAiCarePlanListMutationResult = Awaited<
  ReturnType<typeof AiControllerService.getAiCarePlanList>
>;
export type AiControllerServiceLlmauAudioChatWithMyNurseMutationResult = Awaited<
  ReturnType<typeof AiControllerService.llmauAudioChatWithMyNurse>
>;
export type AiControllerServiceLlmauAnalyzeMedicationMutationResult = Awaited<
  ReturnType<typeof AiControllerService.llmauAnalyzeMedication>
>;
export type UserControllerServiceUpdateUserArchiveStatusMutationResult = Awaited<
  ReturnType<typeof UserControllerService.updateUserArchiveStatus>
>;
export type UserControllerServiceUpdateUserMutationResult = Awaited<
  ReturnType<typeof UserControllerService.updateUser>
>;
export type UserControllerServiceChangeAvatar3MutationResult = Awaited<
  ReturnType<typeof UserControllerService.changeAvatar3>
>;
export type PatientVitalSettingControllerServiceUpdatePatientVitalSettingMutationResult = Awaited<
  ReturnType<typeof PatientVitalSettingControllerService.updatePatientVitalSetting>
>;
export type TimeLogControllerServiceUpdateTimeLogMutationResult = Awaited<
  ReturnType<typeof TimeLogControllerService.updateTimeLog>
>;
export type TaskControllerServiceUpdateTaskMutationResult = Awaited<
  ReturnType<typeof TaskControllerService.updateTask>
>;
export type TaskControllerServiceUpdateTaskStatusMutationResult = Awaited<
  ReturnType<typeof TaskControllerService.updateTaskStatus>
>;
export type RolesAndPrivilegesControllerServiceUpdateRoleMutationResult = Awaited<
  ReturnType<typeof RolesAndPrivilegesControllerService.updateRole>
>;
export type RolesAndPrivilegesControllerServiceAddUpdateRolePermissionsMutationResult = Awaited<
  ReturnType<typeof RolesAndPrivilegesControllerService.addUpdateRolePermissions>
>;
export type RolesAndPrivilegesControllerServiceUpdateRolePermissionMutationResult = Awaited<
  ReturnType<typeof RolesAndPrivilegesControllerService.updateRolePermission>
>;
export type ProviderControllerServiceUpdateProviderMutationResult = Awaited<
  ReturnType<typeof ProviderControllerService.updateProvider>
>;
export type ProviderControllerServiceUpdateProviderOnboardingStatusMutationResult = Awaited<
  ReturnType<typeof ProviderControllerService.updateProviderOnboardingStatus>
>;
export type ProviderControllerServiceUpdateProviderAvatarStatusMutationResult = Awaited<
  ReturnType<typeof ProviderControllerService.updateProviderAvatarStatus>
>;
export type ProviderControllerServiceUpdateProviderArchiveStatusMutationResult = Awaited<
  ReturnType<typeof ProviderControllerService.updateProviderArchiveStatus>
>;
export type ProviderControllerServiceUploadVideoMutationResult = Awaited<
  ReturnType<typeof ProviderControllerService.uploadVideo>
>;
export type ProviderControllerServiceNotifyPatientMutationResult = Awaited<
  ReturnType<typeof ProviderControllerService.notifyPatient>
>;
export type ProviderControllerServiceChangeAvatarMutationResult = Awaited<
  ReturnType<typeof ProviderControllerService.changeAvatar>
>;
export type ProviderGroupControllerServiceUpdateProviderGroupMutationResult = Awaited<
  ReturnType<typeof ProviderGroupControllerService.updateProviderGroup>
>;
export type ProviderGroupControllerServiceSyncDatabaseSchemaMutationResult = Awaited<
  ReturnType<typeof ProviderGroupControllerService.syncDatabaseSchema>
>;
export type ProviderGroupControllerServiceUpdateProviderGroupArchiveStatusMutationResult = Awaited<
  ReturnType<typeof ProviderGroupControllerService.updateProviderGroupArchiveStatus>
>;
export type ProviderGroupControllerServiceUpdateProviderGroupConfigurationMutationResult = Awaited<
  ReturnType<typeof ProviderGroupControllerService.updateProviderGroupConfiguration>
>;
export type ProviderGroupControllerServiceCreateRealmMutationResult = Awaited<
  ReturnType<typeof ProviderGroupControllerService.createRealm>
>;
export type ProviderGroupControllerServiceChangeAvatar1MutationResult = Awaited<
  ReturnType<typeof ProviderGroupControllerService.changeAvatar1>
>;
export type PatientControllerServiceUpdatePatientMutationResult = Awaited<
  ReturnType<typeof PatientControllerService.updatePatient>
>;
export type PatientControllerServiceUpdatePatientArchiveStatusMutationResult = Awaited<
  ReturnType<typeof PatientControllerService.updatePatientArchiveStatus>
>;
export type PatientControllerServiceChangeAvatar2MutationResult = Awaited<
  ReturnType<typeof PatientControllerService.changeAvatar2>
>;
export type PatientVitalControllerServiceUpdatePatientVitalMutationResult = Awaited<
  ReturnType<typeof PatientVitalControllerService.updatePatientVital>
>;
export type PatientVitalControllerServiceSyncPatientVitalMutationResult = Awaited<
  ReturnType<typeof PatientVitalControllerService.syncPatientVital>
>;
export type PatientMedicationControllerServiceUpdatePatientMedicationMutationResult = Awaited<
  ReturnType<typeof PatientMedicationControllerService.updatePatientMedication>
>;
export type PatientMedicationControllerServiceUpdateBulkPatientMedicationDosageMutationResult = Awaited<
  ReturnType<typeof PatientMedicationControllerService.updateBulkPatientMedicationDosage>
>;
export type PatientMedicationControllerServiceSyncPatientMedicationMutationResult = Awaited<
  ReturnType<typeof PatientMedicationControllerService.syncPatientMedication>
>;
export type PatientMedicationControllerServiceDeletePatientMedicationIdMutationResult = Awaited<
  ReturnType<typeof PatientMedicationControllerService.deletePatientMedicationId>
>;
export type PatientDiagnosisControllerServiceUpdatePatientDiagnosisMutationResult = Awaited<
  ReturnType<typeof PatientDiagnosisControllerService.updatePatientDiagnosis>
>;
export type PatientDiagnosisControllerServiceUpdatePatientDiagnosisArchiveStatusMutationResult = Awaited<
  ReturnType<typeof PatientDiagnosisControllerService.updatePatientDiagnosisArchiveStatus>
>;
export type PatientDiagnosisControllerServiceSyncPatientDiagnosisMutationResult = Awaited<
  ReturnType<typeof PatientDiagnosisControllerService.syncPatientDiagnosis>
>;
export type ConsentFormControllerServiceUpdatePatientConsentStatusMutationResult = Awaited<
  ReturnType<typeof ConsentFormControllerService.updatePatientConsentStatus>
>;
export type ConsentFormControllerServiceUpdateConsentFormsMutationResult = Awaited<
  ReturnType<typeof ConsentFormControllerService.updateConsentForms>
>;
export type ConsentFormControllerServiceUpdateConsentFormArchiveStatusMutationResult = Awaited<
  ReturnType<typeof ConsentFormControllerService.updateConsentFormArchiveStatus>
>;
export type PatientCarePlanControllerServiceUpdatePatientCarePlanMutationResult = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.updatePatientCarePlan>
>;
export type PatientCarePlanControllerServiceUpdateCarePlanArchiveStatusMutationResult = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.updateCarePlanArchiveStatus>
>;
export type PatientCarePlanControllerServiceUpdatePatientCarePlanStatusMutationResult = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.updatePatientCarePlanStatus>
>;
export type PatientCarePlanControllerServiceUpdateProgramGoalTrackMutationResult = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.updateProgramGoalTrack>
>;
export type PatientCarePlanControllerServiceUpdateVitalReferenceRangeMutationResult = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.updateVitalReferenceRange>
>;
export type PatientCarePlanControllerServiceBulkAssignCarePlansMutationResult = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.bulkAssignCarePlans>
>;
export type PatientCarePlanControllerServiceAssignCarePlanMutationResult = Awaited<
  ReturnType<typeof PatientCarePlanControllerService.assignCarePlan>
>;
export type PatientAllergyControllerServiceUpdatePatientAllergyMutationResult = Awaited<
  ReturnType<typeof PatientAllergyControllerService.updatePatientAllergy>
>;
export type PatientAllergyControllerServiceUpdatePatientAllergyArchiveStatusMutationResult = Awaited<
  ReturnType<typeof PatientAllergyControllerService.updatePatientAllergyArchiveStatus>
>;
export type PatientAllergyControllerServiceSyncPatientAllergyMutationResult = Awaited<
  ReturnType<typeof PatientAllergyControllerService.syncPatientAllergy>
>;
export type MedicalCodeControllerServiceUpdateMedicalCodeMutationResult = Awaited<
  ReturnType<typeof MedicalCodeControllerService.updateMedicalCode>
>;
export type MedicalCodeControllerServiceUpdateMedicalCodeStatusMutationResult = Awaited<
  ReturnType<typeof MedicalCodeControllerService.updateMedicalCodeStatus>
>;
export type MedicalCodeControllerServiceUpdateMedicalCodeArchiveStatusMutationResult = Awaited<
  ReturnType<typeof MedicalCodeControllerService.updateMedicalCodeArchiveStatus>
>;
export type LocationControllerServiceUpdateLocationMutationResult = Awaited<
  ReturnType<typeof LocationControllerService.updateLocation>
>;
export type LocationControllerServiceUpdateLocationArchiveStatusMutationResult = Awaited<
  ReturnType<typeof LocationControllerService.updateLocationArchiveStatus>
>;
export type DeviceControllerServiceUpdateDeviceMutationResult = Awaited<
  ReturnType<typeof DeviceControllerService.updateDevice>
>;
export type DeviceControllerServiceUpdateDeviceStatusMutationResult = Awaited<
  ReturnType<typeof DeviceControllerService.updateDeviceStatus>
>;
export type DeviceControllerServiceUpdateDeviceArchiveStatusMutationResult = Awaited<
  ReturnType<typeof DeviceControllerService.updateDeviceArchiveStatus>
>;
export type DeviceControllerServiceAssignDeviceMutationResult = Awaited<
  ReturnType<typeof DeviceControllerService.assignDevice>
>;
export type ClinicalNoteControllerServiceUpdateClinicalNoteMutationResult = Awaited<
  ReturnType<typeof ClinicalNoteControllerService.updateClinicalNote>
>;
export type CarePlanControllerServiceUpdateCarePlanMutationResult = Awaited<
  ReturnType<typeof CarePlanControllerService.updateCarePlan>
>;
export type CarePlanControllerServiceUpdateUserStatusMutationResult = Awaited<
  ReturnType<typeof CarePlanControllerService.updateUserStatus>
>;
export type CarePlanControllerServiceUpdateCarePlanArchiveStatus1MutationResult = Awaited<
  ReturnType<typeof CarePlanControllerService.updateCarePlanArchiveStatus1>
>;
export type AppointmentControllerServiceUpdateAppointmentMutationResult = Awaited<
  ReturnType<typeof AppointmentControllerService.updateAppointment>
>;
export type AppointmentControllerServiceUpdateAppointmentStatusMutationResult = Awaited<
  ReturnType<typeof AppointmentControllerService.updateAppointmentStatus>
>;
export type AppointmentControllerServiceRescheduleAppointmentMutationResult = Awaited<
  ReturnType<typeof AppointmentControllerService.rescheduleAppointment>
>;
export type AppointmentControllerServiceBroadCastAppointmentMutationResult = Awaited<
  ReturnType<typeof AppointmentControllerService.broadCastAppointment>
>;
export type TimeLogControllerServiceDeleteTimeLogByIdMutationResult = Awaited<
  ReturnType<typeof TimeLogControllerService.deleteTimeLogById>
>;
export type TaskControllerServiceArchiveTaskMutationResult = Awaited<
  ReturnType<typeof TaskControllerService.archiveTask>
>;
export type ProviderControllerServiceDeleteVideoMutationResult = Awaited<
  ReturnType<typeof ProviderControllerService.deleteVideo>
>;
export type FirebaseMessageControllerServiceDeleteTokenMutationResult = Awaited<
  ReturnType<typeof FirebaseMessageControllerService.deleteToken>
>;
