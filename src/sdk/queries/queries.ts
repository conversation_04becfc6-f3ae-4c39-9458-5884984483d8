// generated with @7nohe/openapi-react-query-codegen@1.4.1
import { UseMutationOptions, UseQueryOptions, useMutation, useQuery } from "@tanstack/react-query";

import {
  ActivityControllerService,
  AiControllerService,
  AppointmentControllerService,
  AvailabilityControllerService,
  CarePlanControllerService,
  ClinicalNoteControllerService,
  ConditionControllerService,
  ConsentFormControllerService,
  DeviceControllerService,
  EhrControllerService,
  EhrProviderControllerService,
  EqrControllerService,
  FirebaseMessageControllerService,
  LicenseStateControllerService,
  LocationControllerService,
  MedicalCodeControllerService,
  NotificationControllerService,
  PatientAllergyControllerService,
  PatientCarePlanControllerService,
  PatientControllerService,
  PatientDiagnosisControllerService,
  PatientMedicationControllerService,
  PatientTrainingControllerService,
  PatientVitalControllerService,
  PatientVitalSettingControllerService,
  ProviderControllerService,
  ProviderGroupControllerService,
  RolesAndPrivilegesControllerService,
  TaskControllerService,
  TimeLogControllerService,
  UserControllerService,
  VitalControllerService,
  ZoomControllerService,
} from "../requests/services.gen";
import {
  Appointment,
  AppointmentRequest,
  AppointmentStatusChange,
  AvailabilitySetting,
  BulkCarePlanRequest,
  CarePlan,
  ChangeAvatarRequest,
  ChangePasswordRequest,
  ClinicalNote,
  ConsentFormTemplate,
  Device,
  GenerateAvatarRequest,
  Location,
  LoginRequest,
  LogoutRequest,
  MedicalCode,
  MedicationAudioRequest,
  Message,
  MessageRequest,
  Patient,
  PatientAllergy,
  PatientCarePlanRequest,
  PatientCarePlanStatusChange,
  PatientCarePlanUpdateRequest,
  PatientConsentForm,
  PatientDTO,
  PatientDeviceRequest,
  PatientDiagnosis,
  PatientMedication,
  PatientProgramGoalTrack,
  PatientRequest,
  PatientVital,
  PatientVitalRequest,
  PlayerTimeLogRequestDto,
  Provider,
  ProviderGroup,
  RescheduleRequest,
  ResetPasswordRequest,
  Role,
  RolePrivilegeUpdateRequest,
  SendDirectMessageRequest,
  Task,
  TimeLogRequest,
  User,
  UserInvitationRequest,
  VitalReference,
} from "../requests/types.gen";
import * as Common from "./common";

export const useUserControllerServiceGetAllUsers = <
  TData = Common.UserControllerServiceGetAllUsersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    locationId,
    page,
    role,
    roleType,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    locationId?: string;
    page?: number;
    role?: string;
    roleType?: "PROVIDER" | "STAFF" | "PATIENT";
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseUserControllerServiceGetAllUsersKeyFn(
      { archive, locationId, page, role, roleType, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      UserControllerService.getAllUsers({
        archive,
        locationId,
        page,
        role,
        roleType,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useUserControllerServiceGetUser = <
  TData = Common.UserControllerServiceGetUserDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    userId,
    xTenantId,
  }: {
    userId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseUserControllerServiceGetUserKeyFn({ userId, xTenantId }, queryKey),
    queryFn: () => UserControllerService.getUser({ userId, xTenantId }) as TData,
    ...options,
  });
export const useUserControllerServiceGetProfile1 = <
  TData = Common.UserControllerServiceGetProfile1DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseUserControllerServiceGetProfile1KeyFn({ xTenantId }, queryKey),
    queryFn: () => UserControllerService.getProfile1({ xTenantId }) as TData,
    ...options,
  });
export const usePatientVitalSettingControllerServiceGetVitalSettings = <
  TData = Common.PatientVitalSettingControllerServiceGetVitalSettingsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientVitalSettingControllerServiceGetVitalSettingsKeyFn({ xTenantId }, queryKey),
    queryFn: () => PatientVitalSettingControllerService.getVitalSettings({ xTenantId }) as TData,
    ...options,
  });
export const usePatientVitalSettingControllerServiceGetPatientVitalSetting = <
  TData = Common.PatientVitalSettingControllerServiceGetPatientVitalSettingDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientVitalSettingControllerServiceGetPatientVitalSettingKeyFn(
      { patientUuid, xTenantId },
      queryKey
    ),
    queryFn: () => PatientVitalSettingControllerService.getPatientVitalSetting({ patientUuid, xTenantId }) as TData,
    ...options,
  });
export const useTimeLogControllerServiceGetAllPatientTimeLogs = <
  TData = Common.TimeLogControllerServiceGetAllPatientTimeLogsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    activityName,
    loggedBy,
    loggedEntryType,
    month,
    page,
    patientId,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    activityName?: string;
    loggedBy?: string;
    loggedEntryType?: string;
    month: string;
    page?: number;
    patientId: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseTimeLogControllerServiceGetAllPatientTimeLogsKeyFn(
      { activityName, loggedBy, loggedEntryType, month, page, patientId, size, sort, sortBy, xTenantId },
      queryKey
    ),
    queryFn: () =>
      TimeLogControllerService.getAllPatientTimeLogs({
        activityName,
        loggedBy,
        loggedEntryType,
        month,
        page,
        patientId,
        size,
        sort,
        sortBy,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useTimeLogControllerServiceGetTimeLogById = <
  TData = Common.TimeLogControllerServiceGetTimeLogByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    timeLogId,
    xTenantId,
  }: {
    timeLogId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseTimeLogControllerServiceGetTimeLogByIdKeyFn({ timeLogId, xTenantId }, queryKey),
    queryFn: () => TimeLogControllerService.getTimeLogById({ timeLogId, xTenantId }) as TData,
    ...options,
  });
export const useTaskControllerServiceGetAllTasks = <
  TData = Common.TaskControllerServiceGetAllTasksDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    active,
    archive,
    assignedBy,
    assignedDate,
    assignedTo,
    currentUserUuid,
    dueDate,
    page,
    patientId,
    priority,
    searchAssignTo,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    type,
    xTenantId,
  }: {
    active?: boolean;
    archive?: boolean;
    assignedBy?: string;
    assignedDate?: string;
    assignedTo?: string;
    currentUserUuid?: string;
    dueDate?: string;
    page?: number;
    patientId?: string;
    priority?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    searchAssignTo?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: "PENDING" | "COMPLETED" | "DISCARDED";
    type?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseTaskControllerServiceGetAllTasksKeyFn(
      {
        active,
        archive,
        assignedBy,
        assignedDate,
        assignedTo,
        currentUserUuid,
        dueDate,
        page,
        patientId,
        priority,
        searchAssignTo,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        type,
        xTenantId,
      },
      queryKey
    ),
    queryFn: () =>
      TaskControllerService.getAllTasks({
        active,
        archive,
        assignedBy,
        assignedDate,
        assignedTo,
        currentUserUuid,
        dueDate,
        page,
        patientId,
        priority,
        searchAssignTo,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useTaskControllerServiceGetTaskByUuid = <
  TData = Common.TaskControllerServiceGetTaskByUuidDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    taskUuid,
    xTenantId,
  }: {
    taskUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseTaskControllerServiceGetTaskByUuidKeyFn({ taskUuid, xTenantId }, queryKey),
    queryFn: () => TaskControllerService.getTaskByUuid({ taskUuid, xTenantId }) as TData,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceGetAllRoles = <
  TData = Common.RolesAndPrivilegesControllerServiceGetAllRolesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseRolesAndPrivilegesControllerServiceGetAllRolesKeyFn({ xTenantId }, queryKey),
    queryFn: () => RolesAndPrivilegesControllerService.getAllRoles({ xTenantId }) as TData,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceGetAllPrivileges = <
  TData = Common.RolesAndPrivilegesControllerServiceGetAllPrivilegesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseRolesAndPrivilegesControllerServiceGetAllPrivilegesKeyFn({ xTenantId }, queryKey),
    queryFn: () => RolesAndPrivilegesControllerService.getAllPrivileges({ xTenantId }) as TData,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceGetAllRolesPermissions = <
  TData = Common.RolesAndPrivilegesControllerServiceGetAllRolesPermissionsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    realm,
    xTenantId,
  }: {
    realm: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseRolesAndPrivilegesControllerServiceGetAllRolesPermissionsKeyFn({ realm, xTenantId }, queryKey),
    queryFn: () => RolesAndPrivilegesControllerService.getAllRolesPermissions({ realm, xTenantId }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetAllProviders = <
  TData = Common.ProviderControllerServiceGetAllProvidersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    role,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    role?:
      | "PROVIDER"
      | "PATIENT"
      | "SUPER_ADMIN"
      | "ADMIN"
      | "FRONTDESK"
      | "BILLER"
      | "SITE_ADMIN"
      | "PROVIDER_GROUP_ADMIN"
      | "NURSE"
      | "ANONYMOUS";
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetAllProvidersKeyFn(
      { archive, page, role, searchString, size, sortBy, sortDirection, state, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ProviderControllerService.getAllProviders({
        archive,
        page,
        role,
        searchString,
        size,
        sortBy,
        sortDirection,
        state,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetProviderById = <
  TData = Common.ProviderControllerServiceGetProviderByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerUuid,
    xTenantId,
  }: {
    providerUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetProviderByIdKeyFn({ providerUuid, xTenantId }, queryKey),
    queryFn: () => ProviderControllerService.getProviderById({ providerUuid, xTenantId }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetNurseReportDashboard = <
  TData = Common.ProviderControllerServiceGetNurseReportDashboardDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    page,
    providerId,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month: number;
    page?: number;
    providerId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetNurseReportDashboardKeyFn(
      { month, page, providerId, size, sortBy, sortDirection, xTenantId, year },
      queryKey
    ),
    queryFn: () =>
      ProviderControllerService.getNurseReportDashboard({
        month,
        page,
        providerId,
        size,
        sortBy,
        sortDirection,
        xTenantId,
        year,
      }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetPatientDashboard = <
  TData = Common.ProviderControllerServiceGetPatientDashboardDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    page,
    providerId,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month: number;
    page?: number;
    providerId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetPatientDashboardKeyFn(
      { month, page, providerId, size, sortBy, sortDirection, xTenantId, year },
      queryKey
    ),
    queryFn: () =>
      ProviderControllerService.getPatientDashboard({
        month,
        page,
        providerId,
        size,
        sortBy,
        sortDirection,
        xTenantId,
        year,
      }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetUserIdByProvider = <
  TData = Common.ProviderControllerServiceGetUserIdByProviderDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    userId,
    xTenantId,
  }: {
    userId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetUserIdByProviderKeyFn({ userId, xTenantId }, queryKey),
    queryFn: () => ProviderControllerService.getUserIdByProvider({ userId, xTenantId }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetProfile = <
  TData = Common.ProviderControllerServiceGetProfileDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetProfileKeyFn({ xTenantId }, queryKey),
    queryFn: () => ProviderControllerService.getProfile({ xTenantId }) as TData,
    ...options,
  });
export const useProviderGroupControllerServiceGetAllProviderGroups = <
  TData = Common.ProviderGroupControllerServiceGetAllProviderGroupsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseProviderGroupControllerServiceGetAllProviderGroupsKeyFn(
      { archive, page, searchString, size, sortBy, sortDirection, state, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ProviderGroupControllerService.getAllProviderGroups({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        state,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useProviderGroupControllerServiceGetProviderGroupById = <
  TData = Common.ProviderGroupControllerServiceGetProviderGroupByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerGroupId,
    xTenantId,
  }: {
    providerGroupId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseProviderGroupControllerServiceGetProviderGroupByIdKeyFn(
      { providerGroupId, xTenantId },
      queryKey
    ),
    queryFn: () => ProviderGroupControllerService.getProviderGroupById({ providerGroupId, xTenantId }) as TData,
    ...options,
  });
export const useProviderGroupControllerServiceGetProviderGroupBySchema = <
  TData = Common.ProviderGroupControllerServiceGetProviderGroupBySchemaDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseProviderGroupControllerServiceGetProviderGroupBySchemaKeyFn({ xTenantId }, queryKey),
    queryFn: () => ProviderGroupControllerService.getProviderGroupBySchema({ xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetAllPatient = <
  TData = Common.PatientControllerServiceGetAllPatientDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    genderFilter,
    mrn,
    name,
    nurseId,
    page,
    providerId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    genderFilter?: "MALE" | "FEMALE" | "OTHER" | "BOTH";
    mrn?: string;
    name?: string;
    nurseId?: string;
    page?: number;
    providerId?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetAllPatientKeyFn(
      {
        archive,
        genderFilter,
        mrn,
        name,
        nurseId,
        page,
        providerId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      },
      queryKey
    ),
    queryFn: () =>
      PatientControllerService.getAllPatient({
        archive,
        genderFilter,
        mrn,
        name,
        nurseId,
        page,
        providerId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetPatientById = <
  TData = Common.PatientControllerServiceGetPatientByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetPatientByIdKeyFn({ patientUuid, xTenantId }, queryKey),
    queryFn: () => PatientControllerService.getPatientById({ patientUuid, xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetPatientStatistic = <
  TData = Common.PatientControllerServiceGetPatientStatisticDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetPatientStatisticKeyFn({ patientId, xTenantId }, queryKey),
    queryFn: () => PatientControllerService.getPatientStatistic({ patientId, xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetPatientRecord = <
  TData = Common.PatientControllerServiceGetPatientRecordDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetPatientRecordKeyFn({ patientId, xTenantId }, queryKey),
    queryFn: () => PatientControllerService.getPatientRecord({ patientId, xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetProfile2 = <
  TData = Common.PatientControllerServiceGetProfile2DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetProfile2KeyFn({ xTenantId }, queryKey),
    queryFn: () => PatientControllerService.getProfile2({ xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceDownloadTemplate = <
  TData = Common.PatientControllerServiceDownloadTemplateDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceDownloadTemplateKeyFn({ xTenantId }, queryKey),
    queryFn: () => PatientControllerService.downloadTemplate({ xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetPatientList = <
  TData = Common.PatientControllerServiceGetPatientListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    nurseId,
    page,
    providerId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    nurseId?: string;
    page?: number;
    providerId?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetPatientListKeyFn(
      { archive, nurseId, page, providerId, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientControllerService.getPatientList({
        archive,
        nurseId,
        page,
        providerId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetAssignedDevices = <
  TData = Common.PatientControllerServiceGetAssignedDevicesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    page,
    patientId,
    size,
    sortBy,
    sortDirection,
    type,
    xTenantId,
  }: {
    page?: number;
    patientId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    type?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetAssignedDevicesKeyFn(
      { page, patientId, size, sortBy, sortDirection, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientControllerService.getAssignedDevices({
        page,
        patientId,
        size,
        sortBy,
        sortDirection,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientVitalControllerServiceGetPatientVitals1 = <
  TData = Common.PatientVitalControllerServiceGetPatientVitals1DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    endDate,
    page,
    patientUuid,
    size,
    sort,
    sortBy,
    startDate,
    timeFilter,
    vitalName,
    xTenantId,
  }: {
    endDate?: string;
    page?: number;
    patientUuid: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    startDate?: string;
    timeFilter?: "LAST_MONTH" | "LAST_WEEK" | "PAST_24_HOURS" | "DATE_RANGE";
    vitalName?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientVitalControllerServiceGetPatientVitals1KeyFn(
      { endDate, page, patientUuid, size, sort, sortBy, startDate, timeFilter, vitalName, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientVitalControllerService.getPatientVitals1({
        endDate,
        page,
        patientUuid,
        size,
        sort,
        sortBy,
        startDate,
        timeFilter,
        vitalName,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientVitalControllerServiceGetPatientVitalById = <
  TData = Common.PatientVitalControllerServiceGetPatientVitalByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientVitalId,
    xTenantId,
  }: {
    patientVitalId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientVitalControllerServiceGetPatientVitalByIdKeyFn({ patientVitalId, xTenantId }, queryKey),
    queryFn: () => PatientVitalControllerService.getPatientVitalById({ patientVitalId, xTenantId }) as TData,
    ...options,
  });
export const usePatientVitalControllerServiceGetPatientLatestVitals = <
  TData = Common.PatientVitalControllerServiceGetPatientLatestVitalsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientVitalControllerServiceGetPatientLatestVitalsKeyFn({ patientUuid, xTenantId }, queryKey),
    queryFn: () => PatientVitalControllerService.getPatientLatestVitals({ patientUuid, xTenantId }) as TData,
    ...options,
  });
export const usePatientVitalControllerServiceGetEcgValue = <
  TData = Common.PatientVitalControllerServiceGetEcgValueDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    ecgId,
    xTenantId,
  }: {
    ecgId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientVitalControllerServiceGetEcgValueKeyFn({ ecgId, xTenantId }, queryKey),
    queryFn: () => PatientVitalControllerService.getEcgValue({ ecgId, xTenantId }) as TData,
    ...options,
  });
export const usePatientMedicationControllerServiceGetPatientMedication = <
  TData = Common.PatientMedicationControllerServiceGetPatientMedicationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sort,
    sortBy,
    status,
    timeFilter,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    status?: boolean;
    timeFilter?: "CURRENT" | "PAST";
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientMedicationControllerServiceGetPatientMedicationKeyFn(
      { archive, page, patientUuid, searchString, size, sort, sortBy, status, timeFilter, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientMedicationControllerService.getPatientMedication({
        archive,
        page,
        patientUuid,
        searchString,
        size,
        sort,
        sortBy,
        status,
        timeFilter,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientMedicationControllerServiceGetPatientMedicationById = <
  TData = Common.PatientMedicationControllerServiceGetPatientMedicationByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientMedicationId,
    xTenantId,
  }: {
    patientMedicationId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientMedicationControllerServiceGetPatientMedicationByIdKeyFn(
      { patientMedicationId, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientMedicationControllerService.getPatientMedicationById({ patientMedicationId, xTenantId }) as TData,
    ...options,
  });
export const usePatientMedicationControllerServiceSendPatientNotification = <
  TData = Common.PatientMedicationControllerServiceSendPatientNotificationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientMedicationControllerServiceSendPatientNotificationKeyFn({ xTenantId }, queryKey),
    queryFn: () => PatientMedicationControllerService.sendPatientNotification({ xTenantId }) as TData,
    ...options,
  });
export const usePatientDiagnosisControllerServiceGetPatientDiagnosis = <
  TData = Common.PatientDiagnosisControllerServiceGetPatientDiagnosisDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientDiagnosisControllerServiceGetPatientDiagnosisKeyFn(
      { archive, page, patientUuid, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientDiagnosisControllerService.getPatientDiagnosis({
        archive,
        page,
        patientUuid,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientDiagnosisControllerServiceGetPatientDiagnosisById = <
  TData = Common.PatientDiagnosisControllerServiceGetPatientDiagnosisByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientDiagnosisId,
    xTenantId,
  }: {
    patientDiagnosisId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientDiagnosisControllerServiceGetPatientDiagnosisByIdKeyFn(
      { patientDiagnosisId, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientDiagnosisControllerService.getPatientDiagnosisById({ patientDiagnosisId, xTenantId }) as TData,
    ...options,
  });
export const useConsentFormControllerServiceGetAllConsentFormTemplate = <
  TData = Common.ConsentFormControllerServiceGetAllConsentFormTemplateDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseConsentFormControllerServiceGetAllConsentFormTemplateKeyFn(
      { archive, page, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ConsentFormControllerService.getAllConsentFormTemplate({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useConsentFormControllerServiceGetAllPatientConsentForm = <
  TData = Common.ConsentFormControllerServiceGetAllPatientConsentFormDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    page,
    patientUuid,
    searchString,
    size,
    sortBy,
    sortDirection,
    xTenantId,
  }: {
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseConsentFormControllerServiceGetAllPatientConsentFormKeyFn(
      { page, patientUuid, searchString, size, sortBy, sortDirection, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ConsentFormControllerService.getAllPatientConsentForm({
        page,
        patientUuid,
        searchString,
        size,
        sortBy,
        sortDirection,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useConsentFormControllerServiceGetPatientConsentFormById = <
  TData = Common.ConsentFormControllerServiceGetPatientConsentFormByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientConsentFormUuid,
    xTenantId,
  }: {
    patientConsentFormUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseConsentFormControllerServiceGetPatientConsentFormByIdKeyFn(
      { patientConsentFormUuid, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ConsentFormControllerService.getPatientConsentFormById({ patientConsentFormUuid, xTenantId }) as TData,
    ...options,
  });
export const useConsentFormControllerServiceGetConsentFormId = <
  TData = Common.ConsentFormControllerServiceGetConsentFormIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    consentFormId,
    xTenantId,
  }: {
    consentFormId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseConsentFormControllerServiceGetConsentFormIdKeyFn({ consentFormId, xTenantId }, queryKey),
    queryFn: () => ConsentFormControllerService.getConsentFormId({ consentFormId, xTenantId }) as TData,
    ...options,
  });
export const usePatientCarePlanControllerServiceGetAllCarePlans = <
  TData = Common.PatientCarePlanControllerServiceGetAllCarePlansDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    carePlanStatus,
    page,
    patientId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    timeFilter,
    xTenantId,
  }: {
    archive?: boolean;
    carePlanStatus?: string;
    page?: number;
    patientId: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    timeFilter?: "CURRENT" | "PAST";
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientCarePlanControllerServiceGetAllCarePlansKeyFn(
      {
        archive,
        carePlanStatus,
        page,
        patientId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        timeFilter,
        xTenantId,
      },
      queryKey
    ),
    queryFn: () =>
      PatientCarePlanControllerService.getAllCarePlans({
        archive,
        carePlanStatus,
        page,
        patientId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        timeFilter,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientCarePlanControllerServiceGetPatientCarePlanById = <
  TData = Common.PatientCarePlanControllerServiceGetPatientCarePlanByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientCarePlanId,
    xTenantId,
  }: {
    patientCarePlanId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientCarePlanControllerServiceGetPatientCarePlanByIdKeyFn(
      { patientCarePlanId, xTenantId },
      queryKey
    ),
    queryFn: () => PatientCarePlanControllerService.getPatientCarePlanById({ patientCarePlanId, xTenantId }) as TData,
    ...options,
  });
export const usePatientCarePlanControllerServiceGetProgramGoalTrackDetails = <
  TData = Common.PatientCarePlanControllerServiceGetProgramGoalTrackDetailsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    programGoalId,
    xTenantId,
  }: {
    programGoalId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientCarePlanControllerServiceGetProgramGoalTrackDetailsKeyFn(
      { programGoalId, xTenantId },
      queryKey
    ),
    queryFn: () => PatientCarePlanControllerService.getProgramGoalTrackDetails({ programGoalId, xTenantId }) as TData,
    ...options,
  });
export const usePatientCarePlanControllerServiceGetPatientActiveCarePlan = <
  TData = Common.PatientCarePlanControllerServiceGetPatientActiveCarePlanDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientCarePlanControllerServiceGetPatientActiveCarePlanKeyFn(
      { patientId, xTenantId },
      queryKey
    ),
    queryFn: () => PatientCarePlanControllerService.getPatientActiveCarePlan({ patientId, xTenantId }) as TData,
    ...options,
  });
export const usePatientAllergyControllerServiceGetPatientAllergy = <
  TData = Common.PatientAllergyControllerServiceGetPatientAllergyDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sort,
    sortBy,
    status,
    type,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    status?: boolean;
    type?: "OTHER" | "DRUG" | "FOOD" | "ENVIRONMENT";
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientAllergyControllerServiceGetPatientAllergyKeyFn(
      { archive, page, patientUuid, searchString, size, sort, sortBy, status, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientAllergyControllerService.getPatientAllergy({
        archive,
        page,
        patientUuid,
        searchString,
        size,
        sort,
        sortBy,
        status,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientAllergyControllerServiceGetPatientAllergyById = <
  TData = Common.PatientAllergyControllerServiceGetPatientAllergyByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientAllergyId,
    xTenantId,
  }: {
    patientAllergyId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UsePatientAllergyControllerServiceGetPatientAllergyByIdKeyFn(
      { patientAllergyId, xTenantId },
      queryKey
    ),
    queryFn: () => PatientAllergyControllerService.getPatientAllergyById({ patientAllergyId, xTenantId }) as TData,
    ...options,
  });
export const useMedicalCodeControllerServiceGetMedicalCodes = <
  TData = Common.MedicalCodeControllerServiceGetMedicalCodesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    active,
    archive,
    page,
    searchString,
    size,
    sort,
    sortBy,
    type,
    xTenantId,
  }: {
    active?: boolean;
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    type?: "ICD10" | "CPT" | "ALL";
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseMedicalCodeControllerServiceGetMedicalCodesKeyFn(
      { active, archive, page, searchString, size, sort, sortBy, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      MedicalCodeControllerService.getMedicalCodes({
        active,
        archive,
        page,
        searchString,
        size,
        sort,
        sortBy,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useMedicalCodeControllerServiceGetMedicalCodeById = <
  TData = Common.MedicalCodeControllerServiceGetMedicalCodeByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    medicalCodeId,
    xTenantId,
  }: {
    medicalCodeId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseMedicalCodeControllerServiceGetMedicalCodeByIdKeyFn({ medicalCodeId, xTenantId }, queryKey),
    queryFn: () => MedicalCodeControllerService.getMedicalCodeById({ medicalCodeId, xTenantId }) as TData,
    ...options,
  });
export const useLocationControllerServiceGetAllLocations = <
  TData = Common.LocationControllerServiceGetAllLocationsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseLocationControllerServiceGetAllLocationsKeyFn(
      { archive, page, searchString, size, sortBy, sortDirection, state, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      LocationControllerService.getAllLocations({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        state,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useLocationControllerServiceGetLocationById = <
  TData = Common.LocationControllerServiceGetLocationByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    locationId,
    xTenantId,
  }: {
    locationId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseLocationControllerServiceGetLocationByIdKeyFn({ locationId, xTenantId }, queryKey),
    queryFn: () => LocationControllerService.getLocationById({ locationId, xTenantId }) as TData,
    ...options,
  });
export const useDeviceControllerServiceGetAllDevices = <
  TData = Common.DeviceControllerServiceGetAllDevicesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    category,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    type,
    xTenantId,
  }: {
    archive?: boolean;
    category?: string;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    type?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseDeviceControllerServiceGetAllDevicesKeyFn(
      { archive, category, page, searchString, size, sortBy, sortDirection, status, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      DeviceControllerService.getAllDevices({
        archive,
        category,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useDeviceControllerServiceGetDeviceById = <
  TData = Common.DeviceControllerServiceGetDeviceByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    deviceUuid,
    xTenantId,
  }: {
    deviceUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseDeviceControllerServiceGetDeviceByIdKeyFn({ deviceUuid, xTenantId }, queryKey),
    queryFn: () => DeviceControllerService.getDeviceById({ deviceUuid, xTenantId }) as TData,
    ...options,
  });
export const useClinicalNoteControllerServiceGetClinicalNoteByUuid = <
  TData = Common.ClinicalNoteControllerServiceGetClinicalNoteByUuidDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    clinicalNoteUuid,
    xTenantId,
  }: {
    clinicalNoteUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseClinicalNoteControllerServiceGetClinicalNoteByUuidKeyFn(
      { clinicalNoteUuid, xTenantId },
      queryKey
    ),
    queryFn: () => ClinicalNoteControllerService.getClinicalNoteByUuid({ clinicalNoteUuid, xTenantId }) as TData,
    ...options,
  });
export const useClinicalNoteControllerServiceGetClinicalNoteByAppointmentId = <
  TData = Common.ClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    appointmentId,
    xTenantId,
  }: {
    appointmentId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdKeyFn(
      { appointmentId, xTenantId },
      queryKey
    ),
    queryFn: () => ClinicalNoteControllerService.getClinicalNoteByAppointmentId({ appointmentId, xTenantId }) as TData,
    ...options,
  });
export const useCarePlanControllerServiceGetAllCarePlans1 = <
  TData = Common.CarePlanControllerServiceGetAllCarePlans1DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseCarePlanControllerServiceGetAllCarePlans1KeyFn(
      { archive, page, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      CarePlanControllerService.getAllCarePlans1({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useCarePlanControllerServiceGetCarePlanById = <
  TData = Common.CarePlanControllerServiceGetCarePlanByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    carePlanId,
    globalCarePlan,
    xTenantId,
  }: {
    carePlanId: string;
    globalCarePlan?: boolean;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseCarePlanControllerServiceGetCarePlanByIdKeyFn(
      { carePlanId, globalCarePlan, xTenantId },
      queryKey
    ),
    queryFn: () => CarePlanControllerService.getCarePlanById({ carePlanId, globalCarePlan, xTenantId }) as TData,
    ...options,
  });
export const useCarePlanControllerServiceGetAllReferenceRanges = <
  TData = Common.CarePlanControllerServiceGetAllReferenceRangesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseCarePlanControllerServiceGetAllReferenceRangesKeyFn({ xTenantId }, queryKey),
    queryFn: () => CarePlanControllerService.getAllReferenceRanges({ xTenantId }) as TData,
    ...options,
  });
export const useCarePlanControllerServiceGetAllProtocols = <
  TData = Common.CarePlanControllerServiceGetAllProtocolsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    protocolType,
    xTenantId,
  }: {
    protocolType: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseCarePlanControllerServiceGetAllProtocolsKeyFn({ protocolType, xTenantId }, queryKey),
    queryFn: () => CarePlanControllerService.getAllProtocols({ protocolType, xTenantId }) as TData,
    ...options,
  });
export const useAppointmentControllerServiceGetAllAppointments = <
  TData = Common.AppointmentControllerServiceGetAllAppointmentsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    assigned,
    endDate,
    filter,
    mode,
    nurseId,
    page,
    patientId,
    providerId,
    size,
    sortBy,
    sortDirection,
    startDate,
    status,
    xTenantId,
  }: {
    assigned?: boolean;
    endDate?: string;
    filter?: "PAST" | "ALL" | "UPCOMING" | "REQUESTED";
    mode?: string;
    nurseId?: string;
    page?: number;
    patientId?: string;
    providerId?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    startDate?: string;
    status?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAppointmentControllerServiceGetAllAppointmentsKeyFn(
      {
        assigned,
        endDate,
        filter,
        mode,
        nurseId,
        page,
        patientId,
        providerId,
        size,
        sortBy,
        sortDirection,
        startDate,
        status,
        xTenantId,
      },
      queryKey
    ),
    queryFn: () =>
      AppointmentControllerService.getAllAppointments({
        assigned,
        endDate,
        filter,
        mode,
        nurseId,
        page,
        patientId,
        providerId,
        size,
        sortBy,
        sortDirection,
        startDate,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useAppointmentControllerServiceGetAppointmentById = <
  TData = Common.AppointmentControllerServiceGetAppointmentByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    appointmentId,
    xTenantId,
  }: {
    appointmentId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAppointmentControllerServiceGetAppointmentByIdKeyFn({ appointmentId, xTenantId }, queryKey),
    queryFn: () => AppointmentControllerService.getAppointmentById({ appointmentId, xTenantId }) as TData,
    ...options,
  });
export const useAppointmentControllerServiceEscalateAppointment = <
  TData = Common.AppointmentControllerServiceEscalateAppointmentDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    email,
    schema,
    xTenantId,
  }: {
    email: string;
    schema?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAppointmentControllerServiceEscalateAppointmentKeyFn({ email, schema, xTenantId }, queryKey),
    queryFn: () => AppointmentControllerService.escalateAppointment({ email, schema, xTenantId }) as TData,
    ...options,
  });
export const useAppointmentControllerServiceGetAppointmentList = <
  TData = Common.AppointmentControllerServiceGetAppointmentListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    endDate,
    filter,
    nurseId,
    patientUuid,
    providerId,
    startDate,
    status,
    type,
    xTenantId,
  }: {
    endDate: string;
    filter?: "PAST" | "ALL" | "UPCOMING" | "REQUESTED";
    nurseId?: string;
    patientUuid?: string;
    providerId?: string;
    startDate: string;
    status?: string;
    type?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAppointmentControllerServiceGetAppointmentListKeyFn(
      { endDate, filter, nurseId, patientUuid, providerId, startDate, status, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      AppointmentControllerService.getAppointmentList({
        endDate,
        filter,
        nurseId,
        patientUuid,
        providerId,
        startDate,
        status,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useAvailabilityControllerServiceGetProviderSlots = <
  TData = Common.AvailabilityControllerServiceGetProviderSlotsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    duration,
    endDate,
    page,
    providerUuid,
    size,
    startDate,
    xTenantId,
  }: {
    duration: number;
    endDate?: string;
    page?: number;
    providerUuid: string;
    size?: number;
    startDate?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAvailabilityControllerServiceGetProviderSlotsKeyFn(
      { duration, endDate, page, providerUuid, size, startDate, xTenantId },
      queryKey
    ),
    queryFn: () =>
      AvailabilityControllerService.getProviderSlots({
        duration,
        endDate,
        page,
        providerUuid,
        size,
        startDate,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useAvailabilityControllerServiceGetProviderAvailabilitySetting = <
  TData = Common.AvailabilityControllerServiceGetProviderAvailabilitySettingDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerUuid,
    xTenantId,
  }: {
    providerUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAvailabilityControllerServiceGetProviderAvailabilitySettingKeyFn(
      { providerUuid, xTenantId },
      queryKey
    ),
    queryFn: () => AvailabilityControllerService.getProviderAvailabilitySetting({ providerUuid, xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceGetPatientReports = <
  TData = Common.AiControllerServiceGetPatientReportsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    page,
    patientUuid,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month?: number;
    page?: number;
    patientUuid: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year?: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetPatientReportsKeyFn(
      { month, page, patientUuid, size, sortBy, sortDirection, xTenantId, year },
      queryKey
    ),
    queryFn: () =>
      AiControllerService.getPatientReports({
        month,
        page,
        patientUuid,
        size,
        sortBy,
        sortDirection,
        xTenantId,
        year,
      }) as TData,
    ...options,
  });
export const useAiControllerServiceLlmauGetVoiceRecommendation = <
  TData = Common.AiControllerServiceLlmauGetVoiceRecommendationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceLlmauGetVoiceRecommendationKeyFn({ xTenantId }, queryKey),
    queryFn: () => AiControllerService.llmauGetVoiceRecommendation({ xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceGetNurseReports = <
  TData = Common.AiControllerServiceGetNurseReportsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    reportUuid,
    xTenantId,
  }: {
    reportUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetNurseReportsKeyFn({ reportUuid, xTenantId }, queryKey),
    queryFn: () => AiControllerService.getNurseReports({ reportUuid, xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceLlmauGetMyNurseAvatar = <
  TData = Common.AiControllerServiceLlmauGetMyNurseAvatarDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerId,
    xTenantId,
  }: {
    providerId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceLlmauGetMyNurseAvatarKeyFn({ providerId, xTenantId }, queryKey),
    queryFn: () => AiControllerService.llmauGetMyNurseAvatar({ providerId, xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceGetNurseActionStatistics = <
  TData = Common.AiControllerServiceGetNurseActionStatisticsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetNurseActionStatisticsKeyFn(
      { month, patientUuid, xTenantId, year },
      queryKey
    ),
    queryFn: () => AiControllerService.getNurseActionStatistics({ month, patientUuid, xTenantId, year }) as TData,
    ...options,
  });
export const useAiControllerServiceLlmauGetAvatar = <
  TData = Common.AiControllerServiceLlmauGetAvatarDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceLlmauGetAvatarKeyFn({ xTenantId }, queryKey),
    queryFn: () => AiControllerService.llmauGetAvatar({ xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceGetNurseActionInExcel = <
  TData = Common.AiControllerServiceGetNurseActionInExcelDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetNurseActionInExcelKeyFn(
      { month, patientUuid, xTenantId, year },
      queryKey
    ),
    queryFn: () => AiControllerService.getNurseActionInExcel({ month, patientUuid, xTenantId, year }) as TData,
    ...options,
  });
export const useAiControllerServiceGetChatbotReports = <
  TData = Common.AiControllerServiceGetChatbotReportsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetChatbotReportsKeyFn({ month, patientUuid, xTenantId, year }, queryKey),
    queryFn: () => AiControllerService.getChatbotReports({ month, patientUuid, xTenantId, year }) as TData,
    ...options,
  });
export const useAiControllerServiceGetChatbotHistory = <
  TData = Common.AiControllerServiceGetChatbotHistoryDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetChatbotHistoryKeyFn({ xTenantId }, queryKey),
    queryFn: () => AiControllerService.getChatbotHistory({ xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceLlmauGetAvatarVideoByTitle = <
  TData = Common.AiControllerServiceLlmauGetAvatarVideoByTitleDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerId,
    videoTitle,
    xTenantId,
  }: {
    providerId: string;
    videoTitle: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceLlmauGetAvatarVideoByTitleKeyFn(
      { providerId, videoTitle, xTenantId },
      queryKey
    ),
    queryFn: () => AiControllerService.llmauGetAvatarVideoByTitle({ providerId, videoTitle, xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceAvqGetAlertAudio = <
  TData = Common.AiControllerServiceAvqGetAlertAudioDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerId,
    videoTitle,
    xTenantId,
  }: {
    providerId: string;
    videoTitle: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceAvqGetAlertAudioKeyFn({ providerId, videoTitle, xTenantId }, queryKey),
    queryFn: () => AiControllerService.avqGetAlertAudio({ providerId, videoTitle, xTenantId }) as TData,
    ...options,
  });
export const useVitalControllerServiceGetPatientVitals = <
  TData = Common.VitalControllerServiceGetPatientVitalsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    page,
    searchString,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    page?: number;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseVitalControllerServiceGetPatientVitalsKeyFn(
      { page, searchString, size, sort, sortBy, xTenantId },
      queryKey
    ),
    queryFn: () =>
      VitalControllerService.getPatientVitals({ page, searchString, size, sort, sortBy, xTenantId }) as TData,
    ...options,
  });
export const useZoomControllerServiceGetAuthToken = <
  TData = Common.ZoomControllerServiceGetAuthTokenDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    roomId,
    xTenantId,
  }: {
    roomId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseZoomControllerServiceGetAuthTokenKeyFn({ roomId, xTenantId }, queryKey),
    queryFn: () => ZoomControllerService.getAuthToken({ roomId, xTenantId }) as TData,
    ...options,
  });
export const useZoomControllerServiceSubscribe = <
  TData = Common.ZoomControllerServiceSubscribeDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    eventKey,
    xTenantId,
  }: {
    eventKey: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseZoomControllerServiceSubscribeKeyFn({ eventKey, xTenantId }, queryKey),
    queryFn: () => ZoomControllerService.subscribe({ eventKey, xTenantId }) as TData,
    ...options,
  });
export const useZoomControllerServiceEmit = <
  TData = Common.ZoomControllerServiceEmitDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    eventKey,
    xTenantId,
  }: {
    eventKey: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseZoomControllerServiceEmitKeyFn({ eventKey, xTenantId }, queryKey),
    queryFn: () => ZoomControllerService.emit({ eventKey, xTenantId }) as TData,
    ...options,
  });
export const useLicenseStateControllerServiceGetAllLicensedStates = <
  TData = Common.LicenseStateControllerServiceGetAllLicensedStatesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    xTenantId,
  }: {
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseLicenseStateControllerServiceGetAllLicensedStatesKeyFn(
      { page, searchString, size, sortBy, sortDirection, xTenantId },
      queryKey
    ),
    queryFn: () =>
      LicenseStateControllerService.getAllLicensedStates({
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useEqrControllerServiceGetInfusionTherapyById = <
  TData = Common.EqrControllerServiceGetInfusionTherapyByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    therapyId,
    xTenantId,
  }: {
    therapyId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEqrControllerServiceGetInfusionTherapyByIdKeyFn({ therapyId, xTenantId }, queryKey),
    queryFn: () => EqrControllerService.getInfusionTherapyById({ therapyId, xTenantId }) as TData,
    ...options,
  });
export const useEhrProviderControllerServiceGetAllEhrProviders = <
  TData = Common.EhrProviderControllerServiceGetAllEhrProvidersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrProviderControllerServiceGetAllEhrProvidersKeyFn({ xTenantId }, queryKey),
    queryFn: () => EhrProviderControllerService.getAllEhrProviders({ xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetPractitionerByProviderId = <
  TData = Common.EhrControllerServiceGetPractitionerByProviderIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    practitionerId,
    xTenantId,
  }: {
    practitionerId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetPractitionerByProviderIdKeyFn({ practitionerId, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getPractitionerByProviderId({ practitionerId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceSearchPractitioners = <
  TData = Common.EhrControllerServiceSearchPractitionersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    name,
    xTenantId,
  }: {
    name: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceSearchPractitionersKeyFn({ name, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.searchPractitioners({ name, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetPatientVitals2 = <
  TData = Common.EhrControllerServiceGetPatientVitals2DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    date,
    patientId,
    xTenantId,
  }: {
    date?: string;
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetPatientVitals2KeyFn({ date, patientId, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getPatientVitals2({ date, patientId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetPatientEncounterDiagnosisByPatientId = <
  TData = Common.EhrControllerServiceGetPatientEncounterDiagnosisByPatientIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetPatientEncounterDiagnosisByPatientIdKeyFn(
      { patientId, xTenantId },
      queryKey
    ),
    queryFn: () => EhrControllerService.getPatientEncounterDiagnosisByPatientId({ patientId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetAllergiesByPatientId = <
  TData = Common.EhrControllerServiceGetAllergiesByPatientIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    clinicalStatus,
    patientId,
    recordedDate,
    xTenantId,
  }: {
    clinicalStatus?: string;
    patientId: string;
    recordedDate?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetAllergiesByPatientIdKeyFn(
      { clinicalStatus, patientId, recordedDate, xTenantId },
      queryKey
    ),
    queryFn: () =>
      EhrControllerService.getAllergiesByPatientId({ clinicalStatus, patientId, recordedDate, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceSearchPatients = <
  TData = Common.EhrControllerServiceSearchPatientsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    birthdate,
    family,
    given,
    organisationId,
    patientId,
    xTenantId,
  }: {
    birthdate?: string;
    family?: string;
    given?: string;
    organisationId: string;
    patientId?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceSearchPatientsKeyFn(
      { birthdate, family, given, organisationId, patientId, xTenantId },
      queryKey
    ),
    queryFn: () =>
      EhrControllerService.searchPatients({ birthdate, family, given, organisationId, patientId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetOrganizationByPracticeId = <
  TData = Common.EhrControllerServiceGetOrganizationByPracticeIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    practiceId,
    xTenantId,
  }: {
    practiceId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetOrganizationByPracticeIdKeyFn({ practiceId, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getOrganizationByPracticeId({ practiceId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetMedicationRequestByPatientId = <
  TData = Common.EhrControllerServiceGetMedicationRequestByPatientIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    status,
    xTenantId,
  }: {
    patientId: string;
    status?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetMedicationRequestByPatientIdKeyFn(
      { patientId, status, xTenantId },
      queryKey
    ),
    queryFn: () => EhrControllerService.getMedicationRequestByPatientId({ patientId, status, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetMedicationDispenseByPatientId = <
  TData = Common.EhrControllerServiceGetMedicationDispenseByPatientIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    status,
    whenhandedover,
    xTenantId,
  }: {
    patientId: string;
    status?: string;
    whenhandedover?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetMedicationDispenseByPatientIdKeyFn(
      { patientId, status, whenhandedover, xTenantId },
      queryKey
    ),
    queryFn: () =>
      EhrControllerService.getMedicationDispenseByPatientId({ patientId, status, whenhandedover, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetLocationByLocationId = <
  TData = Common.EhrControllerServiceGetLocationByLocationIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    locationId,
    xTenantId,
  }: {
    locationId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetLocationByLocationIdKeyFn({ locationId, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getLocationByLocationId({ locationId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetAccessToken1 = <
  TData = Common.EhrControllerServiceGetAccessToken1DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetAccessToken1KeyFn({ xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getAccessToken1({ xTenantId }) as TData,
    ...options,
  });
export const useConditionControllerServiceGetAllConditions = <
  TData = Common.ConditionControllerServiceGetAllConditionsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    name,
    page,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    name?: string;
    page?: number;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseConditionControllerServiceGetAllConditionsKeyFn(
      { name, page, size, sort, sortBy, xTenantId },
      queryKey
    ),
    queryFn: () => ConditionControllerService.getAllConditions({ name, page, size, sort, sortBy, xTenantId }) as TData,
    ...options,
  });
export const useActivityControllerServiceGetAllActivities = <
  TData = Common.ActivityControllerServiceGetAllActivitiesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    search,
    xTenantId,
  }: {
    search?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useQuery<TData, TError>({
    queryKey: Common.UseActivityControllerServiceGetAllActivitiesKeyFn({ search, xTenantId }, queryKey),
    queryFn: () => ActivityControllerService.getAllActivities({ search, xTenantId }) as TData,
    ...options,
  });
export const useUserControllerServiceAddUser = <
  TData = Common.UserControllerServiceAddUserMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: User;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: User;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      UserControllerService.addUser({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceVerifyUser = <
  TData = Common.UserControllerServiceVerifyUserMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        email: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      email: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ email, xTenantId }) =>
      UserControllerService.verifyUser({ email, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceVerifyOtp = <
  TData = Common.UserControllerServiceVerifyOtpMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        email: string;
        linkType: string;
        otp: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      email: string;
      linkType: string;
      otp: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ email, linkType, otp, xTenantId }) =>
      UserControllerService.verifyOtp({ email, linkType, otp, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceSetPassword = <
  TData = Common.UserControllerServiceSetPasswordMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        linkType: string;
        requestBody: ResetPasswordRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      linkType: string;
      requestBody: ResetPasswordRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ linkType, requestBody, xTenantId }) =>
      UserControllerService.setPassword({ linkType, requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceResendOtp = <
  TData = Common.UserControllerServiceResendOtpMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        email: string;
        linkType: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      email: string;
      linkType: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ email, linkType, xTenantId }) =>
      UserControllerService.resendOtp({ email, linkType, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceLogout = <
  TData = Common.UserControllerServiceLogoutMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: LogoutRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: LogoutRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      UserControllerService.logout({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceGetAccessToken = <
  TData = Common.UserControllerServiceGetAccessTokenMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: LoginRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: LoginRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      UserControllerService.getAccessToken({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceChangePassword = <
  TData = Common.UserControllerServiceChangePasswordMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ChangePasswordRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ChangePasswordRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      UserControllerService.changePassword({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceGetAccessTokenFromRefreshToken = <
  TData = Common.UserControllerServiceGetAccessTokenFromRefreshTokenMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        refreshToken: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      refreshToken: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ refreshToken, xTenantId }) =>
      UserControllerService.getAccessTokenFromRefreshToken({ refreshToken, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useTimeLogControllerServiceCreateTimeLogAsync = <
  TData = Common.TimeLogControllerServiceCreateTimeLogAsyncMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: TimeLogRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: TimeLogRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      TimeLogControllerService.createTimeLogAsync({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useTimeLogControllerServicePlayerTimeLog = <
  TData = Common.TimeLogControllerServicePlayerTimeLogMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PlayerTimeLogRequestDto;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PlayerTimeLogRequestDto;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      TimeLogControllerService.playerTimeLog({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useTaskControllerServiceAddTask = <
  TData = Common.TaskControllerServiceAddTaskMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Task;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Task;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      TaskControllerService.addTask({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceAddRole = <
  TData = Common.RolesAndPrivilegesControllerServiceAddRoleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Role;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Role;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      RolesAndPrivilegesControllerService.addRole({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceResetAllRolePrivileges = <
  TData = Common.RolesAndPrivilegesControllerServiceResetAllRolePrivilegesMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ xTenantId }) =>
      RolesAndPrivilegesControllerService.resetAllRolePrivileges({ xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderControllerServiceCreateProvider = <
  TData = Common.ProviderControllerServiceCreateProviderMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Provider;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Provider;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      ProviderControllerService.createProvider({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderGroupControllerServiceCreateProviderGroup = <
  TData = Common.ProviderGroupControllerServiceCreateProviderGroupMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ProviderGroup;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ProviderGroup;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      ProviderGroupControllerService.createProviderGroup({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientControllerServiceCreatePatient = <
  TData = Common.PatientControllerServiceCreatePatientMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        isAiGenerated?: boolean;
        requestBody: Patient;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      isAiGenerated?: boolean;
      requestBody: Patient;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ isAiGenerated, requestBody, xTenantId }) =>
      PatientControllerService.createPatient({ isAiGenerated, requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientControllerServiceUploadFile = <
  TData = Common.PatientControllerServiceUploadFileMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        formData: { file: Blob | File };
        isAiGenerated?: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      formData: { file: Blob | File };
      isAiGenerated?: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ formData, isAiGenerated, xTenantId }) =>
      PatientControllerService.uploadFile({ formData, isAiGenerated, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientVitalControllerServiceCreatePatientVital = <
  TData = Common.PatientVitalControllerServiceCreatePatientVitalMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientVital;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientVital;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientVitalControllerService.createPatientVital({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientVitalControllerServiceCreateBulkPatientVital = <
  TData = Common.PatientVitalControllerServiceCreateBulkPatientVitalMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientVitalRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientVitalRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientVitalControllerService.createBulkPatientVital({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientMedicationControllerServiceCreatePatientMedication = <
  TData = Common.PatientMedicationControllerServiceCreatePatientMedicationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientMedication;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientMedication;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientMedicationControllerService.createPatientMedication({
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientMedicationControllerServiceCreateBulkPatientMedication = <
  TData = Common.PatientMedicationControllerServiceCreateBulkPatientMedicationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientId: string;
        requestBody: PatientMedication[];
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientId: string;
      requestBody: PatientMedication[];
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientId, requestBody, xTenantId }) =>
      PatientMedicationControllerService.createBulkPatientMedication({
        patientId,
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientMedicationControllerServiceCreateBulkPatientMedicationWithImage = <
  TData = Common.PatientMedicationControllerServiceCreateBulkPatientMedicationWithImageMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientId: string;
        requestBody?: { medications: string; images: (Blob | File)[] };
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientId: string;
      requestBody?: { medications: string; images: (Blob | File)[] };
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientId, requestBody, xTenantId }) =>
      PatientMedicationControllerService.createBulkPatientMedicationWithImage({
        patientId,
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientDiagnosisControllerServiceCreatePatientDiagnosis = <
  TData = Common.PatientDiagnosisControllerServiceCreatePatientDiagnosisMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientDiagnosis;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientDiagnosis;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientDiagnosisControllerService.createPatientDiagnosis({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientDiagnosisControllerServiceCreateBulkPatientDiagnosis = <
  TData = Common.PatientDiagnosisControllerServiceCreateBulkPatientDiagnosisMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientId: string;
        requestBody: PatientDiagnosis[];
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientId: string;
      requestBody: PatientDiagnosis[];
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientId, requestBody, xTenantId }) =>
      PatientDiagnosisControllerService.createBulkPatientDiagnosis({
        patientId,
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useConsentFormControllerServiceCreateConsentForms = <
  TData = Common.ConsentFormControllerServiceCreateConsentFormsMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ConsentFormTemplate;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ConsentFormTemplate;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      ConsentFormControllerService.createConsentForms({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useConsentFormControllerServiceAddPatientConsent = <
  TData = Common.ConsentFormControllerServiceAddPatientConsentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientConsentForm;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientConsentForm;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      ConsentFormControllerService.addPatientConsent({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientCarePlanControllerServiceAddProgramGoalTrack = <
  TData = Common.PatientCarePlanControllerServiceAddProgramGoalTrackMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientProgramGoalTrack;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientProgramGoalTrack;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientCarePlanControllerService.addProgramGoalTrack({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientAllergyControllerServiceCreatePatientAllergy = <
  TData = Common.PatientAllergyControllerServiceCreatePatientAllergyMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientAllergy;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientAllergy;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientAllergyControllerService.createPatientAllergy({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientAllergyControllerServiceCreateBulkPatientAllergy = <
  TData = Common.PatientAllergyControllerServiceCreateBulkPatientAllergyMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientId: string;
        requestBody: PatientAllergy[];
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientId: string;
      requestBody: PatientAllergy[];
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientId, requestBody, xTenantId }) =>
      PatientAllergyControllerService.createBulkPatientAllergy({
        patientId,
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useMedicalCodeControllerServiceCreateMedicalCode = <
  TData = Common.MedicalCodeControllerServiceCreateMedicalCodeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: MedicalCode;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: MedicalCode;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      MedicalCodeControllerService.createMedicalCode({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useMedicalCodeControllerServiceUploadFile1 = <
  TData = Common.MedicalCodeControllerServiceUploadFile1MutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        category: "ICD10" | "CPT" | "ALL";
        formData: { file: Blob | File };
        providerGroupUuid?: string;
        title: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      category: "ICD10" | "CPT" | "ALL";
      formData: { file: Blob | File };
      providerGroupUuid?: string;
      title: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ category, formData, providerGroupUuid, title, xTenantId }) =>
      MedicalCodeControllerService.uploadFile1({
        category,
        formData,
        providerGroupUuid,
        title,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useLocationControllerServiceCreateLocation = <
  TData = Common.LocationControllerServiceCreateLocationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Location;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Location;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      LocationControllerService.createLocation({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useDeviceControllerServiceCreateDevice = <
  TData = Common.DeviceControllerServiceCreateDeviceMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Device;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Device;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      DeviceControllerService.createDevice({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useClinicalNoteControllerServiceCreateClinicalNote = <
  TData = Common.ClinicalNoteControllerServiceCreateClinicalNoteMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ClinicalNote;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ClinicalNote;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      ClinicalNoteControllerService.createClinicalNote({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useClinicalNoteControllerServiceExportAndEmailPdf = <
  TData = Common.ClinicalNoteControllerServiceExportAndEmailPdfMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        clinicalNoteUuid: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      clinicalNoteUuid: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ clinicalNoteUuid, xTenantId }) =>
      ClinicalNoteControllerService.exportAndEmailPdf({ clinicalNoteUuid, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useCarePlanControllerServiceCreateCarePlan = <
  TData = Common.CarePlanControllerServiceCreateCarePlanMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CarePlan;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CarePlan;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      CarePlanControllerService.createCarePlan({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAppointmentControllerServiceCreateAppointment = <
  TData = Common.AppointmentControllerServiceCreateAppointmentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Appointment;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Appointment;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AppointmentControllerService.createAppointment({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAppointmentControllerServiceBookAppointmentRequest = <
  TData = Common.AppointmentControllerServiceBookAppointmentRequestMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: AppointmentRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: AppointmentRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AppointmentControllerService.bookAppointmentRequest({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAppointmentControllerServiceSendInvitationLink = <
  TData = Common.AppointmentControllerServiceSendInvitationLinkMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UserInvitationRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UserInvitationRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AppointmentControllerService.sendInvitationLink({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAvailabilityControllerServiceSetProviderAvailabilitySetting = <
  TData = Common.AvailabilityControllerServiceSetProviderAvailabilitySettingMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: AvailabilitySetting;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: AvailabilitySetting;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AvailabilityControllerService.setProviderAvailabilitySetting({
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientTrainingControllerServiceAddUpdateTrainedDevice = <
  TData = Common.PatientTrainingControllerServiceAddUpdateTrainedDeviceMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientDeviceRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientDeviceRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientTrainingControllerService.addUpdateTrainedDevice({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useNotificationControllerServiceSendDirectedMessage = <
  TData = Common.NotificationControllerServiceSendDirectedMessageMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: SendDirectMessageRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: SendDirectMessageRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      NotificationControllerService.sendDirectedMessage({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useNotificationControllerServiceTestNotif = <
  TData = Common.NotificationControllerServiceTestNotifMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: string[];
        uuid: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: string[];
      uuid: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, uuid, xTenantId }) =>
      NotificationControllerService.testNotif({ requestBody, uuid, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useNotificationControllerServiceTestNotifImage = <
  TData = Common.NotificationControllerServiceTestNotifImageMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        image: string;
        requestBody: string[];
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      image: string;
      requestBody: string[];
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ image, requestBody, xTenantId }) =>
      NotificationControllerService.testNotifImage({ image, requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useFirebaseMessageControllerServiceSendMulticastMessage = <
  TData = Common.FirebaseMessageControllerServiceSendMulticastMessageMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: MessageRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: MessageRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      FirebaseMessageControllerService.sendMulticastMessage({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useFirebaseMessageControllerServiceSaveToken = <
  TData = Common.FirebaseMessageControllerServiceSaveTokenMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        fcmToken: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      fcmToken: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ fcmToken, xTenantId }) =>
      FirebaseMessageControllerService.saveToken({ fcmToken, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceStoreNurseActionStatistics = <
  TData = Common.AiControllerServiceStoreNurseActionStatisticsMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ xTenantId }) =>
      AiControllerService.storeNurseActionStatistics({ xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceAvqSetupAvatar = <
  TData = Common.AiControllerServiceAvqSetupAvatarMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody?: { request: string; audio_files: (Blob | File)[] };
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody?: { request: string; audio_files: (Blob | File)[] };
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AiControllerService.avqSetupAvatar({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceAvqSetupAvatarEdge = <
  TData = Common.AiControllerServiceAvqSetupAvatarEdgeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: GenerateAvatarRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: GenerateAvatarRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AiControllerService.avqSetupAvatarEdge({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceSendPatientAlert = <
  TData = Common.AiControllerServiceSendPatientAlertMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ xTenantId }) => AiControllerService.sendPatientAlert({ xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceLlmauGenerateMedicationAudio = <
  TData = Common.AiControllerServiceLlmauGenerateMedicationAudioMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: MedicationAudioRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: MedicationAudioRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AiControllerService.llmauGenerateMedicationAudio({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceVygenGenerateAvatar = <
  TData = Common.AiControllerServiceVygenGenerateAvatarMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: GenerateAvatarRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: GenerateAvatarRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AiControllerService.vygenGenerateAvatar({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceGenerateAudioMonthlyQuickSummary = <
  TData = Common.AiControllerServiceGenerateAudioMonthlyQuickSummaryMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ xTenantId }) =>
      AiControllerService.generateAudioMonthlyQuickSummary({ xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceVygenDetectFace = <
  TData = Common.AiControllerServiceVygenDetectFaceMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: GenerateAvatarRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: GenerateAvatarRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AiControllerService.vygenDetectFace({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceLlmauChatWithMyNurse = <
  TData = Common.AiControllerServiceLlmauChatWithMyNurseMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerId: string;
        requestBody: Message;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerId: string;
      requestBody: Message;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerId, requestBody, xTenantId }) =>
      AiControllerService.llmauChatWithMyNurse({ providerId, requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceGetAiCarePlan = <
  TData = Common.AiControllerServiceGetAiCarePlanMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AiControllerService.getAiCarePlan({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceGetAiCarePlanList = <
  TData = Common.AiControllerServiceGetAiCarePlanListMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientDTO;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientDTO;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AiControllerService.getAiCarePlanList({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceLlmauAudioChatWithMyNurse = <
  TData = Common.AiControllerServiceLlmauAudioChatWithMyNurseMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerId: string;
        requestBody?: { timezone?: string; audio_files: Blob | File };
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerId: string;
      requestBody?: { timezone?: string; audio_files: Blob | File };
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerId, requestBody, xTenantId }) =>
      AiControllerService.llmauAudioChatWithMyNurse({
        providerId,
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useAiControllerServiceLlmauAnalyzeMedication = <
  TData = Common.AiControllerServiceLlmauAnalyzeMedicationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody?: { images: (Blob | File)[] };
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody?: { images: (Blob | File)[] };
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AiControllerService.llmauAnalyzeMedication({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceUpdateUserArchiveStatus = <
  TData = Common.UserControllerServiceUpdateUserArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        status: boolean;
        userId: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      status: boolean;
      userId: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ status, userId, xTenantId }) =>
      UserControllerService.updateUserArchiveStatus({ status, userId, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceUpdateUser = <
  TData = Common.UserControllerServiceUpdateUserMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: User;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: User;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      UserControllerService.updateUser({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useUserControllerServiceChangeAvatar3 = <
  TData = Common.UserControllerServiceChangeAvatar3MutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ChangeAvatarRequest;
        userUuid: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ChangeAvatarRequest;
      userUuid: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, userUuid, xTenantId }) =>
      UserControllerService.changeAvatar3({ requestBody, userUuid, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientVitalSettingControllerServiceUpdatePatientVitalSetting = <
  TData = Common.PatientVitalSettingControllerServiceUpdatePatientVitalSettingMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientId: string;
        settingName: "HEART_RATE" | "ECG" | "HRV" | "OXYGEN_SATURATION" | "STRESS";
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientId: string;
      settingName: "HEART_RATE" | "ECG" | "HRV" | "OXYGEN_SATURATION" | "STRESS";
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientId, settingName, status, xTenantId }) =>
      PatientVitalSettingControllerService.updatePatientVitalSetting({
        patientId,
        settingName,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useTimeLogControllerServiceUpdateTimeLog = <
  TData = Common.TimeLogControllerServiceUpdateTimeLogMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: TimeLogRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: TimeLogRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      TimeLogControllerService.updateTimeLog({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useTaskControllerServiceUpdateTask = <
  TData = Common.TaskControllerServiceUpdateTaskMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Task;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Task;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      TaskControllerService.updateTask({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useTaskControllerServiceUpdateTaskStatus = <
  TData = Common.TaskControllerServiceUpdateTaskStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        note?: string;
        status: "PENDING" | "COMPLETED" | "DISCARDED";
        taskUuid: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      note?: string;
      status: "PENDING" | "COMPLETED" | "DISCARDED";
      taskUuid: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ note, status, taskUuid, xTenantId }) =>
      TaskControllerService.updateTaskStatus({ note, status, taskUuid, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceUpdateRole = <
  TData = Common.RolesAndPrivilegesControllerServiceUpdateRoleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Role;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Role;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      RolesAndPrivilegesControllerService.updateRole({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceAddUpdateRolePermissions = <
  TData = Common.RolesAndPrivilegesControllerServiceAddUpdateRolePermissionsMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ xTenantId }) =>
      RolesAndPrivilegesControllerService.addUpdateRolePermissions({ xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceUpdateRolePermission = <
  TData = Common.RolesAndPrivilegesControllerServiceUpdateRolePermissionMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RolePrivilegeUpdateRequest[];
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RolePrivilegeUpdateRequest[];
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      RolesAndPrivilegesControllerService.updateRolePermission({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderControllerServiceUpdateProvider = <
  TData = Common.ProviderControllerServiceUpdateProviderMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Provider;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Provider;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      ProviderControllerService.updateProvider({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderControllerServiceUpdateProviderOnboardingStatus = <
  TData = Common.ProviderControllerServiceUpdateProviderOnboardingStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerId: string;
        status: "PENDING" | "COMPLETED";
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerId: string;
      status: "PENDING" | "COMPLETED";
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerId, status, xTenantId }) =>
      ProviderControllerService.updateProviderOnboardingStatus({
        providerId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderControllerServiceUpdateProviderAvatarStatus = <
  TData = Common.ProviderControllerServiceUpdateProviderAvatarStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerId: string;
        status: "PENDING" | "COMPLETED" | "IN_PROGRESS" | "READY_TO_PREVIEW";
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerId: string;
      status: "PENDING" | "COMPLETED" | "IN_PROGRESS" | "READY_TO_PREVIEW";
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerId, status, xTenantId }) =>
      ProviderControllerService.updateProviderAvatarStatus({
        providerId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderControllerServiceUpdateProviderArchiveStatus = <
  TData = Common.ProviderControllerServiceUpdateProviderArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerId, status, xTenantId }) =>
      ProviderControllerService.updateProviderArchiveStatus({
        providerId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderControllerServiceUploadVideo = <
  TData = Common.ProviderControllerServiceUploadVideoMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerUuid: string;
        requestBody?: { file: Blob | File };
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerUuid: string;
      requestBody?: { file: Blob | File };
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerUuid, requestBody, xTenantId }) =>
      ProviderControllerService.uploadVideo({ providerUuid, requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderControllerServiceNotifyPatient = <
  TData = Common.ProviderControllerServiceNotifyPatientMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        appointmentId: string;
        notifyType:
          | "BROADCAST"
          | "PATIENT_APPOINTMENT_ACCEPTED"
          | "PATIENT_APPOINTMENT_REQUEST"
          | "APPOINTMENT_REMINDER"
          | "PATIENT_MEDICATION_REMINDER"
          | "CARE_PLAN_REMINDER"
          | "PATIENT_VITAL_ALERT"
          | "SMART_ALERT_REMINDER";
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      appointmentId: string;
      notifyType:
        | "BROADCAST"
        | "PATIENT_APPOINTMENT_ACCEPTED"
        | "PATIENT_APPOINTMENT_REQUEST"
        | "APPOINTMENT_REMINDER"
        | "PATIENT_MEDICATION_REMINDER"
        | "CARE_PLAN_REMINDER"
        | "PATIENT_VITAL_ALERT"
        | "SMART_ALERT_REMINDER";
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ appointmentId, notifyType, xTenantId }) =>
      ProviderControllerService.notifyPatient({ appointmentId, notifyType, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderControllerServiceChangeAvatar = <
  TData = Common.ProviderControllerServiceChangeAvatarMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerUuid: string;
        requestBody: ChangeAvatarRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerUuid: string;
      requestBody: ChangeAvatarRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerUuid, requestBody, xTenantId }) =>
      ProviderControllerService.changeAvatar({ providerUuid, requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderGroupControllerServiceUpdateProviderGroup = <
  TData = Common.ProviderGroupControllerServiceUpdateProviderGroupMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ProviderGroup;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ProviderGroup;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      ProviderGroupControllerService.updateProviderGroup({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderGroupControllerServiceSyncDatabaseSchema = <
  TData = Common.ProviderGroupControllerServiceSyncDatabaseSchemaMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        roleSync?: boolean;
        uuid: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      roleSync?: boolean;
      uuid: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ roleSync, uuid, xTenantId }) =>
      ProviderGroupControllerService.syncDatabaseSchema({ roleSync, uuid, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderGroupControllerServiceUpdateProviderGroupArchiveStatus = <
  TData = Common.ProviderGroupControllerServiceUpdateProviderGroupArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerGroupId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerGroupId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerGroupId, status, xTenantId }) =>
      ProviderGroupControllerService.updateProviderGroupArchiveStatus({
        providerGroupId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderGroupControllerServiceUpdateProviderGroupConfiguration = <
  TData = Common.ProviderGroupControllerServiceUpdateProviderGroupConfigurationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ProviderGroup;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ProviderGroup;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      ProviderGroupControllerService.updateProviderGroupConfiguration({
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderGroupControllerServiceCreateRealm = <
  TData = Common.ProviderGroupControllerServiceCreateRealmMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        realmName: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      realmName: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ realmName, xTenantId }) =>
      ProviderGroupControllerService.createRealm({ realmName, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderGroupControllerServiceChangeAvatar1 = <
  TData = Common.ProviderGroupControllerServiceChangeAvatar1MutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerGroupId: string;
        requestBody: ChangeAvatarRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerGroupId: string;
      requestBody: ChangeAvatarRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerGroupId, requestBody, xTenantId }) =>
      ProviderGroupControllerService.changeAvatar1({
        providerGroupId,
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientControllerServiceUpdatePatient = <
  TData = Common.PatientControllerServiceUpdatePatientMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Patient;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Patient;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientControllerService.updatePatient({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientControllerServiceUpdatePatientArchiveStatus = <
  TData = Common.PatientControllerServiceUpdatePatientArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientId, status, xTenantId }) =>
      PatientControllerService.updatePatientArchiveStatus({
        patientId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientControllerServiceChangeAvatar2 = <
  TData = Common.PatientControllerServiceChangeAvatar2MutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientUuid: string;
        requestBody: ChangeAvatarRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientUuid: string;
      requestBody: ChangeAvatarRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientUuid, requestBody, xTenantId }) =>
      PatientControllerService.changeAvatar2({ patientUuid, requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientVitalControllerServiceUpdatePatientVital = <
  TData = Common.PatientVitalControllerServiceUpdatePatientVitalMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientVital;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientVital;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientVitalControllerService.updatePatientVital({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientVitalControllerServiceSyncPatientVital = <
  TData = Common.PatientVitalControllerServiceSyncPatientVitalMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientEhrId: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientEhrId: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientEhrId, xTenantId }) =>
      PatientVitalControllerService.syncPatientVital({ patientEhrId, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientMedicationControllerServiceUpdatePatientMedication = <
  TData = Common.PatientMedicationControllerServiceUpdatePatientMedicationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientMedication;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientMedication;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientMedicationControllerService.updatePatientMedication({
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientMedicationControllerServiceUpdateBulkPatientMedicationDosage = <
  TData = Common.PatientMedicationControllerServiceUpdateBulkPatientMedicationDosageMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientId: string;
        requestBody: PatientMedication[];
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientId: string;
      requestBody: PatientMedication[];
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientId, requestBody, xTenantId }) =>
      PatientMedicationControllerService.updateBulkPatientMedicationDosage({
        patientId,
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientMedicationControllerServiceSyncPatientMedication = <
  TData = Common.PatientMedicationControllerServiceSyncPatientMedicationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientEhrId: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientEhrId: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientEhrId, xTenantId }) =>
      PatientMedicationControllerService.syncPatientMedication({
        patientEhrId,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientMedicationControllerServiceDeletePatientMedicationId = <
  TData = Common.PatientMedicationControllerServiceDeletePatientMedicationIdMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientMedicationId: string;
        status?: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientMedicationId: string;
      status?: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientMedicationId, status, xTenantId }) =>
      PatientMedicationControllerService.deletePatientMedicationId({
        patientMedicationId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientDiagnosisControllerServiceUpdatePatientDiagnosis = <
  TData = Common.PatientDiagnosisControllerServiceUpdatePatientDiagnosisMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientDiagnosis;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientDiagnosis;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientDiagnosisControllerService.updatePatientDiagnosis({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientDiagnosisControllerServiceUpdatePatientDiagnosisArchiveStatus = <
  TData = Common.PatientDiagnosisControllerServiceUpdatePatientDiagnosisArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientDiagnosisId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientDiagnosisId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientDiagnosisId, status, xTenantId }) =>
      PatientDiagnosisControllerService.updatePatientDiagnosisArchiveStatus({
        patientDiagnosisId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientDiagnosisControllerServiceSyncPatientDiagnosis = <
  TData = Common.PatientDiagnosisControllerServiceSyncPatientDiagnosisMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientEhrId: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientEhrId: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientEhrId, xTenantId }) =>
      PatientDiagnosisControllerService.syncPatientDiagnosis({ patientEhrId, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useConsentFormControllerServiceUpdatePatientConsentStatus = <
  TData = Common.ConsentFormControllerServiceUpdatePatientConsentStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientUuid: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientUuid: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientUuid, status, xTenantId }) =>
      ConsentFormControllerService.updatePatientConsentStatus({
        patientUuid,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useConsentFormControllerServiceUpdateConsentForms = <
  TData = Common.ConsentFormControllerServiceUpdateConsentFormsMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ConsentFormTemplate;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ConsentFormTemplate;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      ConsentFormControllerService.updateConsentForms({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useConsentFormControllerServiceUpdateConsentFormArchiveStatus = <
  TData = Common.ConsentFormControllerServiceUpdateConsentFormArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        consentFormId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      consentFormId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ consentFormId, status, xTenantId }) =>
      ConsentFormControllerService.updateConsentFormArchiveStatus({
        consentFormId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientCarePlanControllerServiceUpdatePatientCarePlan = <
  TData = Common.PatientCarePlanControllerServiceUpdatePatientCarePlanMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientCarePlanUpdateRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientCarePlanUpdateRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientCarePlanControllerService.updatePatientCarePlan({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientCarePlanControllerServiceUpdateCarePlanArchiveStatus = <
  TData = Common.PatientCarePlanControllerServiceUpdateCarePlanArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientCarePlanId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientCarePlanId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientCarePlanId, status, xTenantId }) =>
      PatientCarePlanControllerService.updateCarePlanArchiveStatus({
        patientCarePlanId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientCarePlanControllerServiceUpdatePatientCarePlanStatus = <
  TData = Common.PatientCarePlanControllerServiceUpdatePatientCarePlanStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientCarePlanStatusChange;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientCarePlanStatusChange;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientCarePlanControllerService.updatePatientCarePlanStatus({
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientCarePlanControllerServiceUpdateProgramGoalTrack = <
  TData = Common.PatientCarePlanControllerServiceUpdateProgramGoalTrackMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientProgramGoalTrack;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientProgramGoalTrack;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientCarePlanControllerService.updateProgramGoalTrack({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientCarePlanControllerServiceUpdateVitalReferenceRange = <
  TData = Common.PatientCarePlanControllerServiceUpdateVitalReferenceRangeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: VitalReference;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: VitalReference;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientCarePlanControllerService.updateVitalReferenceRange({
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientCarePlanControllerServiceBulkAssignCarePlans = <
  TData = Common.PatientCarePlanControllerServiceBulkAssignCarePlansMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: BulkCarePlanRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: BulkCarePlanRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientCarePlanControllerService.bulkAssignCarePlans({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientCarePlanControllerServiceAssignCarePlan = <
  TData = Common.PatientCarePlanControllerServiceAssignCarePlanMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientId: string;
        requestBody: PatientCarePlanRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientId: string;
      requestBody: PatientCarePlanRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientId, requestBody, xTenantId }) =>
      PatientCarePlanControllerService.assignCarePlan({
        patientId,
        requestBody,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientAllergyControllerServiceUpdatePatientAllergy = <
  TData = Common.PatientAllergyControllerServiceUpdatePatientAllergyMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: PatientAllergy;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: PatientAllergy;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      PatientAllergyControllerService.updatePatientAllergy({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientAllergyControllerServiceUpdatePatientAllergyArchiveStatus = <
  TData = Common.PatientAllergyControllerServiceUpdatePatientAllergyArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientAllergyId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientAllergyId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientAllergyId, status, xTenantId }) =>
      PatientAllergyControllerService.updatePatientAllergyArchiveStatus({
        patientAllergyId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const usePatientAllergyControllerServiceSyncPatientAllergy = <
  TData = Common.PatientAllergyControllerServiceSyncPatientAllergyMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        patientEhrId: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      patientEhrId: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ patientEhrId, xTenantId }) =>
      PatientAllergyControllerService.syncPatientAllergy({ patientEhrId, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useMedicalCodeControllerServiceUpdateMedicalCode = <
  TData = Common.MedicalCodeControllerServiceUpdateMedicalCodeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: MedicalCode;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: MedicalCode;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      MedicalCodeControllerService.updateMedicalCode({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useMedicalCodeControllerServiceUpdateMedicalCodeStatus = <
  TData = Common.MedicalCodeControllerServiceUpdateMedicalCodeStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        medicalCodeId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      medicalCodeId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ medicalCodeId, status, xTenantId }) =>
      MedicalCodeControllerService.updateMedicalCodeStatus({
        medicalCodeId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useMedicalCodeControllerServiceUpdateMedicalCodeArchiveStatus = <
  TData = Common.MedicalCodeControllerServiceUpdateMedicalCodeArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        medicalCodeId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      medicalCodeId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ medicalCodeId, status, xTenantId }) =>
      MedicalCodeControllerService.updateMedicalCodeArchiveStatus({
        medicalCodeId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useLocationControllerServiceUpdateLocation = <
  TData = Common.LocationControllerServiceUpdateLocationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Location;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Location;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      LocationControllerService.updateLocation({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useLocationControllerServiceUpdateLocationArchiveStatus = <
  TData = Common.LocationControllerServiceUpdateLocationArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        locationId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      locationId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ locationId, status, xTenantId }) =>
      LocationControllerService.updateLocationArchiveStatus({
        locationId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useDeviceControllerServiceUpdateDevice = <
  TData = Common.DeviceControllerServiceUpdateDeviceMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Device;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Device;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      DeviceControllerService.updateDevice({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useDeviceControllerServiceUpdateDeviceStatus = <
  TData = Common.DeviceControllerServiceUpdateDeviceStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        deviceUuid: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      deviceUuid: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ deviceUuid, status, xTenantId }) =>
      DeviceControllerService.updateDeviceStatus({ deviceUuid, status, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useDeviceControllerServiceUpdateDeviceArchiveStatus = <
  TData = Common.DeviceControllerServiceUpdateDeviceArchiveStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        deviceUuid: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      deviceUuid: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ deviceUuid, status, xTenantId }) =>
      DeviceControllerService.updateDeviceArchiveStatus({ deviceUuid, status, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useDeviceControllerServiceAssignDevice = <
  TData = Common.DeviceControllerServiceAssignDeviceMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ xTenantId }) => DeviceControllerService.assignDevice({ xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useClinicalNoteControllerServiceUpdateClinicalNote = <
  TData = Common.ClinicalNoteControllerServiceUpdateClinicalNoteMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ClinicalNote;
        signOff?: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ClinicalNote;
      signOff?: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, signOff, xTenantId }) =>
      ClinicalNoteControllerService.updateClinicalNote({
        requestBody,
        signOff,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useCarePlanControllerServiceUpdateCarePlan = <
  TData = Common.CarePlanControllerServiceUpdateCarePlanMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CarePlan;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CarePlan;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      CarePlanControllerService.updateCarePlan({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useCarePlanControllerServiceUpdateUserStatus = <
  TData = Common.CarePlanControllerServiceUpdateUserStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        carePlanId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      carePlanId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ carePlanId, status, xTenantId }) =>
      CarePlanControllerService.updateUserStatus({ carePlanId, status, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useCarePlanControllerServiceUpdateCarePlanArchiveStatus1 = <
  TData = Common.CarePlanControllerServiceUpdateCarePlanArchiveStatus1MutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        carePlanId: string;
        status: boolean;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      carePlanId: string;
      status: boolean;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ carePlanId, status, xTenantId }) =>
      CarePlanControllerService.updateCarePlanArchiveStatus1({
        carePlanId,
        status,
        xTenantId,
      }) as unknown as Promise<TData>,
    ...options,
  });
export const useAppointmentControllerServiceUpdateAppointment = <
  TData = Common.AppointmentControllerServiceUpdateAppointmentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: Appointment;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: Appointment;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AppointmentControllerService.updateAppointment({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAppointmentControllerServiceUpdateAppointmentStatus = <
  TData = Common.AppointmentControllerServiceUpdateAppointmentStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: AppointmentStatusChange;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: AppointmentStatusChange;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AppointmentControllerService.updateAppointmentStatus({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAppointmentControllerServiceRescheduleAppointment = <
  TData = Common.AppointmentControllerServiceRescheduleAppointmentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RescheduleRequest;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RescheduleRequest;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AppointmentControllerService.rescheduleAppointment({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useAppointmentControllerServiceBroadCastAppointment = <
  TData = Common.AppointmentControllerServiceBroadCastAppointmentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: AppointmentStatusChange;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: AppointmentStatusChange;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ requestBody, xTenantId }) =>
      AppointmentControllerService.broadCastAppointment({ requestBody, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useTimeLogControllerServiceDeleteTimeLogById = <
  TData = Common.TimeLogControllerServiceDeleteTimeLogByIdMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        timeLogId: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      timeLogId: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ timeLogId, xTenantId }) =>
      TimeLogControllerService.deleteTimeLogById({ timeLogId, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useTaskControllerServiceArchiveTask = <
  TData = Common.TaskControllerServiceArchiveTaskMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        status: boolean;
        taskUuid: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      status: boolean;
      taskUuid: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ status, taskUuid, xTenantId }) =>
      TaskControllerService.archiveTask({ status, taskUuid, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useProviderControllerServiceDeleteVideo = <
  TData = Common.ProviderControllerServiceDeleteVideoMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        providerUuid: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      providerUuid: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ providerUuid, xTenantId }) =>
      ProviderControllerService.deleteVideo({ providerUuid, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
export const useFirebaseMessageControllerServiceDeleteToken = <
  TData = Common.FirebaseMessageControllerServiceDeleteTokenMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        fcmToken: string;
        xTenantId?: string;
      },
      TContext
    >,
    "mutationFn"
  >
) =>
  useMutation<
    TData,
    TError,
    {
      fcmToken: string;
      xTenantId?: string;
    },
    TContext
  >({
    mutationFn: ({ fcmToken, xTenantId }) =>
      FirebaseMessageControllerService.deleteToken({ fcmToken, xTenantId }) as unknown as Promise<TData>,
    ...options,
  });
