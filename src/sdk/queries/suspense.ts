// generated with @7nohe/openapi-react-query-codegen@1.4.1
import { UseQueryOptions, useSuspenseQuery } from "@tanstack/react-query";

import {
  ActivityControllerService,
  AiControllerService,
  AppointmentControllerService,
  AvailabilityControllerService,
  CarePlanControllerService,
  ClinicalNoteControllerService,
  ConditionControllerService,
  ConsentFormControllerService,
  DeviceControllerService,
  EhrControllerService,
  EhrProviderControllerService,
  EqrControllerService,
  LicenseStateControllerService,
  LocationControllerService,
  MedicalCodeControllerService,
  PatientAllergyControllerService,
  PatientCarePlanControllerService,
  PatientControllerService,
  PatientDiagnosisControllerService,
  PatientMedicationControllerService,
  PatientVitalControllerService,
  PatientVitalSettingControllerService,
  ProviderControllerService,
  ProviderGroupControllerService,
  RolesAndPrivilegesControllerService,
  TaskControllerService,
  TimeLogControllerService,
  UserControllerService,
  VitalControllerService,
  ZoomControllerService,
} from "../requests/services.gen";
import * as Common from "./common";

export const useUserControllerServiceGetAllUsersSuspense = <
  TData = Common.UserControllerServiceGetAllUsersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    locationId,
    page,
    role,
    roleType,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    locationId?: string;
    page?: number;
    role?: string;
    roleType?: "PROVIDER" | "STAFF" | "PATIENT";
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseUserControllerServiceGetAllUsersKeyFn(
      { archive, locationId, page, role, roleType, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      UserControllerService.getAllUsers({
        archive,
        locationId,
        page,
        role,
        roleType,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useUserControllerServiceGetUserSuspense = <
  TData = Common.UserControllerServiceGetUserDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    userId,
    xTenantId,
  }: {
    userId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseUserControllerServiceGetUserKeyFn({ userId, xTenantId }, queryKey),
    queryFn: () => UserControllerService.getUser({ userId, xTenantId }) as TData,
    ...options,
  });
export const useUserControllerServiceGetProfile1Suspense = <
  TData = Common.UserControllerServiceGetProfile1DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseUserControllerServiceGetProfile1KeyFn({ xTenantId }, queryKey),
    queryFn: () => UserControllerService.getProfile1({ xTenantId }) as TData,
    ...options,
  });
export const usePatientVitalSettingControllerServiceGetVitalSettingsSuspense = <
  TData = Common.PatientVitalSettingControllerServiceGetVitalSettingsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientVitalSettingControllerServiceGetVitalSettingsKeyFn({ xTenantId }, queryKey),
    queryFn: () => PatientVitalSettingControllerService.getVitalSettings({ xTenantId }) as TData,
    ...options,
  });
export const usePatientVitalSettingControllerServiceGetPatientVitalSettingSuspense = <
  TData = Common.PatientVitalSettingControllerServiceGetPatientVitalSettingDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientVitalSettingControllerServiceGetPatientVitalSettingKeyFn(
      { patientUuid, xTenantId },
      queryKey
    ),
    queryFn: () => PatientVitalSettingControllerService.getPatientVitalSetting({ patientUuid, xTenantId }) as TData,
    ...options,
  });
export const useTimeLogControllerServiceGetAllPatientTimeLogsSuspense = <
  TData = Common.TimeLogControllerServiceGetAllPatientTimeLogsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    activityName,
    loggedBy,
    loggedEntryType,
    month,
    page,
    patientId,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    activityName?: string;
    loggedBy?: string;
    loggedEntryType?: string;
    month: string;
    page?: number;
    patientId: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseTimeLogControllerServiceGetAllPatientTimeLogsKeyFn(
      { activityName, loggedBy, loggedEntryType, month, page, patientId, size, sort, sortBy, xTenantId },
      queryKey
    ),
    queryFn: () =>
      TimeLogControllerService.getAllPatientTimeLogs({
        activityName,
        loggedBy,
        loggedEntryType,
        month,
        page,
        patientId,
        size,
        sort,
        sortBy,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useTimeLogControllerServiceGetTimeLogByIdSuspense = <
  TData = Common.TimeLogControllerServiceGetTimeLogByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    timeLogId,
    xTenantId,
  }: {
    timeLogId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseTimeLogControllerServiceGetTimeLogByIdKeyFn({ timeLogId, xTenantId }, queryKey),
    queryFn: () => TimeLogControllerService.getTimeLogById({ timeLogId, xTenantId }) as TData,
    ...options,
  });
export const useTaskControllerServiceGetAllTasksSuspense = <
  TData = Common.TaskControllerServiceGetAllTasksDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    active,
    archive,
    assignedBy,
    assignedDate,
    assignedTo,
    currentUserUuid,
    dueDate,
    page,
    patientId,
    priority,
    searchAssignTo,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    type,
    xTenantId,
  }: {
    active?: boolean;
    archive?: boolean;
    assignedBy?: string;
    assignedDate?: string;
    assignedTo?: string;
    currentUserUuid?: string;
    dueDate?: string;
    page?: number;
    patientId?: string;
    priority?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    searchAssignTo?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: "PENDING" | "COMPLETED" | "DISCARDED";
    type?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseTaskControllerServiceGetAllTasksKeyFn(
      {
        active,
        archive,
        assignedBy,
        assignedDate,
        assignedTo,
        currentUserUuid,
        dueDate,
        page,
        patientId,
        priority,
        searchAssignTo,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        type,
        xTenantId,
      },
      queryKey
    ),
    queryFn: () =>
      TaskControllerService.getAllTasks({
        active,
        archive,
        assignedBy,
        assignedDate,
        assignedTo,
        currentUserUuid,
        dueDate,
        page,
        patientId,
        priority,
        searchAssignTo,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useTaskControllerServiceGetTaskByUuidSuspense = <
  TData = Common.TaskControllerServiceGetTaskByUuidDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    taskUuid,
    xTenantId,
  }: {
    taskUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseTaskControllerServiceGetTaskByUuidKeyFn({ taskUuid, xTenantId }, queryKey),
    queryFn: () => TaskControllerService.getTaskByUuid({ taskUuid, xTenantId }) as TData,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceGetAllRolesSuspense = <
  TData = Common.RolesAndPrivilegesControllerServiceGetAllRolesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseRolesAndPrivilegesControllerServiceGetAllRolesKeyFn({ xTenantId }, queryKey),
    queryFn: () => RolesAndPrivilegesControllerService.getAllRoles({ xTenantId }) as TData,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceGetAllPrivilegesSuspense = <
  TData = Common.RolesAndPrivilegesControllerServiceGetAllPrivilegesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseRolesAndPrivilegesControllerServiceGetAllPrivilegesKeyFn({ xTenantId }, queryKey),
    queryFn: () => RolesAndPrivilegesControllerService.getAllPrivileges({ xTenantId }) as TData,
    ...options,
  });
export const useRolesAndPrivilegesControllerServiceGetAllRolesPermissionsSuspense = <
  TData = Common.RolesAndPrivilegesControllerServiceGetAllRolesPermissionsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    realm,
    xTenantId,
  }: {
    realm: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseRolesAndPrivilegesControllerServiceGetAllRolesPermissionsKeyFn({ realm, xTenantId }, queryKey),
    queryFn: () => RolesAndPrivilegesControllerService.getAllRolesPermissions({ realm, xTenantId }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetAllProvidersSuspense = <
  TData = Common.ProviderControllerServiceGetAllProvidersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    role,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    role?:
      | "PROVIDER"
      | "PATIENT"
      | "SUPER_ADMIN"
      | "ADMIN"
      | "FRONTDESK"
      | "BILLER"
      | "SITE_ADMIN"
      | "PROVIDER_GROUP_ADMIN"
      | "NURSE"
      | "ANONYMOUS";
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetAllProvidersKeyFn(
      { archive, page, role, searchString, size, sortBy, sortDirection, state, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ProviderControllerService.getAllProviders({
        archive,
        page,
        role,
        searchString,
        size,
        sortBy,
        sortDirection,
        state,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetProviderByIdSuspense = <
  TData = Common.ProviderControllerServiceGetProviderByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerUuid,
    xTenantId,
  }: {
    providerUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetProviderByIdKeyFn({ providerUuid, xTenantId }, queryKey),
    queryFn: () => ProviderControllerService.getProviderById({ providerUuid, xTenantId }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetNurseReportDashboardSuspense = <
  TData = Common.ProviderControllerServiceGetNurseReportDashboardDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    page,
    providerId,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month: number;
    page?: number;
    providerId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetNurseReportDashboardKeyFn(
      { month, page, providerId, size, sortBy, sortDirection, xTenantId, year },
      queryKey
    ),
    queryFn: () =>
      ProviderControllerService.getNurseReportDashboard({
        month,
        page,
        providerId,
        size,
        sortBy,
        sortDirection,
        xTenantId,
        year,
      }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetPatientDashboardSuspense = <
  TData = Common.ProviderControllerServiceGetPatientDashboardDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    page,
    providerId,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month: number;
    page?: number;
    providerId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetPatientDashboardKeyFn(
      { month, page, providerId, size, sortBy, sortDirection, xTenantId, year },
      queryKey
    ),
    queryFn: () =>
      ProviderControllerService.getPatientDashboard({
        month,
        page,
        providerId,
        size,
        sortBy,
        sortDirection,
        xTenantId,
        year,
      }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetUserIdByProviderSuspense = <
  TData = Common.ProviderControllerServiceGetUserIdByProviderDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    userId,
    xTenantId,
  }: {
    userId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetUserIdByProviderKeyFn({ userId, xTenantId }, queryKey),
    queryFn: () => ProviderControllerService.getUserIdByProvider({ userId, xTenantId }) as TData,
    ...options,
  });
export const useProviderControllerServiceGetProfileSuspense = <
  TData = Common.ProviderControllerServiceGetProfileDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseProviderControllerServiceGetProfileKeyFn({ xTenantId }, queryKey),
    queryFn: () => ProviderControllerService.getProfile({ xTenantId }) as TData,
    ...options,
  });
export const useProviderGroupControllerServiceGetAllProviderGroupsSuspense = <
  TData = Common.ProviderGroupControllerServiceGetAllProviderGroupsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseProviderGroupControllerServiceGetAllProviderGroupsKeyFn(
      { archive, page, searchString, size, sortBy, sortDirection, state, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ProviderGroupControllerService.getAllProviderGroups({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        state,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useProviderGroupControllerServiceGetProviderGroupByIdSuspense = <
  TData = Common.ProviderGroupControllerServiceGetProviderGroupByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerGroupId,
    xTenantId,
  }: {
    providerGroupId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseProviderGroupControllerServiceGetProviderGroupByIdKeyFn(
      { providerGroupId, xTenantId },
      queryKey
    ),
    queryFn: () => ProviderGroupControllerService.getProviderGroupById({ providerGroupId, xTenantId }) as TData,
    ...options,
  });
export const useProviderGroupControllerServiceGetProviderGroupBySchemaSuspense = <
  TData = Common.ProviderGroupControllerServiceGetProviderGroupBySchemaDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseProviderGroupControllerServiceGetProviderGroupBySchemaKeyFn({ xTenantId }, queryKey),
    queryFn: () => ProviderGroupControllerService.getProviderGroupBySchema({ xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetAllPatientSuspense = <
  TData = Common.PatientControllerServiceGetAllPatientDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    genderFilter,
    mrn,
    name,
    nurseId,
    page,
    providerId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    genderFilter?: "MALE" | "FEMALE" | "OTHER" | "BOTH";
    mrn?: string;
    name?: string;
    nurseId?: string;
    page?: number;
    providerId?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetAllPatientKeyFn(
      {
        archive,
        genderFilter,
        mrn,
        name,
        nurseId,
        page,
        providerId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      },
      queryKey
    ),
    queryFn: () =>
      PatientControllerService.getAllPatient({
        archive,
        genderFilter,
        mrn,
        name,
        nurseId,
        page,
        providerId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetPatientByIdSuspense = <
  TData = Common.PatientControllerServiceGetPatientByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetPatientByIdKeyFn({ patientUuid, xTenantId }, queryKey),
    queryFn: () => PatientControllerService.getPatientById({ patientUuid, xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetPatientStatisticSuspense = <
  TData = Common.PatientControllerServiceGetPatientStatisticDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetPatientStatisticKeyFn({ patientId, xTenantId }, queryKey),
    queryFn: () => PatientControllerService.getPatientStatistic({ patientId, xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetPatientRecordSuspense = <
  TData = Common.PatientControllerServiceGetPatientRecordDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetPatientRecordKeyFn({ patientId, xTenantId }, queryKey),
    queryFn: () => PatientControllerService.getPatientRecord({ patientId, xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetProfile2Suspense = <
  TData = Common.PatientControllerServiceGetProfile2DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetProfile2KeyFn({ xTenantId }, queryKey),
    queryFn: () => PatientControllerService.getProfile2({ xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceDownloadTemplateSuspense = <
  TData = Common.PatientControllerServiceDownloadTemplateDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceDownloadTemplateKeyFn({ xTenantId }, queryKey),
    queryFn: () => PatientControllerService.downloadTemplate({ xTenantId }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetPatientListSuspense = <
  TData = Common.PatientControllerServiceGetPatientListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    nurseId,
    page,
    providerId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    nurseId?: string;
    page?: number;
    providerId?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetPatientListKeyFn(
      { archive, nurseId, page, providerId, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientControllerService.getPatientList({
        archive,
        nurseId,
        page,
        providerId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientControllerServiceGetAssignedDevicesSuspense = <
  TData = Common.PatientControllerServiceGetAssignedDevicesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    page,
    patientId,
    size,
    sortBy,
    sortDirection,
    type,
    xTenantId,
  }: {
    page?: number;
    patientId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    type?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientControllerServiceGetAssignedDevicesKeyFn(
      { page, patientId, size, sortBy, sortDirection, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientControllerService.getAssignedDevices({
        page,
        patientId,
        size,
        sortBy,
        sortDirection,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientVitalControllerServiceGetPatientVitals1Suspense = <
  TData = Common.PatientVitalControllerServiceGetPatientVitals1DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    endDate,
    page,
    patientUuid,
    size,
    sort,
    sortBy,
    startDate,
    timeFilter,
    vitalName,
    xTenantId,
  }: {
    endDate?: string;
    page?: number;
    patientUuid: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    startDate?: string;
    timeFilter?: "LAST_MONTH" | "LAST_WEEK" | "PAST_24_HOURS" | "DATE_RANGE";
    vitalName?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientVitalControllerServiceGetPatientVitals1KeyFn(
      { endDate, page, patientUuid, size, sort, sortBy, startDate, timeFilter, vitalName, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientVitalControllerService.getPatientVitals1({
        endDate,
        page,
        patientUuid,
        size,
        sort,
        sortBy,
        startDate,
        timeFilter,
        vitalName,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientVitalControllerServiceGetPatientVitalByIdSuspense = <
  TData = Common.PatientVitalControllerServiceGetPatientVitalByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientVitalId,
    xTenantId,
  }: {
    patientVitalId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientVitalControllerServiceGetPatientVitalByIdKeyFn({ patientVitalId, xTenantId }, queryKey),
    queryFn: () => PatientVitalControllerService.getPatientVitalById({ patientVitalId, xTenantId }) as TData,
    ...options,
  });
export const usePatientVitalControllerServiceGetPatientLatestVitalsSuspense = <
  TData = Common.PatientVitalControllerServiceGetPatientLatestVitalsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientVitalControllerServiceGetPatientLatestVitalsKeyFn({ patientUuid, xTenantId }, queryKey),
    queryFn: () => PatientVitalControllerService.getPatientLatestVitals({ patientUuid, xTenantId }) as TData,
    ...options,
  });
export const usePatientVitalControllerServiceGetEcgValueSuspense = <
  TData = Common.PatientVitalControllerServiceGetEcgValueDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    ecgId,
    xTenantId,
  }: {
    ecgId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientVitalControllerServiceGetEcgValueKeyFn({ ecgId, xTenantId }, queryKey),
    queryFn: () => PatientVitalControllerService.getEcgValue({ ecgId, xTenantId }) as TData,
    ...options,
  });
export const usePatientMedicationControllerServiceGetPatientMedicationSuspense = <
  TData = Common.PatientMedicationControllerServiceGetPatientMedicationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sort,
    sortBy,
    status,
    timeFilter,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    status?: boolean;
    timeFilter?: "CURRENT" | "PAST";
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientMedicationControllerServiceGetPatientMedicationKeyFn(
      { archive, page, patientUuid, searchString, size, sort, sortBy, status, timeFilter, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientMedicationControllerService.getPatientMedication({
        archive,
        page,
        patientUuid,
        searchString,
        size,
        sort,
        sortBy,
        status,
        timeFilter,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientMedicationControllerServiceGetPatientMedicationByIdSuspense = <
  TData = Common.PatientMedicationControllerServiceGetPatientMedicationByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientMedicationId,
    xTenantId,
  }: {
    patientMedicationId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientMedicationControllerServiceGetPatientMedicationByIdKeyFn(
      { patientMedicationId, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientMedicationControllerService.getPatientMedicationById({ patientMedicationId, xTenantId }) as TData,
    ...options,
  });
export const usePatientMedicationControllerServiceSendPatientNotificationSuspense = <
  TData = Common.PatientMedicationControllerServiceSendPatientNotificationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientMedicationControllerServiceSendPatientNotificationKeyFn({ xTenantId }, queryKey),
    queryFn: () => PatientMedicationControllerService.sendPatientNotification({ xTenantId }) as TData,
    ...options,
  });
export const usePatientDiagnosisControllerServiceGetPatientDiagnosisSuspense = <
  TData = Common.PatientDiagnosisControllerServiceGetPatientDiagnosisDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientDiagnosisControllerServiceGetPatientDiagnosisKeyFn(
      { archive, page, patientUuid, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientDiagnosisControllerService.getPatientDiagnosis({
        archive,
        page,
        patientUuid,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientDiagnosisControllerServiceGetPatientDiagnosisByIdSuspense = <
  TData = Common.PatientDiagnosisControllerServiceGetPatientDiagnosisByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientDiagnosisId,
    xTenantId,
  }: {
    patientDiagnosisId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientDiagnosisControllerServiceGetPatientDiagnosisByIdKeyFn(
      { patientDiagnosisId, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientDiagnosisControllerService.getPatientDiagnosisById({ patientDiagnosisId, xTenantId }) as TData,
    ...options,
  });
export const useConsentFormControllerServiceGetAllConsentFormTemplateSuspense = <
  TData = Common.ConsentFormControllerServiceGetAllConsentFormTemplateDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseConsentFormControllerServiceGetAllConsentFormTemplateKeyFn(
      { archive, page, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ConsentFormControllerService.getAllConsentFormTemplate({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useConsentFormControllerServiceGetAllPatientConsentFormSuspense = <
  TData = Common.ConsentFormControllerServiceGetAllPatientConsentFormDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    page,
    patientUuid,
    searchString,
    size,
    sortBy,
    sortDirection,
    xTenantId,
  }: {
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseConsentFormControllerServiceGetAllPatientConsentFormKeyFn(
      { page, patientUuid, searchString, size, sortBy, sortDirection, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ConsentFormControllerService.getAllPatientConsentForm({
        page,
        patientUuid,
        searchString,
        size,
        sortBy,
        sortDirection,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useConsentFormControllerServiceGetPatientConsentFormByIdSuspense = <
  TData = Common.ConsentFormControllerServiceGetPatientConsentFormByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientConsentFormUuid,
    xTenantId,
  }: {
    patientConsentFormUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseConsentFormControllerServiceGetPatientConsentFormByIdKeyFn(
      { patientConsentFormUuid, xTenantId },
      queryKey
    ),
    queryFn: () =>
      ConsentFormControllerService.getPatientConsentFormById({ patientConsentFormUuid, xTenantId }) as TData,
    ...options,
  });
export const useConsentFormControllerServiceGetConsentFormIdSuspense = <
  TData = Common.ConsentFormControllerServiceGetConsentFormIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    consentFormId,
    xTenantId,
  }: {
    consentFormId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseConsentFormControllerServiceGetConsentFormIdKeyFn({ consentFormId, xTenantId }, queryKey),
    queryFn: () => ConsentFormControllerService.getConsentFormId({ consentFormId, xTenantId }) as TData,
    ...options,
  });
export const usePatientCarePlanControllerServiceGetAllCarePlansSuspense = <
  TData = Common.PatientCarePlanControllerServiceGetAllCarePlansDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    carePlanStatus,
    page,
    patientId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    timeFilter,
    xTenantId,
  }: {
    archive?: boolean;
    carePlanStatus?: string;
    page?: number;
    patientId: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    timeFilter?: "CURRENT" | "PAST";
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientCarePlanControllerServiceGetAllCarePlansKeyFn(
      {
        archive,
        carePlanStatus,
        page,
        patientId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        timeFilter,
        xTenantId,
      },
      queryKey
    ),
    queryFn: () =>
      PatientCarePlanControllerService.getAllCarePlans({
        archive,
        carePlanStatus,
        page,
        patientId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        timeFilter,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientCarePlanControllerServiceGetPatientCarePlanByIdSuspense = <
  TData = Common.PatientCarePlanControllerServiceGetPatientCarePlanByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientCarePlanId,
    xTenantId,
  }: {
    patientCarePlanId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientCarePlanControllerServiceGetPatientCarePlanByIdKeyFn(
      { patientCarePlanId, xTenantId },
      queryKey
    ),
    queryFn: () => PatientCarePlanControllerService.getPatientCarePlanById({ patientCarePlanId, xTenantId }) as TData,
    ...options,
  });
export const usePatientCarePlanControllerServiceGetProgramGoalTrackDetailsSuspense = <
  TData = Common.PatientCarePlanControllerServiceGetProgramGoalTrackDetailsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    programGoalId,
    xTenantId,
  }: {
    programGoalId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientCarePlanControllerServiceGetProgramGoalTrackDetailsKeyFn(
      { programGoalId, xTenantId },
      queryKey
    ),
    queryFn: () => PatientCarePlanControllerService.getProgramGoalTrackDetails({ programGoalId, xTenantId }) as TData,
    ...options,
  });
export const usePatientCarePlanControllerServiceGetPatientActiveCarePlanSuspense = <
  TData = Common.PatientCarePlanControllerServiceGetPatientActiveCarePlanDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientCarePlanControllerServiceGetPatientActiveCarePlanKeyFn(
      { patientId, xTenantId },
      queryKey
    ),
    queryFn: () => PatientCarePlanControllerService.getPatientActiveCarePlan({ patientId, xTenantId }) as TData,
    ...options,
  });
export const usePatientAllergyControllerServiceGetPatientAllergySuspense = <
  TData = Common.PatientAllergyControllerServiceGetPatientAllergyDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sort,
    sortBy,
    status,
    type,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    status?: boolean;
    type?: "OTHER" | "DRUG" | "FOOD" | "ENVIRONMENT";
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientAllergyControllerServiceGetPatientAllergyKeyFn(
      { archive, page, patientUuid, searchString, size, sort, sortBy, status, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      PatientAllergyControllerService.getPatientAllergy({
        archive,
        page,
        patientUuid,
        searchString,
        size,
        sort,
        sortBy,
        status,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const usePatientAllergyControllerServiceGetPatientAllergyByIdSuspense = <
  TData = Common.PatientAllergyControllerServiceGetPatientAllergyByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientAllergyId,
    xTenantId,
  }: {
    patientAllergyId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UsePatientAllergyControllerServiceGetPatientAllergyByIdKeyFn(
      { patientAllergyId, xTenantId },
      queryKey
    ),
    queryFn: () => PatientAllergyControllerService.getPatientAllergyById({ patientAllergyId, xTenantId }) as TData,
    ...options,
  });
export const useMedicalCodeControllerServiceGetMedicalCodesSuspense = <
  TData = Common.MedicalCodeControllerServiceGetMedicalCodesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    active,
    archive,
    page,
    searchString,
    size,
    sort,
    sortBy,
    type,
    xTenantId,
  }: {
    active?: boolean;
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    type?: "ICD10" | "CPT" | "ALL";
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseMedicalCodeControllerServiceGetMedicalCodesKeyFn(
      { active, archive, page, searchString, size, sort, sortBy, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      MedicalCodeControllerService.getMedicalCodes({
        active,
        archive,
        page,
        searchString,
        size,
        sort,
        sortBy,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useMedicalCodeControllerServiceGetMedicalCodeByIdSuspense = <
  TData = Common.MedicalCodeControllerServiceGetMedicalCodeByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    medicalCodeId,
    xTenantId,
  }: {
    medicalCodeId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseMedicalCodeControllerServiceGetMedicalCodeByIdKeyFn({ medicalCodeId, xTenantId }, queryKey),
    queryFn: () => MedicalCodeControllerService.getMedicalCodeById({ medicalCodeId, xTenantId }) as TData,
    ...options,
  });
export const useLocationControllerServiceGetAllLocationsSuspense = <
  TData = Common.LocationControllerServiceGetAllLocationsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseLocationControllerServiceGetAllLocationsKeyFn(
      { archive, page, searchString, size, sortBy, sortDirection, state, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      LocationControllerService.getAllLocations({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        state,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useLocationControllerServiceGetLocationByIdSuspense = <
  TData = Common.LocationControllerServiceGetLocationByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    locationId,
    xTenantId,
  }: {
    locationId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseLocationControllerServiceGetLocationByIdKeyFn({ locationId, xTenantId }, queryKey),
    queryFn: () => LocationControllerService.getLocationById({ locationId, xTenantId }) as TData,
    ...options,
  });
export const useDeviceControllerServiceGetAllDevicesSuspense = <
  TData = Common.DeviceControllerServiceGetAllDevicesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    category,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    type,
    xTenantId,
  }: {
    archive?: boolean;
    category?: string;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    type?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseDeviceControllerServiceGetAllDevicesKeyFn(
      { archive, category, page, searchString, size, sortBy, sortDirection, status, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      DeviceControllerService.getAllDevices({
        archive,
        category,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useDeviceControllerServiceGetDeviceByIdSuspense = <
  TData = Common.DeviceControllerServiceGetDeviceByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    deviceUuid,
    xTenantId,
  }: {
    deviceUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseDeviceControllerServiceGetDeviceByIdKeyFn({ deviceUuid, xTenantId }, queryKey),
    queryFn: () => DeviceControllerService.getDeviceById({ deviceUuid, xTenantId }) as TData,
    ...options,
  });
export const useClinicalNoteControllerServiceGetClinicalNoteByUuidSuspense = <
  TData = Common.ClinicalNoteControllerServiceGetClinicalNoteByUuidDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    clinicalNoteUuid,
    xTenantId,
  }: {
    clinicalNoteUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseClinicalNoteControllerServiceGetClinicalNoteByUuidKeyFn(
      { clinicalNoteUuid, xTenantId },
      queryKey
    ),
    queryFn: () => ClinicalNoteControllerService.getClinicalNoteByUuid({ clinicalNoteUuid, xTenantId }) as TData,
    ...options,
  });
export const useClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdSuspense = <
  TData = Common.ClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    appointmentId,
    xTenantId,
  }: {
    appointmentId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdKeyFn(
      { appointmentId, xTenantId },
      queryKey
    ),
    queryFn: () => ClinicalNoteControllerService.getClinicalNoteByAppointmentId({ appointmentId, xTenantId }) as TData,
    ...options,
  });
export const useCarePlanControllerServiceGetAllCarePlans1Suspense = <
  TData = Common.CarePlanControllerServiceGetAllCarePlans1DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseCarePlanControllerServiceGetAllCarePlans1KeyFn(
      { archive, page, searchString, size, sortBy, sortDirection, status, xTenantId },
      queryKey
    ),
    queryFn: () =>
      CarePlanControllerService.getAllCarePlans1({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useCarePlanControllerServiceGetCarePlanByIdSuspense = <
  TData = Common.CarePlanControllerServiceGetCarePlanByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    carePlanId,
    globalCarePlan,
    xTenantId,
  }: {
    carePlanId: string;
    globalCarePlan?: boolean;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseCarePlanControllerServiceGetCarePlanByIdKeyFn(
      { carePlanId, globalCarePlan, xTenantId },
      queryKey
    ),
    queryFn: () => CarePlanControllerService.getCarePlanById({ carePlanId, globalCarePlan, xTenantId }) as TData,
    ...options,
  });
export const useCarePlanControllerServiceGetAllReferenceRangesSuspense = <
  TData = Common.CarePlanControllerServiceGetAllReferenceRangesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseCarePlanControllerServiceGetAllReferenceRangesKeyFn({ xTenantId }, queryKey),
    queryFn: () => CarePlanControllerService.getAllReferenceRanges({ xTenantId }) as TData,
    ...options,
  });
export const useCarePlanControllerServiceGetAllProtocolsSuspense = <
  TData = Common.CarePlanControllerServiceGetAllProtocolsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    protocolType,
    xTenantId,
  }: {
    protocolType: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseCarePlanControllerServiceGetAllProtocolsKeyFn({ protocolType, xTenantId }, queryKey),
    queryFn: () => CarePlanControllerService.getAllProtocols({ protocolType, xTenantId }) as TData,
    ...options,
  });
export const useAppointmentControllerServiceGetAllAppointmentsSuspense = <
  TData = Common.AppointmentControllerServiceGetAllAppointmentsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    assigned,
    endDate,
    filter,
    mode,
    nurseId,
    page,
    patientId,
    providerId,
    size,
    sortBy,
    sortDirection,
    startDate,
    status,
    xTenantId,
  }: {
    assigned?: boolean;
    endDate?: string;
    filter?: "PAST" | "ALL" | "UPCOMING" | "REQUESTED";
    mode?: string;
    nurseId?: string;
    page?: number;
    patientId?: string;
    providerId?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    startDate?: string;
    status?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAppointmentControllerServiceGetAllAppointmentsKeyFn(
      {
        assigned,
        endDate,
        filter,
        mode,
        nurseId,
        page,
        patientId,
        providerId,
        size,
        sortBy,
        sortDirection,
        startDate,
        status,
        xTenantId,
      },
      queryKey
    ),
    queryFn: () =>
      AppointmentControllerService.getAllAppointments({
        assigned,
        endDate,
        filter,
        mode,
        nurseId,
        page,
        patientId,
        providerId,
        size,
        sortBy,
        sortDirection,
        startDate,
        status,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useAppointmentControllerServiceGetAppointmentByIdSuspense = <
  TData = Common.AppointmentControllerServiceGetAppointmentByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    appointmentId,
    xTenantId,
  }: {
    appointmentId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAppointmentControllerServiceGetAppointmentByIdKeyFn({ appointmentId, xTenantId }, queryKey),
    queryFn: () => AppointmentControllerService.getAppointmentById({ appointmentId, xTenantId }) as TData,
    ...options,
  });
export const useAppointmentControllerServiceEscalateAppointmentSuspense = <
  TData = Common.AppointmentControllerServiceEscalateAppointmentDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    email,
    schema,
    xTenantId,
  }: {
    email: string;
    schema?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAppointmentControllerServiceEscalateAppointmentKeyFn({ email, schema, xTenantId }, queryKey),
    queryFn: () => AppointmentControllerService.escalateAppointment({ email, schema, xTenantId }) as TData,
    ...options,
  });
export const useAppointmentControllerServiceGetAppointmentListSuspense = <
  TData = Common.AppointmentControllerServiceGetAppointmentListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    endDate,
    filter,
    nurseId,
    patientUuid,
    providerId,
    startDate,
    status,
    type,
    xTenantId,
  }: {
    endDate: string;
    filter?: "PAST" | "ALL" | "UPCOMING" | "REQUESTED";
    nurseId?: string;
    patientUuid?: string;
    providerId?: string;
    startDate: string;
    status?: string;
    type?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAppointmentControllerServiceGetAppointmentListKeyFn(
      { endDate, filter, nurseId, patientUuid, providerId, startDate, status, type, xTenantId },
      queryKey
    ),
    queryFn: () =>
      AppointmentControllerService.getAppointmentList({
        endDate,
        filter,
        nurseId,
        patientUuid,
        providerId,
        startDate,
        status,
        type,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useAvailabilityControllerServiceGetProviderSlotsSuspense = <
  TData = Common.AvailabilityControllerServiceGetProviderSlotsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    duration,
    endDate,
    page,
    providerUuid,
    size,
    startDate,
    xTenantId,
  }: {
    duration: number;
    endDate?: string;
    page?: number;
    providerUuid: string;
    size?: number;
    startDate?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAvailabilityControllerServiceGetProviderSlotsKeyFn(
      { duration, endDate, page, providerUuid, size, startDate, xTenantId },
      queryKey
    ),
    queryFn: () =>
      AvailabilityControllerService.getProviderSlots({
        duration,
        endDate,
        page,
        providerUuid,
        size,
        startDate,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useAvailabilityControllerServiceGetProviderAvailabilitySettingSuspense = <
  TData = Common.AvailabilityControllerServiceGetProviderAvailabilitySettingDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerUuid,
    xTenantId,
  }: {
    providerUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAvailabilityControllerServiceGetProviderAvailabilitySettingKeyFn(
      { providerUuid, xTenantId },
      queryKey
    ),
    queryFn: () => AvailabilityControllerService.getProviderAvailabilitySetting({ providerUuid, xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceGetPatientReportsSuspense = <
  TData = Common.AiControllerServiceGetPatientReportsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    page,
    patientUuid,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month?: number;
    page?: number;
    patientUuid: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year?: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetPatientReportsKeyFn(
      { month, page, patientUuid, size, sortBy, sortDirection, xTenantId, year },
      queryKey
    ),
    queryFn: () =>
      AiControllerService.getPatientReports({
        month,
        page,
        patientUuid,
        size,
        sortBy,
        sortDirection,
        xTenantId,
        year,
      }) as TData,
    ...options,
  });
export const useAiControllerServiceLlmauGetVoiceRecommendationSuspense = <
  TData = Common.AiControllerServiceLlmauGetVoiceRecommendationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceLlmauGetVoiceRecommendationKeyFn({ xTenantId }, queryKey),
    queryFn: () => AiControllerService.llmauGetVoiceRecommendation({ xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceGetNurseReportsSuspense = <
  TData = Common.AiControllerServiceGetNurseReportsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    reportUuid,
    xTenantId,
  }: {
    reportUuid: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetNurseReportsKeyFn({ reportUuid, xTenantId }, queryKey),
    queryFn: () => AiControllerService.getNurseReports({ reportUuid, xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceLlmauGetMyNurseAvatarSuspense = <
  TData = Common.AiControllerServiceLlmauGetMyNurseAvatarDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerId,
    xTenantId,
  }: {
    providerId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceLlmauGetMyNurseAvatarKeyFn({ providerId, xTenantId }, queryKey),
    queryFn: () => AiControllerService.llmauGetMyNurseAvatar({ providerId, xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceGetNurseActionStatisticsSuspense = <
  TData = Common.AiControllerServiceGetNurseActionStatisticsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetNurseActionStatisticsKeyFn(
      { month, patientUuid, xTenantId, year },
      queryKey
    ),
    queryFn: () => AiControllerService.getNurseActionStatistics({ month, patientUuid, xTenantId, year }) as TData,
    ...options,
  });
export const useAiControllerServiceLlmauGetAvatarSuspense = <
  TData = Common.AiControllerServiceLlmauGetAvatarDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceLlmauGetAvatarKeyFn({ xTenantId }, queryKey),
    queryFn: () => AiControllerService.llmauGetAvatar({ xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceGetNurseActionInExcelSuspense = <
  TData = Common.AiControllerServiceGetNurseActionInExcelDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetNurseActionInExcelKeyFn(
      { month, patientUuid, xTenantId, year },
      queryKey
    ),
    queryFn: () => AiControllerService.getNurseActionInExcel({ month, patientUuid, xTenantId, year }) as TData,
    ...options,
  });
export const useAiControllerServiceGetChatbotReportsSuspense = <
  TData = Common.AiControllerServiceGetChatbotReportsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetChatbotReportsKeyFn({ month, patientUuid, xTenantId, year }, queryKey),
    queryFn: () => AiControllerService.getChatbotReports({ month, patientUuid, xTenantId, year }) as TData,
    ...options,
  });
export const useAiControllerServiceGetChatbotHistorySuspense = <
  TData = Common.AiControllerServiceGetChatbotHistoryDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceGetChatbotHistoryKeyFn({ xTenantId }, queryKey),
    queryFn: () => AiControllerService.getChatbotHistory({ xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceLlmauGetAvatarVideoByTitleSuspense = <
  TData = Common.AiControllerServiceLlmauGetAvatarVideoByTitleDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerId,
    videoTitle,
    xTenantId,
  }: {
    providerId: string;
    videoTitle: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceLlmauGetAvatarVideoByTitleKeyFn(
      { providerId, videoTitle, xTenantId },
      queryKey
    ),
    queryFn: () => AiControllerService.llmauGetAvatarVideoByTitle({ providerId, videoTitle, xTenantId }) as TData,
    ...options,
  });
export const useAiControllerServiceAvqGetAlertAudioSuspense = <
  TData = Common.AiControllerServiceAvqGetAlertAudioDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    providerId,
    videoTitle,
    xTenantId,
  }: {
    providerId: string;
    videoTitle: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseAiControllerServiceAvqGetAlertAudioKeyFn({ providerId, videoTitle, xTenantId }, queryKey),
    queryFn: () => AiControllerService.avqGetAlertAudio({ providerId, videoTitle, xTenantId }) as TData,
    ...options,
  });
export const useVitalControllerServiceGetPatientVitalsSuspense = <
  TData = Common.VitalControllerServiceGetPatientVitalsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    page,
    searchString,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    page?: number;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseVitalControllerServiceGetPatientVitalsKeyFn(
      { page, searchString, size, sort, sortBy, xTenantId },
      queryKey
    ),
    queryFn: () =>
      VitalControllerService.getPatientVitals({ page, searchString, size, sort, sortBy, xTenantId }) as TData,
    ...options,
  });
export const useZoomControllerServiceGetAuthTokenSuspense = <
  TData = Common.ZoomControllerServiceGetAuthTokenDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    roomId,
    xTenantId,
  }: {
    roomId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseZoomControllerServiceGetAuthTokenKeyFn({ roomId, xTenantId }, queryKey),
    queryFn: () => ZoomControllerService.getAuthToken({ roomId, xTenantId }) as TData,
    ...options,
  });
export const useZoomControllerServiceSubscribeSuspense = <
  TData = Common.ZoomControllerServiceSubscribeDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    eventKey,
    xTenantId,
  }: {
    eventKey: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseZoomControllerServiceSubscribeKeyFn({ eventKey, xTenantId }, queryKey),
    queryFn: () => ZoomControllerService.subscribe({ eventKey, xTenantId }) as TData,
    ...options,
  });
export const useZoomControllerServiceEmitSuspense = <
  TData = Common.ZoomControllerServiceEmitDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    eventKey,
    xTenantId,
  }: {
    eventKey: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseZoomControllerServiceEmitKeyFn({ eventKey, xTenantId }, queryKey),
    queryFn: () => ZoomControllerService.emit({ eventKey, xTenantId }) as TData,
    ...options,
  });
export const useLicenseStateControllerServiceGetAllLicensedStatesSuspense = <
  TData = Common.LicenseStateControllerServiceGetAllLicensedStatesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    xTenantId,
  }: {
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseLicenseStateControllerServiceGetAllLicensedStatesKeyFn(
      { page, searchString, size, sortBy, sortDirection, xTenantId },
      queryKey
    ),
    queryFn: () =>
      LicenseStateControllerService.getAllLicensedStates({
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        xTenantId,
      }) as TData,
    ...options,
  });
export const useEqrControllerServiceGetInfusionTherapyByIdSuspense = <
  TData = Common.EqrControllerServiceGetInfusionTherapyByIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    therapyId,
    xTenantId,
  }: {
    therapyId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEqrControllerServiceGetInfusionTherapyByIdKeyFn({ therapyId, xTenantId }, queryKey),
    queryFn: () => EqrControllerService.getInfusionTherapyById({ therapyId, xTenantId }) as TData,
    ...options,
  });
export const useEhrProviderControllerServiceGetAllEhrProvidersSuspense = <
  TData = Common.EhrProviderControllerServiceGetAllEhrProvidersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrProviderControllerServiceGetAllEhrProvidersKeyFn({ xTenantId }, queryKey),
    queryFn: () => EhrProviderControllerService.getAllEhrProviders({ xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetPractitionerByProviderIdSuspense = <
  TData = Common.EhrControllerServiceGetPractitionerByProviderIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    practitionerId,
    xTenantId,
  }: {
    practitionerId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetPractitionerByProviderIdKeyFn({ practitionerId, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getPractitionerByProviderId({ practitionerId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceSearchPractitionersSuspense = <
  TData = Common.EhrControllerServiceSearchPractitionersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    name,
    xTenantId,
  }: {
    name: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceSearchPractitionersKeyFn({ name, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.searchPractitioners({ name, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetPatientVitals2Suspense = <
  TData = Common.EhrControllerServiceGetPatientVitals2DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    date,
    patientId,
    xTenantId,
  }: {
    date?: string;
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetPatientVitals2KeyFn({ date, patientId, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getPatientVitals2({ date, patientId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetPatientEncounterDiagnosisByPatientIdSuspense = <
  TData = Common.EhrControllerServiceGetPatientEncounterDiagnosisByPatientIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetPatientEncounterDiagnosisByPatientIdKeyFn(
      { patientId, xTenantId },
      queryKey
    ),
    queryFn: () => EhrControllerService.getPatientEncounterDiagnosisByPatientId({ patientId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetAllergiesByPatientIdSuspense = <
  TData = Common.EhrControllerServiceGetAllergiesByPatientIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    clinicalStatus,
    patientId,
    recordedDate,
    xTenantId,
  }: {
    clinicalStatus?: string;
    patientId: string;
    recordedDate?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetAllergiesByPatientIdKeyFn(
      { clinicalStatus, patientId, recordedDate, xTenantId },
      queryKey
    ),
    queryFn: () =>
      EhrControllerService.getAllergiesByPatientId({ clinicalStatus, patientId, recordedDate, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceSearchPatientsSuspense = <
  TData = Common.EhrControllerServiceSearchPatientsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    birthdate,
    family,
    given,
    organisationId,
    patientId,
    xTenantId,
  }: {
    birthdate?: string;
    family?: string;
    given?: string;
    organisationId: string;
    patientId?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceSearchPatientsKeyFn(
      { birthdate, family, given, organisationId, patientId, xTenantId },
      queryKey
    ),
    queryFn: () =>
      EhrControllerService.searchPatients({ birthdate, family, given, organisationId, patientId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetOrganizationByPracticeIdSuspense = <
  TData = Common.EhrControllerServiceGetOrganizationByPracticeIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    practiceId,
    xTenantId,
  }: {
    practiceId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetOrganizationByPracticeIdKeyFn({ practiceId, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getOrganizationByPracticeId({ practiceId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetMedicationRequestByPatientIdSuspense = <
  TData = Common.EhrControllerServiceGetMedicationRequestByPatientIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    status,
    xTenantId,
  }: {
    patientId: string;
    status?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetMedicationRequestByPatientIdKeyFn(
      { patientId, status, xTenantId },
      queryKey
    ),
    queryFn: () => EhrControllerService.getMedicationRequestByPatientId({ patientId, status, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetMedicationDispenseByPatientIdSuspense = <
  TData = Common.EhrControllerServiceGetMedicationDispenseByPatientIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    patientId,
    status,
    whenhandedover,
    xTenantId,
  }: {
    patientId: string;
    status?: string;
    whenhandedover?: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetMedicationDispenseByPatientIdKeyFn(
      { patientId, status, whenhandedover, xTenantId },
      queryKey
    ),
    queryFn: () =>
      EhrControllerService.getMedicationDispenseByPatientId({ patientId, status, whenhandedover, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetLocationByLocationIdSuspense = <
  TData = Common.EhrControllerServiceGetLocationByLocationIdDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    locationId,
    xTenantId,
  }: {
    locationId: string;
    xTenantId?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetLocationByLocationIdKeyFn({ locationId, xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getLocationByLocationId({ locationId, xTenantId }) as TData,
    ...options,
  });
export const useEhrControllerServiceGetAccessToken1Suspense = <
  TData = Common.EhrControllerServiceGetAccessToken1DefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseEhrControllerServiceGetAccessToken1KeyFn({ xTenantId }, queryKey),
    queryFn: () => EhrControllerService.getAccessToken1({ xTenantId }) as TData,
    ...options,
  });
export const useConditionControllerServiceGetAllConditionsSuspense = <
  TData = Common.ConditionControllerServiceGetAllConditionsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    name,
    page,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    name?: string;
    page?: number;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseConditionControllerServiceGetAllConditionsKeyFn(
      { name, page, size, sort, sortBy, xTenantId },
      queryKey
    ),
    queryFn: () => ConditionControllerService.getAllConditions({ name, page, size, sort, sortBy, xTenantId }) as TData,
    ...options,
  });
export const useActivityControllerServiceGetAllActivitiesSuspense = <
  TData = Common.ActivityControllerServiceGetAllActivitiesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    search,
    xTenantId,
  }: {
    search?: string;
    xTenantId?: string;
  } = {},
  queryKey?: TQueryKey,
  options?: Omit<UseQueryOptions<TData, TError>, "queryKey" | "queryFn">
) =>
  useSuspenseQuery<TData, TError>({
    queryKey: Common.UseActivityControllerServiceGetAllActivitiesKeyFn({ search, xTenantId }, queryKey),
    queryFn: () => ActivityControllerService.getAllActivities({ search, xTenantId }) as TData,
    ...options,
  });
