// generated with @7nohe/openapi-react-query-codegen@1.4.1
import { type QueryClient } from "@tanstack/react-query";

import {
  ActivityControllerService,
  AiControllerService,
  AppointmentControllerService,
  AvailabilityControllerService,
  CarePlanControllerService,
  ClinicalNoteControllerService,
  ConditionControllerService,
  ConsentFormControllerService,
  DeviceControllerService,
  EhrControllerService,
  EhrProviderControllerService,
  EqrControllerService,
  LicenseStateControllerService,
  LocationControllerService,
  MedicalCodeControllerService,
  PatientAllergyControllerService,
  PatientCarePlanControllerService,
  PatientControllerService,
  PatientDiagnosisControllerService,
  PatientMedicationControllerService,
  PatientVitalControllerService,
  PatientVitalSettingControllerService,
  ProviderControllerService,
  ProviderGroupControllerService,
  RolesAndPrivilegesControllerService,
  TaskControllerService,
  TimeLogControllerService,
  UserControllerService,
  VitalControllerService,
  ZoomControllerService,
} from "../requests/services.gen";
import * as Common from "./common";

export const prefetchUseUserControllerServiceGetAllUsers = (
  queryClient: QueryClient,
  {
    archive,
    locationId,
    page,
    role,
    roleType,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    locationId?: string;
    page?: number;
    role?: string;
    roleType?: "PROVIDER" | "STAFF" | "PATIENT";
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseUserControllerServiceGetAllUsersKeyFn({
      archive,
      locationId,
      page,
      role,
      roleType,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      xTenantId,
    }),
    queryFn: () =>
      UserControllerService.getAllUsers({
        archive,
        locationId,
        page,
        role,
        roleType,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }),
  });
export const prefetchUseUserControllerServiceGetUser = (
  queryClient: QueryClient,
  {
    userId,
    xTenantId,
  }: {
    userId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseUserControllerServiceGetUserKeyFn({ userId, xTenantId }),
    queryFn: () => UserControllerService.getUser({ userId, xTenantId }),
  });
export const prefetchUseUserControllerServiceGetProfile1 = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseUserControllerServiceGetProfile1KeyFn({ xTenantId }),
    queryFn: () => UserControllerService.getProfile1({ xTenantId }),
  });
export const prefetchUsePatientVitalSettingControllerServiceGetVitalSettings = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientVitalSettingControllerServiceGetVitalSettingsKeyFn({ xTenantId }),
    queryFn: () => PatientVitalSettingControllerService.getVitalSettings({ xTenantId }),
  });
export const prefetchUsePatientVitalSettingControllerServiceGetPatientVitalSetting = (
  queryClient: QueryClient,
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientVitalSettingControllerServiceGetPatientVitalSettingKeyFn({ patientUuid, xTenantId }),
    queryFn: () => PatientVitalSettingControllerService.getPatientVitalSetting({ patientUuid, xTenantId }),
  });
export const prefetchUseTimeLogControllerServiceGetAllPatientTimeLogs = (
  queryClient: QueryClient,
  {
    activityName,
    loggedBy,
    loggedEntryType,
    month,
    page,
    patientId,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    activityName?: string;
    loggedBy?: string;
    loggedEntryType?: string;
    month: string;
    page?: number;
    patientId: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseTimeLogControllerServiceGetAllPatientTimeLogsKeyFn({
      activityName,
      loggedBy,
      loggedEntryType,
      month,
      page,
      patientId,
      size,
      sort,
      sortBy,
      xTenantId,
    }),
    queryFn: () =>
      TimeLogControllerService.getAllPatientTimeLogs({
        activityName,
        loggedBy,
        loggedEntryType,
        month,
        page,
        patientId,
        size,
        sort,
        sortBy,
        xTenantId,
      }),
  });
export const prefetchUseTimeLogControllerServiceGetTimeLogById = (
  queryClient: QueryClient,
  {
    timeLogId,
    xTenantId,
  }: {
    timeLogId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseTimeLogControllerServiceGetTimeLogByIdKeyFn({ timeLogId, xTenantId }),
    queryFn: () => TimeLogControllerService.getTimeLogById({ timeLogId, xTenantId }),
  });
export const prefetchUseTaskControllerServiceGetAllTasks = (
  queryClient: QueryClient,
  {
    active,
    archive,
    assignedBy,
    assignedDate,
    assignedTo,
    currentUserUuid,
    dueDate,
    page,
    patientId,
    priority,
    searchAssignTo,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    type,
    xTenantId,
  }: {
    active?: boolean;
    archive?: boolean;
    assignedBy?: string;
    assignedDate?: string;
    assignedTo?: string;
    currentUserUuid?: string;
    dueDate?: string;
    page?: number;
    patientId?: string;
    priority?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    searchAssignTo?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: "PENDING" | "COMPLETED" | "DISCARDED";
    type?: string;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseTaskControllerServiceGetAllTasksKeyFn({
      active,
      archive,
      assignedBy,
      assignedDate,
      assignedTo,
      currentUserUuid,
      dueDate,
      page,
      patientId,
      priority,
      searchAssignTo,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      type,
      xTenantId,
    }),
    queryFn: () =>
      TaskControllerService.getAllTasks({
        active,
        archive,
        assignedBy,
        assignedDate,
        assignedTo,
        currentUserUuid,
        dueDate,
        page,
        patientId,
        priority,
        searchAssignTo,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        type,
        xTenantId,
      }),
  });
export const prefetchUseTaskControllerServiceGetTaskByUuid = (
  queryClient: QueryClient,
  {
    taskUuid,
    xTenantId,
  }: {
    taskUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseTaskControllerServiceGetTaskByUuidKeyFn({ taskUuid, xTenantId }),
    queryFn: () => TaskControllerService.getTaskByUuid({ taskUuid, xTenantId }),
  });
export const prefetchUseRolesAndPrivilegesControllerServiceGetAllRoles = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseRolesAndPrivilegesControllerServiceGetAllRolesKeyFn({ xTenantId }),
    queryFn: () => RolesAndPrivilegesControllerService.getAllRoles({ xTenantId }),
  });
export const prefetchUseRolesAndPrivilegesControllerServiceGetAllPrivileges = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseRolesAndPrivilegesControllerServiceGetAllPrivilegesKeyFn({ xTenantId }),
    queryFn: () => RolesAndPrivilegesControllerService.getAllPrivileges({ xTenantId }),
  });
export const prefetchUseRolesAndPrivilegesControllerServiceGetAllRolesPermissions = (
  queryClient: QueryClient,
  {
    realm,
    xTenantId,
  }: {
    realm: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseRolesAndPrivilegesControllerServiceGetAllRolesPermissionsKeyFn({ realm, xTenantId }),
    queryFn: () => RolesAndPrivilegesControllerService.getAllRolesPermissions({ realm, xTenantId }),
  });
export const prefetchUseProviderControllerServiceGetAllProviders = (
  queryClient: QueryClient,
  {
    archive,
    page,
    role,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    role?:
      | "PROVIDER"
      | "PATIENT"
      | "SUPER_ADMIN"
      | "ADMIN"
      | "FRONTDESK"
      | "BILLER"
      | "SITE_ADMIN"
      | "PROVIDER_GROUP_ADMIN"
      | "NURSE"
      | "ANONYMOUS";
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseProviderControllerServiceGetAllProvidersKeyFn({
      archive,
      page,
      role,
      searchString,
      size,
      sortBy,
      sortDirection,
      state,
      status,
      xTenantId,
    }),
    queryFn: () =>
      ProviderControllerService.getAllProviders({
        archive,
        page,
        role,
        searchString,
        size,
        sortBy,
        sortDirection,
        state,
        status,
        xTenantId,
      }),
  });
export const prefetchUseProviderControllerServiceGetProviderById = (
  queryClient: QueryClient,
  {
    providerUuid,
    xTenantId,
  }: {
    providerUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseProviderControllerServiceGetProviderByIdKeyFn({ providerUuid, xTenantId }),
    queryFn: () => ProviderControllerService.getProviderById({ providerUuid, xTenantId }),
  });
export const prefetchUseProviderControllerServiceGetNurseReportDashboard = (
  queryClient: QueryClient,
  {
    month,
    page,
    providerId,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month: number;
    page?: number;
    providerId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year: number;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseProviderControllerServiceGetNurseReportDashboardKeyFn({
      month,
      page,
      providerId,
      size,
      sortBy,
      sortDirection,
      xTenantId,
      year,
    }),
    queryFn: () =>
      ProviderControllerService.getNurseReportDashboard({
        month,
        page,
        providerId,
        size,
        sortBy,
        sortDirection,
        xTenantId,
        year,
      }),
  });
export const prefetchUseProviderControllerServiceGetPatientDashboard = (
  queryClient: QueryClient,
  {
    month,
    page,
    providerId,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month: number;
    page?: number;
    providerId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year: number;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseProviderControllerServiceGetPatientDashboardKeyFn({
      month,
      page,
      providerId,
      size,
      sortBy,
      sortDirection,
      xTenantId,
      year,
    }),
    queryFn: () =>
      ProviderControllerService.getPatientDashboard({
        month,
        page,
        providerId,
        size,
        sortBy,
        sortDirection,
        xTenantId,
        year,
      }),
  });
export const prefetchUseProviderControllerServiceGetUserIdByProvider = (
  queryClient: QueryClient,
  {
    userId,
    xTenantId,
  }: {
    userId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseProviderControllerServiceGetUserIdByProviderKeyFn({ userId, xTenantId }),
    queryFn: () => ProviderControllerService.getUserIdByProvider({ userId, xTenantId }),
  });
export const prefetchUseProviderControllerServiceGetProfile = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseProviderControllerServiceGetProfileKeyFn({ xTenantId }),
    queryFn: () => ProviderControllerService.getProfile({ xTenantId }),
  });
export const prefetchUseProviderGroupControllerServiceGetAllProviderGroups = (
  queryClient: QueryClient,
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseProviderGroupControllerServiceGetAllProviderGroupsKeyFn({
      archive,
      page,
      searchString,
      size,
      sortBy,
      sortDirection,
      state,
      status,
      xTenantId,
    }),
    queryFn: () =>
      ProviderGroupControllerService.getAllProviderGroups({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        state,
        status,
        xTenantId,
      }),
  });
export const prefetchUseProviderGroupControllerServiceGetProviderGroupById = (
  queryClient: QueryClient,
  {
    providerGroupId,
    xTenantId,
  }: {
    providerGroupId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseProviderGroupControllerServiceGetProviderGroupByIdKeyFn({ providerGroupId, xTenantId }),
    queryFn: () => ProviderGroupControllerService.getProviderGroupById({ providerGroupId, xTenantId }),
  });
export const prefetchUseProviderGroupControllerServiceGetProviderGroupBySchema = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseProviderGroupControllerServiceGetProviderGroupBySchemaKeyFn({ xTenantId }),
    queryFn: () => ProviderGroupControllerService.getProviderGroupBySchema({ xTenantId }),
  });
export const prefetchUsePatientControllerServiceGetAllPatient = (
  queryClient: QueryClient,
  {
    archive,
    genderFilter,
    mrn,
    name,
    nurseId,
    page,
    providerId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    genderFilter?: "MALE" | "FEMALE" | "OTHER" | "BOTH";
    mrn?: string;
    name?: string;
    nurseId?: string;
    page?: number;
    providerId?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientControllerServiceGetAllPatientKeyFn({
      archive,
      genderFilter,
      mrn,
      name,
      nurseId,
      page,
      providerId,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      xTenantId,
    }),
    queryFn: () =>
      PatientControllerService.getAllPatient({
        archive,
        genderFilter,
        mrn,
        name,
        nurseId,
        page,
        providerId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }),
  });
export const prefetchUsePatientControllerServiceGetPatientById = (
  queryClient: QueryClient,
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientControllerServiceGetPatientByIdKeyFn({ patientUuid, xTenantId }),
    queryFn: () => PatientControllerService.getPatientById({ patientUuid, xTenantId }),
  });
export const prefetchUsePatientControllerServiceGetPatientStatistic = (
  queryClient: QueryClient,
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientControllerServiceGetPatientStatisticKeyFn({ patientId, xTenantId }),
    queryFn: () => PatientControllerService.getPatientStatistic({ patientId, xTenantId }),
  });
export const prefetchUsePatientControllerServiceGetPatientRecord = (
  queryClient: QueryClient,
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientControllerServiceGetPatientRecordKeyFn({ patientId, xTenantId }),
    queryFn: () => PatientControllerService.getPatientRecord({ patientId, xTenantId }),
  });
export const prefetchUsePatientControllerServiceGetProfile2 = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientControllerServiceGetProfile2KeyFn({ xTenantId }),
    queryFn: () => PatientControllerService.getProfile2({ xTenantId }),
  });
export const prefetchUsePatientControllerServiceDownloadTemplate = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientControllerServiceDownloadTemplateKeyFn({ xTenantId }),
    queryFn: () => PatientControllerService.downloadTemplate({ xTenantId }),
  });
export const prefetchUsePatientControllerServiceGetPatientList = (
  queryClient: QueryClient,
  {
    archive,
    nurseId,
    page,
    providerId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    nurseId?: string;
    page?: number;
    providerId?: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientControllerServiceGetPatientListKeyFn({
      archive,
      nurseId,
      page,
      providerId,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      xTenantId,
    }),
    queryFn: () =>
      PatientControllerService.getPatientList({
        archive,
        nurseId,
        page,
        providerId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }),
  });
export const prefetchUsePatientControllerServiceGetAssignedDevices = (
  queryClient: QueryClient,
  {
    page,
    patientId,
    size,
    sortBy,
    sortDirection,
    type,
    xTenantId,
  }: {
    page?: number;
    patientId: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    type?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientControllerServiceGetAssignedDevicesKeyFn({
      page,
      patientId,
      size,
      sortBy,
      sortDirection,
      type,
      xTenantId,
    }),
    queryFn: () =>
      PatientControllerService.getAssignedDevices({ page, patientId, size, sortBy, sortDirection, type, xTenantId }),
  });
export const prefetchUsePatientVitalControllerServiceGetPatientVitals1 = (
  queryClient: QueryClient,
  {
    endDate,
    page,
    patientUuid,
    size,
    sort,
    sortBy,
    startDate,
    timeFilter,
    vitalName,
    xTenantId,
  }: {
    endDate?: string;
    page?: number;
    patientUuid: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    startDate?: string;
    timeFilter?: "LAST_MONTH" | "LAST_WEEK" | "PAST_24_HOURS" | "DATE_RANGE";
    vitalName?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientVitalControllerServiceGetPatientVitals1KeyFn({
      endDate,
      page,
      patientUuid,
      size,
      sort,
      sortBy,
      startDate,
      timeFilter,
      vitalName,
      xTenantId,
    }),
    queryFn: () =>
      PatientVitalControllerService.getPatientVitals1({
        endDate,
        page,
        patientUuid,
        size,
        sort,
        sortBy,
        startDate,
        timeFilter,
        vitalName,
        xTenantId,
      }),
  });
export const prefetchUsePatientVitalControllerServiceGetPatientVitalById = (
  queryClient: QueryClient,
  {
    patientVitalId,
    xTenantId,
  }: {
    patientVitalId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientVitalControllerServiceGetPatientVitalByIdKeyFn({ patientVitalId, xTenantId }),
    queryFn: () => PatientVitalControllerService.getPatientVitalById({ patientVitalId, xTenantId }),
  });
export const prefetchUsePatientVitalControllerServiceGetPatientLatestVitals = (
  queryClient: QueryClient,
  {
    patientUuid,
    xTenantId,
  }: {
    patientUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientVitalControllerServiceGetPatientLatestVitalsKeyFn({ patientUuid, xTenantId }),
    queryFn: () => PatientVitalControllerService.getPatientLatestVitals({ patientUuid, xTenantId }),
  });
export const prefetchUsePatientVitalControllerServiceGetEcgValue = (
  queryClient: QueryClient,
  {
    ecgId,
    xTenantId,
  }: {
    ecgId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientVitalControllerServiceGetEcgValueKeyFn({ ecgId, xTenantId }),
    queryFn: () => PatientVitalControllerService.getEcgValue({ ecgId, xTenantId }),
  });
export const prefetchUsePatientMedicationControllerServiceGetPatientMedication = (
  queryClient: QueryClient,
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sort,
    sortBy,
    status,
    timeFilter,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    status?: boolean;
    timeFilter?: "CURRENT" | "PAST";
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientMedicationControllerServiceGetPatientMedicationKeyFn({
      archive,
      page,
      patientUuid,
      searchString,
      size,
      sort,
      sortBy,
      status,
      timeFilter,
      xTenantId,
    }),
    queryFn: () =>
      PatientMedicationControllerService.getPatientMedication({
        archive,
        page,
        patientUuid,
        searchString,
        size,
        sort,
        sortBy,
        status,
        timeFilter,
        xTenantId,
      }),
  });
export const prefetchUsePatientMedicationControllerServiceGetPatientMedicationById = (
  queryClient: QueryClient,
  {
    patientMedicationId,
    xTenantId,
  }: {
    patientMedicationId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientMedicationControllerServiceGetPatientMedicationByIdKeyFn({
      patientMedicationId,
      xTenantId,
    }),
    queryFn: () => PatientMedicationControllerService.getPatientMedicationById({ patientMedicationId, xTenantId }),
  });
export const prefetchUsePatientMedicationControllerServiceSendPatientNotification = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientMedicationControllerServiceSendPatientNotificationKeyFn({ xTenantId }),
    queryFn: () => PatientMedicationControllerService.sendPatientNotification({ xTenantId }),
  });
export const prefetchUsePatientDiagnosisControllerServiceGetPatientDiagnosis = (
  queryClient: QueryClient,
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientDiagnosisControllerServiceGetPatientDiagnosisKeyFn({
      archive,
      page,
      patientUuid,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      xTenantId,
    }),
    queryFn: () =>
      PatientDiagnosisControllerService.getPatientDiagnosis({
        archive,
        page,
        patientUuid,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }),
  });
export const prefetchUsePatientDiagnosisControllerServiceGetPatientDiagnosisById = (
  queryClient: QueryClient,
  {
    patientDiagnosisId,
    xTenantId,
  }: {
    patientDiagnosisId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientDiagnosisControllerServiceGetPatientDiagnosisByIdKeyFn({
      patientDiagnosisId,
      xTenantId,
    }),
    queryFn: () => PatientDiagnosisControllerService.getPatientDiagnosisById({ patientDiagnosisId, xTenantId }),
  });
export const prefetchUseConsentFormControllerServiceGetAllConsentFormTemplate = (
  queryClient: QueryClient,
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseConsentFormControllerServiceGetAllConsentFormTemplateKeyFn({
      archive,
      page,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      xTenantId,
    }),
    queryFn: () =>
      ConsentFormControllerService.getAllConsentFormTemplate({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }),
  });
export const prefetchUseConsentFormControllerServiceGetAllPatientConsentForm = (
  queryClient: QueryClient,
  {
    page,
    patientUuid,
    searchString,
    size,
    sortBy,
    sortDirection,
    xTenantId,
  }: {
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseConsentFormControllerServiceGetAllPatientConsentFormKeyFn({
      page,
      patientUuid,
      searchString,
      size,
      sortBy,
      sortDirection,
      xTenantId,
    }),
    queryFn: () =>
      ConsentFormControllerService.getAllPatientConsentForm({
        page,
        patientUuid,
        searchString,
        size,
        sortBy,
        sortDirection,
        xTenantId,
      }),
  });
export const prefetchUseConsentFormControllerServiceGetPatientConsentFormById = (
  queryClient: QueryClient,
  {
    patientConsentFormUuid,
    xTenantId,
  }: {
    patientConsentFormUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseConsentFormControllerServiceGetPatientConsentFormByIdKeyFn({
      patientConsentFormUuid,
      xTenantId,
    }),
    queryFn: () => ConsentFormControllerService.getPatientConsentFormById({ patientConsentFormUuid, xTenantId }),
  });
export const prefetchUseConsentFormControllerServiceGetConsentFormId = (
  queryClient: QueryClient,
  {
    consentFormId,
    xTenantId,
  }: {
    consentFormId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseConsentFormControllerServiceGetConsentFormIdKeyFn({ consentFormId, xTenantId }),
    queryFn: () => ConsentFormControllerService.getConsentFormId({ consentFormId, xTenantId }),
  });
export const prefetchUsePatientCarePlanControllerServiceGetAllCarePlans = (
  queryClient: QueryClient,
  {
    archive,
    carePlanStatus,
    page,
    patientId,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    timeFilter,
    xTenantId,
  }: {
    archive?: boolean;
    carePlanStatus?: string;
    page?: number;
    patientId: string;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    timeFilter?: "CURRENT" | "PAST";
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientCarePlanControllerServiceGetAllCarePlansKeyFn({
      archive,
      carePlanStatus,
      page,
      patientId,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      timeFilter,
      xTenantId,
    }),
    queryFn: () =>
      PatientCarePlanControllerService.getAllCarePlans({
        archive,
        carePlanStatus,
        page,
        patientId,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        timeFilter,
        xTenantId,
      }),
  });
export const prefetchUsePatientCarePlanControllerServiceGetPatientCarePlanById = (
  queryClient: QueryClient,
  {
    patientCarePlanId,
    xTenantId,
  }: {
    patientCarePlanId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientCarePlanControllerServiceGetPatientCarePlanByIdKeyFn({ patientCarePlanId, xTenantId }),
    queryFn: () => PatientCarePlanControllerService.getPatientCarePlanById({ patientCarePlanId, xTenantId }),
  });
export const prefetchUsePatientCarePlanControllerServiceGetProgramGoalTrackDetails = (
  queryClient: QueryClient,
  {
    programGoalId,
    xTenantId,
  }: {
    programGoalId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientCarePlanControllerServiceGetProgramGoalTrackDetailsKeyFn({ programGoalId, xTenantId }),
    queryFn: () => PatientCarePlanControllerService.getProgramGoalTrackDetails({ programGoalId, xTenantId }),
  });
export const prefetchUsePatientCarePlanControllerServiceGetPatientActiveCarePlan = (
  queryClient: QueryClient,
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientCarePlanControllerServiceGetPatientActiveCarePlanKeyFn({ patientId, xTenantId }),
    queryFn: () => PatientCarePlanControllerService.getPatientActiveCarePlan({ patientId, xTenantId }),
  });
export const prefetchUsePatientAllergyControllerServiceGetPatientAllergy = (
  queryClient: QueryClient,
  {
    archive,
    page,
    patientUuid,
    searchString,
    size,
    sort,
    sortBy,
    status,
    type,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    patientUuid: string;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    status?: boolean;
    type?: "OTHER" | "DRUG" | "FOOD" | "ENVIRONMENT";
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientAllergyControllerServiceGetPatientAllergyKeyFn({
      archive,
      page,
      patientUuid,
      searchString,
      size,
      sort,
      sortBy,
      status,
      type,
      xTenantId,
    }),
    queryFn: () =>
      PatientAllergyControllerService.getPatientAllergy({
        archive,
        page,
        patientUuid,
        searchString,
        size,
        sort,
        sortBy,
        status,
        type,
        xTenantId,
      }),
  });
export const prefetchUsePatientAllergyControllerServiceGetPatientAllergyById = (
  queryClient: QueryClient,
  {
    patientAllergyId,
    xTenantId,
  }: {
    patientAllergyId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UsePatientAllergyControllerServiceGetPatientAllergyByIdKeyFn({ patientAllergyId, xTenantId }),
    queryFn: () => PatientAllergyControllerService.getPatientAllergyById({ patientAllergyId, xTenantId }),
  });
export const prefetchUseMedicalCodeControllerServiceGetMedicalCodes = (
  queryClient: QueryClient,
  {
    active,
    archive,
    page,
    searchString,
    size,
    sort,
    sortBy,
    type,
    xTenantId,
  }: {
    active?: boolean;
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    type?: "ICD10" | "CPT" | "ALL";
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseMedicalCodeControllerServiceGetMedicalCodesKeyFn({
      active,
      archive,
      page,
      searchString,
      size,
      sort,
      sortBy,
      type,
      xTenantId,
    }),
    queryFn: () =>
      MedicalCodeControllerService.getMedicalCodes({
        active,
        archive,
        page,
        searchString,
        size,
        sort,
        sortBy,
        type,
        xTenantId,
      }),
  });
export const prefetchUseMedicalCodeControllerServiceGetMedicalCodeById = (
  queryClient: QueryClient,
  {
    medicalCodeId,
    xTenantId,
  }: {
    medicalCodeId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseMedicalCodeControllerServiceGetMedicalCodeByIdKeyFn({ medicalCodeId, xTenantId }),
    queryFn: () => MedicalCodeControllerService.getMedicalCodeById({ medicalCodeId, xTenantId }),
  });
export const prefetchUseLocationControllerServiceGetAllLocations = (
  queryClient: QueryClient,
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    state,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    state?: string;
    status?: boolean;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseLocationControllerServiceGetAllLocationsKeyFn({
      archive,
      page,
      searchString,
      size,
      sortBy,
      sortDirection,
      state,
      status,
      xTenantId,
    }),
    queryFn: () =>
      LocationControllerService.getAllLocations({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        state,
        status,
        xTenantId,
      }),
  });
export const prefetchUseLocationControllerServiceGetLocationById = (
  queryClient: QueryClient,
  {
    locationId,
    xTenantId,
  }: {
    locationId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseLocationControllerServiceGetLocationByIdKeyFn({ locationId, xTenantId }),
    queryFn: () => LocationControllerService.getLocationById({ locationId, xTenantId }),
  });
export const prefetchUseDeviceControllerServiceGetAllDevices = (
  queryClient: QueryClient,
  {
    archive,
    category,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    type,
    xTenantId,
  }: {
    archive?: boolean;
    category?: string;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    type?: string;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseDeviceControllerServiceGetAllDevicesKeyFn({
      archive,
      category,
      page,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      type,
      xTenantId,
    }),
    queryFn: () =>
      DeviceControllerService.getAllDevices({
        archive,
        category,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        type,
        xTenantId,
      }),
  });
export const prefetchUseDeviceControllerServiceGetDeviceById = (
  queryClient: QueryClient,
  {
    deviceUuid,
    xTenantId,
  }: {
    deviceUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseDeviceControllerServiceGetDeviceByIdKeyFn({ deviceUuid, xTenantId }),
    queryFn: () => DeviceControllerService.getDeviceById({ deviceUuid, xTenantId }),
  });
export const prefetchUseClinicalNoteControllerServiceGetClinicalNoteByUuid = (
  queryClient: QueryClient,
  {
    clinicalNoteUuid,
    xTenantId,
  }: {
    clinicalNoteUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseClinicalNoteControllerServiceGetClinicalNoteByUuidKeyFn({ clinicalNoteUuid, xTenantId }),
    queryFn: () => ClinicalNoteControllerService.getClinicalNoteByUuid({ clinicalNoteUuid, xTenantId }),
  });
export const prefetchUseClinicalNoteControllerServiceGetClinicalNoteByAppointmentId = (
  queryClient: QueryClient,
  {
    appointmentId,
    xTenantId,
  }: {
    appointmentId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseClinicalNoteControllerServiceGetClinicalNoteByAppointmentIdKeyFn({ appointmentId, xTenantId }),
    queryFn: () => ClinicalNoteControllerService.getClinicalNoteByAppointmentId({ appointmentId, xTenantId }),
  });
export const prefetchUseCarePlanControllerServiceGetAllCarePlans1 = (
  queryClient: QueryClient,
  {
    archive,
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    status,
    xTenantId,
  }: {
    archive?: boolean;
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    status?: boolean;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseCarePlanControllerServiceGetAllCarePlans1KeyFn({
      archive,
      page,
      searchString,
      size,
      sortBy,
      sortDirection,
      status,
      xTenantId,
    }),
    queryFn: () =>
      CarePlanControllerService.getAllCarePlans1({
        archive,
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        status,
        xTenantId,
      }),
  });
export const prefetchUseCarePlanControllerServiceGetCarePlanById = (
  queryClient: QueryClient,
  {
    carePlanId,
    globalCarePlan,
    xTenantId,
  }: {
    carePlanId: string;
    globalCarePlan?: boolean;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseCarePlanControllerServiceGetCarePlanByIdKeyFn({ carePlanId, globalCarePlan, xTenantId }),
    queryFn: () => CarePlanControllerService.getCarePlanById({ carePlanId, globalCarePlan, xTenantId }),
  });
export const prefetchUseCarePlanControllerServiceGetAllReferenceRanges = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseCarePlanControllerServiceGetAllReferenceRangesKeyFn({ xTenantId }),
    queryFn: () => CarePlanControllerService.getAllReferenceRanges({ xTenantId }),
  });
export const prefetchUseCarePlanControllerServiceGetAllProtocols = (
  queryClient: QueryClient,
  {
    protocolType,
    xTenantId,
  }: {
    protocolType: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseCarePlanControllerServiceGetAllProtocolsKeyFn({ protocolType, xTenantId }),
    queryFn: () => CarePlanControllerService.getAllProtocols({ protocolType, xTenantId }),
  });
export const prefetchUseAppointmentControllerServiceGetAllAppointments = (
  queryClient: QueryClient,
  {
    assigned,
    endDate,
    filter,
    mode,
    nurseId,
    page,
    patientId,
    providerId,
    size,
    sortBy,
    sortDirection,
    startDate,
    status,
    xTenantId,
  }: {
    assigned?: boolean;
    endDate?: string;
    filter?: "PAST" | "ALL" | "UPCOMING" | "REQUESTED";
    mode?: string;
    nurseId?: string;
    page?: number;
    patientId?: string;
    providerId?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    startDate?: string;
    status?: string;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAppointmentControllerServiceGetAllAppointmentsKeyFn({
      assigned,
      endDate,
      filter,
      mode,
      nurseId,
      page,
      patientId,
      providerId,
      size,
      sortBy,
      sortDirection,
      startDate,
      status,
      xTenantId,
    }),
    queryFn: () =>
      AppointmentControllerService.getAllAppointments({
        assigned,
        endDate,
        filter,
        mode,
        nurseId,
        page,
        patientId,
        providerId,
        size,
        sortBy,
        sortDirection,
        startDate,
        status,
        xTenantId,
      }),
  });
export const prefetchUseAppointmentControllerServiceGetAppointmentById = (
  queryClient: QueryClient,
  {
    appointmentId,
    xTenantId,
  }: {
    appointmentId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAppointmentControllerServiceGetAppointmentByIdKeyFn({ appointmentId, xTenantId }),
    queryFn: () => AppointmentControllerService.getAppointmentById({ appointmentId, xTenantId }),
  });
export const prefetchUseAppointmentControllerServiceEscalateAppointment = (
  queryClient: QueryClient,
  {
    email,
    schema,
    xTenantId,
  }: {
    email: string;
    schema?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAppointmentControllerServiceEscalateAppointmentKeyFn({ email, schema, xTenantId }),
    queryFn: () => AppointmentControllerService.escalateAppointment({ email, schema, xTenantId }),
  });
export const prefetchUseAppointmentControllerServiceGetAppointmentList = (
  queryClient: QueryClient,
  {
    endDate,
    filter,
    nurseId,
    patientUuid,
    providerId,
    startDate,
    status,
    type,
    xTenantId,
  }: {
    endDate: string;
    filter?: "PAST" | "ALL" | "UPCOMING" | "REQUESTED";
    nurseId?: string;
    patientUuid?: string;
    providerId?: string;
    startDate: string;
    status?: string;
    type?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAppointmentControllerServiceGetAppointmentListKeyFn({
      endDate,
      filter,
      nurseId,
      patientUuid,
      providerId,
      startDate,
      status,
      type,
      xTenantId,
    }),
    queryFn: () =>
      AppointmentControllerService.getAppointmentList({
        endDate,
        filter,
        nurseId,
        patientUuid,
        providerId,
        startDate,
        status,
        type,
        xTenantId,
      }),
  });
export const prefetchUseAvailabilityControllerServiceGetProviderSlots = (
  queryClient: QueryClient,
  {
    duration,
    endDate,
    page,
    providerUuid,
    size,
    startDate,
    xTenantId,
  }: {
    duration: number;
    endDate?: string;
    page?: number;
    providerUuid: string;
    size?: number;
    startDate?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAvailabilityControllerServiceGetProviderSlotsKeyFn({
      duration,
      endDate,
      page,
      providerUuid,
      size,
      startDate,
      xTenantId,
    }),
    queryFn: () =>
      AvailabilityControllerService.getProviderSlots({
        duration,
        endDate,
        page,
        providerUuid,
        size,
        startDate,
        xTenantId,
      }),
  });
export const prefetchUseAvailabilityControllerServiceGetProviderAvailabilitySetting = (
  queryClient: QueryClient,
  {
    providerUuid,
    xTenantId,
  }: {
    providerUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAvailabilityControllerServiceGetProviderAvailabilitySettingKeyFn({ providerUuid, xTenantId }),
    queryFn: () => AvailabilityControllerService.getProviderAvailabilitySetting({ providerUuid, xTenantId }),
  });
export const prefetchUseAiControllerServiceGetPatientReports = (
  queryClient: QueryClient,
  {
    month,
    page,
    patientUuid,
    size,
    sortBy,
    sortDirection,
    xTenantId,
    year,
  }: {
    month?: number;
    page?: number;
    patientUuid: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
    year?: number;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceGetPatientReportsKeyFn({
      month,
      page,
      patientUuid,
      size,
      sortBy,
      sortDirection,
      xTenantId,
      year,
    }),
    queryFn: () =>
      AiControllerService.getPatientReports({ month, page, patientUuid, size, sortBy, sortDirection, xTenantId, year }),
  });
export const prefetchUseAiControllerServiceLlmauGetVoiceRecommendation = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceLlmauGetVoiceRecommendationKeyFn({ xTenantId }),
    queryFn: () => AiControllerService.llmauGetVoiceRecommendation({ xTenantId }),
  });
export const prefetchUseAiControllerServiceGetNurseReports = (
  queryClient: QueryClient,
  {
    reportUuid,
    xTenantId,
  }: {
    reportUuid: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceGetNurseReportsKeyFn({ reportUuid, xTenantId }),
    queryFn: () => AiControllerService.getNurseReports({ reportUuid, xTenantId }),
  });
export const prefetchUseAiControllerServiceLlmauGetMyNurseAvatar = (
  queryClient: QueryClient,
  {
    providerId,
    xTenantId,
  }: {
    providerId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceLlmauGetMyNurseAvatarKeyFn({ providerId, xTenantId }),
    queryFn: () => AiControllerService.llmauGetMyNurseAvatar({ providerId, xTenantId }),
  });
export const prefetchUseAiControllerServiceGetNurseActionStatistics = (
  queryClient: QueryClient,
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceGetNurseActionStatisticsKeyFn({ month, patientUuid, xTenantId, year }),
    queryFn: () => AiControllerService.getNurseActionStatistics({ month, patientUuid, xTenantId, year }),
  });
export const prefetchUseAiControllerServiceLlmauGetAvatar = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceLlmauGetAvatarKeyFn({ xTenantId }),
    queryFn: () => AiControllerService.llmauGetAvatar({ xTenantId }),
  });
export const prefetchUseAiControllerServiceGetNurseActionInExcel = (
  queryClient: QueryClient,
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceGetNurseActionInExcelKeyFn({ month, patientUuid, xTenantId, year }),
    queryFn: () => AiControllerService.getNurseActionInExcel({ month, patientUuid, xTenantId, year }),
  });
export const prefetchUseAiControllerServiceGetChatbotReports = (
  queryClient: QueryClient,
  {
    month,
    patientUuid,
    xTenantId,
    year,
  }: {
    month: number;
    patientUuid: string;
    xTenantId?: string;
    year: number;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceGetChatbotReportsKeyFn({ month, patientUuid, xTenantId, year }),
    queryFn: () => AiControllerService.getChatbotReports({ month, patientUuid, xTenantId, year }),
  });
export const prefetchUseAiControllerServiceGetChatbotHistory = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceGetChatbotHistoryKeyFn({ xTenantId }),
    queryFn: () => AiControllerService.getChatbotHistory({ xTenantId }),
  });
export const prefetchUseAiControllerServiceLlmauGetAvatarVideoByTitle = (
  queryClient: QueryClient,
  {
    providerId,
    videoTitle,
    xTenantId,
  }: {
    providerId: string;
    videoTitle: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceLlmauGetAvatarVideoByTitleKeyFn({ providerId, videoTitle, xTenantId }),
    queryFn: () => AiControllerService.llmauGetAvatarVideoByTitle({ providerId, videoTitle, xTenantId }),
  });
export const prefetchUseAiControllerServiceAvqGetAlertAudio = (
  queryClient: QueryClient,
  {
    providerId,
    videoTitle,
    xTenantId,
  }: {
    providerId: string;
    videoTitle: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseAiControllerServiceAvqGetAlertAudioKeyFn({ providerId, videoTitle, xTenantId }),
    queryFn: () => AiControllerService.avqGetAlertAudio({ providerId, videoTitle, xTenantId }),
  });
export const prefetchUseVitalControllerServiceGetPatientVitals = (
  queryClient: QueryClient,
  {
    page,
    searchString,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    page?: number;
    searchString?: string;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseVitalControllerServiceGetPatientVitalsKeyFn({
      page,
      searchString,
      size,
      sort,
      sortBy,
      xTenantId,
    }),
    queryFn: () => VitalControllerService.getPatientVitals({ page, searchString, size, sort, sortBy, xTenantId }),
  });
export const prefetchUseZoomControllerServiceGetAuthToken = (
  queryClient: QueryClient,
  {
    roomId,
    xTenantId,
  }: {
    roomId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseZoomControllerServiceGetAuthTokenKeyFn({ roomId, xTenantId }),
    queryFn: () => ZoomControllerService.getAuthToken({ roomId, xTenantId }),
  });
export const prefetchUseZoomControllerServiceSubscribe = (
  queryClient: QueryClient,
  {
    eventKey,
    xTenantId,
  }: {
    eventKey: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseZoomControllerServiceSubscribeKeyFn({ eventKey, xTenantId }),
    queryFn: () => ZoomControllerService.subscribe({ eventKey, xTenantId }),
  });
export const prefetchUseZoomControllerServiceEmit = (
  queryClient: QueryClient,
  {
    eventKey,
    xTenantId,
  }: {
    eventKey: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseZoomControllerServiceEmitKeyFn({ eventKey, xTenantId }),
    queryFn: () => ZoomControllerService.emit({ eventKey, xTenantId }),
  });
export const prefetchUseLicenseStateControllerServiceGetAllLicensedStates = (
  queryClient: QueryClient,
  {
    page,
    searchString,
    size,
    sortBy,
    sortDirection,
    xTenantId,
  }: {
    page?: number;
    searchString?: string;
    size?: number;
    sortBy?: string;
    sortDirection?: string;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseLicenseStateControllerServiceGetAllLicensedStatesKeyFn({
      page,
      searchString,
      size,
      sortBy,
      sortDirection,
      xTenantId,
    }),
    queryFn: () =>
      LicenseStateControllerService.getAllLicensedStates({
        page,
        searchString,
        size,
        sortBy,
        sortDirection,
        xTenantId,
      }),
  });
export const prefetchUseEqrControllerServiceGetInfusionTherapyById = (
  queryClient: QueryClient,
  {
    therapyId,
    xTenantId,
  }: {
    therapyId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEqrControllerServiceGetInfusionTherapyByIdKeyFn({ therapyId, xTenantId }),
    queryFn: () => EqrControllerService.getInfusionTherapyById({ therapyId, xTenantId }),
  });
export const prefetchUseEhrProviderControllerServiceGetAllEhrProviders = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrProviderControllerServiceGetAllEhrProvidersKeyFn({ xTenantId }),
    queryFn: () => EhrProviderControllerService.getAllEhrProviders({ xTenantId }),
  });
export const prefetchUseEhrControllerServiceGetPractitionerByProviderId = (
  queryClient: QueryClient,
  {
    practitionerId,
    xTenantId,
  }: {
    practitionerId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceGetPractitionerByProviderIdKeyFn({ practitionerId, xTenantId }),
    queryFn: () => EhrControllerService.getPractitionerByProviderId({ practitionerId, xTenantId }),
  });
export const prefetchUseEhrControllerServiceSearchPractitioners = (
  queryClient: QueryClient,
  {
    name,
    xTenantId,
  }: {
    name: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceSearchPractitionersKeyFn({ name, xTenantId }),
    queryFn: () => EhrControllerService.searchPractitioners({ name, xTenantId }),
  });
export const prefetchUseEhrControllerServiceGetPatientVitals2 = (
  queryClient: QueryClient,
  {
    date,
    patientId,
    xTenantId,
  }: {
    date?: string;
    patientId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceGetPatientVitals2KeyFn({ date, patientId, xTenantId }),
    queryFn: () => EhrControllerService.getPatientVitals2({ date, patientId, xTenantId }),
  });
export const prefetchUseEhrControllerServiceGetPatientEncounterDiagnosisByPatientId = (
  queryClient: QueryClient,
  {
    patientId,
    xTenantId,
  }: {
    patientId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceGetPatientEncounterDiagnosisByPatientIdKeyFn({ patientId, xTenantId }),
    queryFn: () => EhrControllerService.getPatientEncounterDiagnosisByPatientId({ patientId, xTenantId }),
  });
export const prefetchUseEhrControllerServiceGetAllergiesByPatientId = (
  queryClient: QueryClient,
  {
    clinicalStatus,
    patientId,
    recordedDate,
    xTenantId,
  }: {
    clinicalStatus?: string;
    patientId: string;
    recordedDate?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceGetAllergiesByPatientIdKeyFn({
      clinicalStatus,
      patientId,
      recordedDate,
      xTenantId,
    }),
    queryFn: () => EhrControllerService.getAllergiesByPatientId({ clinicalStatus, patientId, recordedDate, xTenantId }),
  });
export const prefetchUseEhrControllerServiceSearchPatients = (
  queryClient: QueryClient,
  {
    birthdate,
    family,
    given,
    organisationId,
    patientId,
    xTenantId,
  }: {
    birthdate?: string;
    family?: string;
    given?: string;
    organisationId: string;
    patientId?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceSearchPatientsKeyFn({
      birthdate,
      family,
      given,
      organisationId,
      patientId,
      xTenantId,
    }),
    queryFn: () =>
      EhrControllerService.searchPatients({ birthdate, family, given, organisationId, patientId, xTenantId }),
  });
export const prefetchUseEhrControllerServiceGetOrganizationByPracticeId = (
  queryClient: QueryClient,
  {
    practiceId,
    xTenantId,
  }: {
    practiceId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceGetOrganizationByPracticeIdKeyFn({ practiceId, xTenantId }),
    queryFn: () => EhrControllerService.getOrganizationByPracticeId({ practiceId, xTenantId }),
  });
export const prefetchUseEhrControllerServiceGetMedicationRequestByPatientId = (
  queryClient: QueryClient,
  {
    patientId,
    status,
    xTenantId,
  }: {
    patientId: string;
    status?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceGetMedicationRequestByPatientIdKeyFn({ patientId, status, xTenantId }),
    queryFn: () => EhrControllerService.getMedicationRequestByPatientId({ patientId, status, xTenantId }),
  });
export const prefetchUseEhrControllerServiceGetMedicationDispenseByPatientId = (
  queryClient: QueryClient,
  {
    patientId,
    status,
    whenhandedover,
    xTenantId,
  }: {
    patientId: string;
    status?: string;
    whenhandedover?: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceGetMedicationDispenseByPatientIdKeyFn({
      patientId,
      status,
      whenhandedover,
      xTenantId,
    }),
    queryFn: () =>
      EhrControllerService.getMedicationDispenseByPatientId({ patientId, status, whenhandedover, xTenantId }),
  });
export const prefetchUseEhrControllerServiceGetLocationByLocationId = (
  queryClient: QueryClient,
  {
    locationId,
    xTenantId,
  }: {
    locationId: string;
    xTenantId?: string;
  }
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceGetLocationByLocationIdKeyFn({ locationId, xTenantId }),
    queryFn: () => EhrControllerService.getLocationByLocationId({ locationId, xTenantId }),
  });
export const prefetchUseEhrControllerServiceGetAccessToken1 = (
  queryClient: QueryClient,
  {
    xTenantId,
  }: {
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseEhrControllerServiceGetAccessToken1KeyFn({ xTenantId }),
    queryFn: () => EhrControllerService.getAccessToken1({ xTenantId }),
  });
export const prefetchUseConditionControllerServiceGetAllConditions = (
  queryClient: QueryClient,
  {
    name,
    page,
    size,
    sort,
    sortBy,
    xTenantId,
  }: {
    name?: string;
    page?: number;
    size?: number;
    sort?: string;
    sortBy?: string;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseConditionControllerServiceGetAllConditionsKeyFn({ name, page, size, sort, sortBy, xTenantId }),
    queryFn: () => ConditionControllerService.getAllConditions({ name, page, size, sort, sortBy, xTenantId }),
  });
export const prefetchUseActivityControllerServiceGetAllActivities = (
  queryClient: QueryClient,
  {
    search,
    xTenantId,
  }: {
    search?: string;
    xTenantId?: string;
  } = {}
) =>
  queryClient.prefetchQuery({
    queryKey: Common.UseActivityControllerServiceGetAllActivitiesKeyFn({ search, xTenantId }),
    queryFn: () => ActivityControllerService.getAllActivities({ search, xTenantId }),
  });
