// This file is auto-generated by @hey-api/openapi-ts
import type { CancelablePromise } from "./core/CancelablePromise";
import { OpenAPI } from "./core/OpenAPI";
import { request as __request } from "./core/request";
import type {
  AddPatientConsentData,
  AddPatientConsentResponse,
  AddProgramGoalTrackData,
  AddProgramGoalTrackResponse,
  AddRoleData,
  AddRoleResponse,
  AddTaskData,
  AddTaskResponse,
  AddUpdateRolePermissionsData,
  AddUpdateRolePermissionsResponse,
  AddUpdateTrainedDeviceData,
  AddUpdateTrainedDeviceResponse,
  AddUserData,
  AddUserResponse,
  ArchiveTaskData,
  ArchiveTaskResponse,
  AssessNursePerformanceData,
  AssessNursePerformanceResponse,
  AssignCarePlanData,
  AssignCarePlanResponse,
  AssignDeviceData,
  AssignDeviceResponse,
  AvqGetAlertAudioData,
  AvqGetAlertAudioResponse,
  AvqSetupAvatarData,
  AvqSetupAvatarEdgeData,
  AvqSetupAvatarEdgeResponse,
  AvqSetupAvatarResponse,
  BookAppointmentRequestData,
  BookAppointmentRequestResponse,
  BroadCastAppointmentData,
  BroadCastAppointmentResponse,
  BulkAssignCarePlansData,
  BulkAssignCarePlansResponse,
  ChangeAvatar1Data,
  ChangeAvatar1Response,
  ChangeAvatar2Data,
  ChangeAvatar2Response,
  ChangeAvatar3Data,
  ChangeAvatar3Response,
  ChangeAvatarData,
  ChangeAvatarResponse,
  ChangePasswordData,
  ChangePasswordResponse,
  CreateAppointmentData,
  CreateAppointmentResponse,
  CreateBulkPatientAllergyData,
  CreateBulkPatientAllergyResponse,
  CreateBulkPatientDiagnosisData,
  CreateBulkPatientDiagnosisResponse,
  CreateBulkPatientMedicationData,
  CreateBulkPatientMedicationResponse,
  CreateBulkPatientMedicationWithImageData,
  CreateBulkPatientMedicationWithImageResponse,
  CreateBulkPatientVitalData,
  CreateBulkPatientVitalResponse,
  CreateCarePlanData,
  CreateCarePlanResponse,
  CreateClinicalNoteData,
  CreateClinicalNoteResponse,
  CreateConsentFormsData,
  CreateConsentFormsResponse,
  CreateDeviceData,
  CreateDeviceResponse,
  CreateLocationData,
  CreateLocationResponse,
  CreateMedicalCodeData,
  CreateMedicalCodeResponse,
  CreatePatientAllergyData,
  CreatePatientAllergyResponse,
  CreatePatientData,
  CreatePatientDiagnosisData,
  CreatePatientDiagnosisResponse,
  CreatePatientMedicationData,
  CreatePatientMedicationResponse,
  CreatePatientResponse,
  CreatePatientVitalData,
  CreatePatientVitalResponse,
  CreateProviderData,
  CreateProviderGroupData,
  CreateProviderGroupResponse,
  CreateProviderResponse,
  CreateRealmData,
  CreateRealmResponse,
  CreateTimeLogAsyncData,
  CreateTimeLogAsyncResponse,
  DeletePatientMedicationIdData,
  DeletePatientMedicationIdResponse,
  DeleteTimeLogByIdData,
  DeleteTimeLogByIdResponse,
  DeleteTokenData,
  DeleteTokenResponse,
  DeleteVideoData,
  DeleteVideoResponse,
  DownloadTemplateData,
  DownloadTemplateResponse,
  EmitData,
  EmitResponse,
  EscalateAppointmentData,
  EscalateAppointmentResponse,
  ExportAndEmailPdfData,
  ExportAndEmailPdfResponse,
  GenerateAudioMonthlyQuickSummaryData,
  GenerateAudioMonthlyQuickSummaryResponse,
  GeneratePatientRpmBillData,
  GeneratePatientRpmBillResponse,
  GetAccessToken1Data,
  GetAccessToken1Response,
  GetAccessTokenData,
  GetAccessTokenFromRefreshTokenData,
  GetAccessTokenFromRefreshTokenResponse,
  GetAccessTokenResponse,
  GetAiCarePlanData,
  GetAiCarePlanListData,
  GetAiCarePlanListResponse,
  GetAiCarePlanResponse,
  GetAllActivitiesData,
  GetAllActivitiesResponse,
  GetAllAppointmentsData,
  GetAllAppointmentsResponse,
  GetAllBillsByPatientIdData,
  GetAllBillsByPatientIdResponse,
  GetAllCarePlans1Data,
  GetAllCarePlans1Response,
  GetAllCarePlansData,
  GetAllCarePlansResponse,
  GetAllConditionsData,
  GetAllConditionsResponse,
  GetAllConsentFormTemplateData,
  GetAllConsentFormTemplateResponse,
  GetAllDevicesData,
  GetAllDevicesResponse,
  GetAllEhrProvidersData,
  GetAllEhrProvidersResponse,
  GetAllLicensedStatesData,
  GetAllLicensedStatesResponse,
  GetAllLocationsData,
  GetAllLocationsResponse,
  GetAllPatientConsentFormData,
  GetAllPatientConsentFormResponse,
  GetAllPatientData,
  GetAllPatientResponse,
  GetAllPatientTimeLogsData,
  GetAllPatientTimeLogsResponse,
  GetAllPrivilegesData,
  GetAllPrivilegesResponse,
  GetAllProtocolsData,
  GetAllProtocolsResponse,
  GetAllProviderGroupsData,
  GetAllProviderGroupsResponse,
  GetAllProvidersData,
  GetAllProvidersResponse,
  GetAllReferenceRangesData,
  GetAllReferenceRangesResponse,
  GetAllRolesData,
  GetAllRolesPermissionsData,
  GetAllRolesPermissionsResponse,
  GetAllRolesResponse,
  GetAllTasksData,
  GetAllTasksResponse,
  GetAllUsersData,
  GetAllUsersResponse,
  GetAllergiesByPatientIdData,
  GetAllergiesByPatientIdResponse,
  GetAppointmentByIdData,
  GetAppointmentByIdResponse,
  GetAppointmentListData,
  GetAppointmentListResponse,
  GetAssignedDevicesData,
  GetAssignedDevicesResponse,
  GetAuthTokenData,
  GetAuthTokenResponse,
  GetCarePlanByIdData,
  GetCarePlanByIdResponse,
  GetChatbotHistoryData,
  GetChatbotHistoryResponse,
  GetChatbotReportsData,
  GetChatbotReportsResponse,
  GetClinicalNoteByAppointmentIdData,
  GetClinicalNoteByAppointmentIdResponse,
  GetClinicalNoteByUuidData,
  GetClinicalNoteByUuidResponse,
  GetConsentFormIdData,
  GetConsentFormIdResponse,
  GetDeviceByIdData,
  GetDeviceByIdResponse,
  GetEcgValueData,
  GetEcgValueResponse,
  GetInfusionTherapyByIdData,
  GetInfusionTherapyByIdResponse,
  GetLocationByIdData,
  GetLocationByIdResponse,
  GetLocationByLocationIdData,
  GetLocationByLocationIdResponse,
  GetMedicalCodeByIdData,
  GetMedicalCodeByIdResponse,
  GetMedicalCodesData,
  GetMedicalCodesResponse,
  GetMedicationDispenseByPatientIdData,
  GetMedicationDispenseByPatientIdResponse,
  GetMedicationRequestByPatientIdData,
  GetMedicationRequestByPatientIdResponse,
  GetNurseActionInExcelData,
  GetNurseActionInExcelResponse,
  GetNurseActionStatisticsData,
  GetNurseActionStatisticsResponse,
  GetNurseAssessmentPerformanceData,
  GetNurseAssessmentPerformanceResponse,
  GetNurseReportDashboardData,
  GetNurseReportDashboardResponse,
  GetNurseReportsData,
  GetNurseReportsResponse,
  GetOrganizationByPracticeIdData,
  GetOrganizationByPracticeIdResponse,
  GetPatientActiveCarePlanData,
  GetPatientActiveCarePlanResponse,
  GetPatientAllergyByIdData,
  GetPatientAllergyByIdResponse,
  GetPatientAllergyData,
  GetPatientAllergyResponse,
  GetPatientByIdData,
  GetPatientByIdResponse,
  GetPatientCarePlanByIdData,
  GetPatientCarePlanByIdResponse,
  GetPatientConsentFormByIdData,
  GetPatientConsentFormByIdResponse,
  GetPatientDashboardData,
  GetPatientDashboardResponse,
  GetPatientDiagnosisByIdData,
  GetPatientDiagnosisByIdResponse,
  GetPatientDiagnosisData,
  GetPatientDiagnosisResponse,
  GetPatientEncounterDiagnosisByPatientIdData,
  GetPatientEncounterDiagnosisByPatientIdResponse,
  GetPatientLatestVitalsData,
  GetPatientLatestVitalsResponse,
  GetPatientListData,
  GetPatientListResponse,
  GetPatientMedicationByIdData,
  GetPatientMedicationByIdResponse,
  GetPatientMedicationData,
  GetPatientMedicationResponse,
  GetPatientRecordData,
  GetPatientRecordResponse,
  GetPatientReportsData,
  GetPatientReportsResponse,
  GetPatientStatisticData,
  GetPatientStatisticResponse,
  GetPatientVitalByIdData,
  GetPatientVitalByIdResponse,
  GetPatientVitalSettingData,
  GetPatientVitalSettingResponse,
  GetPatientVitals1Data,
  GetPatientVitals1Response,
  GetPatientVitals2Data,
  GetPatientVitals2Response,
  GetPatientVitalsData,
  GetPatientVitalsResponse,
  GetPractitionerByProviderIdData,
  GetPractitionerByProviderIdResponse,
  GetProfile1Data,
  GetProfile1Response,
  GetProfile2Data,
  GetProfile2Response,
  GetProfileData,
  GetProfileResponse,
  GetProgramGoalTrackDetailsData,
  GetProgramGoalTrackDetailsResponse,
  GetProviderAvailabilitySettingData,
  GetProviderAvailabilitySettingResponse,
  GetProviderByIdData,
  GetProviderByIdResponse,
  GetProviderGroupByIdData,
  GetProviderGroupByIdResponse,
  GetProviderGroupBySchemaData,
  GetProviderGroupBySchemaResponse,
  GetProviderSlotsData,
  GetProviderSlotsResponse,
  GetTaskByUuidData,
  GetTaskByUuidResponse,
  GetTimeLogByIdData,
  GetTimeLogByIdResponse,
  GetUserData,
  GetUserIdByProviderData,
  GetUserIdByProviderResponse,
  GetUserResponse,
  GetVitalSettingsData,
  GetVitalSettingsResponse,
  LlmauAnalyzeMedicationData,
  LlmauAnalyzeMedicationResponse,
  LlmauAudioChatWithMyNurseData,
  LlmauAudioChatWithMyNurseResponse,
  LlmauChatWithMyNurseData,
  LlmauChatWithMyNurseResponse,
  LlmauGenerateMedicationAudioData,
  LlmauGenerateMedicationAudioResponse,
  LlmauGetAvatarData,
  LlmauGetAvatarResponse,
  LlmauGetAvatarVideoByTitleData,
  LlmauGetAvatarVideoByTitleResponse,
  LlmauGetMyNurseAvatarData,
  LlmauGetMyNurseAvatarResponse,
  LlmauGetVoiceRecommendationData,
  LlmauGetVoiceRecommendationResponse,
  LogoutData,
  LogoutResponse,
  NotifyPatientData,
  NotifyPatientResponse,
  PlayerTimeLogData,
  PlayerTimeLogResponse,
  RescheduleAppointmentData,
  RescheduleAppointmentResponse,
  ResendOtpData,
  ResendOtpResponse,
  ResetAllRolePrivilegesData,
  ResetAllRolePrivilegesResponse,
  SaveTokenData,
  SaveTokenResponse,
  SearchPatientsData,
  SearchPatientsResponse,
  SearchPractitionersData,
  SearchPractitionersResponse,
  SendDirectedMessageData,
  SendDirectedMessageResponse,
  SendInvitationLinkData,
  SendInvitationLinkResponse,
  SendMulticastMessageData,
  SendMulticastMessageResponse,
  SendNurseAssessmentPerformanceData,
  SendNurseAssessmentPerformanceResponse,
  SendPatientAlertData,
  SendPatientAlertResponse,
  SendPatientNotificationData,
  SendPatientNotificationResponse,
  SetPasswordData,
  SetPasswordResponse,
  SetProviderAvailabilitySettingData,
  SetProviderAvailabilitySettingResponse,
  StoreNurseActionStatisticsData,
  StoreNurseActionStatisticsResponse,
  SubscribeData,
  SubscribeResponse,
  SyncDatabaseSchemaData,
  SyncDatabaseSchemaResponse,
  SyncPatientAllergyData,
  SyncPatientAllergyResponse,
  SyncPatientDiagnosisData,
  SyncPatientDiagnosisResponse,
  SyncPatientMedicationData,
  SyncPatientMedicationResponse,
  SyncPatientVitalData,
  SyncPatientVitalResponse,
  TestNotifData,
  TestNotifImageData,
  TestNotifImageResponse,
  TestNotifResponse,
  UpdateAppointmentData,
  UpdateAppointmentResponse,
  UpdateAppointmentStatusData,
  UpdateAppointmentStatusResponse,
  UpdateBulkPatientMedicationDosageData,
  UpdateBulkPatientMedicationDosageResponse,
  UpdateCarePlanArchiveStatus1Data,
  UpdateCarePlanArchiveStatus1Response,
  UpdateCarePlanArchiveStatusData,
  UpdateCarePlanArchiveStatusResponse,
  UpdateCarePlanData,
  UpdateCarePlanResponse,
  UpdateClinicalNoteData,
  UpdateClinicalNoteResponse,
  UpdateConsentFormArchiveStatusData,
  UpdateConsentFormArchiveStatusResponse,
  UpdateConsentFormsData,
  UpdateConsentFormsResponse,
  UpdateDeviceArchiveStatusData,
  UpdateDeviceArchiveStatusResponse,
  UpdateDeviceData,
  UpdateDeviceResponse,
  UpdateDeviceStatusData,
  UpdateDeviceStatusResponse,
  UpdateLocationArchiveStatusData,
  UpdateLocationArchiveStatusResponse,
  UpdateLocationData,
  UpdateLocationResponse,
  UpdateMedicalCodeArchiveStatusData,
  UpdateMedicalCodeArchiveStatusResponse,
  UpdateMedicalCodeData,
  UpdateMedicalCodeResponse,
  UpdateMedicalCodeStatusData,
  UpdateMedicalCodeStatusResponse,
  UpdatePatientAllergyArchiveStatusData,
  UpdatePatientAllergyArchiveStatusResponse,
  UpdatePatientAllergyData,
  UpdatePatientAllergyResponse,
  UpdatePatientArchiveStatusData,
  UpdatePatientArchiveStatusResponse,
  UpdatePatientCarePlanData,
  UpdatePatientCarePlanResponse,
  UpdatePatientCarePlanStatusData,
  UpdatePatientCarePlanStatusResponse,
  UpdatePatientConsentStatusData,
  UpdatePatientConsentStatusResponse,
  UpdatePatientData,
  UpdatePatientDiagnosisArchiveStatusData,
  UpdatePatientDiagnosisArchiveStatusResponse,
  UpdatePatientDiagnosisData,
  UpdatePatientDiagnosisResponse,
  UpdatePatientMedicationData,
  UpdatePatientMedicationResponse,
  UpdatePatientResponse,
  UpdatePatientVitalData,
  UpdatePatientVitalResponse,
  UpdatePatientVitalSettingData,
  UpdatePatientVitalSettingResponse,
  UpdateProgramGoalTrackData,
  UpdateProgramGoalTrackResponse,
  UpdateProviderArchiveStatusData,
  UpdateProviderArchiveStatusResponse,
  UpdateProviderAvatarStatusData,
  UpdateProviderAvatarStatusResponse,
  UpdateProviderData,
  UpdateProviderGroupArchiveStatusData,
  UpdateProviderGroupArchiveStatusResponse,
  UpdateProviderGroupConfigurationData,
  UpdateProviderGroupConfigurationResponse,
  UpdateProviderGroupData,
  UpdateProviderGroupResponse,
  UpdateProviderOnboardingStatusData,
  UpdateProviderOnboardingStatusResponse,
  UpdateProviderResponse,
  UpdateRoleData,
  UpdateRolePermissionData,
  UpdateRolePermissionResponse,
  UpdateRoleResponse,
  UpdateTaskData,
  UpdateTaskResponse,
  UpdateTaskStatusData,
  UpdateTaskStatusResponse,
  UpdateTimeLogData,
  UpdateTimeLogResponse,
  UpdateUserArchiveStatusData,
  UpdateUserArchiveStatusResponse,
  UpdateUserData,
  UpdateUserResponse,
  UpdateUserStatusData,
  UpdateUserStatusResponse,
  UpdateVitalReferenceRangeData,
  UpdateVitalReferenceRangeResponse,
  UploadFile1Data,
  UploadFile1Response,
  UploadFileData,
  UploadFileResponse,
  UploadVideoData,
  UploadVideoResponse,
  VerifyOtpData,
  VerifyOtpResponse,
  VerifyUserData,
  VerifyUserResponse,
  VygenDetectFaceData,
  VygenDetectFaceResponse,
  VygenGenerateAvatarData,
  VygenGenerateAvatarResponse,
} from "./types.gen";

export class UserControllerService {
  /**
   * Archive/Unarchive User
   * Change User archive status to either archived or unarchived.
   * @param data The data for the request.
   * @param data.userId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateUserArchiveStatus(
    data: UpdateUserArchiveStatusData
  ): CancelablePromise<UpdateUserArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/{userId}/archive-status/{status}",
      path: {
        userId: data.userId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * List Staff Users
   * List staff users using Filter with Pagination and Sorting with filter options on status, archive and role and search functionality on firstName, lastName and email,
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.role
   * @param data.roleType
   * @param data.searchString
   * @param data.locationId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllUsers(data: GetAllUsersData = {}): CancelablePromise<GetAllUsersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/user",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        role: data.role,
        roleType: data.roleType,
        searchString: data.searchString,
        locationId: data.locationId,
      },
    });
  }

  /**
   * Update Staff Users
   * Edit Staff User with editable fields as firstName, lastName, phone, roleType, role.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateUser(data: UpdateUserData): CancelablePromise<UpdateUserResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/user",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Staff User
   * Add a new Staff User into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static addUser(data: AddUserData): CancelablePromise<AddUserResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/user",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Change Avatar
   * Change user avatar
   * @param data The data for the request.
   * @param data.userUuid
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static changeAvatar3(data: ChangeAvatar3Data): CancelablePromise<ChangeAvatar3Response> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/change-avatar/{userUuid}",
      path: {
        userUuid: data.userUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Verify user
   * Verify user
   * @param data The data for the request.
   * @param data.email
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static verifyUser(data: VerifyUserData): CancelablePromise<VerifyUserResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/verify-user/{email}",
      path: {
        email: data.email,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Verify otp
   * Verify user otp
   * @param data The data for the request.
   * @param data.email
   * @param data.otp
   * @param data.linkType
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static verifyOtp(data: VerifyOtpData): CancelablePromise<VerifyOtpResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/verify-otp/{linkType}/{email}/{otp}",
      path: {
        email: data.email,
        otp: data.otp,
        linkType: data.linkType,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Set/Reset Password
   * Set/Reset Password for User
   * @param data The data for the request.
   * @param data.linkType
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static setPassword(data: SetPasswordData): CancelablePromise<SetPasswordResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/set-password/{linkType}",
      path: {
        linkType: data.linkType,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Resend otp
   * Resend otp
   * @param data The data for the request.
   * @param data.linkType
   * @param data.email
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static resendOtp(data: ResendOtpData): CancelablePromise<ResendOtpResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/resend-otp/{linkType}/{email}",
      path: {
        linkType: data.linkType,
        email: data.email,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Logout
   * Invalidate active access tokens
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static logout(data: LogoutData): CancelablePromise<LogoutResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/logout",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Login
   * Get access token using Username and Password
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAccessToken(data: GetAccessTokenData): CancelablePromise<GetAccessTokenResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/login",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Change Password
   * Change Password for Logged-in User
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static changePassword(data: ChangePasswordData): CancelablePromise<ChangePasswordResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/change-password",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.refreshToken
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAccessTokenFromRefreshToken(
    data: GetAccessTokenFromRefreshTokenData
  ): CancelablePromise<GetAccessTokenFromRefreshTokenResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/access-token",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        refreshToken: data.refreshToken,
      },
    });
  }

  /**
   * Staff User
   * Get Staff User by userUuid
   * @param data The data for the request.
   * @param data.userId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getUser(data: GetUserData): CancelablePromise<GetUserResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/user/{userId}",
      path: {
        userId: data.userId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * User Profile
   * Get Logged-in User Profile
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getProfile1(data: GetProfile1Data = {}): CancelablePromise<GetProfile1Response> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/profile",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class PatientVitalSettingControllerService {
  /**
   * Update Patient Vital Setting Status
   * Update the Patient Vital Setting
   * @param data The data for the request.
   * @param data.patientId
   * @param data.settingName
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientVitalSetting(
    data: UpdatePatientVitalSettingData
  ): CancelablePromise<UpdatePatientVitalSettingResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/vital-setting/{patientId}/setting-name/{settingName}/{status}",
      path: {
        patientId: data.patientId,
        settingName: data.settingName,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get vital settings
   * Get vital settings
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getVitalSettings(data: GetVitalSettingsData = {}): CancelablePromise<GetVitalSettingsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/vital-setting",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get patient vital setting
   * Get patient vital setting
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientVitalSetting(
    data: GetPatientVitalSettingData
  ): CancelablePromise<GetPatientVitalSettingResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/vital-setting/patient/{patientUuid}",
      path: {
        patientUuid: data.patientUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class TimeLogControllerService {
  /**
   * Get all Patient Logs
   * Get all patient logs for given criteria
   * @param data The data for the request.
   * @param data.patientId
   * @param data.month
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sort
   * @param data.activityName
   * @param data.loggedBy
   * @param data.loggedEntryType
   * @param data.endTimeDate
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllPatientTimeLogs(
    data: GetAllPatientTimeLogsData
  ): CancelablePromise<GetAllPatientTimeLogsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/time-log",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sort: data.sort,
        patientId: data.patientId,
        activityName: data.activityName,
        loggedBy: data.loggedBy,
        loggedEntryType: data.loggedEntryType,
        month: data.month,
        endTimeDate: data.endTimeDate,
      },
    });
  }

  /**
   * update the time log
   * update time log for the patient
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateTimeLog(data: UpdateTimeLogData): CancelablePromise<UpdateTimeLogResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/time-log",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * create the time log
   * add time log for the patient
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createTimeLogAsync(data: CreateTimeLogAsyncData): CancelablePromise<CreateTimeLogAsyncResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/time-log",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * create the player time log
   * add time log for the patient
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static playerTimeLog(data: PlayerTimeLogData): CancelablePromise<PlayerTimeLogResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/time-log/player-time-log",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * get the time log
   * get time log for the patient
   * @param data The data for the request.
   * @param data.timeLogId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getTimeLogById(data: GetTimeLogByIdData): CancelablePromise<GetTimeLogByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/time-log/{timeLogId}",
      path: {
        timeLogId: data.timeLogId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.timeLogId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static deleteTimeLogById(data: DeleteTimeLogByIdData): CancelablePromise<DeleteTimeLogByIdResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/master/time-log/{timeLogId}",
      path: {
        timeLogId: data.timeLogId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class TaskControllerService {
  /**
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.searchString
   * @param data.searchAssignTo
   * @param data.status
   * @param data.priority
   * @param data.currentUserUuid
   * @param data.archive
   * @param data.active
   * @param data.assignedTo
   * @param data.assignedBy
   * @param data.patientId
   * @param data.type
   * @param data.dueDate
   * @param data.assignedDate
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllTasks(data: GetAllTasksData = {}): CancelablePromise<GetAllTasksResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/task",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        searchString: data.searchString,
        searchAssignTo: data.searchAssignTo,
        status: data.status,
        priority: data.priority,
        currentUserUuid: data.currentUserUuid,
        archive: data.archive,
        active: data.active,
        assignedTo: data.assignedTo,
        assignedBy: data.assignedBy,
        patientId: data.patientId,
        type: data.type,
        dueDate: data.dueDate,
        assignedDate: data.assignedDate,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateTask(data: UpdateTaskData): CancelablePromise<UpdateTaskResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/task",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static addTask(data: AddTaskData): CancelablePromise<AddTaskResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/task",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.taskUuid
   * @param data.status
   * @param data.note
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateTaskStatus(data: UpdateTaskStatusData): CancelablePromise<UpdateTaskStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/task/{taskUuid}/status/{status}",
      path: {
        taskUuid: data.taskUuid,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        note: data.note,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.taskUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getTaskByUuid(data: GetTaskByUuidData): CancelablePromise<GetTaskByUuidResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/task/{taskUuid}",
      path: {
        taskUuid: data.taskUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.taskUuid
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static archiveTask(data: ArchiveTaskData): CancelablePromise<ArchiveTaskResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/master/task/{taskUuid}/archive-status/{status}",
      path: {
        taskUuid: data.taskUuid,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class RolesAndPrivilegesControllerService {
  /**
   * Get all Roles with Privileges.
   * Get all Roles list with their Privileges
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllRoles(data: GetAllRolesData = {}): CancelablePromise<GetAllRolesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/role",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Update Role
   * Update an existing Role
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateRole(data: UpdateRoleData): CancelablePromise<UpdateRoleResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/role",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Role
   * Add a new Role
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static addRole(data: AddRoleData): CancelablePromise<AddRoleResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/role",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static addUpdateRolePermissions(
    data: AddUpdateRolePermissionsData = {}
  ): CancelablePromise<AddUpdateRolePermissionsResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/role/update-role-permission",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get all Privileges.
   * Get all Privileges
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllPrivileges(data: GetAllPrivilegesData = {}): CancelablePromise<GetAllPrivilegesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/role/privileges",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Update Role Permission
   * Update permission for a specific role
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateRolePermission(data: UpdateRolePermissionData): CancelablePromise<UpdateRolePermissionResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/role/privileges",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Reset All Role Privileges
   * Reset all role-privilege mappings to their default values. This will remove all existing mappings and create new ones based on default rules.
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static resetAllRolePrivileges(
    data: ResetAllRolePrivilegesData = {}
  ): CancelablePromise<ResetAllRolePrivilegesResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/role/privileges/reset",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.realm
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllRolesPermissions(
    data: GetAllRolesPermissionsData
  ): CancelablePromise<GetAllRolesPermissionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/role/{realm}/permissions",
      path: {
        realm: data.realm,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class ProviderControllerService {
  /**
   * Get all providers/Nurse
   * Get all providers/Nurse list with search option on firstName, lastName, npi and filter options on status, archive and state.
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.state
   * @param data.role
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllProviders(data: GetAllProvidersData = {}): CancelablePromise<GetAllProvidersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        state: data.state,
        role: data.role,
        searchString: data.searchString,
      },
    });
  }

  /**
   * Update provider/Nurse
   * Edit Provider/Nurse with editable fields as firstName, lastName, phone, providerType, gender, npi, address, licensedStates
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateProvider(data: UpdateProviderData): CancelablePromise<UpdateProviderResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Provider/Nurse User
   * Add a new Provider/Nurse User into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createProvider(data: CreateProviderData): CancelablePromise<CreateProviderResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/provider",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Update Onboarding Provider/Nurse Progress
   * To update the onboarding progress for the provider/nurse
   * @param data The data for the request.
   * @param data.providerId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateProviderOnboardingStatus(
    data: UpdateProviderOnboardingStatusData
  ): CancelablePromise<UpdateProviderOnboardingStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider/{providerId}/onboarding-status/{status}",
      path: {
        providerId: data.providerId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Update Avatar Provider/Nurse Status
   * To update the avatar status for the provider/nurse
   * @param data The data for the request.
   * @param data.providerId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateProviderAvatarStatus(
    data: UpdateProviderAvatarStatusData
  ): CancelablePromise<UpdateProviderAvatarStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider/{providerId}/avatar-status/{status}",
      path: {
        providerId: data.providerId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Archive/Unarchived Provider/Nurse
   * Change Provider/Nurse archive status to either archived or unarchived.
   * @param data The data for the request.
   * @param data.providerId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateProviderArchiveStatus(
    data: UpdateProviderArchiveStatusData
  ): CancelablePromise<UpdateProviderArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider/{providerId}/archive-status/{status}",
      path: {
        providerId: data.providerId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Upload nurse introduction video
   * Upload nurse introduction video.
   * @param data The data for the request.
   * @param data.providerUuid
   * @param data.xTenantId
   * @param data.requestBody
   * @returns Response OK
   * @throws ApiError
   */
  public static uploadVideo(data: UploadVideoData): CancelablePromise<UploadVideoResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider/upload",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        providerUuid: data.providerUuid,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.appointmentId
   * @param data.notifyType
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static notifyPatient(data: NotifyPatientData): CancelablePromise<NotifyPatientResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider/notify/patient/{appointmentId}",
      path: {
        appointmentId: data.appointmentId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        notifyType: data.notifyType,
      },
    });
  }

  /**
   * Change Avatar
   * Change provider avatar
   * @param data The data for the request.
   * @param data.providerUuid
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static changeAvatar(data: ChangeAvatarData): CancelablePromise<ChangeAvatarResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider/change-avatar/{providerUuid}",
      path: {
        providerUuid: data.providerUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Get provider/Nurse by providerUuid.
   * Get provider/Nurse by providerUuid
   * @param data The data for the request.
   * @param data.providerUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getProviderById(data: GetProviderByIdData): CancelablePromise<GetProviderByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider/{providerUuid}",
      path: {
        providerUuid: data.providerUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.providerId
   * @param data.year
   * @param data.month
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getNurseReportDashboard(
    data: GetNurseReportDashboardData
  ): CancelablePromise<GetNurseReportDashboardResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider/{providerId}/reports/dashboard",
      path: {
        providerId: data.providerId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        year: data.year,
        month: data.month,
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.providerId
   * @param data.year
   * @param data.month
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientDashboard(data: GetPatientDashboardData): CancelablePromise<GetPatientDashboardResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider/{providerId}/patient/dashboard",
      path: {
        providerId: data.providerId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        year: data.year,
        month: data.month,
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.userId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getUserIdByProvider(data: GetUserIdByProviderData): CancelablePromise<GetUserIdByProviderResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider/user/{userId}",
      path: {
        userId: data.userId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get provider/Nurse profile
   * Get provider/Nurse profile
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getProfile(data: GetProfileData = {}): CancelablePromise<GetProfileResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider/profile",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Delete nurse introduction video
   * Delete nurse introduction video.
   * @param data The data for the request.
   * @param data.providerUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static deleteVideo(data: DeleteVideoData): CancelablePromise<DeleteVideoResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/master/provider/video/{providerUuid}",
      path: {
        providerUuid: data.providerUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class ProviderGroupControllerService {
  /**
   * Get all provider groups.
   * Get all provider groups list for super admin
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.state
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllProviderGroups(
    data: GetAllProviderGroupsData = {}
  ): CancelablePromise<GetAllProviderGroupsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider-group",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        state: data.state,
        searchString: data.searchString,
      },
    });
  }

  /**
   * Update Provider Group
   * Edit Provider Group with editable fields as phone, website, fax, description, address
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateProviderGroup(data: UpdateProviderGroupData): CancelablePromise<UpdateProviderGroupResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider-group",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add provider group
   * Add a new provider group into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createProviderGroup(data: CreateProviderGroupData): CancelablePromise<CreateProviderGroupResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/provider-group",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.uuid
   * @param data.roleSync
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static syncDatabaseSchema(data: SyncDatabaseSchemaData): CancelablePromise<SyncDatabaseSchemaResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider-group/{uuid}/sync",
      path: {
        uuid: data.uuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        roleSync: data.roleSync,
      },
    });
  }

  /**
   * Archive/Un-archive Provider Group
   * Change Provider Group  archive status to either archived or unarchived.
   * @param data The data for the request.
   * @param data.providerGroupId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateProviderGroupArchiveStatus(
    data: UpdateProviderGroupArchiveStatusData
  ): CancelablePromise<UpdateProviderGroupArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider-group/{providerGroupId}/archive-status/{status}",
      path: {
        providerGroupId: data.providerGroupId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Update Provider Group Configuration
   * Edit Provider Group with editable fields as billing, billing threashold ranges
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateProviderGroupConfiguration(
    data: UpdateProviderGroupConfigurationData
  ): CancelablePromise<UpdateProviderGroupConfigurationResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider-group/update-configuration",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.realmName
   * @param data.xTenantId
   * @returns unknown OK
   * @throws ApiError
   */
  public static createRealm(data: CreateRealmData): CancelablePromise<CreateRealmResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider-group/realm/{realmName}",
      path: {
        realmName: data.realmName,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Change Avatar
   * Change Provider Group avatar
   * @param data The data for the request.
   * @param data.providerGroupId
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static changeAvatar1(data: ChangeAvatar1Data): CancelablePromise<ChangeAvatar1Response> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/provider-group/change-avatar/{providerGroupId}",
      path: {
        providerGroupId: data.providerGroupId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Get provider group by providerGroupUuid.
   * Get provider group by providerGroupUuid
   * @param data The data for the request.
   * @param data.providerGroupId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getProviderGroupById(data: GetProviderGroupByIdData): CancelablePromise<GetProviderGroupByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider-group/{providerGroupId}",
      path: {
        providerGroupId: data.providerGroupId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get provider group by schema.
   * Get provider group by schema
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getProviderGroupBySchema(
    data: GetProviderGroupBySchemaData = {}
  ): CancelablePromise<GetProviderGroupBySchemaResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider-group/profile",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class PatientControllerService {
  /**
   * Get all patient
   * Get all patient list with search option on firstName, lastName.
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.searchString
   * @param data.name
   * @param data.mrn
   * @param data.nurseId
   * @param data.providerId
   * @param data.genderFilter
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllPatient(data: GetAllPatientData = {}): CancelablePromise<GetAllPatientResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        searchString: data.searchString,
        name: data.name,
        mrn: data.mrn,
        nurseId: data.nurseId,
        providerId: data.providerId,
        genderFilter: data.genderFilter,
      },
    });
  }

  /**
   * Update Patient User
   * Update  Patient User into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatient(data: UpdatePatientData): CancelablePromise<UpdatePatientResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Patient User
   * Add a new Patient User into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.isAiGenerated
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createPatient(data: CreatePatientData): CancelablePromise<CreatePatientResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        isAiGenerated: data.isAiGenerated,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Archive/Unarchived Patient
   * Change Patient archive status to either archived or unarchived.
   * @param data The data for the request.
   * @param data.patientId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientArchiveStatus(
    data: UpdatePatientArchiveStatusData
  ): CancelablePromise<UpdatePatientArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient/{patientId}/archive-status/{status}",
      path: {
        patientId: data.patientId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Change Avatar
   * Change patient avatar
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static changeAvatar2(data: ChangeAvatar2Data): CancelablePromise<ChangeAvatar2Response> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient/change-avatar/{patientUuid}",
      path: {
        patientUuid: data.patientUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Upload a CSV file
   * Uploads a CSV file for the specified category and title
   * @param data The data for the request.
   * @param data.formData
   * @param data.isAiGenerated
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static uploadFile(data: UploadFileData): CancelablePromise<UploadFileResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient/patient-upload",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        isAiGenerated: data.isAiGenerated,
      },
      formData: data.formData,
      mediaType: "multipart/form-data",
    });
  }

  /**
   * Get patient by patientUuid.
   * Get patient by patientUuid
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientById(data: GetPatientByIdData): CancelablePromise<GetPatientByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient/{patientUuid}",
      path: {
        patientUuid: data.patientUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.patientId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientStatistic(data: GetPatientStatisticData): CancelablePromise<GetPatientStatisticResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient/statistic/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.patientId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientRecord(data: GetPatientRecordData): CancelablePromise<GetPatientRecordResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient/record/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get patient profile
   * Get patient profile
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getProfile2(data: GetProfile2Data = {}): CancelablePromise<GetProfile2Response> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient/profile",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Download a template CSV file
   * Downloads a template CSV file based on the specified category
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns binary OK
   * @throws ApiError
   */
  public static downloadTemplate(data: DownloadTemplateData = {}): CancelablePromise<DownloadTemplateResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient/patient-template",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get all provider patient
   * Get all provider patient list with search option on firstName, lastName.
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.searchString
   * @param data.nurseId
   * @param data.providerId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientList(data: GetPatientListData = {}): CancelablePromise<GetPatientListResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient/list",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        searchString: data.searchString,
        nurseId: data.nurseId,
        providerId: data.providerId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.patientId
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.type
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAssignedDevices(data: GetAssignedDevicesData): CancelablePromise<GetAssignedDevicesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient/devices/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        type: data.type,
      },
    });
  }
}

export class PatientVitalControllerService {
  /**
   * Get All Patient Vitals
   * Get all Patient Vital records with search and filter options
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sort
   * @param data.vitalName
   * @param data.startDate
   * @param data.endDate
   * @param data.timeFilter
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientVitals1(data: GetPatientVitals1Data): CancelablePromise<GetPatientVitals1Response> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-vital",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sort: data.sort,
        patientUuid: data.patientUuid,
        vitalName: data.vitalName,
        startDate: data.startDate,
        endDate: data.endDate,
        timeFilter: data.timeFilter,
      },
    });
  }

  /**
   * Update Patient Note Vital
   * Update a new patient Note vital record into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientVital(data: UpdatePatientVitalData): CancelablePromise<UpdatePatientVitalResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-vital",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Patient Vital
   * Add a new patient vital record into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createPatientVital(data: CreatePatientVitalData): CancelablePromise<CreatePatientVitalResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-vital",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Sync Patient Vitals
   * Add a sync Patient Vital into the system
   * @param data The data for the request.
   * @param data.patientEhrId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static syncPatientVital(data: SyncPatientVitalData): CancelablePromise<SyncPatientVitalResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-vital/sync/{patientEhrId}",
      path: {
        patientEhrId: data.patientEhrId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Add List of Patient Vital
   * Add List of patient vital record into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createBulkPatientVital(
    data: CreateBulkPatientVitalData
  ): CancelablePromise<CreateBulkPatientVitalResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-vital/list",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Get Patient Vital by ID
   * Retrieve a Patient Vital record by its unique ID
   * @param data The data for the request.
   * @param data.patientVitalId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientVitalById(data: GetPatientVitalByIdData): CancelablePromise<GetPatientVitalByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-vital/{patientVitalId}",
      path: {
        patientVitalId: data.patientVitalId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get All Latest Patient Vitals
   * Get All Patient Latest records with search and filter options
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientLatestVitals(
    data: GetPatientLatestVitalsData
  ): CancelablePromise<GetPatientLatestVitalsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-vital/latest",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        patientUuid: data.patientUuid,
      },
    });
  }

  /**
   * Get ECG value by Patient Vital Key
   * Retrieve a ECG Vital value by its unique key
   * @param data The data for the request.
   * @param data.ecgId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getEcgValue(data: GetEcgValueData): CancelablePromise<GetEcgValueResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-vital/ecg-value",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        ecgId: data.ecgId,
      },
    });
  }
}

export class PatientMedicationControllerService {
  /**
   * Get all Patient Medication.
   * Get all Patient Medication list
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sort
   * @param data.status
   * @param data.archive
   * @param data.timeFilter
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientMedication(data: GetPatientMedicationData): CancelablePromise<GetPatientMedicationResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-medication",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        patientUuid: data.patientUuid,
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sort: data.sort,
        status: data.status,
        archive: data.archive,
        timeFilter: data.timeFilter,
        searchString: data.searchString,
      },
    });
  }

  /**
   * Update Patient Medication
   * Update an existing Patient Medication
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientMedication(
    data: UpdatePatientMedicationData
  ): CancelablePromise<UpdatePatientMedicationResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-medication",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Patient Medication
   * Add a new patient medication into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createPatientMedication(
    data: CreatePatientMedicationData
  ): CancelablePromise<CreatePatientMedicationResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-medication",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Update Bulk Patient Medication Dosage
   * Update bulk existings Patient Medication Dosage
   * @param data The data for the request.
   * @param data.patientId
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateBulkPatientMedicationDosage(
    data: UpdateBulkPatientMedicationDosageData
  ): CancelablePromise<UpdateBulkPatientMedicationDosageResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-medication/update-bulk/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Sync Patient Medication
   * Add a sync patient Medication into the system
   * @param data The data for the request.
   * @param data.patientEhrId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static syncPatientMedication(
    data: SyncPatientMedicationData
  ): CancelablePromise<SyncPatientMedicationResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-medication/sync/{patientEhrId}",
      path: {
        patientEhrId: data.patientEhrId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Archive Patient Medication
   * Archive a Patient Medication by its ID
   * @param data The data for the request.
   * @param data.patientMedicationId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static deletePatientMedicationId(
    data: DeletePatientMedicationIdData
  ): CancelablePromise<DeletePatientMedicationIdResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-medication/archive-status/{patientMedicationId}",
      path: {
        patientMedicationId: data.patientMedicationId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        status: data.status,
      },
    });
  }

  /**
   * Add Bulk Patient Medication
   * Add a new patient medication into the system
   * @param data The data for the request.
   * @param data.patientId
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createBulkPatientMedication(
    data: CreateBulkPatientMedicationData
  ): CancelablePromise<CreateBulkPatientMedicationResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-medication/bulk/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Bulk Patient Medication
   * Add a new patient medication into the system with image
   * @param data The data for the request.
   * @param data.patientId
   * @param data.xTenantId
   * @param data.requestBody
   * @returns Response OK
   * @throws ApiError
   */
  public static createBulkPatientMedicationWithImage(
    data: CreateBulkPatientMedicationWithImageData
  ): CancelablePromise<CreateBulkPatientMedicationWithImageResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-medication/bulk-with-image/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Get Patient Medication by ID
   * Retrieve a Patient Medication by its unique ID
   * @param data The data for the request.
   * @param data.patientMedicationId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientMedicationById(
    data: GetPatientMedicationByIdData
  ): CancelablePromise<GetPatientMedicationByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-medication/{patientMedicationId}",
      path: {
        patientMedicationId: data.patientMedicationId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static sendPatientNotification(
    data: SendPatientNotificationData = {}
  ): CancelablePromise<SendPatientNotificationResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-medication/notification",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class PatientDiagnosisControllerService {
  /**
   * Get all Patient Diagnosis.
   * Get all Patient Diagnosis list with search option on name, patientId and filter options on active, archive.
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientDiagnosis(data: GetPatientDiagnosisData): CancelablePromise<GetPatientDiagnosisResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-diagnosis",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        patientUuid: data.patientUuid,
        searchString: data.searchString,
      },
    });
  }

  /**
   * Update Patient Diagnosis
   * Update an existing Patient Diagnosis
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientDiagnosis(
    data: UpdatePatientDiagnosisData
  ): CancelablePromise<UpdatePatientDiagnosisResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-diagnosis",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Patient Diagnosis
   * Add a new patient diagnosis into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createPatientDiagnosis(
    data: CreatePatientDiagnosisData
  ): CancelablePromise<CreatePatientDiagnosisResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-diagnosis",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Archive Patient Diagnosis
   * Archive a Patient Diagnosis by its ID
   * @param data The data for the request.
   * @param data.patientDiagnosisId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientDiagnosisArchiveStatus(
    data: UpdatePatientDiagnosisArchiveStatusData
  ): CancelablePromise<UpdatePatientDiagnosisArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-diagnosis/{patientDiagnosisId}/archive-status/{status}",
      path: {
        patientDiagnosisId: data.patientDiagnosisId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Sync Patient Diagnosis
   * Add a sync patient Diagnosis into the system
   * @param data The data for the request.
   * @param data.patientEhrId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static syncPatientDiagnosis(data: SyncPatientDiagnosisData): CancelablePromise<SyncPatientDiagnosisResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-diagnosis/sync/{patientEhrId}",
      path: {
        patientEhrId: data.patientEhrId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Add Bulk Patient Diagnosis
   * Add a new patient diagnosis into the system
   * @param data The data for the request.
   * @param data.patientId
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createBulkPatientDiagnosis(
    data: CreateBulkPatientDiagnosisData
  ): CancelablePromise<CreateBulkPatientDiagnosisResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-diagnosis/bulk/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Get Patient Diagnosis by ID
   * Retrieve a Patient Diagnosis by its unique ID
   * @param data The data for the request.
   * @param data.patientDiagnosisId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientDiagnosisById(
    data: GetPatientDiagnosisByIdData
  ): CancelablePromise<GetPatientDiagnosisByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-diagnosis/{patientDiagnosisId}",
      path: {
        patientDiagnosisId: data.patientDiagnosisId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class ConsentFormControllerService {
  /**
   * Patient Consent status
   * Change Patient Consent status to either true or false.
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientConsentStatus(
    data: UpdatePatientConsentStatusData
  ): CancelablePromise<UpdatePatientConsentStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-consent-form/{patientUuid}/status/{status}",
      path: {
        patientUuid: data.patientUuid,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get all Consent forms.
   * Get all Consent forms list filter by status and archive and search functionality by consent name
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllConsentFormTemplate(
    data: GetAllConsentFormTemplateData = {}
  ): CancelablePromise<GetAllConsentFormTemplateResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/consent-form",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        searchString: data.searchString,
      },
    });
  }

  /**
   * Update Consent form
   * Update a consent forms into the system.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateConsentForms(data: UpdateConsentFormsData): CancelablePromise<UpdateConsentFormsResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/consent-form",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Consent form
   * Add a new Consent forms into the system.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createConsentForms(data: CreateConsentFormsData): CancelablePromise<CreateConsentFormsResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/consent-form",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Archive/Un-archive Consent Form
   * Change Consent Form  archive status to either archived or unarchived.
   * @param data The data for the request.
   * @param data.consentFormId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateConsentFormArchiveStatus(
    data: UpdateConsentFormArchiveStatusData
  ): CancelablePromise<UpdateConsentFormArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/consent-form/{consentFormId}/archive-status/{status}",
      path: {
        consentFormId: data.consentFormId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get all Patient Consent forms.
   * Get all PatientConsent forms list
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllPatientConsentForm(
    data: GetAllPatientConsentFormData
  ): CancelablePromise<GetAllPatientConsentFormResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-consent-form",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        patientUuid: data.patientUuid,
        searchString: data.searchString,
      },
    });
  }

  /**
   * Add Patient Consent
   * Add a Patient Consent forms into the system.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static addPatientConsent(data: AddPatientConsentData): CancelablePromise<AddPatientConsentResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-consent-form",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Get Signed Patient Consent form.
   * Get Signed PatientConsent forms Details
   * @param data The data for the request.
   * @param data.patientConsentFormUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientConsentFormById(
    data: GetPatientConsentFormByIdData
  ): CancelablePromise<GetPatientConsentFormByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-consent-form/{patientConsentFormUuid}",
      path: {
        patientConsentFormUuid: data.patientConsentFormUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get Consent form by consentFormId.
   * Get Consent form by consentFormUuid.
   * @param data The data for the request.
   * @param data.consentFormId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getConsentFormId(data: GetConsentFormIdData): CancelablePromise<GetConsentFormIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/consent-form/{consentFormId}",
      path: {
        consentFormId: data.consentFormId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class PatientCarePlanControllerService {
  /**
   * Get all patient care plans.
   * Get all care plan with provided criteria
   * @param data The data for the request.
   * @param data.patientId
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.searchString
   * @param data.timeFilter
   * @param data.carePlanStatus
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllCarePlans(data: GetAllCarePlansData): CancelablePromise<GetAllCarePlansResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-care-plan",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        searchString: data.searchString,
        patientId: data.patientId,
        timeFilter: data.timeFilter,
        carePlanStatus: data.carePlanStatus,
      },
    });
  }

  /**
   * update patient care plan
   * update a care plan with with reference range and status
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientCarePlan(
    data: UpdatePatientCarePlanData
  ): CancelablePromise<UpdatePatientCarePlanResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-care-plan",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Archive/Unarchived Care Plan
   * Change Care Plan archive status to either archived or unarchived.
   * @param data The data for the request.
   * @param data.patientCarePlanId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateCarePlanArchiveStatus(
    data: UpdateCarePlanArchiveStatusData
  ): CancelablePromise<UpdateCarePlanArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-care-plan/{patientCarePlanId}/archive-status/{status}",
      path: {
        patientCarePlanId: data.patientCarePlanId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * accept or reject patient care plan
   * Update a care plan status
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientCarePlanStatus(
    data: UpdatePatientCarePlanStatusData
  ): CancelablePromise<UpdatePatientCarePlanStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-care-plan/update-status",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateProgramGoalTrack(
    data: UpdateProgramGoalTrackData
  ): CancelablePromise<UpdateProgramGoalTrackResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-care-plan/track-goals",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static addProgramGoalTrack(data: AddProgramGoalTrackData): CancelablePromise<AddProgramGoalTrackResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-care-plan/track-goals",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * update vital reference range
   * update specific reference range by uuid
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateVitalReferenceRange(
    data: UpdateVitalReferenceRangeData
  ): CancelablePromise<UpdateVitalReferenceRangeResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-care-plan/reference-range",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * bulk assign care plan
   * bulk assign care plan to patient
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static bulkAssignCarePlans(data: BulkAssignCarePlansData): CancelablePromise<BulkAssignCarePlansResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-care-plan/bulk-assign",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * assign care plan
   * assign care plan to patient
   * @param data The data for the request.
   * @param data.patientId
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static assignCarePlan(data: AssignCarePlanData): CancelablePromise<AssignCarePlanResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-care-plan/assign/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.patientCarePlanId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientCarePlanById(
    data: GetPatientCarePlanByIdData
  ): CancelablePromise<GetPatientCarePlanByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-care-plan/{patientCarePlanId}",
      path: {
        patientCarePlanId: data.patientCarePlanId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.programGoalId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getProgramGoalTrackDetails(
    data: GetProgramGoalTrackDetailsData
  ): CancelablePromise<GetProgramGoalTrackDetailsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-care-plan/track-goals/{programGoalId}",
      path: {
        programGoalId: data.programGoalId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.patientId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientActiveCarePlan(
    data: GetPatientActiveCarePlanData
  ): CancelablePromise<GetPatientActiveCarePlanResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-care-plan/active/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class PatientAllergyControllerService {
  /**
   * Get all Patient Allergy.
   * Get all Patient Allergy list with search option on name, patientId and filter options on active, archive.
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sort
   * @param data.type
   * @param data.status
   * @param data.archive
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientAllergy(data: GetPatientAllergyData): CancelablePromise<GetPatientAllergyResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-allergy",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sort: data.sort,
        patientUuid: data.patientUuid,
        type: data.type,
        status: data.status,
        archive: data.archive,
        searchString: data.searchString,
      },
    });
  }

  /**
   * Update Patient Allergy
   * Update an existing Patient Allergy
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientAllergy(data: UpdatePatientAllergyData): CancelablePromise<UpdatePatientAllergyResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-allergy",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Patient Allergy
   * Add a new patient allergy into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createPatientAllergy(data: CreatePatientAllergyData): CancelablePromise<CreatePatientAllergyResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-allergy",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Archive Patient Allergy
   * Archive a Patient Allergy by its ID
   * @param data The data for the request.
   * @param data.patientAllergyId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updatePatientAllergyArchiveStatus(
    data: UpdatePatientAllergyArchiveStatusData
  ): CancelablePromise<UpdatePatientAllergyArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-allergy/{patientAllergyId}/archive-status/{status}",
      path: {
        patientAllergyId: data.patientAllergyId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Sync Patient Allergy
   * Add a sync patient allergy into the system
   * @param data The data for the request.
   * @param data.patientEhrId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static syncPatientAllergy(data: SyncPatientAllergyData): CancelablePromise<SyncPatientAllergyResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/patient-allergy/sync/{patientEhrId}",
      path: {
        patientEhrId: data.patientEhrId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Add Patient Bulk Allergy
   * Add a new patient allergy into the system
   * @param data The data for the request.
   * @param data.patientId
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createBulkPatientAllergy(
    data: CreateBulkPatientAllergyData
  ): CancelablePromise<CreateBulkPatientAllergyResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-allergy/bulk/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Get Patient Allergy by ID
   * Retrieve a Patient Allergy by its unique ID
   * @param data The data for the request.
   * @param data.patientAllergyId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientAllergyById(
    data: GetPatientAllergyByIdData
  ): CancelablePromise<GetPatientAllergyByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/patient-allergy/{patientAllergyId}",
      path: {
        patientAllergyId: data.patientAllergyId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class MedicalCodeControllerService {
  /**
   * Get all MedicalCode.
   * Get all MedicalCodes list for super admin with search option on name, deptId and filter options on active, archive.
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sort
   * @param data.type
   * @param data.active
   * @param data.archive
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getMedicalCodes(data: GetMedicalCodesData = {}): CancelablePromise<GetMedicalCodesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/medical-codes",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sort: data.sort,
        type: data.type,
        active: data.active,
        archive: data.archive,
        searchString: data.searchString,
      },
    });
  }

  /**
   * Update MedicalCode
   * Update an existing MedicalCode
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateMedicalCode(data: UpdateMedicalCodeData): CancelablePromise<UpdateMedicalCodeResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/medical-codes",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add MedicalCode
   * Add a new MedicalCode into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createMedicalCode(data: CreateMedicalCodeData): CancelablePromise<CreateMedicalCodeResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/medical-codes",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Update MedicalCode Status
   * Update the status of a MedicalCode by its ID
   * @param data The data for the request.
   * @param data.medicalCodeId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateMedicalCodeStatus(
    data: UpdateMedicalCodeStatusData
  ): CancelablePromise<UpdateMedicalCodeStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/medical-codes/{medicalCodeId}/status/{status}",
      path: {
        medicalCodeId: data.medicalCodeId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Archive MedicalCode
   * Archive a MedicalCode by its ID
   * @param data The data for the request.
   * @param data.medicalCodeId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateMedicalCodeArchiveStatus(
    data: UpdateMedicalCodeArchiveStatusData
  ): CancelablePromise<UpdateMedicalCodeArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/medical-codes/{medicalCodeId}/archive-status/{status}",
      path: {
        medicalCodeId: data.medicalCodeId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Upload a CSV file
   * Uploads a CSV file for the specified category and title
   * @param data The data for the request.
   * @param data.category
   * @param data.title
   * @param data.formData
   * @param data.providerGroupUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static uploadFile1(data: UploadFile1Data): CancelablePromise<UploadFile1Response> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/medical-codes/{category}/upload",
      path: {
        category: data.category,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        ProviderGroupUuid: data.providerGroupUuid,
        title: data.title,
      },
      formData: data.formData,
      mediaType: "multipart/form-data",
    });
  }

  /**
   * Get MedicalCode by ID
   * Retrieve a MedicalCode by its unique ID
   * @param data The data for the request.
   * @param data.medicalCodeId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getMedicalCodeById(data: GetMedicalCodeByIdData): CancelablePromise<GetMedicalCodeByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/medical-codes/{medicalCodeId}",
      path: {
        medicalCodeId: data.medicalCodeId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class LocationControllerService {
  /**
   * Get all Locations.
   * Get all Locations list for super admin with search option on name, locationId and filter options on active, archive and physical addresses state.
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.state
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllLocations(data: GetAllLocationsData = {}): CancelablePromise<GetAllLocationsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/location",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        state: data.state,
        searchString: data.searchString,
      },
    });
  }

  /**
   * Update Location
   * Edit Location with editable fields as
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateLocation(data: UpdateLocationData): CancelablePromise<UpdateLocationResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/location",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Location
   * Add a new Location into the system.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createLocation(data: CreateLocationData): CancelablePromise<CreateLocationResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/location",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Archive/Un-archive Location
   * Change Location  archive status to either archived or unarchived.
   * @param data The data for the request.
   * @param data.locationId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateLocationArchiveStatus(
    data: UpdateLocationArchiveStatusData
  ): CancelablePromise<UpdateLocationArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/location/{locationId}/archive-status/{status}",
      path: {
        locationId: data.locationId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get Location by locationUuid.
   * Get Location by locationUuid.
   * @param data The data for the request.
   * @param data.locationId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getLocationById(data: GetLocationByIdData): CancelablePromise<GetLocationByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/location/{locationId}",
      path: {
        locationId: data.locationId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class DeviceControllerService {
  /**
   * Get all Devices
   * Fetch all Devices with optional search and filter parameters.
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.searchString
   * @param data.type
   * @param data.category
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllDevices(data: GetAllDevicesData = {}): CancelablePromise<GetAllDevicesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/device",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        searchString: data.searchString,
        type: data.type,
        category: data.category,
      },
    });
  }

  /**
   * Update Device
   * Update an existing Device by modifying its details.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateDevice(data: UpdateDeviceData): CancelablePromise<UpdateDeviceResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/device",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Add Device
   * Add a new Device into the system.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createDevice(data: CreateDeviceData): CancelablePromise<CreateDeviceResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/device",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Update Device Status
   * Update the status of a Device by its UUID
   * @param data The data for the request.
   * @param data.deviceUuid
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateDeviceStatus(data: UpdateDeviceStatusData): CancelablePromise<UpdateDeviceStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/device/{deviceUuid}/status/{status}",
      path: {
        deviceUuid: data.deviceUuid,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Archive/Unarchive Device
   * Change a Device's archive status.
   * @param data The data for the request.
   * @param data.deviceUuid
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateDeviceArchiveStatus(
    data: UpdateDeviceArchiveStatusData
  ): CancelablePromise<UpdateDeviceArchiveStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/device/{deviceUuid}/archive-status/{status}",
      path: {
        deviceUuid: data.deviceUuid,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Assign Device
   * Assign a new Device in the provider group.
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static assignDevice(data: AssignDeviceData = {}): CancelablePromise<AssignDeviceResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/device/assign/{deviceId}",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get Device by UUID
   * Get a Device using its UUID.
   * @param data The data for the request.
   * @param data.deviceUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getDeviceById(data: GetDeviceByIdData): CancelablePromise<GetDeviceByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/device/{deviceUuid}",
      path: {
        deviceUuid: data.deviceUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class ClinicalNoteControllerService {
  /**
   * Update Clinical Note or consultation note
   * Edit a Clinical Note with editable fields such as note and status. Allows signing off the note if applicable.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.signOff
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateClinicalNote(data: UpdateClinicalNoteData): CancelablePromise<UpdateClinicalNoteResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/clinical-note",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        signOff: data.signOff,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createClinicalNote(data: CreateClinicalNoteData): CancelablePromise<CreateClinicalNoteResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/clinical-note",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Export Clinical Note as PDF and Email to Nurse
   * Export a Clinical Note as a password-protected PDF and email it to the assigned nurse. The password will be firstName_lastName_mrn of the patient.
   * @param data The data for the request.
   * @param data.clinicalNoteUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static exportAndEmailPdf(data: ExportAndEmailPdfData): CancelablePromise<ExportAndEmailPdfResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/clinical-note/{clinicalNoteUuid}/export-pdf",
      path: {
        clinicalNoteUuid: data.clinicalNoteUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.clinicalNoteUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getClinicalNoteByUuid(
    data: GetClinicalNoteByUuidData
  ): CancelablePromise<GetClinicalNoteByUuidResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/clinical-note/{clinicalNoteUuid}",
      path: {
        clinicalNoteUuid: data.clinicalNoteUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.appointmentId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getClinicalNoteByAppointmentId(
    data: GetClinicalNoteByAppointmentIdData
  ): CancelablePromise<GetClinicalNoteByAppointmentIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/clinical-note/appointment/{appointmentId}",
      path: {
        appointmentId: data.appointmentId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class CarePlanControllerService {
  /**
   * Get all care plans.
   * Get all care plan with provided criteria
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.status
   * @param data.archive
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllCarePlans1(data: GetAllCarePlans1Data = {}): CancelablePromise<GetAllCarePlans1Response> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/care-plan",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        status: data.status,
        archive: data.archive,
        searchString: data.searchString,
      },
    });
  }

  /**
   * update care plan
   * update a care plan with with its details
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateCarePlan(data: UpdateCarePlanData): CancelablePromise<UpdateCarePlanResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/care-plan",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * create care plan
   * create a care plan with with its details
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createCarePlan(data: CreateCarePlanData): CancelablePromise<CreateCarePlanResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/care-plan",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Enable/Disable Care Plan
   * Change care plan status to either active or inactive
   * @param data The data for the request.
   * @param data.carePlanId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateUserStatus(data: UpdateUserStatusData): CancelablePromise<UpdateUserStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/care-plan/{carePlanId}/status/{status}",
      path: {
        carePlanId: data.carePlanId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Archive/Unarchived Care Plan
   * Change Care Plan archive status to either archived or unarchived.
   * @param data The data for the request.
   * @param data.carePlanId
   * @param data.status
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateCarePlanArchiveStatus1(
    data: UpdateCarePlanArchiveStatus1Data
  ): CancelablePromise<UpdateCarePlanArchiveStatus1Response> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/care-plan/{carePlanId}/archive-status/{status}",
      path: {
        carePlanId: data.carePlanId,
        status: data.status,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * get care plan
   * get care plan details by uuid
   * @param data The data for the request.
   * @param data.carePlanId
   * @param data.globalCarePlan
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getCarePlanById(data: GetCarePlanByIdData): CancelablePromise<GetCarePlanByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/care-plan/{carePlanId}",
      path: {
        carePlanId: data.carePlanId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        globalCarePlan: data.globalCarePlan,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllReferenceRanges(
    data: GetAllReferenceRangesData = {}
  ): CancelablePromise<GetAllReferenceRangesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/care-plan/reference-ranges",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get protocol based on out-of-range type
   * @param data The data for the request.
   * @param data.protocolType
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllProtocols(data: GetAllProtocolsData): CancelablePromise<GetAllProtocolsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/care-plan/protocols/{protocolType}",
      path: {
        protocolType: data.protocolType,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class AppointmentControllerService {
  /**
   * Get all appointments.
   * Get all appointments with filter options on patient, provider
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.patientId
   * @param data.nurseId
   * @param data.providerId
   * @param data.assigned
   * @param data.filter
   * @param data.startDate
   * @param data.endDate
   * @param data.status
   * @param data.mode
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllAppointments(data: GetAllAppointmentsData = {}): CancelablePromise<GetAllAppointmentsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/appointment",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        patientId: data.patientId,
        nurseId: data.nurseId,
        providerId: data.providerId,
        assigned: data.assigned,
        filter: data.filter,
        startDate: data.startDate,
        endDate: data.endDate,
        status: data.status,
        mode: data.mode,
      },
    });
  }

  /**
   * Update Appointment
   * Update Appointment into the system
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateAppointment(data: UpdateAppointmentData): CancelablePromise<UpdateAppointmentResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/appointment",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Book appointment
   * Book a appointment of patient with particular provider at particular location.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static createAppointment(data: CreateAppointmentData): CancelablePromise<CreateAppointmentResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/appointment",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Update appointment status.
   * Update appointment status for reschedule or cancel.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static updateAppointmentStatus(
    data: UpdateAppointmentStatusData
  ): CancelablePromise<UpdateAppointmentStatusResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/appointment/update-status",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static rescheduleAppointment(
    data: RescheduleAppointmentData
  ): CancelablePromise<RescheduleAppointmentResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/appointment/reschedule",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * broadcast appointment
   * broadcast appointment status
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static broadCastAppointment(data: BroadCastAppointmentData): CancelablePromise<BroadCastAppointmentResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/master/appointment/broadcast",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Book appointment
   * Book a appointment of patient with particular provider at particular location.
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static bookAppointmentRequest(
    data: BookAppointmentRequestData
  ): CancelablePromise<BookAppointmentRequestResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/appointment/request",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static sendInvitationLink(data: SendInvitationLinkData): CancelablePromise<SendInvitationLinkResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/appointment/invite-guest",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Get appointment by ID
   * Get appointment by UUID.
   * @param data The data for the request.
   * @param data.appointmentId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAppointmentById(data: GetAppointmentByIdData): CancelablePromise<GetAppointmentByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/appointment/{appointmentId}",
      path: {
        appointmentId: data.appointmentId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.email
   * @param data.schema
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static escalateAppointment(data: EscalateAppointmentData): CancelablePromise<EscalateAppointmentResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/appointment/escalate",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        email: data.email,
        schema: data.schema,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.startDate
   * @param data.endDate
   * @param data.nurseId
   * @param data.patientUuid
   * @param data.filter
   * @param data.type
   * @param data.status
   * @param data.providerId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAppointmentList(data: GetAppointmentListData): CancelablePromise<GetAppointmentListResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/appointment/calendar",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        nurseId: data.nurseId,
        patientUuid: data.patientUuid,
        startDate: data.startDate,
        endDate: data.endDate,
        filter: data.filter,
        type: data.type,
        status: data.status,
        providerId: data.providerId,
      },
    });
  }
}

export class AvailabilityControllerService {
  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static setProviderAvailabilitySetting(
    data: SetProviderAvailabilitySettingData
  ): CancelablePromise<SetProviderAvailabilitySettingResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/provider/availability-setting",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.providerUuid
   * @param data.duration
   * @param data.page
   * @param data.size
   * @param data.startDate
   * @param data.endDate
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getProviderSlots(data: GetProviderSlotsData): CancelablePromise<GetProviderSlotsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider/{providerUuid}/slots/{duration}",
      path: {
        providerUuid: data.providerUuid,
        duration: data.duration,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        startDate: data.startDate,
        endDate: data.endDate,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.providerUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getProviderAvailabilitySetting(
    data: GetProviderAvailabilitySettingData
  ): CancelablePromise<GetProviderAvailabilitySettingResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/provider/{providerUuid}/availability-setting",
      path: {
        providerUuid: data.providerUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class PatientTrainingControllerService {
  /**
   * update training status
   * update training status
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static addUpdateTrainedDevice(
    data: AddUpdateTrainedDeviceData
  ): CancelablePromise<AddUpdateTrainedDeviceResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/patient-training",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }
}

export class NotificationControllerService {
  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static sendDirectedMessage(data: SendDirectedMessageData): CancelablePromise<SendDirectedMessageResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/notification",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.uuid
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static testNotif(data: TestNotifData): CancelablePromise<TestNotifResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/notification/test-notif/{uuid}",
      path: {
        uuid: data.uuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.image
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static testNotifImage(data: TestNotifImageData): CancelablePromise<TestNotifImageResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/notification/test-notif-with-image",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        image: data.image,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }
}

export class FirebaseMessageControllerService {
  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns string OK
   * @throws ApiError
   */
  public static sendMulticastMessage(data: SendMulticastMessageData): CancelablePromise<SendMulticastMessageResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/message/send",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.fcmToken
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static saveToken(data: SaveTokenData): CancelablePromise<SaveTokenResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/message/save",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        fcmToken: data.fcmToken,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.fcmToken
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static deleteToken(data: DeleteTokenData): CancelablePromise<DeleteTokenResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/master/message/remove",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        fcmToken: data.fcmToken,
      },
    });
  }
}

export class AiControllerService {
  /**
   * Store Nurse Action Statistics
   * Store nurse action statistic based on month and year with cronjob
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static storeNurseActionStatistics(
    data: StoreNurseActionStatisticsData = {}
  ): CancelablePromise<StoreNurseActionStatisticsResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/store-nurse-action-statistic",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Setup Avatar
   * Setup the Nurse Voice
   * @param data The data for the request.
   * @param data.xTenantId
   * @param data.requestBody
   * @returns Response OK
   * @throws ApiError
   */
  public static avqSetupAvatar(data: AvqSetupAvatarData = {}): CancelablePromise<AvqSetupAvatarResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/setup-avatar",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Setup Avatar Edge
   * Setup the Recommended Voice
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static avqSetupAvatarEdge(data: AvqSetupAvatarEdgeData): CancelablePromise<AvqSetupAvatarEdgeResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/setup-avatar-edge",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Alert reminder
   * Remind the patient, part of smart alert
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static sendPatientAlert(data: SendPatientAlertData = {}): CancelablePromise<SendPatientAlertResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/notification-alert",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Medication Audio
   * Generate and play the audio describing the medications
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static llmauGenerateMedicationAudio(
    data: LlmauGenerateMedicationAudioData
  ): CancelablePromise<LlmauGenerateMedicationAudioResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/medication-audio",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Generate Avatar
   * Generate the Nurse Avatar
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static vygenGenerateAvatar(data: VygenGenerateAvatarData): CancelablePromise<VygenGenerateAvatarResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/generate-avatar",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Store Nurse Action Statistics
   * Store nurse action statistic based on month and year with cronjob
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static generateAudioMonthlyQuickSummary(
    data: GenerateAudioMonthlyQuickSummaryData = {}
  ): CancelablePromise<GenerateAudioMonthlyQuickSummaryResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/generate-audio-nurse-action-statistic",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Detect Face
   * Detecting the selfie taken by nurse to make sure it is a human face
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static vygenDetectFace(data: VygenDetectFaceData): CancelablePromise<VygenDetectFaceResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/detect-face",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Chat with AI Nurse
   * Patient chat with the AI Nurse
   * @param data The data for the request.
   * @param data.providerId
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static llmauChatWithMyNurse(data: LlmauChatWithMyNurseData): CancelablePromise<LlmauChatWithMyNurseResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/chat-my-nurse/{providerId}",
      path: {
        providerId: data.providerId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAiCarePlan(data: GetAiCarePlanData): CancelablePromise<GetAiCarePlanResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/care_plan_suggester",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * @param data The data for the request.
   * @param data.requestBody
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAiCarePlanList(data: GetAiCarePlanListData): CancelablePromise<GetAiCarePlanListResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/bulk_care_plan_suggester",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Chat with AI Nurse via Audio
   * Patient chat with the AI Nurse with their recorded voice
   * @param data The data for the request.
   * @param data.providerId
   * @param data.xTenantId
   * @param data.requestBody
   * @returns Response OK
   * @throws ApiError
   */
  public static llmauAudioChatWithMyNurse(
    data: LlmauAudioChatWithMyNurseData
  ): CancelablePromise<LlmauAudioChatWithMyNurseResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/audio-chat-my-nurse/{providerId}",
      path: {
        providerId: data.providerId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Assess Nurse Performance
   * Assess nurse performance at the end of month with cronjob
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static assessNursePerformance(
    data: AssessNursePerformanceData = {}
  ): CancelablePromise<AssessNursePerformanceResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/assess-nurse-performance",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Analyze Medication
   * Patient upload the medications image, the information will be stored and patient will receive notifications for it
   * @param data The data for the request.
   * @param data.xTenantId
   * @param data.requestBody
   * @returns Response OK
   * @throws ApiError
   */
  public static llmauAnalyzeMedication(
    data: LlmauAnalyzeMedicationData = {}
  ): CancelablePromise<LlmauAnalyzeMedicationResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/master/ai_connection/analyze-medication",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      body: data.requestBody,
      mediaType: "application/json",
    });
  }

  /**
   * Send Nurse Assessment
   * Send Nurse Monthly Performance through email
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static sendNurseAssessmentPerformance(
    data: SendNurseAssessmentPerformanceData = {}
  ): CancelablePromise<SendNurseAssessmentPerformanceResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/send-nurse-performance",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get Patient Reports
   * Get patient reports based on month and year
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.year
   * @param data.month
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientReports(data: GetPatientReportsData): CancelablePromise<GetPatientReportsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/patient-reports/{patientUuid}",
      path: {
        patientUuid: data.patientUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        year: data.year,
        month: data.month,
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
      },
    });
  }

  /**
   * Get recommended default voice
   * Get recommended default voice and preview the sample for nurse
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static llmauGetVoiceRecommendation(
    data: LlmauGetVoiceRecommendationData = {}
  ): CancelablePromise<LlmauGetVoiceRecommendationResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/nurse-voice-recommendation",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get Nurse Report
   * Get nurse reports for alerts
   * @param data The data for the request.
   * @param data.reportUuid
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getNurseReports(data: GetNurseReportsData): CancelablePromise<GetNurseReportsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/nurse-reports/{reportUuid}",
      path: {
        reportUuid: data.reportUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get Nurse Assessment
   * Get Nurse Monthly Performance
   * @param data The data for the request.
   * @param data.providerId
   * @param data.year
   * @param data.month
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getNurseAssessmentPerformance(
    data: GetNurseAssessmentPerformanceData
  ): CancelablePromise<GetNurseAssessmentPerformanceResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/nurse-performance/{providerId}",
      path: {
        providerId: data.providerId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        year: data.year,
        month: data.month,
      },
    });
  }

  /**
   * The Patient's Nurse Avatar Data
   * Getting the nurse avatar data for the patient
   * @param data The data for the request.
   * @param data.providerId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static llmauGetMyNurseAvatar(
    data: LlmauGetMyNurseAvatarData
  ): CancelablePromise<LlmauGetMyNurseAvatarResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/nurse-avatar/{providerId}",
      path: {
        providerId: data.providerId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get Nurse Action Statistics
   * Get nurse action statistic based on month and year
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.year
   * @param data.month
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getNurseActionStatistics(
    data: GetNurseActionStatisticsData
  ): CancelablePromise<GetNurseActionStatisticsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/nurse-action-statistic/{patientUuid}",
      path: {
        patientUuid: data.patientUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        year: data.year,
        month: data.month,
      },
    });
  }

  /**
   * The Nurse Avatar Data
   * Getting the nurse avatar data for the nurse
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static llmauGetAvatar(data: LlmauGetAvatarData = {}): CancelablePromise<LlmauGetAvatarResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/my-avatar",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get Nurse Action in Excel
   * Get Nurse action summary generated in Excel
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.year
   * @param data.month
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getNurseActionInExcel(
    data: GetNurseActionInExcelData
  ): CancelablePromise<GetNurseActionInExcelResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/excel-nurse-action-download/{patientUuid}",
      path: {
        patientUuid: data.patientUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        year: data.year,
        month: data.month,
      },
    });
  }

  /**
   * Get Chatbot Reports
   * Get summaries of the health alerts & intervention in this month
   * @param data The data for the request.
   * @param data.patientUuid
   * @param data.year
   * @param data.month
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getChatbotReports(data: GetChatbotReportsData): CancelablePromise<GetChatbotReportsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/chatbot-reports/{patientUuid}",
      path: {
        patientUuid: data.patientUuid,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        year: data.year,
        month: data.month,
      },
    });
  }

  /**
   * Get chatbot messages history
   * Fetches the patient's chatbot messages with AI nurse
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getChatbotHistory(data: GetChatbotHistoryData = {}): CancelablePromise<GetChatbotHistoryResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/chatbot-message-history",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Get Avatar Video
   * Get avatar video by title given
   * @param data The data for the request.
   * @param data.providerId
   * @param data.videoTitle
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static llmauGetAvatarVideoByTitle(
    data: LlmauGetAvatarVideoByTitleData
  ): CancelablePromise<LlmauGetAvatarVideoByTitleResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/avatar-video/{providerId}",
      path: {
        providerId: data.providerId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        videoTitle: data.videoTitle,
      },
    });
  }

  /**
   * Get Alert Audio
   * get alert audio to be played in chat when given the title
   * @param data The data for the request.
   * @param data.providerId
   * @param data.videoTitle
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static avqGetAlertAudio(data: AvqGetAlertAudioData): CancelablePromise<AvqGetAlertAudioResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ai_connection/alert-audio/{providerId}",
      path: {
        providerId: data.providerId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        videoTitle: data.videoTitle,
      },
    });
  }
}

export class VitalControllerService {
  /**
   * Get Vitals
   * Get all Vital records with search by name
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sort
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getPatientVitals(data: GetPatientVitalsData = {}): CancelablePromise<GetPatientVitalsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/vital",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sort: data.sort,
        searchString: data.searchString,
      },
    });
  }
}

export class ZoomControllerService {
  /**
   * @param data The data for the request.
   * @param data.roomId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAuthToken(data: GetAuthTokenData): CancelablePromise<GetAuthTokenResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/token/{roomId}",
      path: {
        roomId: data.roomId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.eventKey
   * @param data.xTenantId
   * @returns SseEmitter OK
   * @throws ApiError
   */
  public static subscribe(data: SubscribeData): CancelablePromise<SubscribeResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/event/subscribe/{eventKey}",
      path: {
        eventKey: data.eventKey,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * @param data The data for the request.
   * @param data.eventKey
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static emit(data: EmitData): CancelablePromise<EmitResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/event/emit/{eventKey}",
      path: {
        eventKey: data.eventKey,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class LicenseStateControllerService {
  /**
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.searchString
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllLicensedStates(
    data: GetAllLicensedStatesData = {}
  ): CancelablePromise<GetAllLicensedStatesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/license-state",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        searchString: data.searchString,
      },
    });
  }
}

export class EqrControllerService {
  /**
   * @param data The data for the request.
   * @param data.therapyId
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getInfusionTherapyById(
    data: GetInfusionTherapyByIdData
  ): CancelablePromise<GetInfusionTherapyByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/eqr/{therapyId}",
      path: {
        therapyId: data.therapyId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class EligibilityCheckControllerService {
  /**
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns boolean OK
   * @throws ApiError
   */
  public static generatePatientRpmBill(
    data: GeneratePatientRpmBillData = {}
  ): CancelablePromise<GeneratePatientRpmBillResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/eligibility",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class EhrProviderControllerService {
  /**
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllEhrProviders(data: GetAllEhrProvidersData = {}): CancelablePromise<GetAllEhrProvidersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class EhrControllerService {
  /**
   * Practitioner
   * Get practitioner by practitionerId
   * @param data The data for the request.
   * @param data.practitionerId
   * @param data.xTenantId
   * @returns Provider OK
   * @throws ApiError
   */
  public static getPractitionerByProviderId(
    data: GetPractitionerByProviderIdData
  ): CancelablePromise<GetPractitionerByProviderIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-practitioner/{practitionerId}",
      path: {
        practitionerId: data.practitionerId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Search Practitioners
   * Search practitioners by family name
   * @param data The data for the request.
   * @param data.name
   * @param data.xTenantId
   * @returns Provider OK
   * @throws ApiError
   */
  public static searchPractitioners(data: SearchPractitionersData): CancelablePromise<SearchPractitionersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-practitioner/search",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        name: data.name,
      },
    });
  }

  /**
   * Patient vital
   * Get Patient Observation vital signs by patientId
   * @param data The data for the request.
   * @param data.patientId
   * @param data.date
   * @param data.xTenantId
   * @returns PatientVital OK
   * @throws ApiError
   */
  public static getPatientVitals2(data: GetPatientVitals2Data): CancelablePromise<GetPatientVitals2Response> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-patient/{patientId}/vital-sign",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        date: data.date,
      },
    });
  }

  /**
   * Patient Encounter Diagnosis
   * Get Patient Encounter Diagnosis by patientId
   * @param data The data for the request.
   * @param data.patientId
   * @param data.xTenantId
   * @returns PatientDiagnosis OK
   * @throws ApiError
   */
  public static getPatientEncounterDiagnosisByPatientId(
    data: GetPatientEncounterDiagnosisByPatientIdData
  ): CancelablePromise<GetPatientEncounterDiagnosisByPatientIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-patient/{patientId}/encounter-diagnosis",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Patient Allergy
   * Get allergies by patientId
   * @param data The data for the request.
   * @param data.patientId
   * @param data.clinicalStatus
   * @param data.recordedDate
   * @param data.xTenantId
   * @returns PatientAllergy OK
   * @throws ApiError
   */
  public static getAllergiesByPatientId(
    data: GetAllergiesByPatientIdData
  ): CancelablePromise<GetAllergiesByPatientIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-patient/{patientId}/allergies",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        clinicalStatus: data.clinicalStatus,
        recordedDate: data.recordedDate,
      },
    });
  }

  /**
   * Search Patients
   * Search patients by family name and birthdate
   * @param data The data for the request.
   * @param data.organisationId
   * @param data.family
   * @param data.given
   * @param data.birthdate
   * @param data.patientId
   * @param data.xTenantId
   * @returns Patient OK
   * @throws ApiError
   */
  public static searchPatients(data: SearchPatientsData): CancelablePromise<SearchPatientsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-patient/search",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        family: data.family,
        given: data.given,
        birthdate: data.birthdate,
        patientId: data.patientId,
        organisationId: data.organisationId,
      },
    });
  }

  /**
   * Organization
   * Get organization by practiceId
   * @param data The data for the request.
   * @param data.practiceId
   * @param data.xTenantId
   * @returns ProviderGroup OK
   * @throws ApiError
   */
  public static getOrganizationByPracticeId(
    data: GetOrganizationByPracticeIdData
  ): CancelablePromise<GetOrganizationByPracticeIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-organization/{practiceId}",
      path: {
        practiceId: data.practiceId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Medication Request
   * Get medication request by patient id
   * @param data The data for the request.
   * @param data.patientId
   * @param data.status
   * @param data.xTenantId
   * @returns unknown OK
   * @throws ApiError
   */
  public static getMedicationRequestByPatientId(
    data: GetMedicationRequestByPatientIdData
  ): CancelablePromise<GetMedicationRequestByPatientIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-medication-request/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        status: data.status,
      },
    });
  }

  /**
   * Medication
   * Get medication by patient id
   * @param data The data for the request.
   * @param data.patientId
   * @param data.status
   * @param data.whenhandedover
   * @param data.xTenantId
   * @returns unknown OK
   * @throws ApiError
   */
  public static getMedicationDispenseByPatientId(
    data: GetMedicationDispenseByPatientIdData
  ): CancelablePromise<GetMedicationDispenseByPatientIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-medication-dispense/{patientId}",
      path: {
        patientId: data.patientId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        status: data.status,
        whenhandedover: data.whenhandedover,
      },
    });
  }

  /**
   * Location
   * Get location by locationId
   * @param data The data for the request.
   * @param data.locationId
   * @param data.xTenantId
   * @returns Location OK
   * @throws ApiError
   */
  public static getLocationByLocationId(
    data: GetLocationByLocationIdData
  ): CancelablePromise<GetLocationByLocationIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-location/{locationId}",
      path: {
        locationId: data.locationId,
      },
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }

  /**
   * Authorization
   * Get access token
   * @param data The data for the request.
   * @param data.xTenantId
   * @returns EhrAccessToken OK
   * @throws ApiError
   */
  public static getAccessToken1(data: GetAccessToken1Data = {}): CancelablePromise<GetAccessToken1Response> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/ehr-access-token",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
    });
  }
}

export class ConditionControllerService {
  /**
   * Get all condition .
   * Get all care plan condition list with search option on name.
   * @param data The data for the request.
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sort
   * @param data.name
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllConditions(data: GetAllConditionsData = {}): CancelablePromise<GetAllConditionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/condition",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sort: data.sort,
        name: data.name,
      },
    });
  }
}

export class BillingControllerService {
  /**
   * Get all bills by patient ID
   * Get all billing records for a specific patient with pagination and sorting options
   * @param data The data for the request.
   * @param data.patientId
   * @param data.billingCycle
   * @param data.page
   * @param data.size
   * @param data.sortBy
   * @param data.sortDirection
   * @param data.startDate
   * @param data.endDate
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllBillsByPatientId(
    data: GetAllBillsByPatientIdData
  ): CancelablePromise<GetAllBillsByPatientIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/billing",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        patientId: data.patientId,
        page: data.page,
        size: data.size,
        sortBy: data.sortBy,
        sortDirection: data.sortDirection,
        billingCycle: data.billingCycle,
        startDate: data.startDate,
        endDate: data.endDate,
      },
    });
  }
}

export class ActivityControllerService {
  /**
   * Get all activities.
   * Get all lab activities list with search option on name, patientId and filter options on active, archive.
   * @param data The data for the request.
   * @param data.search
   * @param data.xTenantId
   * @returns Response OK
   * @throws ApiError
   */
  public static getAllActivities(data: GetAllActivitiesData = {}): CancelablePromise<GetAllActivitiesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/master/activity",
      headers: {
        "X-TENANT-ID": data.xTenantId,
      },
      query: {
        search: data.search,
      },
    });
  }
}
