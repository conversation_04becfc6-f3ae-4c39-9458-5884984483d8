// This file is auto-generated by @hey-api/openapi-ts

export const $Response = {
  type: "object",
  properties: {
    date: {
      type: "string",
      format: "date-time",
    },
    code: {
      type: "string",
      enum: [
        "INTERNAL_ERROR",
        "ACCESS_DENIED",
        "BAD_REQUEST",
        "NOT_FOUND",
        "CREATED",
        "UNSUPPORTED_MEDIA_TYPE",
        "OK",
        "UNAUTHORIZED",
        "SERVICE_UNAVAILABLE",
        "ENTITY",
        "DB_ERROR",
        "IAM_ERROR",
        "AWS_ERROR",
        "USER_ARCHIVED",
        "USER_UNARCHIVED",
        "USER_NOT_EXIST",
        "BILLING_INVOICE_STATEMENT",
        "INVALID_CREDENTIALS",
        "LOGIN_FAILED",
        "INVALID_REFRESH_TOKEN",
        "LOGOUT_RESPONSE",
        "LOGOUT_FAILED",
        "SET_PASSWORD_RESPONSE",
        "INVALID_PASSWORD_LINK",
        "INVALID_PASSWORD",
        "INVALID_OLD_PASSWORD",
        "RESET_PASSWORD_FAILED",
        "CHANGE_PASSWORD_RESPONSE",
        "VERIFY_PASSWORD_LINK_RESPONSE",
        "FORGOT_PASSWORD_RESPONSE",
        "RESEND_INVITE_EMAIL_RESPONSE",
        "USER_CREATED",
        "USER_NOT_FOUND",
        "USER_EMAIL_NOT_FOUND",
        "UPDATE_USER_PROFILE_RESPONSE",
        "USER_ENABLED",
        "USER_DISABLED",
        "DUPLICATE_EMAIL_ERROR",
        "CHANGE_AVATAR_RESPONSE",
        "GENERATE_AVATAR_RESPONSE",
        "AVATAR_VOICE_RESPONSE",
        "USER_EMAIL_REQUIRED",
        "USER_EMAIL_UPDATE_ERROR",
        "INVALID_USER_ROLE",
        "USER_EMAIL_VERIFIED",
        "SIGNED_UP",
        "USER_INACTIVE",
        "EMPTY_EMAIL",
        "MAIL_SENT_SUCCESSFULLY",
        "RESEND_OTP",
        "INVALID_OTP",
        "PATIENT_ONBOARD_SUCCESSFULLY",
        "PROVIDER_GROUP_CREATED",
        "PROVIDER_GROUP_UPDATED",
        "FAILED_UPDATE_PROVIDER_GROUP",
        "PROVIDER_GROUP_ENABLED",
        "PROVIDER_GROUP_DISABLED",
        "PROVIDER_GROUP_ARCHIVED",
        "PROVIDER_GROUP_UNARCHIVED",
        "FCM_SAVED",
        "FCM_DELETED",
        "REALMS_DELETED",
        "DEVICE_CREATED",
        "DEVICE_UPDATED",
        "DEVICE_ENABLED",
        "DEVICE_DISABLED",
        "DEVICE_ARCHIVED",
        "DEVICE_UNARCHIVED",
        "DEVICE_NOT_FOUND",
        "ALERT_CREATED",
        "ALERT_UPDATED",
        "LOCATION_CREATED",
        "LOCATION_UPDATED",
        "LOCATION_NOT_FOUND",
        "LOCATION_ENABLED",
        "LOCATION_DISABLED",
        "LOCATION_ARCHIVED",
        "LOCATION_UNARCHIVED",
        "DEPARTMENT_CREATED",
        "DEPARTMENT_UPDATED",
        "DEPARTMENT_ENABLED",
        "DEPARTMENT_DISABLED",
        "DEPARTMENT_ARCHIVED",
        "DEPARTMENT_UNARCHIVED",
        "PROVIDER_CREATED",
        "PROVIDER_UPDATED",
        "PROVIDER_ENABLED",
        "PROVIDER_DISABLED",
        "PROVIDER_ARCHIVED",
        "PROVIDER_UNARCHIVED",
        "NPI_ALREADY_EXIST",
        "PROVIDER_STATUS_UPDATED",
        "PROVIDER_ARCHIVE_STATUS_UPDATED",
        "PROVIDER_ONBOARDING_STATUS_UPDATED",
        "NURSE_CREATED",
        "NURSE_UPDATED",
        "NURSE_AVATAR_STATUS_UPDATED",
        "PATIENT_CREATED",
        "PATIENT_UPDATE",
        "PATIENT_STATUS_UPDATED",
        "PATIENT_ARCHIVE_STATUS_UPDATED",
        "PATIENT_UNARCHIVE_STATUS_UPDATED",
        "PATIENT_ENABLE_DISABLE_RESPONSE",
        "FAILED_PATIENT_UPDATE",
        "PATIENT_NOT_FOUND",
        "USER_MRN_UPDATE_ERROR",
        "PATIENT_DATA_FETCHED",
        "PATIENT_DOCUMENT_REMOVED",
        "UPLOAD_DOC_INVALID_ERROR",
        "FAILED_PATIENT_STATUS_UPDATE",
        "PATIENT_ARCHIVE",
        "FAILED_PATIENT_ARCHIVE",
        "INVALID_BIRTHDATE",
        "FAILED_UPDATE_PATIENT_INSURANCE",
        "ADDED_TO_WAITING_LIST",
        "PATIENT_ENABLED",
        "PATIENT_DISABLED",
        "PATIENT_ARCHIVED",
        "PATIENT_UNARCHIVED",
        "TENANT_CONFIGURATION_CREATED",
        "TENANT_CONFIGURATION_UPDATED",
        "FEE_AMOUNT_ADDED",
        "FEE_AMOUNT_UPDATED",
        "FEE_AMOUNT_STATUS_CHANGED",
        "FEE_AMOUNT_ALREADY_EXISTS",
        "FEE_AMOUNT_NOT_FOUND",
        "FEE_DELETED",
        "INVALID_MEDICAL_CODE",
        "INSURANCEPAYER_NOT_FOUND",
        "INSURANCE_POLICY_NOT_FOUND",
        "TOO_MANY_INSURANCES",
        "INSURANCE_NOT_FOUND",
        "SECONDARY_INSURANCE_NOT_FOUND",
        "INSURANCETYPE_UPDATED",
        "CANNOT_UPDATE_INSURANCETYPE",
        "PATIENT_INSURANCE_UPDATE",
        "SECONDARY_INSURANCE_REMOVED",
        "DUPLICATE_INSURANCE_ERROR",
        "ELIGIBILITY_NOT_ALLOWED",
        "INTAKE_FORM_ADDED",
        "INTAKE_FORM_UPDATED",
        "PATIENT_DOCUMENT_UPLOADED",
        "REASON_IS_MANDATORY",
        "CHARGEABLE_IS_MANDATORY",
        "INTAKE_NEEDED",
        "CONSENT_FORM_TEMPLATE_ADDED",
        "CONSENT_FORM_TEMPLATE_UPDATED",
        "CONSENT_FORM_TEMPLATE_ARCHIVED",
        "CONSENT_FORM_TEMPLATE_UNARCHIVED",
        "CONSENT_FORM_TEMPLATE_ENABLE",
        "CONSENT_FORM_TEMPLATE_DISABLED",
        "CONSENT_FORM_TEMPLATE_NOT_FOUND",
        "PATIENT_CONSENT_STATUS_UPDATED",
        "CONSENT_ARCHIVE_STATUS",
        "ADDED_AVAILABILITY",
        "CONSULT_TIME_CONFLICT",
        "BOOKED_APPOINTMENT_SLOT",
        "SLOT_NOT_AVAILABLE",
        "AVAILABILITY_NOT_FOUND",
        "DAY_SLOT_NOT_FOUND",
        "SLOT_NOT_FOUND",
        "INVALID_TIME_DURATION",
        "PAST_START_TIME",
        "SLOT_ALREADY_BOOKED",
        "APPOINTMENT_CREATED",
        "APPOINTMENT_DATA_FETCHED",
        "APPOINTMENT_NOT_FOUND",
        "APPOINTMENT_UPDATED",
        "APPOINTMENT_CONFIRMED",
        "NEW_APPT_ALREADY_EXIST",
        "NEW_APPT_NOT_EXIST",
        "EXISTING_APPOINTMENT_NOT_FOUND",
        "RESCHEDULE_NOT_ALLOWED",
        "APPOINTMENT_RESCHEDULED",
        "APPOINTMENT_RESCHEDULED_WITH_CHARGE",
        "CHECKED_IN_NOT_ENABLED",
        "FOLLOW_UP_APPT_ERROR",
        "APPT_NOT_COMPLETE_ERROR",
        "EXISTING_APPT_STATUS_ERROR",
        "APPOINTMENT_FEE_SET",
        "APPOINTMENT_FEE_ALREADY_PAID",
        "NO_SHOW_NOT_ALLOWED",
        "BILL_AMOUNT",
        "CANNOT_COMPLETE_APPOINTMENT",
        "APPOINTMENT_REQUEST_CREATED",
        "APPOINTMENT_BROADCASTED_SUCCESSFULLY",
        "APPOINTMENT_REVOKE_SUCCESSFULLY",
        "SEND_MESSAGE_RESPONSE",
        "EMAIL_SUBJECT_MANDATORY",
        "PATIENT_PHONE_NOT_PRESENT",
        "PATIENT_SCREENER_ADDED",
        "PATIENTSCREENER_NOT_FOUND",
        "PATIENT_SCORE_ADDED",
        "PATIENT_SCREENER_ANSWER_UPDATED",
        "INVALID_SCREENER_TYPE",
        "INVALID_ANSWER_OPTION",
        "INCOMPLETE_SCREENER_ANSWERS",
        "PATIENT_SCREENER_SCORE_UPDATED",
        "PATIENT_SCREENER_DATA_FETCHED",
        "PAYMENT_CARD_ADDED",
        "PAYMENT_CARD_ALREADY_EXISTS",
        "PAYMENT_CHARGED",
        "FINE_CHARGED",
        "PAYMENT_CARD_NOT_FOUND",
        "PAYMENT_ALREADY_DONE",
        "PAYMENT_CARD_NOT_EXISTS",
        "PAYMENT_AMOUNT_NOT_SET",
        "PAYMENT_BILL_STATUS_ERROR",
        "INVALID_CARD_EXPIRATION",
        "NO_NOTIFICATIONS",
        "NOTIFICATION_MARKED_AS_SEEN",
        "NOTIFICATIONS_COUNT",
        "PATIENT_CONSENT_FORMS_SIGNED",
        "PATIENT_CONSENT_FORM_NOT_FOUND",
        "TEXTMACRO_SHORTCUT_EXISTS",
        "TEXTMACRO_ADDED",
        "TEXTMACRO_DATA_FETCHED",
        "TEXTMACRO_UPDATED",
        "FAILED_UPDATE_TEXT_MACRO",
        "FAILED_TEXTMACRO_ARCHIVE",
        "TEXTMACRO_ARCHIVE",
        "TEXTMACRO_RESTORED",
        "FAILED_TEXTMACRO_RESTORE",
        "TEXTMACRO_NOT_EXIST",
        "TASK_ADDED",
        "TASK_DATA_FETCHED",
        "TASK_UPDATED",
        "TASK_ARCHIVED",
        "TASK_NOT_EXIST",
        "FAILED_UPDATE_TASK",
        "INVALID_TASK_STATUS",
        "FAILED_TASK_ARCHIVE",
        "INVALID_DUEDATE",
        "FINE_CHARGED_APPOINTMENT",
        "APPOINTMENT_FINE",
        "CANNOT_CHARGE_FINE",
        "PAYMENT_SUCCESSFUL",
        "VITAL_ADDED",
        "VITAL_UPDATED",
        "VITAL_DATA_FETCHED",
        "VITAL_NOT_FOUND",
        "FAILED_VITAL_UPDATED",
        "EMITTED_SUCCESSFULLY",
        "WEBHOOK_SUCCESSFUL",
        "BILL_UPDATED",
        "CLAIM_ADDED",
        "BOOK_APPOINTMENT_ERROR",
        "CANCEL_APPOINTMENT_ERROR",
        "NOT_IMPLEMENTED",
        "COMMENT_ADDED_SUCCESSFULLY",
        "COMMENT_UPDATED_SUCCESSFULLY",
        "COMMENT_DELETED_SUCCESSFULLY",
        "MEDICAL_CODE_ADDED_SUCCESSFULLY",
        "MEDICAL_CODE_UPDATED_SUCCESSFULLY",
        "PROVIDER_REVIEW_ADDED_SUCCESSFULLY",
        "PROVIDER_REVIEW_STATUS_UPDATED_SUCCESSFULLY",
        "CHECK_IN_STATUS",
        "REFILL_RX_ADDED",
        "REFILL_RX_UPDATED",
        "ROI_ADDED",
        "ROI_UPDATED",
        "ROI_STATUS_UPDATED",
        "ROI_NOT_FOUND",
        "INTERNAL_SERVER_ERROR",
        "FILE_UPLOADED",
        "EMPTY_FILE",
        "MEDICAL_CODES_CREATED",
        "MEDICAL_CODES_UPDATED",
        "MEDICAL_CODE_ENABLED",
        "MEDICAL_CODE_DISABLED",
        "MEDICAL_CODE_ARCHIVED",
        "MEDICAL_CODE_UNARCHIVED",
        "PATIENT_ALLERGY_CREATED",
        "PATIENT_ALLERGY_UPDATED",
        "PATIENT_ALLERGY_ENABLED",
        "PATIENT_ALLERGY_DISABLED",
        "PATIENT_ALLERGY_ARCHIVED",
        "PATIENT_ALLERGY_UNARCHIVED",
        "PATIENT_VACCINE_CREATED",
        "PATIENT_VACCINE_UPDATED",
        "PATIENT_VACCINE_ENABLED",
        "PATIENT_VACCINE_DISABLED",
        "PATIENT_VACCINE_ARCHIVED",
        "PATIENT_VACCINE_UNARCHIVED",
        "PATIENT_VITAL_CREATED",
        "PATIENT_VITAL_UPDATED",
        "STICKY_NOTE_CREATED",
        "STICKY_NOTE_UPDATED",
        "STICKY_NOTE_ENABLED",
        "STICKY_NOTE_DISABLED",
        "STICKY_NOTE_ARCHIVED",
        "STICKY_NOTE_UNARCHIVED",
        "PATIENT_MEDICATION_CREATED",
        "PATIENT_MEDICATION_UPDATED",
        "PATIENT_MEDICATION_ENABLE_DISABLE_RESPONSE",
        "PATIENT_MEDICATION_ARCHIVE_STATUS_UPDATED",
        "PATIENT_MEDICATION_ARCHIVED",
        "PATIENT_MEDICATION_UNARCHIVED",
        "PATIENT_MEDICAL_HISTORY_CREATED",
        "PATIENT_MEDICAL_HISTORY_UPDATED",
        "PATIENT_MEDICAL_HISTORY_ENABLE_DISABLE_RESPONSE",
        "PATIENT_MEDICAL_HISTORY_ARCHIVE_STATUS_UPDATED",
        "PATIENT_SURGICAL_HISTORY_CREATED",
        "PATIENT_SURGICAL_HISTORY_UPDATED",
        "PATIENT_SURGICAL_HISTORY_ENABLE_DISABLE_RESPONSE",
        "PATIENT_SURGICAL_HISTORY_ARCHIVE_STATUS_UPDATED",
        "PATIENT_FAMILY_HISTORY_CREATED",
        "PATIENT_FAMILY_HISTORY_UPDATED",
        "PATIENT_FAMILY_HISTORY_ENABLE_DISABLE_RESPONSE",
        "PATIENT_FAMILY_HISTORY_ARCHIVE_STATUS_UPDATED",
        "PATIENT_SOCIAL_HISTORY_CREATED",
        "PATIENT_SOCIAL_HISTORY_UPDATED",
        "PATIENT_SOCIAL_HISTORY_ENABLE_DISABLE_RESPONSE",
        "PATIENT_SOCIAL_HISTORY_ARCHIVE_STATUS_UPDATED",
        "PATIENT_MEDICATION_CREATED_SUCCESSFULLY",
        "PATIENT_MEDICATION_UPDATED_SUCCESSFULLY",
        "DOCUMENT_TAG_CREATED",
        "DOCUMENT_TAG_UPDATED",
        "DOCUMENT_TAG_ENABLED",
        "DOCUMENT_TAG_DISABLED",
        "DOCUMENT_TAG_ARCHIVED",
        "DOCUMENT_TAG_UNARCHIVED",
        "PATIENT_LAB_ORDER_CREATED",
        "PATIENT_LAB_ORDER_UPDATED",
        "PATIENT_LAB_ORDER_NOT_FOUND",
        "PATIENT_LAB_ORDER_ENABLED",
        "PATIENT_LAB_ORDER_DISABLED",
        "PATIENT_LAB_ORDER_ARCHIVED",
        "PATIENT_LAB_ORDER_UNARCHIVED",
        "CLINICAL_NOTE_ALREADY_FOUND",
        "CLINICAL_NOTE_CREATED",
        "CLINICAL_NOTE_UPDATED",
        "CLINICAL_NOTE_NOT_FOUND",
        "CLINICAL_NOTE_ENABLED",
        "CLINICAL_NOTE_DISABLED",
        "CLINICAL_NOTE_ARCHIVED",
        "CLINICAL_NOTE_UNARCHIVED",
        "USER_DOCUMENT_UPLOAD",
        "ROLE_ADDED",
        "PRIVILEGE_NOT_FOUND",
        "ROLE_UPDATED",
        "ROLE_SYNC",
        "ROLE_NOT_FOUND",
        "CUSTOM_FORM_CREATED",
        "CUSTOM_FORM_STATUS_UPDATED",
        "CUSTOM_FORM_UPDATED",
        "CUSTOM_FORM_ENABLED",
        "CUSTOM_FORM_DISABLED",
        "CUSTOM_FORM_ARCHIVED",
        "CUSTOM_FORM_RESTORED",
        "CUSTOM_FORM_COPY",
        "CUSTOM_FORM_TEMPLATE_CREATED",
        "CUSTOM_FORM_TEMPLATE_STATUS_UPDATED",
        "CUSTOM_FORM_TEMPLATE_UPDATED",
        "CUSTOM_FORM_TEMPLATE_ENABLED",
        "CUSTOM_FORM_TEMPLATE_DISABLED",
        "CUSTOM_FORM_TEMPLATE_ARCHIVED",
        "CUSTOM_FORM_TEMPLATE_RESTORED",
        "MICROS_FORM_CREATED",
        "MICROS_FORM_UPDATED",
        "MICROS_FORM_STATUS_UPDATED",
        "MICROS_FORM_ARCHIVE_STATUS_UPDATED",
        "VISIT_NOTE_FORM_CREATED",
        "VISIT_NOTE_FORM_UPDATED",
        "VISIT_NOTE_FORM_STATUS_UPDATED",
        "VISIT_NOTE_FORM_ARCHIVE_STATUS_UPDATED",
        "REVIEW_OF_SYSTEM_FORM_CREATED",
        "REVIEW_OF_SYSTEM_FORM_UPDATED",
        "REVIEW_OF_SYSTEM_FORM_STATUS_UPDATED",
        "REVIEW_OF_SYSTEM_FORM_ARCHIVE_STATUS_UPDATED",
        "CUSTOM_QUESTIONNAIRE_FORM_CREATED",
        "CUSTOM_QUESTIONNAIRE_FORM_UPDATED",
        "CUSTOM_QUESTIONNAIRE_FORM_STATUS_UPDATED",
        "CUSTOM_QUESTIONNAIRE_FORM_ARCHIVE_STATUS_UPDATED",
        "CARE_PLAN_CREATED",
        "CARE_PLAN_UPDATED",
        "CARE_PLAN_ENABLED",
        "CARE_PLAN_DISABLED",
        "CARE_PLAN_ARCHIVE_STATUS_UPDATED",
        "CARE_PLAN_ARCHIVED",
        "CARE_PLAN_UNARCHIVED",
        "CARE_PLAN_ASSIGNED",
        "PATIENT_CARE_PLAN_STATUS_UPDATED",
        "VITAL_REFERENCE_RANGE_UPDATED",
        "PATIENT_VITAL_REFERENCE_RANGE_UPDATED",
        "ANNOTABLE_IMAGE_FORM_CREATED",
        "ANNOTABLE_IMAGE_FORM_UPDATED",
        "ANNOTABLE_IMAGE_FORM_STATUS_UPDATED",
        "ANNOTABLE_IMAGE_FORM_ARCHIVE_STATUS_UPDATED",
        "ORDER_SET_FORM_CREATED",
        "ORDER_SET_FORM_UPDATED",
        "ORDER_SET_FORM_STATUS_UPDATED",
        "PHYSICAL_EXAM_FORM_CREATED",
        "PHYSICAL_EXAM_FORM_UPDATED",
        "PHYSICAL_EXAM_FORM_STATUS_UPDATED",
        "PHYSICAL_EXAM_FORM_ARCHIVE_STATUS_UPDATED",
        "PATIENT_DIAGNOSIS_CREATED",
        "PATIENT_DIAGNOSIS_UPDATED",
        "PATIENT_DIAGNOSIS_ARCHIVED",
        "PATIENT_DIAGNOSIS_UNARCHIVED",
        "PATIENT_DIAGNOSIS_ARCHIVE_STATUS",
        "PATIENT_VITAL_CREATED_SUCCESSFULLY",
        "PATIENT_VITAL_UPDATED_SUCCESSFULLY",
        "PATIENT_VITAL_SETTING_UPDATED",
        "CHECK_CONSULT_TIME",
        "EVENT_EMITTED_SUCCESSFULLY",
        "NOTIFICATION_SEND",
        "TIME_LOG_CREATED",
        "TIME_LOG_UPDATED",
        "TIME_LOG_DELETED",
        "PATIENT_TRAINED",
        "AUTOMATIC_TIME_LOG_CREATED",
        "NURSE_ACTIONS_STATISTIC_GENERATED",
        "AUDIO_QUICK_SUMMARY_GENERATED",
        "INVITATION_SEND_SUCCESSFULLY",
        "EMAIL_SENT",
        "SMS_SENT",
        "GATEWAY_TIMEOUT",
        "NURSE_REASSIGNED_SUCCESSFULLY",
        "UPDATE_PROVIDER_GROUP_CONFIGURATION",
      ],
    },
    message: {
      type: "object",
    },
    data: {
      type: "object",
    },
    errors: {
      type: "object",
      additionalProperties: {
        type: "string",
      },
    },
    path: {
      type: "string",
    },
    requestId: {
      type: "string",
    },
    version: {
      type: "string",
    },
  },
} as const;

export const $Address = {
  required: ["city", "country", "line1", "state", "zipcode"],
  type: "object",
  properties: {
    line1: {
      maxLength: 255,
      minLength: 0,
      pattern: "^[\\S].*[\\S]$",
      type: "string",
    },
    line2: {
      maxLength: 255,
      minLength: 0,
      type: "string",
    },
    city: {
      maxLength: 255,
      minLength: 0,
      pattern: "^[a-zA-Z0-9]+(?:[\\s-][a-zA-Z0-9]+)*$",
      type: "string",
    },
    state: {
      pattern: "^[a-zA-Z0-9]+(?:[\\s-][a-zA-Z0-9]+)*$",
      type: "string",
    },
    country: {
      maxLength: 255,
      minLength: 0,
      pattern: "^[a-zA-Z]+(?:[\\s-][a-zA-Z]+)*$",
      type: "string",
    },
    zipcode: {
      maxLength: 10,
      minLength: 5,
      pattern: "^[0-9-]{5,10}$",
      type: "string",
    },
  },
} as const;

export const $User = {
  required: ["firstName", "gender", "lastName", "phone", "role"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    iamId: {
      type: "string",
    },
    email: {
      maxLength: 64,
      minLength: 5,
      type: "string",
    },
    firstName: {
      maxLength: 32,
      minLength: 2,
      pattern: "^[a-zA-Z]+[\\-'.\\s]*[a-zA-Z]+[.]?$|^[a-zA-Z]+[.]?$",
      type: "string",
    },
    lastName: {
      maxLength: 32,
      minLength: 2,
      pattern: "^[a-zA-Z]+[\\-'.\\s]*[a-zA-Z]+[.]?$|^[a-zA-Z]+[.]?$",
      type: "string",
    },
    middleName: {
      type: "string",
    },
    phone: {
      type: "string",
    },
    gender: {
      type: "string",
      enum: ["MALE", "FEMALE", "OTHER"],
    },
    avatar: {
      type: "string",
      readOnly: true,
    },
    birthDate: {
      type: "string",
      format: "date-time",
    },
    roleType: {
      type: "string",
      enum: ["PROVIDER", "STAFF", "PATIENT"],
    },
    role: {
      type: "string",
      enum: [
        "SUPER_ADMIN",
        "ADMIN",
        "FRONTDESK",
        "BILLER",
        "SITE_ADMIN",
        "PROVIDER_GROUP_ADMIN",
        "PROVIDER",
        "NURSE",
        "PATIENT",
        "ANONYMOUS",
      ],
    },
    address: {
      $ref: "#/components/schemas/Address",
    },
    lastLogin: {
      type: "string",
      format: "date-time",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
      readOnly: true,
    },
    emailVerified: {
      type: "boolean",
      readOnly: true,
    },
    phoneVerified: {
      type: "boolean",
      readOnly: true,
    },
    password: {
      type: "string",
    },
    tenantKey: {
      type: "string",
    },
    acceptTerms: {
      type: "boolean",
    },
    locationId: {
      type: "string",
      format: "uuid",
    },
    locationName: {
      type: "string",
    },
    currentMonthSummary: {
      type: "boolean",
    },
    selfCheckUuid: {
      type: "string",
      format: "uuid",
    },
  },
} as const;

export const $TimeLogRequest = {
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    patientId: {
      type: "string",
      format: "uuid",
    },
    logIdentifier: {
      type: "string",
    },
    logTimeDuration: {
      type: "integer",
      format: "int32",
    },
    logStartTime: {
      type: "string",
      format: "date-time",
    },
    logEndTime: {
      type: "string",
      format: "date-time",
    },
    activityName: {
      type: "string",
    },
    logEntryType: {
      type: "string",
      enum: ["AUTOMATIC", "MANUAL"],
    },
    note: {
      type: "string",
    },
    billingCycle: {
      type: "string",
    },
    loggedBy: {
      type: "string",
      format: "uuid",
    },
    loggedByExternal: {
      type: "boolean",
    },
    totalTimeLog: {
      type: "integer",
      format: "int32",
    },
  },
} as const;

export const $Appointment = {
  required: ["duration", "endTime", "external", "nurseId", "purpose", "startTime", "timezone"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    purpose: {
      type: "string",
    },
    patientId: {
      type: "string",
      format: "uuid",
    },
    nurseId: {
      type: "string",
      format: "uuid",
    },
    external: {
      type: "boolean",
    },
    startTime: {
      type: "string",
      format: "date-time",
    },
    endTime: {
      type: "string",
      format: "date-time",
    },
    requestedStartTime: {
      type: "string",
      format: "date-time",
    },
    requestedEndTime: {
      type: "string",
      format: "date-time",
    },
    requestedDuration: {
      type: "integer",
      format: "int32",
    },
    requestedTimezone: {
      type: "string",
    },
    duration: {
      type: "integer",
      format: "int32",
    },
    timezone: {
      type: "string",
      enum: [
        "PST",
        "EST",
        "CST",
        "MST",
        "AST",
        "HST",
        "EDT",
        "PDT",
        "CDT",
        "ADT",
        "MDT",
        "IST",
        "SGT",
        "AKDT",
        "AKST",
        "UTC",
        "WIT",
      ],
    },
    patientName: {
      type: "string",
    },
    patientMrn: {
      type: "string",
    },
    patientEmail: {
      type: "string",
    },
    patientPhone: {
      type: "string",
    },
    providerName: {
      type: "string",
    },
    nurseName: {
      type: "string",
    },
    mode: {
      type: "string",
      enum: ["HOME_VISIT", "TELE_VISIT"],
    },
    status: {
      type: "string",
      enum: [
        "PENDING",
        "ACCEPTED",
        "REJECTED",
        "CONFIRMED",
        "REQUESTED",
        "CANCELLED",
        "NO_SHOW",
        "CHECKED_IN",
        "IN_PROGRESS",
        "COMPLETED",
        "SCHEDULED",
        "RESCHEDULED",
        "BROADCAST",
        "REVOKE",
        "IN_EXAM",
      ],
    },
    rescheduleReason: {
      type: "string",
    },
    cancelReason: {
      type: "string",
    },
    slotOpen: {
      type: "boolean",
    },
    created: {
      type: "string",
      format: "date-time",
    },
    archive: {
      type: "boolean",
    },
    reason: {
      type: "string",
    },
    address: {
      $ref: "#/components/schemas/Address",
    },
    broadcastNurseName: {
      type: "string",
    },
    broadcastNurseAvatar: {
      type: "string",
    },
    patientAvatar: {
      type: "string",
    },
    nurseAvatar: {
      type: "string",
    },
    broadcast: {
      type: "boolean",
    },
    broadcastBy: {
      type: "string",
      format: "uuid",
    },
    requesterType: {
      type: "string",
      enum: ["PATIENT", "STAFF"],
    },
    noshow: {
      type: "string",
      enum: ["PATIENT", "NURSE"],
    },
    clinicalNote: {
      $ref: "#/components/schemas/ClinicalNote",
    },
    escalated: {
      type: "boolean",
    },
    prevStatus: {
      type: "string",
      enum: [
        "PENDING",
        "ACCEPTED",
        "REJECTED",
        "CONFIRMED",
        "REQUESTED",
        "CANCELLED",
        "NO_SHOW",
        "CHECKED_IN",
        "IN_PROGRESS",
        "COMPLETED",
        "SCHEDULED",
        "RESCHEDULED",
        "BROADCAST",
        "REVOKE",
        "IN_EXAM",
      ],
    },
    cptCode: {
      type: "array",
      items: {
        type: "string",
      },
    },
    sourceTaskId: {
      type: "string",
      format: "uuid",
    },
  },
} as const;

export const $ClinicalNote = {
  required: ["note"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    note: {
      type: "string",
    },
    status: {
      type: "string",
      enum: ["SIGNED_OFF", "IN_PROGRESS", "PENDING", "COMPLETED"],
    },
    appointmentId: {
      type: "string",
      format: "uuid",
    },
  },
} as const;

export const $MedicalCodesEntity = {
  type: "object",
  properties: {
    createdBy: {
      type: "string",
    },
    modifiedBy: {
      type: "string",
    },
    created: {
      type: "string",
      format: "date-time",
    },
    modified: {
      type: "string",
      format: "date-time",
    },
    id: {
      type: "integer",
      format: "int64",
    },
    uuid: {
      type: "string",
      format: "uuid",
    },
    type: {
      type: "string",
      enum: ["ICD10", "CPT", "ALL"],
    },
    code: {
      type: "string",
    },
    description: {
      type: "string",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
  },
} as const;

export const $Note = {
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    name: {
      type: "string",
    },
    goalProgress: {
      type: "string",
      enum: ["NOT_STARTED", "PROGRESSING", "COMPLETED", "ON_HOLD", "CANCELLED", "RESOLVED"],
    },
  },
} as const;

export const $PatientVital = {
  required: ["patientId", "recordedDate", "value1", "vitalName"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    patientId: {
      type: "string",
      format: "uuid",
    },
    vitalName: {
      type: "string",
    },
    value1: {
      type: "number",
      format: "float",
    },
    value2: {
      type: "number",
      format: "float",
    },
    ecgValue: {
      type: "string",
    },
    recordedDate: {
      type: "string",
      format: "date-time",
    },
    unit: {
      type: "string",
    },
    integrationId: {
      type: "string",
    },
    heartRate: {
      type: "integer",
      format: "int32",
    },
    integrationType: {
      type: "string",
      enum: ["DEVICE", "EHR", "MANUAL", "WATCH", "IHEALTH"],
    },
    note: {
      $ref: "#/components/schemas/Note",
    },
    created: {
      type: "string",
      format: "date-time",
    },
    modified: {
      type: "string",
      format: "date-time",
    },
    minRange: {
      type: "string",
    },
    maxRange: {
      type: "string",
    },
    severity: {
      type: "string",
    },
    alertStatus: {
      type: "string",
      enum: ["RESOLVED", "NOT_RESOLVED"],
    },
  },
} as const;

export const $Task = {
  required: ["dueDate", "priority", "status", "title"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    title: {
      type: "string",
    },
    patientUuid: {
      type: "string",
      format: "uuid",
    },
    patientName: {
      type: "string",
    },
    birthDate: {
      type: "string",
      format: "date-time",
    },
    gender: {
      type: "string",
      enum: ["MALE", "FEMALE", "OTHER"],
    },
    mrn: {
      type: "string",
    },
    diagnosisCodes: {
      uniqueItems: true,
      type: "array",
      items: {
        $ref: "#/components/schemas/MedicalCodesEntity",
      },
    },
    assignTo: {
      type: "string",
      format: "uuid",
    },
    assignedExternal: {
      type: "boolean",
    },
    assignedName: {
      type: "string",
    },
    assignBy: {
      type: "string",
      format: "uuid",
    },
    assignedByName: {
      type: "string",
    },
    assignedByExternal: {
      type: "boolean",
    },
    dueDate: {
      type: "string",
      format: "date-time",
    },
    status: {
      type: "string",
      enum: ["PENDING", "COMPLETED", "DISCARDED"],
    },
    priority: {
      type: "string",
      enum: ["LOW", "MEDIUM", "HIGH", "CRITICAL"],
    },
    roleType: {
      type: "string",
      enum: ["STAFF", "PROVIDER", "SELF"],
    },
    role: {
      type: "string",
      enum: [
        "SUPER_ADMIN",
        "ADMIN",
        "FRONTDESK",
        "BILLER",
        "SITE_ADMIN",
        "PROVIDER_GROUP_ADMIN",
        "PROVIDER",
        "NURSE",
        "PATIENT",
        "ANONYMOUS",
      ],
    },
    archive: {
      type: "boolean",
    },
    note: {
      type: "string",
    },
    completionDate: {
      type: "string",
      format: "date-time",
    },
    sourceReferenceId: {
      type: "string",
      format: "uuid",
    },
    type: {
      type: "string",
    },
    patientVital: {
      $ref: "#/components/schemas/PatientVital",
    },
    appointment: {
      $ref: "#/components/schemas/Appointment",
    },
    assignedDate: {
      type: "string",
      format: "date-time",
    },
    description: {
      type: "string",
    },
  },
} as const;

export const $Privilege = {
  required: ["id"],
  type: "object",
  properties: {
    id: {
      type: "integer",
      format: "int64",
    },
    name: {
      type: "string",
    },
    description: {
      type: "string",
    },
    module: {
      type: "string",
    },
    granted: {
      type: "boolean",
    },
  },
} as const;

export const $Role = {
  required: ["name", "type"],
  type: "object",
  properties: {
    id: {
      type: "integer",
      format: "int64",
    },
    name: {
      type: "string",
    },
    type: {
      type: "string",
    },
    privileges: {
      uniqueItems: true,
      type: "array",
      items: {
        $ref: "#/components/schemas/Privilege",
      },
    },
  },
} as const;

export const $RolePrivilegeUpdateRequest = {
  required: ["granted", "privilegeId", "roleId"],
  type: "object",
  properties: {
    roleId: {
      type: "integer",
      format: "int64",
    },
    privilegeId: {
      type: "integer",
      format: "int64",
    },
    granted: {
      type: "boolean",
    },
  },
} as const;

export const $LicenseState = {
  type: "object",
  properties: {
    id: {
      type: "integer",
      format: "int64",
    },
    country: {
      type: "string",
    },
    state: {
      type: "string",
    },
  },
} as const;

export const $Provider = {
  required: ["address", "email", "firstName", "gender", "lastName", "npi", "phone", "role"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    providerType: {
      type: "string",
      enum: ["MD", "PA", "PSYD", "LCSW", "NP", "RN", "BHNP", "FNP", "RD", "NONE", "UNKNOWN", "NPS"],
    },
    email: {
      maxLength: 64,
      minLength: 5,
      type: "string",
    },
    firstName: {
      maxLength: 32,
      minLength: 2,
      pattern: "^[a-zA-Z]+[\\-'.\\s]*[a-zA-Z]+[.]?$|^[a-zA-Z]+[.]?$",
      type: "string",
    },
    lastName: {
      maxLength: 32,
      minLength: 2,
      pattern: "^[a-zA-Z]+[\\-'.\\s]*[a-zA-Z]+[.]?$|^[a-zA-Z]+[.]?$",
      type: "string",
    },
    phone: {
      type: "string",
    },
    videoLink: {
      type: "string",
    },
    avatar: {
      type: "string",
    },
    role: {
      type: "string",
      enum: [
        "SUPER_ADMIN",
        "ADMIN",
        "FRONTDESK",
        "BILLER",
        "SITE_ADMIN",
        "PROVIDER_GROUP_ADMIN",
        "PROVIDER",
        "NURSE",
        "PATIENT",
        "ANONYMOUS",
      ],
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    gender: {
      type: "string",
      enum: ["MALE", "FEMALE", "OTHER"],
    },
    npi: {
      pattern: "^[a-zA-Z0-9]{10}$",
      type: "string",
    },
    introduction: {
      type: "string",
    },
    chatbotTone: {
      type: "string",
      enum: ["PROFESSIONAL", "FRIENDLY", "CASUAL"],
    },
    onboardStatus: {
      type: "string",
      enum: ["PENDING", "COMPLETED"],
    },
    providerLicenseDetails: {
      uniqueItems: true,
      type: "array",
      items: {
        $ref: "#/components/schemas/ProviderLicenseDetails",
      },
    },
    emailVerified: {
      type: "boolean",
    },
    acceptTerms: {
      type: "boolean",
    },
    address: {
      $ref: "#/components/schemas/Address",
    },
    schemaType: {
      type: "string",
      enum: ["INTERNAL", "EXTERNAL"],
    },
    ehrId: {
      type: "string",
    },
    ehrName: {
      type: "string",
    },
    userId: {
      type: "string",
      format: "uuid",
    },
    selfCheckUuid: {
      type: "string",
      format: "uuid",
    },
  },
} as const;

export const $ProviderLicenseDetails = {
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    licenseNumber: {
      type: "string",
    },
    expiryDate: {
      type: "string",
      format: "date-time",
    },
    licensedStates: {
      uniqueItems: true,
      type: "array",
      items: {
        $ref: "#/components/schemas/LicenseState",
      },
    },
  },
} as const;

export const $ChangeAvatarRequest = {
  type: "object",
  properties: {
    newAvatar: {
      type: "string",
    },
  },
} as const;

export const $ProviderGroup = {
  required: ["address", "email", "name", "npi", "phone", "subdomain"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    name: {
      pattern: "^(?!\\s)(?!.*\\s$)[A-Za-z0-9 ]+$",
      type: "string",
    },
    schema: {
      type: "string",
    },
    subdomain: {
      pattern: "^[a-z0-9]([a-z0-9]*[a-z0-9])?$",
      type: "string",
    },
    phone: {
      pattern: "^(\\+1)[0-9\\-]{10,14}$",
      type: "string",
    },
    npi: {
      pattern: "^[a-zA-Z0-9]{10}$",
      type: "string",
    },
    email: {
      type: "string",
    },
    timezone: {
      type: "string",
    },
    address: {
      $ref: "#/components/schemas/Address",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    avatar: {
      type: "string",
    },
    ehrId: {
      type: "string",
    },
    ehrName: {
      type: "string",
    },
    notificationEmail: {
      type: "string",
    },
    billingCycle: {
      type: "string",
      enum: ["NO_BILLING", "ROLLING_30_DAYS", "CALENDAR_MONTH"],
    },
    monitoringThreshold: {
      type: "integer",
      format: "int32",
    },
  },
} as const;

export const $EmergencyContact = {
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    firstName: {
      type: "string",
    },
    lastName: {
      type: "string",
    },
    phone: {
      type: "string",
    },
  },
} as const;

export const $Patient = {
  required: ["email", "firstName", "lastName", "nurseId", "providerId", "schemaType"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    firstName: {
      maxLength: 32,
      minLength: 2,
      pattern: "^[a-zA-Z]+[\\-'.\\s]*[a-zA-Z]+[.]?$|^[a-zA-Z]+[.]?$",
      type: "string",
    },
    lastName: {
      maxLength: 32,
      minLength: 2,
      pattern: "^[a-zA-Z]+[\\-'.\\s]*[a-zA-Z]+[.]?$|^[a-zA-Z]+[.]?$",
      type: "string",
    },
    email: {
      maxLength: 64,
      minLength: 5,
      type: "string",
    },
    mobileNumber: {
      type: "string",
    },
    gender: {
      type: "string",
      enum: ["MALE", "FEMALE", "OTHER"],
    },
    middleName: {
      type: "string",
    },
    mrn: {
      type: "string",
    },
    birthDate: {
      type: "string",
      format: "date-time",
    },
    avatar: {
      type: "string",
    },
    providerId: {
      type: "object",
      additionalProperties: {
        type: "string",
      },
    },
    nurseId: {
      type: "object",
      additionalProperties: {
        type: "string",
      },
    },
    schemaType: {
      type: "string",
      enum: ["INTERNAL", "EXTERNAL"],
    },
    address: {
      $ref: "#/components/schemas/Address",
    },
    signature: {
      type: "string",
    },
    signatureChanged: {
      type: "boolean",
    },
    emergencyContact: {
      $ref: "#/components/schemas/EmergencyContact",
    },
    consentFormSigned: {
      type: "boolean",
    },
    acceptTerms: {
      type: "boolean",
    },
    source: {
      type: "string",
      format: "uuid",
    },
    providerNpi: {
      type: "string",
    },
    nurseEmail: {
      type: "string",
    },
    nurseAvatar: {
      type: "string",
    },
    emailVerified: {
      type: "boolean",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    role: {
      type: "string",
      enum: [
        "SUPER_ADMIN",
        "ADMIN",
        "FRONTDESK",
        "BILLER",
        "SITE_ADMIN",
        "PROVIDER_GROUP_ADMIN",
        "PROVIDER",
        "NURSE",
        "PATIENT",
        "ANONYMOUS",
      ],
    },
    ehrId: {
      type: "string",
    },
    ehrName: {
      type: "string",
    },
    diagnosisCodes: {
      uniqueItems: true,
      type: "array",
      items: {
        type: "string",
      },
    },
    patientCarePlanRequest: {
      $ref: "#/components/schemas/PatientCarePlanRequest",
    },
    carePlanAssigned: {
      type: "boolean",
    },
    height: {
      type: "string",
    },
    selfCheckUuid: {
      type: "string",
      format: "uuid",
    },
  },
} as const;

export const $PatientCarePlanRequest = {
  required: ["carePlanId", "globalCarePlan", "vitalReferences"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    carePlanId: {
      type: "string",
      format: "uuid",
    },
    startDate: {
      type: "string",
      format: "date",
    },
    vitalReferences: {
      type: "array",
      items: {
        $ref: "#/components/schemas/VitalReference",
      },
    },
    protocolType: {
      type: "string",
      enum: ["OUT_OF_RANGE_BP", "OUT_OF_RANGE_HR", "OUT_OF_RANGE_BG"],
    },
    globalCarePlan: {
      type: "boolean",
    },
  },
} as const;

export const $VitalRange = {
  type: "object",
  properties: {
    rangeType: {
      type: "string",
      enum: [
        "NORMAL",
        "MODERATE",
        "CRITICAL",
        "LOW_MODERATE",
        "HIGH_MODERATE",
        "NORMAL_SYSTOLIC",
        "NORMAL_DIASTOLIC",
        "LOW_MODERATE_SYSTOLIC",
        "LOW_MODERATE_DIASTOLIC",
        "HIGH_MODERATE_SYSTOLIC",
        "HIGH_MODERATE_DIASTOLIC",
        "CRITICAL_SYSTOLIC",
        "CRITICAL_DIASTOLIC",
      ],
    },
    min: {
      type: "number",
      format: "double",
    },
    max: {
      type: "number",
      format: "double",
    },
  },
} as const;

export const $VitalReference = {
  required: ["vitalType"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    carePlanId: {
      type: "string",
      format: "uuid",
    },
    vitalType: {
      type: "string",
    },
    targetRange: {
      type: "string",
    },
    vitalRanges: {
      uniqueItems: true,
      type: "array",
      items: {
        $ref: "#/components/schemas/VitalRange",
      },
    },
    deviceName: {
      type: "string",
    },
  },
} as const;

export const $PatientMedication = {
  required: ["medicineName", "patientId"],
  type: "object",
  properties: {
    id: {
      type: "integer",
      format: "int64",
    },
    uuid: {
      type: "string",
      format: "uuid",
    },
    patientId: {
      type: "string",
      format: "uuid",
    },
    medicineName: {
      type: "string",
    },
    startDate: {
      type: "string",
      format: "date",
    },
    endDate: {
      type: "string",
      format: "date",
    },
    quantity: {
      type: "string",
    },
    note: {
      type: "string",
    },
    direction: {
      type: "string",
    },
    medicationDataSource: {
      type: "string",
      enum: ["MANUAL", "EHR", "AI"],
    },
    timezone: {
      type: "string",
    },
    medicineImage: {
      type: "string",
    },
    medicineAudio: {
      type: "string",
    },
    medicineType: {
      type: "string",
      enum: ["TABLET", "SYRUP"],
    },
    handwritten: {
      type: "boolean",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    medicationEhrId: {
      type: "string",
    },
    effiectivePeriodStartDate: {
      type: "string",
      format: "date-time",
    },
    effiectivePeriodEndDate: {
      type: "string",
      format: "date-time",
    },
    patientMedicationDosage: {
      type: "array",
      items: {
        $ref: "#/components/schemas/PatientMedicationDosage",
      },
    },
    patientMedicationDosageEntities: {
      type: "array",
      items: {
        $ref: "#/components/schemas/PatientMedicationDosageEntity",
      },
    },
  },
} as const;

export const $PatientMedicationDosage = {
  type: "object",
  properties: {
    id: {
      type: "integer",
      format: "int64",
    },
    patientInstruction: {
      type: "string",
    },
    route: {
      type: "string",
    },
    timing: {
      type: "string",
    },
    frequency: {
      type: "string",
    },
    unit: {
      type: "string",
    },
    dosage: {
      type: "integer",
      format: "int32",
    },
  },
} as const;

export const $PatientMedicationDosageEntity = {
  type: "object",
  properties: {
    id: {
      type: "integer",
      format: "int64",
    },
    patientInstruction: {
      type: "string",
    },
    route: {
      type: "string",
    },
    timing: {
      type: "string",
    },
    frequency: {
      type: "string",
    },
    unit: {
      type: "string",
    },
    dosage: {
      type: "integer",
      format: "int32",
    },
  },
} as const;

export const $DiagnosisEvidence = {
  type: "object",
  properties: {
    name: {
      type: "string",
    },
  },
} as const;

export const $PatientDiagnosis = {
  required: ["patientId"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    patientId: {
      type: "string",
      format: "uuid",
    },
    name: {
      type: "string",
    },
    medicalCode: {
      type: "string",
    },
    type: {
      type: "string",
      enum: ["CHRONIC", "ACUTE", "PRIMARY", "SECONDARY", "DIFFERENTIAL", "PROVISIONAL", "RESOLVED"],
    },
    startDate: {
      type: "string",
      format: "date",
    },
    onSetDate: {
      type: "string",
      format: "date",
    },
    recordedDate: {
      type: "string",
      format: "date-time",
    },
    encounterEhrId: {
      type: "string",
    },
    note: {
      type: "string",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    evidence: {
      type: "array",
      items: {
        $ref: "#/components/schemas/DiagnosisEvidence",
      },
    },
    lastOccurrence: {
      type: "string",
      format: "date-time",
    },
    modified: {
      type: "string",
      format: "date-time",
    },
    modifiedBy: {
      type: "string",
    },
    diagnosisEhrId: {
      type: "string",
    },
    medicalCodeType: {
      type: "string",
    },
    integrationType: {
      type: "string",
      enum: ["DEVICE", "EHR", "MANUAL", "WATCH", "IHEALTH"],
    },
  },
} as const;

export const $PatientCarePlanUpdateRequest = {
  required: ["vitalReference"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    vitalReference: {
      type: "array",
      items: {
        $ref: "#/components/schemas/VitalReference",
      },
    },
    carePlanStatus: {
      type: "string",
      enum: ["IN_PROGRESS", "PENDING", "COMPLETED"],
    },
  },
} as const;

export const $PatientCarePlanStatusChange = {
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    carePlanStatus: {
      type: "string",
      enum: ["IN_PROGRESS", "PENDING", "COMPLETED"],
    },
    bmi: {
      type: "string",
    },
    bloodPressure: {
      type: "string",
    },
  },
} as const;

export const $PatientProgramGoalTrack = {
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    programGoalId: {
      type: "string",
      format: "uuid",
    },
    trackDate: {
      type: "string",
      format: "date",
    },
    tracked: {
      type: "boolean",
    },
  },
} as const;

export const $BulkCarePlanRequest = {
  required: ["carePlanId", "globalCarePlan", "patientIds", "startDate"],
  type: "object",
  properties: {
    carePlanId: {
      type: "string",
      format: "uuid",
    },
    startDate: {
      type: "string",
      format: "date",
    },
    patientIds: {
      uniqueItems: true,
      type: "array",
      items: {
        type: "string",
        format: "uuid",
      },
    },
    globalCarePlan: {
      type: "boolean",
    },
  },
} as const;

export const $PatientAllergy = {
  required: ["allergyType", "name", "patientId"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    patientId: {
      type: "string",
      format: "uuid",
    },
    allergyType: {
      type: "string",
      enum: ["DRUG", "FOOD", "ENVIRONMENT", "OTHER"],
    },
    name: {
      type: "string",
    },
    reaction: {
      type: "string",
      enum: [
        "PAIN",
        "RUNNY_NOSE",
        "SWELLING",
        "BLOATING",
        "VOMITING",
        "RASHES",
        "ITCHY_NOSE",
        "THROAT_CLOSING",
        "COUGH",
        "REDNESS",
        "UNKNOWN",
        "HIVES",
      ],
    },
    severity: {
      type: "string",
      enum: ["MILD", "HIGH", "MODERATE", "UNDEFINED"],
    },
    imported: {
      type: "boolean",
    },
    onSetDate: {
      type: "string",
      format: "date",
    },
    recordedDate: {
      type: "string",
      format: "date-time",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    allergyEhrId: {
      type: "string",
    },
    note: {
      type: "string",
    },
  },
} as const;

export const $MedicalCode = {
  required: ["code", "type"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    type: {
      type: "string",
      enum: ["ICD10", "CPT", "ALL"],
    },
    code: {
      type: "string",
    },
    description: {
      type: "string",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    errorMessage: {
      type: "string",
    },
  },
} as const;

export const $Location = {
  required: ["address", "email", "name", "phone"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    name: {
      pattern: "^[A-Za-z0-9 ]+$",
      type: "string",
    },
    locationId: {
      type: "string",
    },
    phone: {
      pattern: "^(\\+1)[0-9\\-]{10,14}$",
      type: "string",
    },
    timezone: {
      type: "string",
    },
    email: {
      type: "string",
    },
    address: {
      $ref: "#/components/schemas/Address",
    },
    locationHours: {
      uniqueItems: true,
      type: "array",
      items: {
        $ref: "#/components/schemas/LocationHour",
      },
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    ehrId: {
      type: "string",
    },
    ehrName: {
      type: "string",
    },
  },
} as const;

export const $LocationHour = {
  type: "object",
  properties: {
    dayOfWeek: {
      type: "string",
      enum: ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"],
    },
    openingTime: {
      type: "string",
    },
    closingTime: {
      type: "string",
    },
  },
} as const;

export const $Device = {
  required: ["name"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    name: {
      pattern: "^[A-Za-z0-9 ]+$",
      type: "string",
    },
    deviceType: {
      type: "string",
    },
    description: {
      type: "string",
    },
    guideLink: {
      type: "string",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    category: {
      type: "string",
      enum: ["MECHANICAL", "ELECTRICAL", "DIGITAL"],
    },
    createdBy: {
      type: "string",
    },
    created: {
      type: "string",
      format: "date-time",
    },
    modified: {
      type: "string",
      format: "date-time",
    },
  },
} as const;

export const $ConsentFormTemplate = {
  required: ["document", "name"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    name: {
      type: "string",
    },
    document: {
      type: "string",
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    changeConsent: {
      type: "boolean",
    },
    signed: {
      type: "boolean",
    },
  },
} as const;

export const $CarePlan = {
  required: ["diagnosisCodes"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    title: {
      type: "string",
    },
    duration: {
      type: "integer",
      format: "int32",
    },
    durationUnit: {
      type: "string",
      enum: ["DAY", "WEEK", "MONTH", "YEAR"],
    },
    overview: {
      type: "string",
    },
    gender: {
      type: "string",
      enum: ["MALE", "FEMALE", "UNISEX"],
    },
    ageCriteria: {
      type: "string",
    },
    age: {
      type: "string",
    },
    deviceName: {
      uniqueItems: true,
      type: "array",
      items: {
        type: "string",
      },
    },
    devices: {
      uniqueItems: true,
      type: "array",
      items: {
        $ref: "#/components/schemas/Device",
      },
    },
    routineCheckup: {
      type: "string",
    },
    programGoals: {
      type: "array",
      items: {
        $ref: "#/components/schemas/ProgramGoal",
      },
    },
    vitalReferences: {
      type: "array",
      items: {
        $ref: "#/components/schemas/VitalReference",
      },
    },
    globalCarePlan: {
      type: "boolean",
    },
    external: {
      type: "boolean",
    },
    protocolType: {
      type: "string",
      enum: ["OUT_OF_RANGE_BP", "OUT_OF_RANGE_HR", "OUT_OF_RANGE_BG"],
    },
    active: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    trackedVitals: {
      type: "array",
      items: {
        type: "string",
      },
    },
    modified: {
      type: "string",
      format: "date-time",
    },
    diagnosisCodes: {
      uniqueItems: true,
      type: "array",
      items: {
        type: "string",
      },
    },
    protocol: {
      type: "array",
      items: {
        $ref: "#/components/schemas/Protocol",
      },
    },
  },
} as const;

export const $ProgramGoal = {
  type: "object",
  properties: {
    category: {
      type: "string",
    },
    title: {
      type: "string",
    },
    uuid: {
      type: "string",
      format: "uuid",
    },
    trackBy: {
      type: "string",
      enum: ["DAY", "WEEK", "MONTH", "YEAR"],
    },
    targetType: {
      type: "string",
    },
    targetValue: {
      type: "string",
    },
    unit: {
      type: "string",
    },
    active: {
      type: "boolean",
    },
    objective: {
      type: "string",
    },
    programGoalTasks: {
      type: "array",
      items: {
        $ref: "#/components/schemas/ProgramGoalTask",
      },
    },
    goalTracks: {
      type: "array",
      items: {
        $ref: "#/components/schemas/PatientProgramGoalTrack",
      },
    },
    percentage: {
      type: "string",
    },
    bmi: {
      type: "string",
    },
    bloodPressure: {
      type: "string",
    },
    checkedDaysCount: {
      type: "integer",
      format: "int64",
    },
    skippedDaysCount: {
      type: "integer",
      format: "int64",
    },
  },
} as const;

export const $ProgramGoalTask = {
  type: "object",
  properties: {
    task: {
      type: "string",
    },
    details: {
      type: "string",
    },
  },
} as const;

export const $Protocol = {
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    title: {
      type: "string",
    },
    protocolType: {
      type: "string",
      enum: ["OUT_OF_RANGE_BP", "OUT_OF_RANGE_HR", "OUT_OF_RANGE_BG"],
    },
    range: {
      type: "string",
    },
    description: {
      type: "array",
      items: {
        type: "string",
      },
    },
  },
} as const;

export const $AppointmentStatusChange = {
  required: ["uuid"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    status: {
      type: "string",
      enum: [
        "PENDING",
        "ACCEPTED",
        "REJECTED",
        "CONFIRMED",
        "REQUESTED",
        "CANCELLED",
        "NO_SHOW",
        "CHECKED_IN",
        "IN_PROGRESS",
        "COMPLETED",
        "SCHEDULED",
        "RESCHEDULED",
        "BROADCAST",
        "REVOKE",
        "IN_EXAM",
      ],
    },
    nurseId: {
      type: "string",
      format: "uuid",
    },
    schemaType: {
      type: "string",
      enum: ["INTERNAL", "EXTERNAL"],
    },
    reason: {
      type: "string",
    },
    noshow: {
      type: "string",
      enum: ["PATIENT", "NURSE"],
    },
  },
} as const;

export const $RescheduleRequest = {
  required: ["appointmentId", "duration", "endTime", "reason", "startTime", "timezone"],
  type: "object",
  properties: {
    appointmentId: {
      type: "string",
      format: "uuid",
    },
    duration: {
      type: "integer",
      format: "int32",
    },
    startTime: {
      type: "string",
      format: "date-time",
    },
    endTime: {
      type: "string",
      format: "date-time",
    },
    reason: {
      type: "string",
    },
    timezone: {
      type: "string",
      enum: [
        "PST",
        "EST",
        "CST",
        "MST",
        "AST",
        "HST",
        "EDT",
        "PDT",
        "CDT",
        "ADT",
        "MDT",
        "IST",
        "SGT",
        "AKDT",
        "AKST",
        "UTC",
        "WIT",
      ],
    },
    nurseId: {
      type: "string",
      format: "uuid",
    },
    external: {
      type: "boolean",
    },
  },
} as const;

export const $PlayerTimeLogRequestDto = {
  type: "object",
  properties: {
    timeLogRequest: {
      type: "array",
      items: {
        $ref: "#/components/schemas/TimeLogRequest",
      },
    },
  },
} as const;

export const $ResetPasswordRequest = {
  required: ["email", "newPassword", "otp"],
  type: "object",
  properties: {
    newPassword: {
      pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?":{}|<>])[^ ]{8,}$',
      type: "string",
    },
    email: {
      type: "string",
    },
    otp: {
      type: "string",
    },
  },
} as const;

export const $AvailabilitySetting = {
  required: ["bookingWindow", "followupConsultTime", "initialConsultTime", "providerId", "timezone"],
  type: "object",
  properties: {
    providerId: {
      type: "string",
      format: "uuid",
    },
    bookingWindow: {
      type: "integer",
      format: "int32",
    },
    timezone: {
      type: "string",
      enum: [
        "PST",
        "EST",
        "CST",
        "MST",
        "AST",
        "HST",
        "EDT",
        "PDT",
        "CDT",
        "ADT",
        "MDT",
        "IST",
        "SGT",
        "AKDT",
        "AKST",
        "UTC",
        "WIT",
      ],
    },
    initialConsultTime: {
      type: "integer",
      format: "int32",
    },
    followupConsultTime: {
      type: "integer",
      format: "int32",
    },
    bufferTime: {
      type: "integer",
      format: "int32",
    },
    bookBefore: {
      type: "string",
    },
    blockDays: {
      uniqueItems: true,
      type: "array",
      items: {
        $ref: "#/components/schemas/BlockDay",
      },
    },
    daySlots: {
      uniqueItems: true,
      type: "array",
      items: {
        $ref: "#/components/schemas/DaySlot",
      },
    },
  },
} as const;

export const $BlockDay = {
  type: "object",
  properties: {
    startTime: {
      type: "string",
      format: "date-time",
    },
    endTime: {
      type: "string",
      format: "date-time",
    },
  },
} as const;

export const $DaySlot = {
  type: "object",
  properties: {
    day: {
      type: "string",
      enum: ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"],
    },
    startTime: {
      $ref: "#/components/schemas/LocalTime",
    },
    endTime: {
      $ref: "#/components/schemas/LocalTime",
    },
  },
} as const;

export const $LocalTime = {
  type: "object",
  properties: {
    hour: {
      type: "integer",
      format: "int32",
    },
    minute: {
      type: "integer",
      format: "int32",
    },
    second: {
      type: "integer",
      format: "int32",
    },
    nano: {
      type: "integer",
      format: "int32",
    },
  },
} as const;

export const $PatientVitalRequest = {
  required: ["patientId"],
  type: "object",
  properties: {
    patientId: {
      type: "string",
      format: "uuid",
    },
    patientVital: {
      type: "array",
      items: {
        $ref: "#/components/schemas/PatientVital",
      },
    },
  },
} as const;

export const $PatientDeviceRequest = {
  type: "object",
  properties: {
    deviceModelId: {
      type: "string",
      format: "uuid",
    },
    patientId: {
      type: "string",
      format: "uuid",
    },
    trainingConfirmed: {
      type: "boolean",
    },
  },
} as const;

export const $PatientConsentForm = {
  required: ["consentFormTemplate", "patientUuid"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    patientUuid: {
      type: "string",
      format: "uuid",
    },
    patientName: {
      type: "string",
    },
    signature: {
      type: "string",
    },
    consentFormTemplate: {
      $ref: "#/components/schemas/ConsentFormTemplate",
    },
    modified: {
      type: "string",
      format: "date-time",
    },
  },
} as const;

export const $SendDirectMessageRequest = {
  required: ["type"],
  type: "object",
  properties: {
    subject: {
      type: "string",
    },
    content: {
      type: "string",
    },
    type: {
      type: "string",
      enum: ["EMAIL", "SMS"],
    },
    email: {
      type: "string",
    },
    phone: {
      type: "string",
    },
    attachmentName: {
      type: "string",
    },
  },
} as const;

export const $MessageRequest = {
  type: "object",
  properties: {
    registrationTokens: {
      type: "array",
      items: {
        type: "string",
      },
    },
    title: {
      type: "string",
    },
    body: {
      type: "string",
    },
    response: {
      type: "string",
    },
    notificationType: {
      type: "string",
      enum: [
        "PATIENT_APPOINTMENT_ACCEPTED",
        "PATIENT_APPOINTMENT_REQUEST",
        "BROADCAST",
        "APPOINTMENT_REMINDER",
        "PATIENT_MEDICATION_REMINDER",
        "CARE_PLAN_REMINDER",
        "PATIENT_VITAL_ALERT",
        "SMART_ALERT_REMINDER",
      ],
    },
  },
} as const;

export const $LogoutRequest = {
  required: ["refreshToken"],
  type: "object",
  properties: {
    refreshToken: {
      type: "string",
    },
  },
} as const;

export const $LoginRequest = {
  required: ["password", "username"],
  type: "object",
  properties: {
    username: {
      type: "string",
    },
    password: {
      type: "string",
    },
  },
} as const;

export const $ChangePasswordRequest = {
  required: ["newPassword", "oldPassword"],
  type: "object",
  properties: {
    oldPassword: {
      type: "string",
    },
    newPassword: {
      pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?":{}|<>])[^ ]{8,}$',
      type: "string",
    },
  },
} as const;

export const $AppointmentRequest = {
  required: ["mode", "patientId"],
  type: "object",
  properties: {
    uuid: {
      type: "string",
      format: "uuid",
    },
    patientId: {
      type: "string",
      format: "uuid",
    },
    reason: {
      type: "string",
    },
    status: {
      type: "string",
      enum: [
        "PENDING",
        "ACCEPTED",
        "REJECTED",
        "CONFIRMED",
        "REQUESTED",
        "CANCELLED",
        "NO_SHOW",
        "CHECKED_IN",
        "IN_PROGRESS",
        "COMPLETED",
        "SCHEDULED",
        "RESCHEDULED",
        "BROADCAST",
        "REVOKE",
        "IN_EXAM",
      ],
    },
    mode: {
      type: "string",
      enum: ["HOME_VISIT", "TELE_VISIT"],
    },
    purpose: {
      type: "string",
    },
    nurseId: {
      type: "string",
      format: "uuid",
    },
    nurseName: {
      type: "string",
    },
    external: {
      type: "boolean",
    },
    startTime: {
      type: "string",
      format: "date-time",
    },
    endTime: {
      type: "string",
      format: "date-time",
    },
    requestedTime: {
      type: "string",
    },
    timezone: {
      type: "string",
      enum: [
        "PST",
        "EST",
        "CST",
        "MST",
        "AST",
        "HST",
        "EDT",
        "PDT",
        "CDT",
        "ADT",
        "MDT",
        "IST",
        "SGT",
        "AKDT",
        "AKST",
        "UTC",
        "WIT",
      ],
    },
    cancelReason: {
      type: "string",
    },
    duration: {
      type: "integer",
      format: "int32",
    },
    slotOpen: {
      type: "boolean",
    },
    archive: {
      type: "boolean",
    },
    cptCode: {
      type: "array",
      items: {
        type: "string",
      },
    },
  },
} as const;

export const $UserInvitationRequest = {
  required: ["appointmentId", "schemaType"],
  type: "object",
  properties: {
    appointmentId: {
      type: "string",
      format: "uuid",
    },
    userIds: {
      uniqueItems: true,
      type: "array",
      items: {
        type: "string",
        format: "uuid",
      },
    },
    schemaType: {
      type: "string",
      enum: ["INTERNAL", "EXTERNAL"],
    },
    invitationLink: {
      type: "string",
    },
    isSms: {
      type: "boolean",
    },
    isEmail: {
      type: "boolean",
    },
  },
} as const;

export const $GenerateAvatarRequest = {
  type: "object",
  properties: {
    gender: {
      type: "string",
    },
    nurseId: {
      type: "string",
    },
    schema: {
      type: "string",
    },
    image: {
      type: "string",
    },
    voiceMode: {
      type: "string",
    },
    voiceName: {
      type: "string",
    },
    reset: {
      type: "boolean",
    },
  },
} as const;

export const $JsonNode = {
  type: "object",
} as const;

export const $MedicationAudioRequest = {
  type: "object",
  properties: {
    patientUuid: {
      type: "string",
      format: "uuid",
    },
    schema: {
      type: "string",
    },
    results: {
      $ref: "#/components/schemas/JsonNode",
    },
  },
} as const;

export const $Message = {
  type: "object",
  properties: {
    role: {
      type: "string",
    },
    content: {
      type: "string",
    },
    timezone: {
      type: "string",
    },
  },
} as const;

export const $PatientRequest = {
  type: "object",
  properties: {
    patient_id: {
      type: "integer",
      format: "int64",
    },
    schema: {
      type: "string",
    },
    age: {
      type: "integer",
      format: "int32",
    },
    gender: {
      type: "string",
      enum: ["MALE", "FEMALE", "OTHER"],
    },
    primary_diagnosis: {
      type: "string",
    },
    database: {
      type: "string",
    },
  },
} as const;

export const $PatientDTO = {
  type: "object",
  properties: {
    requests: {
      type: "array",
      items: {
        $ref: "#/components/schemas/PatientRequest",
      },
    },
  },
} as const;

export const $SseEmitter = {
  type: "object",
  properties: {
    timeout: {
      type: "integer",
      format: "int64",
    },
  },
} as const;

export const $EhrAccessToken = {
  type: "object",
  properties: {
    access_token: {
      type: "string",
    },
    token_type: {
      type: "string",
    },
    expires_in: {
      type: "integer",
      format: "int32",
    },
    scope: {
      type: "string",
    },
  },
} as const;
