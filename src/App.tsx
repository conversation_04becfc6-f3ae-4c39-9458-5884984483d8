import { Provider } from "react-redux";
import { RouterProvider } from "react-router-dom";

import { ThemeProvider } from "@mui/material";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import Loader from "./common-components/loader/loader";
import SnackbarAlert from "./common-components/snackbar-alert/snackbar-alert";
import RefreshToken from "./components/refresh-token/refresh-token";
import { reduxStore } from "./redux/store";
import { router } from "./routes/routes";
import { theme } from "./utils/theme";

function App() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false,
      },
    },
  });

  return (
    <ThemeProvider theme={theme}>
      <Provider store={reduxStore}>
        <QueryClientProvider client={queryClient}>
          <Loader />
          <SnackbarAlert />
          <RouterProvider router={router} />
          <RefreshToken />
        </QueryClientProvider>
      </Provider>
    </ThemeProvider>
  );
}

export default App;
