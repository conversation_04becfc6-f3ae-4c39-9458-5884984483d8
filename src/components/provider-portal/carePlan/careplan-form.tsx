import { useEffect, useState } from "react";
import { useRef } from "react";
import { Form<PERSON>rovider, Resolver, useFieldArray, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";

import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import RemoveIcon from "@mui/icons-material/Remove";
import { Box, Button, CircularProgress, Divider, FormControlLabel, IconButton, Typography } from "@mui/material";
import Accordion from "@mui/material/Accordion";
import AccordionDetails from "@mui/material/AccordionDetails";
import AccordionSummary from "@mui/material/AccordionSummary";
import Checkbox from "@mui/material/Checkbox";
import { Grid } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQuery, useQueryClient } from "@tanstack/react-query";

import MultiSelect from "@/common-components/chip-multi-select/Chip-Multi-Select";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import VitalReferenceRange from "@/common-components/vital-reference-range/VitalReferenceRange";

import AutocompleteMultiSelect from "@/components/ui/Form/AutocompleteMultiSelect";
import { Input } from "@/components/ui/Form/Input";
import Select from "@/components/ui/Form/Select";
import useAuthority from "@/hooks/use-authority";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useCarePlanControllerServiceCreateCarePlan, useCarePlanControllerServiceUpdateCarePlan } from "@/sdk/queries";
import { CarePlanControllerService, DeviceControllerService, MedicalCodeControllerService } from "@/sdk/requests";
import { CarePlan, Device, MedicalCode } from "@/sdk/requests/types.gen";
import { GetTenantId } from "@/services/common/get-tenant-id";

import { careplanResolverSchema } from "./care-plan-schema";

interface VitalRange {
  rangeType?:
    | "NORMAL"
    | "LOW_MODERATE"
    | "HIGH_MODERATE"
    | "CRITICAL"
    | "NORMAL_SYSTOLIC"
    | "NORMAL_DIASTOLIC"
    | "LOW_MODERATE_SYSTOLIC"
    | "HIGH_MODERATE_SYSTOLIC"
    | "LOW_MODERATE_DIASTOLIC"
    | "HIGH_MODERATE_DIASTOLIC"
    | "CRITICAL_SYSTOLIC"
    | "CRITICAL_DIASTOLIC";
  min?: number;
  max?: number;
}

interface CareplanFormValues {
  goals: {
    goalCategory: string;
    goalTitle: string;
    trackBy: string;
    targetType: string;
    targetValue: string;
    unit: string;
    objective: string;
    tasks: {
      task: string;
      taskDetails: string;
    }[];
  }[];
  selectedDevices: string[];
  allowManualInputs: boolean;
  protocolType: string[];
  applicableforGender: string[];
  DiagnosisCodes: { value: string; label: string }[];
  programTitle: string;
  programDuration: number;
  programDurationUnit: string;
  programOverview: string;
  applicableforAgeCriteria: string;
  applicableforAge: string;
  medicationAdherence: string;
}

interface RangeType {
  low_critical?: { min: number; max: number };
  low_moderate?: { min: number; max: number };
  normal?: { min: number; max: number };
  high_moderate?: { min: number; max: number };
  high_critical?: { min: number; max: number };
}

export const transformVitalRanges = (
  vitalRanges: VitalRange[]
): RangeType | { systolic: RangeType; diastolic: RangeType } => {
  if (!vitalRanges || !Array.isArray(vitalRanges)) {
    return {
      low_critical: { min: 0, max: 0 },
      low_moderate: { min: 0, max: 0 },
      normal: { min: 0, max: 0 },
      high_moderate: { min: 0, max: 0 },
      high_critical: { min: 0, max: 0 },
    };
  }

  const hasSystolic = vitalRanges.some((range) => range.rangeType?.includes("SYSTOLIC"));

  if (hasSystolic) {
    const systolic = {
      low_critical: {
        min: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE_SYSTOLIC")?.min || 0,
        max: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE_SYSTOLIC")?.max || 0,
      },
      low_moderate: {
        min: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE_SYSTOLIC")?.min || 0,
        max: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE_SYSTOLIC")?.max || 0,
      },
      normal: {
        min: vitalRanges.find((range) => range.rangeType === "NORMAL_SYSTOLIC")?.min || 0,
        max: vitalRanges.find((range) => range.rangeType === "NORMAL_SYSTOLIC")?.max || 0,
      },
      high_moderate: {
        min: vitalRanges.find((range) => range.rangeType === "HIGH_MODERATE_SYSTOLIC")?.min || 0,
        max: vitalRanges.find((range) => range.rangeType === "HIGH_MODERATE_SYSTOLIC")?.max || 0,
      },
      high_critical: {
        min: vitalRanges.find((range) => range.rangeType === "HIGH_MODERATE_SYSTOLIC")?.max || 0,
        max: vitalRanges.find((range) => range.rangeType === "CRITICAL_SYSTOLIC")?.max || 0,
      },
    };

    const diastolic = {
      low_critical: {
        min: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE_DIASTOLIC")?.min || 0,
        max: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE_DIASTOLIC")?.max || 0,
      },
      low_moderate: {
        min: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE_DIASTOLIC")?.min || 0,
        max: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE_DIASTOLIC")?.max || 0,
      },
      normal: {
        min: vitalRanges.find((range) => range.rangeType === "NORMAL_DIASTOLIC")?.min || 0,
        max: vitalRanges.find((range) => range.rangeType === "NORMAL_DIASTOLIC")?.max || 0,
      },
      high_moderate: {
        min: vitalRanges.find((range) => range.rangeType === "HIGH_MODERATE_DIASTOLIC")?.min || 0,
        max: vitalRanges.find((range) => range.rangeType === "HIGH_MODERATE_DIASTOLIC")?.max || 0,
      },
      high_critical: {
        min: vitalRanges.find((range) => range.rangeType === "HIGH_MODERATE_DIASTOLIC")?.max || 0,
        max: vitalRanges.find((range) => range.rangeType === "CRITICAL_DIASTOLIC")?.max || 0,
      },
    };

    return { systolic, diastolic };
  }

  return {
    low_critical: {
      min: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE")?.min || 0,
      max: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE")?.max || 0,
    },
    low_moderate: {
      min: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE")?.min || 0,
      max: vitalRanges.find((range) => range.rangeType === "LOW_MODERATE")?.max || 0,
    },
    normal: {
      min: vitalRanges.find((range) => range.rangeType === "NORMAL")?.min || 0,
      max: vitalRanges.find((range) => range.rangeType === "NORMAL")?.max || 0,
    },
    high_moderate: {
      min: vitalRanges.find((range) => range.rangeType === "HIGH_MODERATE")?.min || 0,
      max: vitalRanges.find((range) => range.rangeType === "HIGH_MODERATE")?.max || 0,
    },
    high_critical: {
      min: vitalRanges.find((range) => range.rangeType === "HIGH_MODERATE")?.max || 0,
      max: vitalRanges.find((range) => range.rangeType === "CRITICAL")?.max || 0,
    },
  };
};

interface Option {
  label: string;
  value: string;
  disabled?: boolean;
}

const CareplanForm = () => {
  const role = useAuthority();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [deviceName, setDeviceName] = useState<{ value: string; label: string }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchDiagnosisCodes, setSearchDiagnosisCodes] = useState<string>("");
  const [DiagnosisCodes, setDiagnosisCodes] = useState<Option[]>([]);
  const [DiagnosisCodesPageSize, setDiagnosisCodesPageSize] = useState(0);

  const location = useLocation();
  const EditData = location.state?.EditData ?? null;
  const carePlanQueryKey = EditData?.uuid ? [`careplan-id-${EditData.uuid}`] : ["careplan-id-new"];
  const [carePlanEditData, setCarePlanEditData] = useState<CarePlan | undefined>();

  const initialValues: CareplanFormValues = {
    goals: [
      {
        goalCategory: "",
        goalTitle: "",
        trackBy: "",
        targetType: "",
        targetValue: "",
        unit: "",
        objective: "",
        tasks: [
          {
            task: "",
            taskDetails: "",
          },
        ],
      },
    ],
    selectedDevices: [],
    allowManualInputs: true,
    protocolType: [],
    applicableforGender: [],
    DiagnosisCodes: [],
    programTitle: "",
    programDuration: 0,
    programDurationUnit: "",
    programOverview: "",
    applicableforAgeCriteria: "",
    applicableforAge: "",
    medicationAdherence: "",
  };

  const methods = useForm<CareplanFormValues>({
    resolver: yupResolver(careplanResolverSchema) as unknown as Resolver<CareplanFormValues>,
    defaultValues: initialValues,
  });

  const { control, handleSubmit, setValue, watch, reset } = methods;
  const { fields, append, remove } = useFieldArray({
    control,
    name: "goals",
  });

  const {
    data: carePlanData,
    isSuccess: isSuccessGetCarePlan,
    isLoading: isCarePlanLoading,
  } = useQuery({
    queryKey: carePlanQueryKey,
    queryFn: () => {
      if (!EditData?.uuid) return null;
      setIsLoading(true);
      return CarePlanControllerService.getCarePlanById({
        carePlanId: EditData?.uuid,
        xTenantId: GetTenantId(),
      });
    },
    enabled: !!EditData?.uuid,
  });

  useEffect(() => {
    reset(initialValues);

    if (!EditData) {
      const emptyCarePlan: Partial<CarePlan> = {
        ...initialValues,
        protocolType: undefined,
        vitalReferences: [],
      };
      setCarePlanEditData(emptyCarePlan as CarePlan);
    }
  }, [EditData?.uuid, reset]);

  useEffect(() => {
    if (isSuccessGetCarePlan && carePlanData?.data) {
      const res = carePlanData.data as CarePlan;
      setCarePlanEditData(res);

      setValue("programTitle", res?.title || "");
      setValue("programDuration", res.duration as number);
      setValue("programDurationUnit", res?.durationUnit || "");
      setValue("programOverview", res.overview || "");
      setValue("applicableforAgeCriteria", res.ageCriteria || "");
      setValue("applicableforAge", res?.age || "");
      setValue(
        "DiagnosisCodes",
        Array.isArray(res.diagnosisCodes)
          ? res.diagnosisCodes.map((code) => (typeof code === "string" ? { value: code, label: code } : code))
          : []
      );
      setValue(
        "applicableforGender",
        res.gender === "UNISEX"
          ? ["MALE", "FEMALE"]
          : res.gender === "MALE"
            ? ["MALE"]
            : res.gender === "FEMALE"
              ? ["FEMALE"]
              : []
      );
      setValue(
        "selectedDevices",
        res.devices ? res.devices.map((device) => device.name).filter((name): name is string => !!name) : []
      );
      setValue("protocolType", res.protocolType ? [res.protocolType] : []);
      setValue(
        "goals",
        res?.programGoals?.map((goal) => ({
          goalCategory: (goal.category as string) || "",
          goalTitle: goal.title || "",
          trackBy: goal.trackBy || "",
          targetType: goal.targetType || "",
          targetValue: goal.targetValue || "",
          unit: goal.unit || "",
          objective: goal.objective || "",
          tasks:
            goal.programGoalTasks?.map((task) => ({
              task: task.task || "",
              taskDetails: task.details || "",
            })) || [],
        })) || [initialValues.goals[0]]
      );

      setIsLoading(false);
    }
  }, [isSuccessGetCarePlan, carePlanData, setValue]);

  const { data: DeviceData, isSuccess } = useQuery({
    queryKey: ["careplan-devices"],
    queryFn: () => {
      setIsLoading(true);
      return DeviceControllerService.getAllDevices({
        size: 100,
        page: 0,
        sortBy: "created",
        sortDirection: "desc",
        xTenantId: GetTenantId(),
      });
    },
  });

  useEffect(() => {
    if (isSuccess) {
      const response = DeviceData.data?.content as Device[];
      const setValue = response?.map((name) => ({
        value: name.name as string,
        label: name.name,
      }));
      setDeviceName(setValue);
      setIsLoading(false);
    }
  }, [isSuccess, DeviceData]);

  const handleOnSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["get-care-plan"] });
    dispatch(
      setSnackbarOn({
        severity: AlertSeverity.SUCCESS,
        message: EditData ? "Care Plan Updated Successfully" : "Care Plan Created Successfully!",
      })
    );
  };

  const addCarePlanService = useCarePlanControllerServiceCreateCarePlan({
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
      setIsLoading(false);
    },
  });

  const EditCarePlanService = useCarePlanControllerServiceUpdateCarePlan({
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
      setIsLoading(false);
    },
  });

  const staticVitalRanges = {
    bloodPressure: [
      {
        rangeType: "LOW_MODERATE_SYSTOLIC",
        min: 91.0,
        max: 100.0,
      },
      {
        rangeType: "HIGH_MODERATE_SYSTOLIC",
        min: 131.0,
        max: 140.0,
      },
      {
        rangeType: "LOW_MODERATE_DIASTOLIC",
        min: 61.0,
        max: 80.0,
      },
      {
        rangeType: "HIGH_MODERATE_DIASTOLIC",
        min: 81.0,
        max: 90.0,
      },
      {
        rangeType: "CRITICAL_SYSTOLIC",
        min: 141.0,
        max: 180.0,
      },
      {
        rangeType: "CRITICAL_DIASTOLIC",
        min: 91.0,
        max: 120.0,
      },
      {
        rangeType: "NORMAL_SYSTOLIC",
        min: 101.0,
        max: 130.0,
      },
      {
        rangeType: "NORMAL_DIASTOLIC",
        min: 61.0,
        max: 80.0,
      },
    ],
    heartRate: [
      {
        rangeType: "LOW_MODERATE",
        min: 41.0,
        max: 60.0,
      },
      {
        rangeType: "HIGH_MODERATE",
        min: 91.0,
        max: 110.0,
      },
      {
        rangeType: "CRITICAL",
        min: 111.0,
        max: 180.0,
      },
      {
        rangeType: "NORMAL",
        min: 61.0,
        max: 90.0,
      },
    ],
    weight: [
      {
        rangeType: "NORMAL",
        min: 18.5,
        max: 24.9,
      },
    ],
  };

  // Diagnosis Codes Query
  const { data: diagnosisCodesData, isLoading: isDiagnosisCodesLoading } = useQuery({
    queryKey: ["diagnosisCodes", searchDiagnosisCodes, DiagnosisCodesPageSize],
    queryFn: () =>
      MedicalCodeControllerService.getMedicalCodes({
        xTenantId: GetTenantId(),
        searchString: searchDiagnosisCodes.toUpperCase(),
        size: 30,
        page: DiagnosisCodesPageSize,
        type: "ICD10",
        sortBy: "code",
        sort: "asc",
      }),
  });

  useEffect(() => {
    if (diagnosisCodesData?.data?.content) {
      const response = diagnosisCodesData.data.content as MedicalCode[];
      const diagnosisOptions = response.map((code) => ({
        label: `${code.code} : ${code.description}`,
        value: code.code || "",
      }));
      if (DiagnosisCodesPageSize === 0) {
        setDiagnosisCodes(diagnosisOptions);
      } else {
        setDiagnosisCodes((prev) => [...prev, ...diagnosisOptions]);
      }
    }
  }, [diagnosisCodesData, DiagnosisCodesPageSize]);

  const searchDiagnosisTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleSearchDiagnosisCodes = (searchValue: string) => {
    setDiagnosisCodesPageSize(0);
    setDiagnosisCodes([]);
    if (searchValue.trim() !== searchDiagnosisCodes) {
      if (searchDiagnosisTimeout.current) {
        clearTimeout(searchDiagnosisTimeout.current);
      }
      searchDiagnosisTimeout.current = setTimeout(() => {
        setSearchDiagnosisCodes(searchValue.trim());
      }, 1500);
    }
  };

  function handleDiagnosisCodesScroll() {
    setDiagnosisCodesPageSize((prev) => prev + 1);
  }

  const onSubmit = async (values: unknown) => {
    setIsLoading(true);
    const formValues = values as CareplanFormValues;

    const payload: CarePlan = {
      title: formValues.programTitle,
      duration: +formValues.programDuration,
      durationUnit: formValues.programDurationUnit?.toUpperCase() as "DAY" | "WEEK" | "MONTH" | "YEAR",
      overview: formValues.programOverview,
      gender:
        formValues.applicableforGender?.length === 1
          ? formValues.applicableforGender[0] === "Male"
            ? "MALE"
            : ("FEMALE" as string as "MALE" | "FEMALE" | "UNISEX")
          : "UNISEX",
      ageCriteria: formValues.applicableforAgeCriteria,
      age: formValues.applicableforAge,
      diagnosisCodes: formValues.DiagnosisCodes?.map((code) => code.value) || [],
      deviceName: formValues.selectedDevices,
      vitalReferences: [
        {
          vitalType: "Blood Pressure",
          vitalRanges: staticVitalRanges.bloodPressure as VitalRange[],
        },
        {
          vitalType: "Heart Rate",
          vitalRanges: staticVitalRanges.heartRate as VitalRange[],
        },
        {
          vitalType: "Weight",
          vitalRanges: staticVitalRanges.weight as VitalRange[],
        },
      ],
      programGoals: formValues.goals?.map((goal) => ({
        category: goal.goalCategory,
        title: goal.goalTitle,
        trackBy: goal.trackBy?.toUpperCase() as "DAY" | "WEEK" | "MONTH" | "YEAR",
        targetType: goal.targetType,
        targetValue: goal.targetValue.toString(),
        unit: goal.unit,
        objective: goal.objective,
        programGoalTasks: goal.tasks?.map((task) => ({
          task: task.task,
          details: task.taskDetails,
        })),
      })),
      protocolType: formValues.protocolType[0] as "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG",
    };

    if (EditData) {
      await EditCarePlanService.mutateAsync({
        requestBody: { ...payload, uuid: EditData.uuid },
        xTenantId: GetTenantId(),
      });
    } else {
      await addCarePlanService.mutateAsync({ requestBody: payload });
    }

    if (role.isProvider) {
      navigate("/provider/settings/?tab=2");
    } else {
      navigate("/provider/settings/?tab=3");
    }
  };

  const StatusIndicator = ({ color, label }: { color: string; label: string }) => (
    <Box style={{ display: "flex", alignItems: "center", gap: "8px" }}>
      <Box style={{ width: 16, height: 16, backgroundColor: color, borderRadius: 4 }}></Box>
      <Typography>{label}</Typography>
    </Box>
  );

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid height="100%" p={2} width="100%">
          <Grid height={"100%"} container flexDirection={"column"}>
            {isLoading || isCarePlanLoading ? (
              <Box display="flex" justifyContent="center" alignItems="center" height="calc(100vh - 200px)">
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Grid
                  container
                  p={2}
                  justifyContent={"space-between"}
                  width={"100%"}
                  position={"sticky"}
                  zIndex={1}
                  top={"0px"}
                  bgcolor={"white"}
                >
                  <Grid display={"flex"} alignItems={"center"} justifyContent={"center"} gap={1}>
                    <IconButton
                      onClick={() => {
                        if (role.isProvider) {
                          navigate("/provider/settings/?tab=2");
                        } else {
                          navigate("/provider/settings/?tab=3");
                        }
                      }}
                    >
                      <CloseIcon />
                    </IconButton>
                    <Typography fontSize={"18px"}>Care Plan Form</Typography>
                  </Grid>
                  <Grid>
                    <Button
                      variant="contained"
                      type="submit"
                      loading={addCarePlanService.isPending || EditCarePlanService.isPending || isLoading}
                    >
                      {EditData ? "Save Care Plan" : "Create Care Plan"}
                    </Button>
                  </Grid>
                </Grid>

                {/* form content */}
                <Grid p={4} container flexDirection={"column"} gap={4}>
                  <Grid container gap={2}>
                    <Grid container size={12} columnSpacing={2}>
                      <Grid container size={8}>
                        <Input name="programTitle" isRequired label="Program Title" />
                      </Grid>
                      <Grid container size={4} columnSpacing={2}>
                        <Grid container size={6}>
                          <Input name="programDuration" isRequired label="Program Duration" />
                        </Grid>
                        <Grid size={6}>
                          <Select
                            name="programDurationUnit"
                            label="Duration Unit"
                            isRequired
                            options={[
                              { label: "Day", value: "DAY" },
                              { label: "Week", value: "WEEK" },
                              { label: "Month", value: "MONTH" },
                              { label: "Year", value: "YEAR" },
                            ]}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid size={12}>
                      <Input name="programOverview" label="Program Overview" isRequired />
                    </Grid>
                  </Grid>

                  {/* Applicable form */}
                  <Grid
                    borderTop={"1px solid #E8EBEC"}
                    display={"flex"}
                    pt={2}
                    container
                    flexDirection={"column"}
                    gap={2.5}
                  >
                    <Grid size={5}>
                      <Typography fontSize={"18px"} fontWeight={500}>
                        Applicable For
                      </Typography>
                    </Grid>

                    <Grid container size={12} columnSpacing={2} alignItems="flex-start">
                      <Grid size={2}>
                        <MultiSelect
                          isRequired
                          placeholder="Select gender"
                          name="applicableforGender"
                          label="Gender"
                          options={[
                            { label: "Male", value: "Male" },
                            { label: "Female", value: "Female" },
                          ]}
                        />
                      </Grid>
                      <Grid container size={4} columnSpacing={2}>
                        <Grid size={6}>
                          <Select
                            isRequired
                            name="applicableforAgeCriteria"
                            label="Age Criteria"
                            options={[
                              { label: "Older than", value: "Older than" },
                              { label: "Younger than", value: "Younger than" },
                              { label: "Equal to", value: "Equal to" },
                              { label: "Between", value: "Between" },
                              { label: "Not equal to", value: "Not equal to" },
                            ]}
                          />
                        </Grid>
                        <Grid size={6}>
                          <Input isRequired name="applicableforAge" label="Age" placeholder="Enter Age" />
                        </Grid>
                      </Grid>
                      <Grid size={6}>
                        <AutocompleteMultiSelect
                          isRequired
                          name="DiagnosisCodes"
                          options={DiagnosisCodes}
                          label={"Diagnosis Codes"}
                          placeholder="Select Diagnosis Codes"
                          onSearch={handleSearchDiagnosisCodes}
                          isLoading={isDiagnosisCodesLoading}
                          isEndOfListScroll={handleDiagnosisCodesScroll}
                        />
                      </Grid>
                    </Grid>
                  </Grid>

                  {/* Routine Check-up */}
                  <Grid container flexDirection={"column"} gap={0}>
                    <Grid borderTop={"1px solid #E8EBEC"} pt={"8px"} container flexDirection={"column"}>
                      <Grid size={5} mt={2}>
                        <Typography
                          fontFamily="Roboto"
                          fontWeight={500}
                          fontSize="18px"
                          lineHeight="21.6px"
                          letterSpacing="0%"
                        >
                          Program Goals
                        </Typography>
                      </Grid>
                    </Grid>

                    {/* Program Goals */}
                    <Grid container flexDirection={"column"} gap={0}>
                      {fields.map((field, index) => {
                        const taskFields = watch(`goals.${index}.tasks`) || [];
                        return (
                          <Accordion key={field.id} defaultExpanded={true} elevation={0}>
                            <AccordionSummary>
                              <Grid container alignItems="center">
                                <Grid>
                                  <Typography
                                    mr={2}
                                    fontFamily="Roboto"
                                    fontWeight={500}
                                    fontSize="16px"
                                    lineHeight="120%"
                                    letterSpacing="0%"
                                  >
                                    Goal {index + 1}{" "}
                                  </Typography>
                                </Grid>
                                <Grid>
                                  <ExpandMoreIcon />
                                </Grid>
                              </Grid>
                              {index > 0 && (
                                <IconButton onClick={() => remove(index)} sx={{ marginLeft: "auto" }}>
                                  <DeleteIcon />
                                </IconButton>
                              )}
                            </AccordionSummary>
                            <AccordionDetails>
                              <Grid container spacing={2}>
                                <Grid size={4}>
                                  <Select
                                    isRequired
                                    name={`goals.${index}.goalCategory`}
                                    label="Select Category"
                                    options={[
                                      { label: "Weight", value: "weight" },
                                      { label: "Blood Pressure", value: "bloodPressure" },
                                      { label: "Diet", value: "diet" },
                                      { label: "Exercise", value: "exercise" },
                                    ]}
                                  />
                                </Grid>
                                <Grid size={4}>
                                  <Input isRequired name={`goals.${index}.goalTitle`} label="Goal Title" />
                                </Grid>

                                <Grid size={4}>
                                  <Select
                                    isRequired
                                    name={`goals.${index}.trackBy`}
                                    label="Track By"
                                    options={[
                                      { label: "Month", value: "MONTH" },
                                      { label: "Week", value: "WEEK" },
                                      { label: "Day", value: "DAY" },
                                    ]}
                                  />
                                </Grid>

                                <Grid size={4}>
                                  <Input isRequired name={`goals.${index}.targetType`} label="Target Type" />
                                </Grid>

                                <Grid size={4}>
                                  <Input isRequired name={`goals.${index}.targetValue`} label="Target Value" />
                                </Grid>

                                <Grid size={4}>
                                  <Input isRequired name={`goals.${index}.unit`} label="Unit" />
                                </Grid>

                                <Grid size={12}>
                                  <Input name={`goals.${index}.objective`} label="Objective" />
                                </Grid>

                                {taskFields.map((_taskField, taskIndex) => (
                                  <Grid container size={12} key={taskIndex} sx={{ width: "100%" }}>
                                    <Grid size={4}>
                                      <Input
                                        isRequired
                                        name={`goals.${index}.tasks.${taskIndex}.task`}
                                        label={`Task ${taskIndex + 1}`}
                                      />
                                    </Grid>
                                    <Grid size={7}>
                                      <Input
                                        isRequired
                                        name={`goals.${index}.tasks.${taskIndex}.taskDetails`}
                                        label={`Task ${taskIndex + 1} Details`}
                                      />
                                    </Grid>
                                    <Grid size={1} pt={3} sx={{ display: "flex", justifyContent: "flex-end" }}>
                                      <Button
                                        onClick={() => {
                                          const updatedTasks = taskFields.filter((_, i) => i !== taskIndex);
                                          setValue(`goals.${index}.tasks`, updatedTasks);
                                        }}
                                      >
                                        {taskIndex != 0 && (
                                          <>
                                            <RemoveIcon sx={{ color: "#008080" }} /> Remove
                                          </>
                                        )}
                                      </Button>
                                    </Grid>
                                  </Grid>
                                ))}

                                <Button
                                  variant="text"
                                  startIcon={<AddIcon />}
                                  onClick={() => {
                                    const updatedTasks = [...taskFields, { task: "", taskDetails: "" }];
                                    setValue(`goals.${index}.tasks`, updatedTasks);
                                  }}
                                  sx={{
                                    textTransform: "none",
                                    color: "#00796B",
                                    padding: "0px",
                                    display: "flex",
                                    justifyContent: "flex-start",
                                    "&:hover": {
                                      backgroundColor: "transparent",
                                    },
                                    "&:click": {
                                      backgroundColor: "transparent",
                                    },
                                  }}
                                >
                                  <Typography variant="body1" sx={{ fontWeight: "medium" }}>
                                    Add Task
                                  </Typography>
                                </Button>
                              </Grid>
                            </AccordionDetails>
                          </Accordion>
                        );
                      })}

                      <Button
                        startIcon={<AddIcon />}
                        variant="outlined"
                        type="button"
                        onClick={() => {
                          append({
                            goalCategory: "",
                            goalTitle: "",
                            trackBy: "",
                            targetType: "",
                            targetValue: "",
                            unit: "",
                            objective: "",
                            tasks: [{ task: "", taskDetails: "" }],
                          });
                        }}
                        sx={{
                          width: "140px",
                          backgroundColor: "#EEFBFF",
                        }}
                      >
                        <Typography
                          variant="bodySmall"
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 500,
                            fontSize: "14px",
                            letterSpacing: "0%",
                          }}
                        >
                          Add Goal
                        </Typography>
                      </Button>
                    </Grid>

                    <Divider sx={{ mt: 3, mb: 2 }} />
                    {/* Assign Device */}

                    <Grid size={12}>
                      <Grid size={12} mt={2}>
                        <Typography
                          fontFamily="Roboto"
                          fontWeight={500}
                          fontSize="18px"
                          lineHeight="21.6px"
                          letterSpacing="0%"
                        >
                          Assign Device
                        </Typography>
                      </Grid>

                      {/* select device */}
                      <Grid container size={12} columnSpacing={2} alignItems="center" pt={2}>
                        <Grid container size={12} display="flex" flexDirection="row" alignItems="center">
                          <Grid size={8}>
                            <MultiSelect
                              isRequired
                              name="selectedDevices"
                              label={"Select Device"}
                              options={deviceName}
                            />
                          </Grid>
                          <Grid size={4} pt={3}>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  name="allowManualInputs"
                                  checked={watch("allowManualInputs")}
                                  onChange={(e) => setValue("allowManualInputs", e.target.checked)}
                                  sx={{ "& .MuiSvgIcon-root": { fontSize: 28 } }}
                                />
                              }
                              label="Allow manual inputs"
                              sx={{
                                margin: 0,
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontSize: "14px",
                                lineHeight: "120%",
                                letterSpacing: "0%",
                                verticalAlign: "middle",
                              }}
                            />
                          </Grid>
                        </Grid>
                      </Grid>

                      <Divider sx={{ mt: 3, mb: 2 }} />

                      {/* Vital Reference Range */}
                      <Grid container display="flex" justifyContent="space-between" alignItems="center">
                        <Grid
                          container
                          size={12}
                          display="flex"
                          justifyContent="space-between"
                          alignItems="center"
                          mb={3}
                          mt={3}
                        >
                          <Grid>
                            <Typography
                              fontFamily="Roboto"
                              fontWeight={500}
                              fontSize="18px"
                              lineHeight="21.6px"
                              letterSpacing="0%"
                            >
                              Vital Reference Range
                            </Typography>
                          </Grid>
                          <Grid style={{ display: "flex", alignItems: "center", gap: "16px" }}>
                            <StatusIndicator color="#7FD067" label="Normal" />
                            <StatusIndicator color="#FCB33B" label="Abnormal" />
                            <StatusIndicator color="#CE0718" label="Critical" />
                          </Grid>
                        </Grid>
                        <Grid container spacing={2} size={12}>
                          {EditData && carePlanEditData?.vitalReferences ? (
                            carePlanEditData.vitalReferences.map((vital, index) => (
                              <Grid
                                key={index}
                                size={4}
                                mb={2}
                                sx={{
                                  ...(carePlanEditData.vitalReferences &&
                                    index === carePlanEditData.vitalReferences.length - 1 &&
                                    carePlanEditData.vitalReferences.length > 3 && {
                                      marginLeft: 0,
                                    }),
                                }}
                              >
                                <VitalReferenceRange
                                  title={vital.vitalType || ""}
                                  unit={
                                    vital.vitalType === "Blood Glucose"
                                      ? "mg/dL"
                                      : vital.vitalType === "Heart Rate"
                                        ? "bpm"
                                        : vital.vitalType === "Weight"
                                          ? "kg"
                                          : vital.vitalType === "ECG"
                                            ? "bpm"
                                            : vital.vitalType === "HRV"
                                              ? "ms"
                                              : vital.vitalType === "Oxygen Saturation"
                                                ? "%"
                                                : vital.vitalType === "Stress"
                                                  ? "%"
                                                  : ""
                                  }
                                  ranges={transformVitalRanges(vital.vitalRanges as VitalRange[])}
                                />
                              </Grid>
                            ))
                          ) : (
                            <>
                              {[
                                { title: "Blood Pressure", unit: "mmHg", ranges: staticVitalRanges.bloodPressure },
                                { title: "Heart Rate", unit: "bpm", ranges: staticVitalRanges.heartRate },
                                // { title: "Blood Glucose", unit: "mg/dL", ranges: staticVitalRanges.bloodGlucose },
                                { title: "Weight", unit: "kg", ranges: staticVitalRanges.weight },
                              ].map((vital, index) => (
                                <Grid key={index} size={4} mb={2}>
                                  <VitalReferenceRange
                                    title={vital.title}
                                    unit={vital.unit}
                                    ranges={transformVitalRanges(vital.ranges as VitalRange[])}
                                  />
                                </Grid>
                              ))}
                            </>
                          )}
                        </Grid>
                      </Grid>

                      <Grid container size={12} mt={4} pt={2} sx={{ borderTop: "1px solid #E8EBEC" }}>
                        <Grid size={12}>
                          <Typography
                            sx={{
                              fontFamily: "Roboto",
                              fontWeight: 500,
                              fontSize: 18,
                              lineHeight: "120%",
                              letterSpacing: "0%",
                            }}
                          >
                            Add Response Protocols
                          </Typography>
                        </Grid>
                        <Grid container size={12} mt={2}>
                          <Grid size={4}>
                            <MultiSelect
                              name="protocolType"
                              options={[{ label: "Out of Range BP", value: "OUT_OF_RANGE_BP" }]}
                              label={"Select Protocols (Viewable to nurses and provider only"}
                            />
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </>
            )}
          </Grid>
        </Grid>
      </form>
    </FormProvider>
  );
};

export default CareplanForm;
