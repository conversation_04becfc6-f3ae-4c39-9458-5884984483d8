import { useEffect, useState } from "react";

import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { InputAdornment } from "@mui/material";
import { Box, OutlinedInput, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";

import CustomAccordion from "@/common-components/accordion/accordion";
import VitalReferenceRange from "@/common-components/vital-reference-range/VitalReferenceRange";

import { CarePlanControllerService, Device, ProgramGoal, Protocol, VitalReference } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";

import { transformVitalRanges } from "../carePlan/careplan-form";

interface VitalRange {
  rangeType?:
    | "NORMAL"
    | "LOW_MODERATE"
    | "HIGH_MODERATE"
    | "CRITICAL"
    | "NORMAL_SYSTOLIC"
    | "NORMAL_DIASTOLIC"
    | "LOW_MODERATE_SYSTOLIC"
    | "HIGH_MODERATE_SYSTOLIC"
    | "LOW_MODERATE_DIASTOLIC"
    | "HIGH_MODERATE_DIASTOLIC"
    | "CRITICAL_SYSTOLIC"
    | "CRITICAL_DIASTOLIC";
  min?: number;
  max?: number;
}

interface ViewCarePlanProps {
  nurseType?: String;
  carePlanId?: string;
}
interface CarePlan {
  uuid?: string;
  title?: string;
  duration?: number;
  durationUnit?: "DAY" | "WEEK" | "MONTH" | "YEAR";
  overview?: string;
  gender?: "MALE" | "FEMALE" | "UNISEX";
  ageCriteria?: string;
  age?: string;
  deviceName?: string[];
  devices?: Device[];
  routineCheckup?: string;
  programGoals?: ProgramGoal[];
  vitalReferences?: VitalReference[];
  globalCarePlan?: boolean;
  external?: boolean;
  protocolType?: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
  active?: boolean;
  archive?: boolean;
  trackedVitals?: string[];
  modified?: string;
  diagnosisCodes?: string[];
  protocol?: Protocol[];
}

const ViewCarePlan = ({ carePlanId, nurseType }: ViewCarePlanProps) => {
  const [carePlanDetails, setCarePlanDetails] = useState<CarePlan | null>(null);
  const [loading, setLoading] = useState(true);

  // Status indicators for vital reference ranges
  const StatusIndicator = ({ color, label }: { color: string; label: string }) => (
    <Box style={{ display: "flex", alignItems: "center", gap: "8px" }}>
      <Box style={{ width: 16, height: 16, backgroundColor: color, borderRadius: 4 }}></Box>
      <Typography>{label}</Typography>
    </Box>
  );

  // Fetch care plan details
  const { data, isLoading } = useQuery({
    queryKey: ["get-patient-care-plan", carePlanId],
    queryFn: async () => {
      if (!carePlanId) return null;
      try {
        const response = await CarePlanControllerService.getCarePlanById({
          carePlanId: carePlanId,
          xTenantId: nurseType == "INTERNAL" ? GetTenantId() : "eamata",
        });
        return response.data;
      } catch (error) {
        return null;
      }
    },
    enabled: !!carePlanId,
  });

  useEffect(() => {
    if (data) {
      setCarePlanDetails(data);
      setLoading(false);
    } else if (!isLoading) {
      setLoading(false);
    }
  }, [data, isLoading]);

  if (loading) {
    return (
      <Box p={3}>
        <Typography>Loading care plan details...</Typography>
      </Box>
    );
  }

  if (!carePlanDetails) {
    return (
      <Box p={3}>
        <Typography>No care plan details found</Typography>
      </Box>
    );
  }

  // Transform gender for display
  const rawGender = carePlanDetails.gender;
  const selectedGender =
    rawGender === "UNISEX"
      ? ["Male", "Female"]
      : rawGender
        ? [rawGender.charAt(0) + rawGender.slice(1).toLowerCase()]
        : [];

  return (
    <Grid
      sx={{
        width: "100%",
        height: "100%",
        padding: "30px 25px",
        overflowY: "auto",
        maxHeight: "calc(100vh - 60px)",
      }}
    >
      <Grid container mb={3}>
        <Grid>
          <Typography variant="h5" fontWeight="bold" color="#333">
            {carePlanDetails.title}
          </Typography>
          <Box display="flex" alignItems="center" mt={1}>
            <Box component="span" display="flex" alignItems="center" mr={1}>
              <CalendarMonthIcon style={{ fontSize: 20, color: "#667085" }} />
            </Box>
            <Typography color="#667085">
              {carePlanDetails.modified
                ? `${new Date(carePlanDetails.modified).toLocaleDateString("en-GB", { day: "2-digit", month: "short", year: "numeric" })} - ${
                    carePlanDetails.duration && carePlanDetails.durationUnit
                      ? new Date(
                          new Date(carePlanDetails.modified).getTime() +
                            getDurationInMs(carePlanDetails.duration, carePlanDetails.durationUnit)
                        ).toLocaleDateString("en-GB", { day: "2-digit", month: "short", year: "numeric" })
                      : "Ongoing"
                  }`
                : `${carePlanDetails.duration || ""} ${carePlanDetails.durationUnit?.toLowerCase() || ""}`}
            </Typography>
          </Box>
          {carePlanDetails.overview && (
            <Typography mt={2} color="#515C5F">
              {carePlanDetails.overview}
            </Typography>
          )}
        </Grid>
      </Grid>
      <Grid container mb={3}>
        <Box display="flex" alignItems="center" mt={2}>
          <Typography color="#515C5F" mr={1}>
            Applicable for:
          </Typography>
          <Box display="flex" gap={1}>
            {selectedGender.map((gender, index) => (
              <Box
                key={index}
                component="span"
                bgcolor="#E0F2FF"
                color="#007BFF"
                px={1}
                py={0.5}
                borderRadius={1}
                fontSize="14px"
              >
                {gender}
              </Box>
            ))}
            <Box component="span" bgcolor="#E0F2FF" color="#007BFF" px={1} py={0.5} borderRadius={1} fontSize="14px">
              {carePlanDetails.ageCriteria && (
                <Box
                  component="span"
                  bgcolor="#E0F2FF"
                  color="#007BFF"
                  px={1}
                  py={0.5}
                  borderRadius={1}
                  fontSize="14px"
                >
                  {carePlanDetails.ageCriteria}
                </Box>
              )}
            </Box>
            <Box component="span" bgcolor="#E0F2FF" color="#007BFF" px={1} py={0.5} borderRadius={1} fontSize="14px">
              {carePlanDetails.age && (
                <Box
                  component="span"
                  bgcolor="#E0F2FF"
                  color="#007BFF"
                  px={1}
                  py={0.5}
                  borderRadius={1}
                  fontSize="14px"
                >
                  {carePlanDetails.age}
                </Box>
              )}
            </Box>
            {/* <Box component="span" bgcolor="#E0F2FF" color="#007BFF" px={1} py={0.5} borderRadius={1} fontSize="14px">
              {carePlanDetails?.diagnosisCodes?.map((code, index) => (
                <Box
                  key={index}
                  component="span"
                  bgcolor="#E0F2FF"
                  color="#007BFF"
                  px={1}
                  py={0.5}
                  borderRadius={1}
                  fontSize="14px"
                  mr={1}
                >
                  {code}
                </Box>
              ))}
            </Box> */}
          </Box>
        </Box>
      </Grid>

      <Grid container spacing={2} alignItems="flex-start">
        <Grid size={12} mt={2} mb={2}>
          <Typography
            variant="medium"
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              lineHeight: "19.2px",
              letterSpacing: "0%",
            }}
          >
            Program Goals
          </Typography>
        </Grid>
        {Array.isArray(carePlanDetails?.programGoals) &&
          carePlanDetails.programGoals.map((item, index: number) => (
            <Grid size={6} key={index} mb={3}>
              <CustomAccordion title={item.title || ""}>
                <Grid container alignItems="center" sx={{ borderBottom: `1px solid ${theme.palette.divider}` }}>
                  <Grid container alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                    <Grid container alignItems="center" justifyContent="flex-start" spacing={2}>
                      <Grid>
                        <Typography variant="body2">{item.category}</Typography>
                      </Grid>
                      <Grid container marginLeft={"180px"}>
                        <OutlinedInput
                          sx={{ width: "20ch", height: "45px" }}
                          id={`outlined-adornment-weight-${index}`}
                          value={String(item.targetValue)}
                          endAdornment={
                            <InputAdornment position="end">
                              <strong
                                style={{
                                  fontFamily: "Roboto",
                                  fontWeight: 500,
                                  fontSize: "14px",
                                  lineHeight: "16.41px",
                                  letterSpacing: "0%",
                                }}
                              >
                                {item.unit}
                              </strong>
                            </InputAdornment>
                          }
                          aria-describedby={`outlined-weight-helper-text-${index}`}
                          inputProps={{
                            "aria-label": "weight",
                          }}
                          disabled
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid container>
                  <ul>
                    {item.programGoalTasks?.map((task, recIndex: number) => (
                      <li key={recIndex}>
                        <Typography
                          variant="body2"
                          sx={{
                            color: "#515C5F",
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontSize: "14px",
                            lineHeight: "22.4px",
                            letterSpacing: "0%",
                          }}
                        >
                          {task.details}
                        </Typography>
                      </li>
                    ))}
                  </ul>
                </Grid>
              </CustomAccordion>
            </Grid>
          ))}
      </Grid>

      <Grid
        padding={"20px 20px"}
        bgcolor={"#F2F7F9"}
        // borderTop={"1px solid #E8EBEC"}
        display={"flex"}
        pt={2}
        container
        flexDirection={"column"}
        gap={2.5}
        borderRadius={2}
      >
        <Grid
          container
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          // borderTop={"1px solid #CDD7DA"}
        ></Grid>
        <Grid
          container
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          // borderTop={"1px solid #CDD7DA"}
        >
          <Grid
            container
            size={12}
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={3}
            // mt={3}
          >
            <Grid>
              <Typography
                fontFamily="Roboto"
                fontWeight={500}
                fontSize="18px"
                lineHeight="21.6px"
                letterSpacing="0%"
                color="#515C5F"
              >
                Vital Reference Range
              </Typography>
            </Grid>
            <Grid style={{ display: "flex", alignItems: "center", gap: "16px" }}>
              <StatusIndicator color="#7FD067" label="Normal" />
              <StatusIndicator color="#FCB33B" label="Abnormal" />
              <StatusIndicator color="#CE0718" label="Critical" />
            </Grid>
          </Grid>
          <Grid container spacing={2} size={12}>
            {carePlanDetails?.vitalReferences?.map((vital, index) => (
              <Grid
                key={index}
                size={6}
                mb={2}
                sx={{
                  ...(carePlanDetails?.vitalReferences &&
                    index === carePlanDetails.vitalReferences.length - 1 &&
                    carePlanDetails.vitalReferences.length > 3 && {
                      marginLeft: 0,
                    }),
                }}
              >
                <VitalReferenceRange
                  title={vital.vitalType || ""}
                  unit={
                    vital.vitalType === "Blood Glucose"
                      ? "mg/dL"
                      : vital.vitalType === "Heart Rate"
                        ? "bpm"
                        : vital.vitalType === "Weight"
                          ? "kg"
                          : vital.vitalType === "ECG"
                            ? "bpm"
                            : vital.vitalType === "HRV"
                              ? "ms"
                              : vital.vitalType === "Oxygen Saturation"
                                ? "%"
                                : vital.vitalType === "Stress"
                                  ? "%"
                                  : vital.vitalType === "Blood Pressure"
                                    ? "mmHg"
                                    : ""
                  }
                  ranges={vital.vitalRanges ? transformVitalRanges(vital.vitalRanges as VitalRange[]) : {}}
                />
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

const getDurationInMs = (duration: number, unit: string): number => {
  const DAY_IN_MS = 24 * 60 * 60 * 1000;
  switch (unit) {
    case "DAY":
      return duration * DAY_IN_MS;
    case "WEEK":
      return duration * 7 * DAY_IN_MS;
    case "MONTH":
      return duration * 30 * DAY_IN_MS;
    case "YEAR":
      return duration * 365 * DAY_IN_MS;
    default:
      return 0;
  }
};

export default ViewCarePlan;
