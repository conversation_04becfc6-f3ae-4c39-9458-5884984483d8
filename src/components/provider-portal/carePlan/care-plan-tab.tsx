import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

import AddIcon from "@mui/icons-material/Add";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import DifferenceOutlinedIcon from "@mui/icons-material/DifferenceOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import RestoreIcon from "@mui/icons-material/Restore";
import {
  Button,
  Divider,
  IconButton,
  Link,
  SelectChangeEvent,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelect from "@/common-components/custom-select/customSelect";
import Paginator from "@/common-components/paginator/paginator";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import Status from "@/common-components/status/status";
import { heading, iconStyles, tableCellCss, typographyCss } from "@/common-components/table/common-table-widgets";
import Toggle from "@/common-components/toggle/toggle";

import ViewCarePlan from "@/components/provider-portal/carePlan/view-careplan";
import { useDrawer } from "@/components/providers/DrawerProvider";
import useAuthority from "@/hooks/use-authority";
import useApiFeedback from "@/hooks/useApiFeedback";
import { ProviderRole } from "@/models/provider/provider-modal";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useCarePlanControllerServiceUpdateCarePlanArchiveStatus1 } from "@/sdk/queries";
import { CarePlan, CarePlanControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";
import { toCamelCase } from "@/utils/toCamelCase";

const headerName = ["Title", "Track", "Duration", "Modified on", "Status", "Action"];

// Add popup table headers
const popupHeaders = [{ header: "Title" }, { header: "Track" }, { header: "Duration" }];

function CarePlanTab() {
  const dispatch = useDispatch();
  const [searchAssignCarePlan, setSearchAssignCarePlan] = useState("");
  const navigate = useNavigate();
  const [carePlanDetails, setCarePlanDetails] = useState<CarePlan[]>();

  const [selectedCarePlan, setSelectedCarePlan] = useState<CarePlan>();
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);
  const [openConfirmDuplicatePopUp, setOpenConfirmDuplicatePopUp] = useState(false);
  const [selectedPlanToDuplicate, setSelectedPlanToDuplicate] = useState<CarePlan | null>(null);
  const [newTitle, setNewTitle] = useState<string>("");

  const [nurseType, setNurseType] = useState("INTERNAL");

  // Add a new loading state
  const [isNurseTypeLoading, setIsNurseTypeLoading] = useState(false);

  // Update xTenantId based on nurseType
  const xTenantId = nurseType === "EXTERNAL" ? "eAmata" : GetTenantId();

  // Pagination state
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState<number>(0);
  const [size, setSize] = useState(10);

  // Sorting state
  const [sortBy, setSortBy] = useState("title");
  const [sortDirection, setSortDirection] = useState("desc");

  const authority = useAuthority();
  const isPGUser = authority.role === ProviderRole.PROVIDER;

  const {
    data: CarePlanGetData,
    isSuccess,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: ["get-care-plan", searchAssignCarePlan, page, size, sortBy, sortDirection],
    queryFn: () =>
      CarePlanControllerService.getAllCarePlans1({
        status: nurseType === "EXTERNAL" ? true : undefined,
        archive: nurseType === "EXTERNAL" ? false : undefined,
        searchString: searchAssignCarePlan,
        page,
        size,
        sortBy,
        sortDirection,
        xTenantId, // Use the updated xTenantId here
      }),
  });

  useEffect(() => {
    if (isSuccess) {
      const response = (CarePlanGetData as unknown as AxiosResponse).data as ContentObject<CarePlan[]>;
      const userData = response?.content;

      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);
      setCarePlanDetails(userData);
    }
  }, [isSuccess, CarePlanGetData, isPGUser]);

  // Modify the nurse type change handler
  const handleNurseTypeChange = (e: SelectChangeEvent<string>) => {
    setIsNurseTypeLoading(true);
    setNurseType(e.target.value);
  };

  // Update the useEffect to handle loading state
  useEffect(() => {
    setPage(0);
    refetch().finally(() => {
      setIsNurseTypeLoading(false);
    });
  }, [nurseType, refetch]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handlePageChange = (_event: ChangeEvent<unknown> | null, page: number) => {
    setPage(page);
  };

  const handleSorting = (column: string) => {
    if (column === "Title") {
      setSortBy("title");
    } else if (column === "Status") {
      setSortBy("active");
    } else if (column === "Duration") {
      setSortBy("duration");
    }
    setSortDirection((prev) => (prev === "desc" ? "asc" : "desc"));
  };

  const handleSearchChange = (searchString: string) => {
    setSearchAssignCarePlan(searchString);
    setPage(0); // Reset to the first page whenever a new search is initiated
  };

  const {
    mutateAsync: mutateAsyncArchive,
    isError: isErrorArchive,
    error: errorArchive,
    // isPending: isPendingArchive,
    isSuccess: isSuccessArchive,
    data: dataArchive,
  } = useCarePlanControllerServiceUpdateCarePlanArchiveStatus1();

  const confirmDelete = async () => {
    await mutateAsyncArchive({
      carePlanId: selectedCarePlan?.uuid || "",
      status: true,
      xTenantId, // Use the updated xTenantId here
    });
    await refetch();
    setOpenConfirmDeletePopUp(false);
  };

  const confirmRestore = async () => {
    await mutateAsyncArchive({
      carePlanId: selectedCarePlan?.uuid || "",
      status: false,
      xTenantId, // Use the updated xTenantId here
    });
    await refetch();
    setOpenConfirmRestorePopUp(false);
  };

  useApiFeedback(
    isErrorArchive,
    errorArchive,
    isSuccessArchive,
    (dataArchive?.message || "Care Plan archive status updated!") as string
  );

  // const handleEditDate = (data: CarePlan) => {
  //   navigate(`/provider/settings/careplan`, { state: { EditData: data } });
  // };
  const { open: openDrawer } = useDrawer();
  const handleDrawer = {
    viewDetails: (carePlan: CarePlan) => {
      openDrawer(
        {
          title: `Care Plan Details`,
          component: <ViewCarePlan carePlanId={carePlan.uuid} nurseType={nurseType} />,
        },
        "60%"
      );
    },
  };

  const renderTableCell = (data: string | number, plan?: CarePlan) => {
    if (plan && data === plan.title) {
      return (
        <TableCell sx={{ ...heading }} align="left">
          <Grid container flexDirection={"column"}>
            <Typography
              sx={{
                color: theme.palette.primary.main,
                cursor: "pointer",
                fontFamily: "Roboto",
                fontWeight: 550,
                fontSize: "14px",
              }}
              variant="bodySmall"
              onClick={() => handleDrawer.viewDetails(plan)}
            >
              {plan.title || "Untitled Care Plan"}
            </Typography>
          </Grid>
        </TableCell>
      );
    }
    return (
      <TableCell sx={{ ...heading }} align="left">
        <Grid container flexDirection={"column"}>
          <Typography sx={typographyCss} variant="bodySmall">
            {data}
          </Typography>
        </Grid>
      </TableCell>
    );
  };

  const updateCarePlanStatus = useMutation({
    mutationFn: ({ carePlanId, status, xTenantId }: { carePlanId: string; status: boolean; xTenantId: string }) =>
      CarePlanControllerService.updateUserStatus({
        carePlanId,
        status,
        xTenantId,
      }),
    onSuccess: (response) => {
      refetch();
      const message = (response as unknown as AxiosResponse)?.data?.message || "Care Plan status updated successfully!";
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  // Create row data for popup when archiving
  const getPopupRowData = (plan: CarePlan) => {
    return [
      plan.title || "-",
      plan.trackedVitals ? toCamelCase(plan.trackedVitals[0]) : "-",
      `${plan.duration || ""} ${toCamelCase(plan.durationUnit || "")}`,
    ];
  };

  const duplicateCarePlan = useMutation({
    mutationFn: async (carePlan: CarePlan) => {
      // First get the detailed care plan
      const detailedPlan = await CarePlanControllerService.getCarePlanById({
        carePlanId: carePlan.uuid || "",
        xTenantId: nurseType === "EXTERNAL" ? "eAmata" : GetTenantId(),
      });

      const newPayload = {
        ...detailedPlan.data,
        uuid: undefined,
        title: newTitle || selectedPlanToDuplicate?.title,
        active: true,
        archive: false,
        diagnosisCodes: selectedPlanToDuplicate?.diagnosisCodes || [],
      };

      return CarePlanControllerService.createCarePlan({
        requestBody: newPayload,
        xTenantId: GetTenantId(),
      });
    },
    onSuccess: () => {
      refetch();
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: "Care Plan duplicated successfully!",
        })
      );
      setOpenConfirmDuplicatePopUp(false);
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: error.body.message || "Failed to duplicate Care Plan",
        })
      );
      setOpenConfirmDuplicatePopUp(false);
    },
  });

  return (
    <Grid border={"1px solid #EAECF0"} borderRadius={2}>
      <Grid container justifyContent={"space-between"} mb={1} px={2} pt={2}>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          <Grid>
            <Typography fontSize={"18px"} fontWeight={500}>
              Care Plans
            </Typography>
          </Grid>
        </Grid>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          <Grid width={"150px"}>
            <CustomSelect
              placeholder={"Select Nurse"}
              items={[
                { value: "EXTERNAL", label: "eAmata" },
                { value: "INTERNAL", label: "Provider Group" },
              ]}
              name={"nurseType"}
              value={nurseType}
              onChange={handleNurseTypeChange}
            />
          </Grid>
          <Grid>
            <CustomInput
              placeholder="Search Care Plan"
              name="careplan"
              hasStartSearchIcon={true}
              value={searchAssignCarePlan}
              onDebounceCall={handleSearchChange}
              onInputEmpty={() => handleSearchChange("")}
            />
          </Grid>
          {nurseType === "INTERNAL" && (
            <Grid>
              <Button
                startIcon={<AddIcon />}
                variant="contained"
                sx={{ borderRadius: "8px", backgroundColor: "#006e8f", color: "#fff" }}
                onClick={() => navigate("/provider/settings/careplan")}
              >
                Add New Care Plan
              </Button>
            </Grid>
          )}
        </Grid>
      </Grid>
      <Grid>
        <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerName.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    {header === "Title" || (header === "Status" && nurseType != "EXTERNAL") || header === "Duration" ? (
                      <Link
                        style={{
                          color: "#667085",
                          textDecoration: "none",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSorting(header)}
                      >
                        <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                          {header}
                          <Typography mt={0.3}>
                            {sortBy === header.toLowerCase() && sortDirection === "asc" ? (
                              <ArrowUpwardIcon fontSize="small" />
                            ) : (
                              <ArrowDownwardIcon fontSize="small" />
                            )}
                          </Typography>
                        </Typography>
                      </Link>
                    ) : (
                      header
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading || isNurseTypeLoading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    {headerName.map((_, cellIndex) => (
                      <TableCell key={cellIndex}>
                        <Skeleton variant="text" width={200} height={30} sx={{ borderRadius: "10px" }} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : carePlanDetails && carePlanDetails?.length > 0 ? (
                carePlanDetails?.map((plan) => (
                  <TableRow key={plan.uuid}>
                    {renderTableCell(plan.title ?? "-", plan)}
                    {renderTableCell(plan?.trackedVitals ? toCamelCase(plan?.trackedVitals[0]) : "-")}
                    {renderTableCell(`${plan.duration ?? ""} ${toCamelCase(plan.durationUnit ?? "")}`)}
                    {renderTableCell(format(plan?.modified ?? "-", "dd MMM yyyy HH:mm a") ?? "-")}
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        {nurseType === "EXTERNAL" ? (
                          <Status status={`${plan.active ? "ACTIVE" : "INACTIVE"}`} width="74px" />
                        ) : (
                          <Toggle
                            status={plan.active ?? false}
                            handleStatusChange={() => {
                              updateCarePlanStatus.mutate({
                                carePlanId: plan.uuid || "",
                                status: !plan.active,
                                xTenantId: xTenantId,
                              });
                            }}
                          />
                        )}
                      </Grid>
                    </TableCell>

                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexWrap={"nowrap"}>
                        <>
                          {nurseType === "EXTERNAL" ? (
                            <></>
                          ) : (
                            // archive and restore and edit
                            <>
                              <Tooltip title="Edit">
                                <IconButton
                                  sx={{ padding: "0px 5px" }}
                                  aria-label="edit"
                                  onClick={() => navigate(`/provider/settings/careplan`, { state: { EditData: plan } })}
                                >
                                  <EditOutlinedIcon sx={iconStyles} />
                                </IconButton>
                              </Tooltip>

                              {!plan.archive ? (
                                <Tooltip title="Archive">
                                  <IconButton
                                    aria-label="delete"
                                    sx={{ padding: "0px" }}
                                    onClick={() => {
                                      setSelectedCarePlan(plan), setOpenConfirmDeletePopUp(true);
                                    }}
                                  >
                                    <ArchiveOutlinedIcon sx={iconStyles} />
                                  </IconButton>
                                </Tooltip>
                              ) : (
                                <Tooltip title="Restore">
                                  <IconButton
                                    aria-label="unarchive"
                                    sx={{ padding: "0px" }}
                                    onClick={() => {
                                      setSelectedCarePlan(plan), setOpenConfirmRestorePopUp(true);
                                    }}
                                  >
                                    <RestoreIcon sx={iconStyles} />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </>
                          )}
                          {/* Duplicate */}

                          <Tooltip title="Duplicate">
                            <IconButton
                              sx={{ padding: "0px 5px" }}
                              aria-label="duplicate"
                              onClick={() => {
                                setSelectedPlanToDuplicate(plan);
                                setOpenConfirmDuplicatePopUp(true);
                                setNewTitle(plan.title || "");
                              }}
                            >
                              <DifferenceOutlinedIcon sx={iconStyles} />
                            </IconButton>
                          </Tooltip>
                        </>
                      </Grid>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={headerName.length} align="center">
                    <Typography variant="bodySmall" fontWeight={550}>
                      No records found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <Paginator
          page={page}
          totalPages={totalPages}
          totalRecord={totalElements}
          defaultSize={size}
          onRecordsPerPageChange={handleRecordsPerPageChange}
          onPageChange={handlePageChange}
        />
        <ConfirmationPopUp
          open={openConfirmDeletePopUp}
          confirmButtonName="Archive"
          onClose={() => setOpenConfirmDeletePopUp(false)}
          onConfirm={() => confirmDelete()}
          message={`Do you really want to archive this Care Plan?`}
          title={`Archive Item`}
          subtitle={"Are you sure you want to archive the following Care plan?"}
          // onlyMsg={false}
          header={popupHeaders}
          rowData={selectedCarePlan ? getPopupRowData(selectedCarePlan) : []}
        />

        <ConfirmationPopUp
          open={openConfirmRestorePopUp}
          onClose={() => setOpenConfirmRestorePopUp(false)}
          onConfirm={() => confirmRestore()}
          message={`Do you really want to restore this Care Plan?`}
          title={`Restore Item`}
          subtitle={"Are you sure you want to restore the following Care plan?"}
          confirmButtonName="Restore"
          // onlyMsg={false}
          header={popupHeaders}
          rowData={selectedCarePlan ? getPopupRowData(selectedCarePlan) : []}
        />

        <CustomDialog
          open={openConfirmDuplicatePopUp}
          onClose={() => setOpenConfirmDuplicatePopUp(false)}
          title="Duplicate Care Plan"
          width="684px"
          height="280px"
          borderRadius="20px"
          titleAlign="left"
          showDivider={false}
          buttonName={["Duplicate"]}
        >
          <Grid container direction="column" spacing={2}>
            <Grid mt={2}>
              <span
                style={{
                  color: "#5F6C72",
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "100%",
                  letterSpacing: "0%",
                }}
              >
                Previous Care Plan details will be copied. Please edit as needed.
              </span>
            </Grid>

            <Grid>
              <label
                style={{
                  display: "block",
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: "12px",
                  lineHeight: "100%",
                  letterSpacing: "0%",
                  marginBottom: "6px",
                }}
              >
                Rename Program Title
              </label>
              <input
                type="text"
                value={newTitle}
                onChange={(e) => setNewTitle(e.target.value)}
                placeholder="Enter new title"
                style={{
                  marginTop: "8px",
                  width: "100%",
                  height: "40px",
                  padding: "10px",
                  border: "1px solid #CDD5DF",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
              />
            </Grid>
            <Divider sx={{ my: "10px" }} />
            <Grid container justifyContent="flex-end">
              <button
                disabled={!newTitle.trim()}
                onClick={() => {
                  if (selectedPlanToDuplicate) {
                    duplicateCarePlan.mutate({ ...selectedPlanToDuplicate, title: newTitle });
                    setOpenConfirmDuplicatePopUp(false);
                  }
                }}
                style={{
                  position: "absolute",
                  right: "20px",
                  bottom: "20px",
                  width: "104px",
                  height: "40px",
                  gap: "8px",
                  borderRadius: "8px",
                  borderWidth: "1px",
                  padding: "10px 18px",
                  backgroundColor: newTitle.trim() ? "#006D8F" : "#CDD5DF",
                  color: "white",
                  border: "none",
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: "16px",
                  lineHeight: "125%",
                  letterSpacing: "0%",
                  cursor: "pointer",
                }}
              >
                Duplicate
              </button>
            </Grid>
          </Grid>
        </CustomDialog>
      </Grid>
    </Grid>
  );
}

export default CarePlanTab;
