import * as yup from "yup";

export const careplanResolverSchema = yup.object().shape({
  programTitle: yup.string().required("Program Title is required"),
  programDuration: yup.number().required("Program Duration is required"),
  programDurationUnit: yup.string().required("Duration Unit is required"),
  programOverview: yup.string().required("Program overview is required"),
  applicableforGender: yup.array().of(yup.string()).required("Gender is required"),
  applicableforAgeCriteria: yup.string().required("Age criteria is required"),
  applicableforAge: yup
    .string()
    .typeError("Age must be a number or range (e.g., 23-30)")
    .transform((value) => {
      return value === "-" ? undefined : value;
    })
    .test("valid-age-format", "Enter a valid age or age range (e.g., 25 or 23-30)", (value) => {
      if (!value || value === "-") return true;
      return /^(\d+)(-\d+)?$/.test(value);
    }),
  DiagnosisCodes: yup
    .array()
    .of(yup.object().shape({ value: yup.string().required("Diagnosis Code is required") }))
    .min(1, "Diagnosis Code is required"),
  goals: yup.array().of(
    yup.object().shape({
      goalCategory: yup.string().required("Category is required"),
      goalTitle: yup.string().required("Goal Title is required"),
      trackBy: yup.string().required("Track By is required"),
      targetType: yup.string().required("Target Type is required"),
      // targetValue: yup
      //   .number()
      //   .typeError("Target Value must be a number")
      //   .test("decimal-place", "Only one decimal place is allowed", (value) => {
      //     if (value === undefined || value === null) return false; // Ensures required validation works
      //     return /^\d+(\.\d{1})?$/.test(value.toString()); // Allows whole numbers and numbers with one decimal place
      //   })
      //   .required("Target Value is required"),
      targetValue: yup
        .string()
        .typeError("Target Value must be a number, range (e.g: 15.4, 15.4-18.6, or 120/180)")
        .matches(
          /^(\d+(\.\d{1})?)(\s*-\s*\d+(\.\d{1})?)?$|^(\d+)\/(\d+)$/,
          "Enter a valid number, range (e.g: 15.4, 15.4-18.6, or 120/180)"
        )
        .required("Target Value is required"),

      unit: yup.string().required("Unit is required"),
      objective: yup.string(),
      tasks: yup.array().of(
        yup.object().shape({
          task: yup.string().required("Task is required"),
          taskDetails: yup.string().required("Task Details is required"),
        })
      ),
    })
  ),

  vitals: yup.array().of(
    yup.object().shape({
      vitalType: yup.string().required("Vital Type is required"),
      unit: yup.string(),
      targetRange: yup
        .string()
        .typeError("Target Range must be a number or a range (e.g., 15.4 - 18.6)")
        .matches(/^(\d+(\.\d{1})?)(\s*-\s*\d+(\.\d{1})?)?$/, "Enter a valid number or range (e.g., 30 - 100)")
        .required("Target Range is required"),
      selectDevice: yup.string(),
      lowRange: yup.object().shape({
        rangeType: yup.string(),
        min: yup.number(),
        max: yup.number(),
      }),
      moderateRange: yup.object().shape({
        rangeType: yup.string(),
        min: yup.number(),
        max: yup.number(),
      }),
      criticalRange: yup.object().shape({
        rangeType: yup.string(),
        min: yup.number(),
        max: yup.number(),
      }),
    })
  ),
  selectedDevices: yup
    .array()
    .of(yup.string())
    .min(1, "Select at least one device")
    .required("Select at least one device"),
  allowManualInputs: yup.boolean(),
  medicationAdherence: yup.string(),
  protocolType: yup.array().of(yup.string()).required("Select at least one protocol"),
});
