import { ChangeEvent, useEffect, useState } from "react";

import { Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelectorSq from "@/common-components/custom-selector-sq/custom-selector-sq";
import Paginator from "@/common-components/paginator/paginator";
import { heading, tableCellCss } from "@/common-components/table/common-table-widgets";

import { ContentObject } from "@/models/response/response-content-entity";
import { MedicalCode, MedicalCodeControllerService } from "@/sdk/requests";

// headers
// let headerName = ["Code", "Description"];

type MedicalCodeFilterType = "All" | "ICD-10" | "CPT";

function DrugLibraryTab() {
  const [searchDrug, setSearchDrug] = useState("");
  const [medicalCodes, setMedicalCodes] = useState<MedicalCode[]>([]);
  const [medicalCodeType, setMedicalCodeType] = useState<MedicalCodeFilterType>("All");

  // Pagination state
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState<number>(0);
  const [size, setSize] = useState(10);

  let headerName = ["Code", "Description"];
  if (medicalCodeType === "All") {
    headerName = ["Type", "Code", "Description"];
  } else {
    headerName = ["Code", "Description"];
  }

  // CSS styles
  const typographyCss = {
    fontFamily: "Roboto",
    fontWeight: 400,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "0%",
    color: "#212D30",
  };

  // API call to get medical codes
  const { data, isLoading, isSuccess, refetch, isRefetching } = useQuery({
    queryKey: ["medical-codes", page, size, searchDrug, medicalCodeType],
    queryFn: () =>
      MedicalCodeControllerService.getMedicalCodes({
        xTenantId: "eAmata",
        page,
        size,
        searchString: searchDrug,
        type: medicalCodeType === "All" ? undefined : medicalCodeType === "ICD-10" ? "ICD10" : "CPT",
      }),
  });

  // Process data when API returns successfully
  useEffect(() => {
    if (isSuccess && data) {
      const response = (data as unknown as AxiosResponse).data as ContentObject<MedicalCode[]>;

      const medicalCodesData = response?.content;
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);

      const mappedData = medicalCodesData?.map((code) => ({
        uuid: code.uuid,
        code: code.code,
        description: code.description || "", // Ensure description is always a string
        type: code.type,
        active: code.active,
        archive: code.archive,
        errorMessage: code.errorMessage,
      }));

      setMedicalCodes(mappedData || []);
    }
  }, [data, isSuccess]);

  useEffect(() => {
    refetch();
  }, [medicalCodeType, refetch]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handlePageChange = (_event: ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage);
  };

  return (
    <Grid border={"1px solid #EAECF0"} borderRadius={2}>
      <Grid container justifyContent={"space-between"} mb={1} px={2} pt={2}>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          <Grid>
            <Grid container direction="column" gap={1}>
              <CustomSelectorSq
                widthOfBtn="100px"
                options={["All", "ICD-10", "CPT"]}
                onSelect={(selectedOption: string) => {
                  setPage(0);
                  // Cast to MedicalCodeFilterType as we know these options are valid
                  setMedicalCodeType(selectedOption as MedicalCodeFilterType);
                }}
                selectedValue={medicalCodeType}
              />
            </Grid>
          </Grid>
        </Grid>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          <Grid>
            <CustomInput
              placeholder={`Search ${medicalCodeType === "All" ? "" : medicalCodeType} Code`}
              name="code"
              hasStartSearchIcon={true}
              value={searchDrug}
              onDebounceCall={(searchString) => setSearchDrug(searchString)}
              onInputEmpty={() => setSearchDrug("")}
            />
          </Grid>
        </Grid>
      </Grid>
      <Grid>
        <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerName.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    <Typography fontWeight={550} variant="bodySmall" color="#667085" sx={{ fontStyle: "Roboto" }}>
                      {header}
                    </Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading || isRefetching ? (
                [...Array(5)].map((_, index) => (
                  <TableRow key={index}>
                    {[...Array(headerName.length)].map((_, cellIndex) => (
                      <TableCell key={cellIndex}>
                        <Skeleton variant="text" width={100} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : medicalCodes.length > 0 ? (
                medicalCodes.map((code, index) => (
                  <TableRow key={index}>
                    {medicalCodeType === "All" && (
                      <TableCell>
                        <Typography sx={typographyCss}>{code.type}</Typography>
                      </TableCell>
                    )}
                    <TableCell>
                      <Typography sx={typographyCss}>{code.code}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{code.description}</Typography>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={2} align="center">
                    <Typography>No {medicalCodeType === "All" ? "" : medicalCodeType} codes found</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <Grid container justifyContent={"flex-end"} p={2}>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElements}
            onPageChange={handlePageChange}
            onRecordsPerPageChange={handleRecordsPerPageChange}
          />
        </Grid>
      </Grid>
    </Grid>
  );
}

export default DrugLibraryTab;
