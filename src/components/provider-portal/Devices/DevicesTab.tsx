import { ChangeEvent, useEffect, useState } from "react";

import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import {
  Box,
  Button,
  Collapse,
  Grid2 as Grid,
  IconButton,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import CustomAutoComplete from "@/common-components/custom-auto-complete/custom-auto-complete";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import Paginator from "@/common-components/paginator/paginator";
import { heading, tableCellCss } from "@/common-components/table/common-table-widgets";

import { ContentObject } from "@/models/response/response-content-entity";
import { DeviceTypes } from "@/pages/provider-portal/commonFiles/staticOptionsData";
import { DeviceControllerService } from "@/sdk/requests/services.gen";
import { Device } from "@/sdk/requests/types.gen";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { formatDateNewFormat } from "@/utils/format/date";
import { theme } from "@/utils/theme";

const headerNames = ["Name", "Type", "Assigned On"];

function DevicesTab() {
  // State for devices data
  const [devices, setDevices] = useState<Device[]>([]);

  // Pagination state
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [searchDevice, setSearchDevice] = useState("");
  const [totalElements, setTotalElements] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // filters
  const [viewFilters, setViewFilters] = useState(false);
  const [filterDeviceType, setFilterDeviceType] = useState<string | null>(null);

  const {
    data: devicesData,
    isLoading,
    isSuccess,
  } = useQuery({
    queryKey: [GetTenantId(), page, size, searchDevice, filterDeviceType],
    queryFn: () =>
      DeviceControllerService.getAllDevices({
        xTenantId: GetTenantId(),
        type: filterDeviceType || undefined,
        page,
        size,
        searchString: searchDevice,
      }),
  });

  useEffect(() => {
    if (isSuccess) {
      const response = (devicesData as unknown as AxiosResponse).data as ContentObject<Device[]>;
      const DevicesData = response?.content;

      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);
      setDevices(DevicesData);
    }
  }, [devicesData]);

  useEffect(() => {
    setPage(0);
  }, [searchDevice]);

  // CSS styles
  const typographyCss = {
    fontFamily: "Roboto",
    fontWeight: 400,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "0%",
    color: "#212D30",
  };

  // Handle page change
  const handlePageChange = (_event: ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage);
  };

  // Handle records per page change
  const handleRecordsPerPageChange = (newSize: number) => {
    setSize(newSize);
    setPage(0);
  };

  // Handle filter button click
  const handleOnClickFilters = () => {
    setViewFilters(!viewFilters);
  };

  const handleClearFilters = () => {
    setFilterDeviceType(null);
    setPage(0);
    setSize(10);
  };

  return (
    <Grid border={"1px solid #EAECF0"} borderRadius={2}>
      {/* Header section */}
      <Grid
        sx={{
          borderBottom: "1px solid #EAECF0",
          padding: "8px 20px",
        }}
        container
        px={2}
        display="flex"
        justifyContent="space-between"
        alignItems="center"
      >
        <Typography variant="h6" fontWeight={600}>
          Devices
        </Typography>

        <Box display="flex" alignItems="center" gap={1}>
          <Grid container alignItems={"center"} columnGap={1} rowGap={2}>
            {!viewFilters && (
              <IconButton onClick={() => setViewFilters(true)}>
                <Grid container border={"1px solid #B6C1C4"} p={1} borderRadius={2}>
                  <FilterAltOutlinedIcon />
                </Grid>
              </IconButton>
            )}
            {viewFilters && (
              <>
                <Grid container width={"fit-content"} justifyContent={"flex-end"}>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      handleClearFilters();
                    }}
                  >
                    <Typography fontWeight={550} variant="bodySmall">
                      Clear Filters
                    </Typography>
                  </Button>
                </Grid>

                <Grid alignContent={"flex-end"} p={1} columnGap={2} rowGap={2} container justifyContent={"flex-end"}>
                  <Button onClick={handleOnClickFilters} variant="outlined">
                    <Typography fontWeight={550} variant="bodySmall" sx={{ padding: "6px 0px" }}>
                      {viewFilters ? "Hide Filters" : "Filters"}
                    </Typography>
                  </Button>
                </Grid>
              </>
            )}
          </Grid>
          <Grid container alignItems={"center"} columnGap={2} rowGap={2}>
            <CustomInput
              placeholder="Search Device"
              hasStartSearchIcon={true}
              name="searchDevice"
              value={searchDevice}
              onDebounceCall={(searchString: string) => setSearchDevice(searchString)}
              onInputEmpty={() => setSearchDevice("")}
            />
          </Grid>
        </Box>
      </Grid>

      <Collapse in={viewFilters} timeout={500}>
        <Grid
          sx={{
            margin: "20px",
            width: "auto",
            backgroundColor: "#F2F7F9",
            borderRadius: "8px",
          }}
          container
          size={12}
          columnGap={2}
          padding={2}
        >
          <Grid size={3}>
            <CustomLabel label="Device Type" />

            <CustomAutoComplete
              placeholder="Filter by Device Type"
              options={DeviceTypes}
              value={filterDeviceType || undefined}
              bgWhite={true}
              onChange={(newValue: string) => {
                setFilterDeviceType(newValue);
              }}
            />
          </Grid>
        </Grid>
      </Collapse>

      {/* Table section */}
      <Grid>
        <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerNames.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    <Typography fontWeight={550} variant="bodySmall" color="#667085" sx={{ fontStyle: "Roboto" }}>
                      {header}
                    </Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                [...Array(5)].map((_, index) => (
                  <TableRow key={index}>
                    {[...Array(headerNames.length)].map((_, cellIndex) => (
                      <TableCell key={cellIndex}>
                        <Skeleton variant="text" width={100} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : devices.length > 0 ? (
                devices.map((device, index) => (
                  <TableRow key={index} hover>
                    <TableCell>
                      <Typography
                        sx={{
                          cursor: "pointer",
                          color: theme.palette.primary.main,
                          fontWeight: 550,
                          fontSize: "14px",
                        }}
                        variant="bodySmall"
                      >
                        {device.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{device.deviceType}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{formatDateNewFormat(device.created)}</Typography>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography>No devices found</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <Grid container justifyContent={"flex-end"} p={2}>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElements}
            onPageChange={handlePageChange}
            onRecordsPerPageChange={handleRecordsPerPageChange}
          />
        </Grid>
      </Grid>
    </Grid>
  );
}

export default DevicesTab;
