import { Paper, Table, TableBody, TableCell, TableContainer, TableRow } from "@mui/material";
import { Grid } from "@mui/system";

const InfusionDetails = () => {
  const rows = [
    { name: "Device Type", result: "RX.jet" },
    { name: "Drug Name", result: "Tylenol" },
    { name: "Patient Name", result: "Gracely.H" },
    { name: "Doctor Name", result: "Dr.<PERSON>" },
    { name: "Prep. Diluent", result: "Sodium Chloride 0.9%" },
    { name: "Prep. Concentration", result: "R500" },
    { name: "Infusion Route", result: "Patient Body Controller" },
    { name: "Device Vol. to be InfusedType", result: "250000mg" },
    { name: "Infusion Duration", result: "12min" },
    { name: "Infusion Duration", result: "344" },
    { name: "Prep. Date", result: "12.44.22" },
    { name: "Cassette Type", result: "2mm.let" },
  ];

  return (
    <Grid container padding={1}>
      <TableContainer component={Paper} sx={{ boxShadow: "0px 0px 4px 0px #040E1C1F" }}>
        <Table aria-label="simple table">
          <TableBody>
            {rows.map((row) => (
              <TableRow
                key={row.name}
                // sx={{ border: "2px solid red" }}
              >
                <TableCell component="th" scope="row" sx={{ fontSize: "14px", fontWeight: 550 }}>
                  {row.name}
                </TableCell>
                <TableCell align="right">{row.result}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Grid>
  );
};

export default InfusionDetails;
