import { useEffect, useMemo, useState } from "react";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import PlaceOutlinedIcon from "@mui/icons-material/PlaceOutlined";
import { Avatar, Button, ButtonBase, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import * as yup from "yup";

import CustomAutoComplete from "@/common-components/custom-auto-complete/custom-auto-complete";
import CustomLabel from "@/common-components/custom-label/custom-label";
import CustomSelect from "@/common-components/custom-select/customSelect";
import DatePicker from "@/common-components/date-picker-field/date-picker-field";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import useApiFeedback from "@/hooks/useApiFeedback";
import { ProviderRole } from "@/models/provider/provider-modal";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setIsLoading } from "@/redux/actions/loader-action";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import {
  useAppointmentControllerServiceRescheduleAppointment,
  useAppointmentControllerServiceUpdateAppointment,
} from "@/sdk/queries";
import {
  Appointment,
  AppointmentControllerService,
  AvailabilityControllerService,
  Provider,
  ProviderControllerService,
  timezone,
} from "@/sdk/requests";
import { AllTimezones, getStartAndEndOfDayUTC2 } from "@/services/common/date-formatter";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";
import { toCamelCase } from "@/utils/toCamelCase";

import { SlotType, TimeSlot, selectAptBtnStyles } from "../schedule-appointment-dialog";

const appointmentSchema = yup.object().shape({
  nurseId: yup.string().required("Nurse is required"),
  date: yup.string().required("Date is required"),
  duration: yup.string().required("Duration is required"),
  timezone: yup.string().required("Timezone is required"),
});

type ReassignNurseDialogType = {
  appointmentDetails: Appointment;
  action: string;
  setOpenReassignAppt?: (opt: boolean) => void;
  refetch?: () => void;
  onCloseDrawer?: () => void;
};

function ReassignNurseDialog(props: ReassignNurseDialogType) {
  const { appointmentDetails, setOpenReassignAppt, action, refetch, onCloseDrawer } = props;

  const [selectedSlot, setSelectedSlot] = useState<TimeSlot>();

  const [nurseType, setNurseType] = useState("INTERNAL");
  const [searchNurseString, setSearchNurseString] = useState("");
  const [nurseOptions, setNurseOptions] = useState<{ key: string; value: string }[]>([{ key: "", value: "" }]);
  const [durationTime, setDurationTime] = useState("");
  const [timeZone, setTimeZone] = useState("");
  const [allSlots, setAllSlots] = useState<SlotType[]>([]);

  const initialValues = {
    nurseId: "",
    date: "",
    duration: "",
    timezone: "",
  };

  const {
    control,
    setValue,
    getValues,
    formState: { errors },
    handleSubmit,
  } = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(appointmentSchema),
  });

  useEffect(() => {
    if (action === "Reschedule" && appointmentDetails?.nurseName) {
      const matchingNurse = nurseOptions.find((nurse) => nurse.value === appointmentDetails.nurseName);
      if (matchingNurse) {
        setValue("nurseId", matchingNurse.key);
      }
    }
  }, [action, appointmentDetails, nurseOptions, setValue]);

  useEffect(() => {
    if (!getValues("nurseId")) {
      setValue("nurseId", appointmentByIddata.nurseId, { shouldValidate: true });
    }
  }, [setValue, getValues]);

  const { data: dataAppt } = useQuery({
    enabled: !!appointmentDetails?.uuid,
    queryKey: ["appt-by-id", appointmentDetails.uuid],
    queryFn: () =>
      AppointmentControllerService.getAppointmentById({
        appointmentId: appointmentDetails?.uuid || "",
        xTenantId: xTenantId || "",
      }),
  });

  let appointmentByIddata: Appointment = (dataAppt?.data as Appointment) || {};
  useEffect(() => {
    appointmentByIddata = dataAppt?.data as Appointment;
  }, [dataAppt]);

  const xTenantId = GetTenantId();
  const getListOfProviderOrNurse = async (role: ProviderRole) => {
    const schemaTypeVal = nurseType;
    const xTenantIdVal =
      role === ProviderRole.PROVIDER
        ? xTenantId
        : role === ProviderRole.NURSE && schemaTypeVal === "EXTERNAL"
          ? "eamata"
          : xTenantId;

    try {
      const res = await ProviderControllerService.getAllProviders({
        page: 0,
        size: 100,
        sortBy: "modified",
        sortDirection: "desc",
        role,
        status: true,
        archive: false,
        xTenantId: xTenantIdVal,
        searchString: role === ProviderRole.NURSE ? searchNurseString : "",
      });

      const data = (res as unknown as AxiosResponse).data as ContentObject<Provider[]>;
      const options = data.content
        .filter((item) => item.uuid !== appointmentDetails?.nurseId) // Filter out assigned nurse
        .map((item) => {
          return {
            key: item.uuid || "",
            value: `${item.firstName} ${item.lastName}`,
          };
        });
      if (role === ProviderRole.NURSE) {
        setNurseOptions(options);
      }
    } finally {
      //
    }
  };

  useEffect(() => {
    getListOfProviderOrNurse(ProviderRole.NURSE);
  }, [nurseType]);

  const AllTimeZone = [
    { label: "IST", value: "Asia/Calcutta" }, // Indian Standard Time (UTC+05:30)
    { label: "SGT", value: "Asia/Singapore" }, // Alaska Standard Time (UTC-09:00)
    { label: "CST", value: "America/Chicago" }, // Central Standard Time (UTC-06:00)
    { label: "EST", value: "America/New_York" }, // Eastern Standard Time (UTC-05:00)
    { label: "MST", value: "America/Denver" }, // Mountain Standard Time (UTC-07:00)
    { label: "PST", value: "America/Los_Angeles" }, // Pacific Standard Time (UTC-08:00)
    { label: "HST", value: "Pacific/Honolulu" }, // Hawaii Standard Time (UTC-10:00)
    { label: "AKST", value: "America/Anchorage" }, // Alaska Standard Time (UTC-09:00)
  ];

  const selectedDate = getValues("date");
  const [isLoadingReScheduleAppointment, setIsLoadingReScheduleAppointment] = useState(false);
  const currentUserTimeZoneInIANAFormat = Intl.DateTimeFormat().resolvedOptions().timeZone;

  useEffect(() => {
    const matchedTimeZone = AllTimeZone.find((tz) => tz.value === currentUserTimeZoneInIANAFormat);
    if (matchedTimeZone) {
      setValue("timezone", matchedTimeZone.value);
      setTimeZone(matchedTimeZone.value);
    }
  }, [setValue]);

  const { startUTC, endUTC } = useMemo(() => {
    return getStartAndEndOfDayUTC2(selectedDate, timeZone);
  }, [selectedDate, timeZone]);

  useEffect(() => {
    if (durationTime && getValues("date")) {
      setValue("duration", durationTime, { shouldValidate: true });
      setValue("date", getValues("date"), { shouldValidate: true });
    }
  }, [durationTime, getValues("date"), setValue]);

  const {
    data: slotData,
    isSuccess: isSuccessSlot,
    isLoading,
  } = useQuery({
    queryKey: [durationTime, getValues("date"), getValues("nurseId"), timeZone],
    enabled: Boolean(durationTime && getValues("date") && getValues("nurseId") && timeZone),
    queryFn: () => {
      const duration = Number(durationTime); // Ensure duration is a number
      // const endOfDayDate = endOfDay(new Date(getValues("date")));
      // const startDateTime = startOfDay(new Date(getValues("date")));
      return AvailabilityControllerService.getProviderSlots({
        duration: duration,
        endDate: endUTC,
        startDate: startUTC,
        providerUuid: getValues("nurseId"),
        page: 0,
        size: 100,
        xTenantId: nurseType === "INTERNAL" ? xTenantId : "eamata",
      });
    },
  });

  useEffect(() => {
    if (isSuccessSlot && slotData?.data?.content) {
      const response = slotData.data.content as SlotType[];

      setAllSlots(response);
    }
  }, [isSuccessSlot, slotData, startUTC, endUTC]);

  const handleSelectSlot = (val: TimeSlot) => {
    setSelectedSlot(val);
  };

  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setIsLoading(isLoading || isLoadingReScheduleAppointment));
  }, [isLoading, isLoadingReScheduleAppointment, dispatch]);

  const { data, mutateAsync, isError, error, isSuccess } = useAppointmentControllerServiceUpdateAppointment();
  useApiFeedback(isError, error, isSuccess, (data?.message || "Re-assign Nurse successfully") as string);

  const xtenantId = GetTenantId();
  const {
    data: dataReschedule,
    mutateAsync: rescheduleMutateAsync,
    isError: isErrorReschedule,
    error: errorReschedule,
    isSuccess: isSuccessReschedule,
  } = useAppointmentControllerServiceRescheduleAppointment();
  useApiFeedback(
    isErrorReschedule,
    errorReschedule,
    isSuccessReschedule,
    (dataReschedule?.message || "Re-schedule Nurse successfully") as string
  );

  // all day slots
  const renderSlotSection = (slots: SlotType[]) => (
    <Grid flexDirection={"column"} container>
      <Grid container columnGap={1} rowGap={1}>
        {slots.length === 0 && isSuccessSlot ? (
          <Typography variant="bodySmall" color={theme.palette.grey[500]}>
            No available time slots
          </Typography>
        ) : slots.length > 0 ? (
          slots.map((slot, index) => (
            <ButtonBase
              key={index}
              onClick={() => handleSelectSlot(slot)}
              sx={selectAptBtnStyles(selectedSlot === slot)}
            >
              <Grid
                width="68px"
                height="26px"
                border={`1px solid ${theme.palette.grey[500]}`}
                borderRadius="4px"
                container
                justifyContent={"center"}
                alignContent={"center"}
              >
                <Typography variant="bodySmall">{handleLocalTimeZone(slot.startTime)}</Typography>
              </Grid>
            </ButtonBase>
          ))
        ) : (
          <Typography variant="bodySmall" color={theme.palette.grey[500]}>
            -
          </Typography>
        )}
      </Grid>
    </Grid>
  );

  const onSubmit = async (values: FieldValues) => {
    const AllTimezone: AllTimezones =
      values.timezone === "Asia/Calcutta"
        ? "IST"
        : values.timezone === "Asia/Singapore"
          ? "SGT"
          : values.timezone === "America/Chicago"
            ? "CST"
            : values.timezone === "America/New_York"
              ? "EST"
              : values.timezone === "America/Denver"
                ? "MST"
                : values.timezone === "America/Los_Angeles"
                  ? "PST"
                  : values.timezone === "Pacific/Honolulu"
                    ? "HST"
                    : values.timezone === "America/Anchorage"
                      ? "AKST"
                      : undefined;

    try {
      setIsLoadingReScheduleAppointment(true);
      if (action === "Reschedule") {
        await rescheduleMutateAsync({
          requestBody: {
            ...appointmentByIddata,
            appointmentId: appointmentByIddata.uuid as string,
            duration: values.duration,
            startTime: selectedSlot?.startTime || "",
            endTime: selectedSlot?.endTime || "",
            nurseId: values.nurseId,
            external: nurseType === "EXTERNAL",
            timezone: AllTimezone as timezone,
            reason: appointmentByIddata.reason || "Reschedule requested",
          },
          xTenantId: xtenantId,
        });
      } else {
        await mutateAsync({
          requestBody: {
            ...appointmentByIddata,
            uuid: appointmentByIddata.uuid,
            duration: values.duration,
            startTime: selectedSlot?.startTime || "",
            endTime: selectedSlot?.endTime || "",
            nurseId: values.nurseId,
            // purpose: "",
            external: nurseType === "EXTERNAL",
            timezone: AllTimezone as timezone,
          },
          xTenantId: xtenantId,
        });
      }

      // Make sure the refetch is called to refresh data, especially the calendar view
      if (refetch) {
        refetch();
      }

      // Close dialogs after refetch
      if (setOpenReassignAppt) {
        setOpenReassignAppt(false);
      }
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message,
        })
      );
    } finally {
      setIsLoadingReScheduleAppointment(false);
      if (onCloseDrawer) {
        onCloseDrawer();
      }
    }
  };

  const handleLocalTimeZone = (slot: string) => {
    const zonedDate = toZonedTime(slot, timeZone);
    return format(zonedDate, "hh:mm a");
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid container flexDirection={"column"} sx={{ minWidth: "692px", minHeight: "400px" }} rowGap={3}>
        <Grid container flexDirection={"column"} rowGap={0.5}>
          <Typography fontSize={"18px"} fontWeight={550}>
            {appointmentByIddata?.purpose || "-"}
          </Typography>{" "}
          <Typography variant="bodySmall" sx={{ color: "#515C5F" }}>
            {appointmentDetails?.startTime && appointmentDetails?.endTime
              ? `${format(new Date(appointmentDetails.startTime), "EEEE, d MMM - h:mm a")} – ${format(new Date(appointmentDetails.endTime), "h:mm a")}`
              : "-"}
          </Typography>
        </Grid>
        <Grid container columnGap={3}>
          <Grid container columnGap={1} rowGap={0.5}>
            <PlaceOutlinedIcon fontSize="small" />
            <Grid container columnGap={1} flexDirection={"column"} rowGap={0.5}>
              <Typography variant="bodySmall" fontWeight={550}>
                {appointmentByIddata.mode ? toCamelCase(appointmentByIddata.mode) : "-"}
              </Typography>{" "}
              <Typography variant="bodySmall" sx={{ color: "#515C5F" }}>
                {appointmentByIddata.address?.line1}, {appointmentByIddata.address?.line2}
                {appointmentByIddata.address?.city}, &nbsp;
                {appointmentByIddata.address?.state},&nbsp;
                {appointmentByIddata.address?.country}&nbsp;
              </Typography>
            </Grid>
          </Grid>
          <Grid container columnGap={1} rowGap={0.5}>
            <Avatar />
            <Grid container flexDirection={"column"} rowGap={0.5}>
              <Typography variant="bodySmall" fontWeight={550}>
                {appointmentByIddata.patientName}
              </Typography>{" "}
              <Typography variant="bodySmall" sx={{ color: "#515C5F" }}>
                Patient
              </Typography>
            </Grid>
          </Grid>
        </Grid>
        <Grid container justifyContent={"space-between"} width={"100%"} rowGap={2}>
          <Grid
            width={"100%"}
            container
            columnGap={18}
            rowGap={2}
            flexDirection={action == "Reschedule" ? "row" : "column"}
          >
            <Grid>
              <CustomLabel label={"Nurse Type :"} />
              {action === "Reschedule" && <Typography>{toCamelCase(appointmentDetails?.mode || "")}</Typography>}
              {action !== "Reschedule" && (
                <CustomSelect
                  isDisabled={action === "Reschedule"}
                  placeholder={"Select Nurse Type"}
                  items={[
                    {
                      value: "INTERNAL",
                      label: `${"Provider Group Nurses"}`,
                    },
                    { value: "EXTERNAL", label: "Eamata Nurses" },
                  ]}
                  onChange={(e) => setNurseType(e.target.value)}
                  name={"nurseType"}
                  value={nurseType}
                />
              )}
            </Grid>
            <Grid>
              <CustomLabel label={"Nurse :"} />

              <Controller
                control={control}
                name="nurseId"
                render={({ field }) => (
                  <>
                    {action == "Reschedule" && <Typography>{appointmentByIddata.nurseName}</Typography>}
                    {action !== "Reschedule" && (
                      <CustomAutoComplete
                        isDisabled={action === "Reschedule"}
                        hasStartSearchIcon
                        placeholder="Select Nurse"
                        hideArrow
                        value={field.value || ""}
                        options={nurseOptions}
                        onChange={function (selectedValue: string): void {
                          setValue("nurseId", selectedValue, { shouldValidate: true });
                        }}
                        onDebounceCall={(value) => setSearchNurseString(value)}
                        onInputEmpty={() => setSearchNurseString("")}
                        hasError={!!errors.nurseId}
                        errorMessage={errors.nurseId?.message}
                      />
                    )}
                  </>
                )}
              />
            </Grid>
          </Grid>
          <Grid width={"48%"}>
            <CustomLabel label={"Select Date"} />

            <Controller
              control={control}
              name={`date`}
              render={({ field }) => (
                <DatePicker
                  bgWhite={false}
                  disablePast
                  value={field.value}
                  onDateChange={(e) => setValue("date", e, { shouldValidate: true })}
                  hasError={!!errors.date}
                  errorMessage={errors.date?.message || ""}
                />
              )}
            />
          </Grid>
          <Grid width={"23%"}>
            <CustomLabel label="Duration" />
            <Controller
              control={control}
              name={"duration"}
              render={({ field }) => (
                <CustomSelect
                  isDisabled={!getValues("date")}
                  {...field}
                  borderRadius={"8px"}
                  placeholder={"Select Duration"}
                  onChange={(e) => {
                    setDurationTime(e.target.value), setValue("duration", e.target.value, { shouldValidate: true });
                  }}
                  value={durationTime || ""}
                  items={[
                    { value: "15", label: "15 minutes" },
                    { value: "30", label: "30 minutes" },
                    { value: "45", label: "45 minutes" },
                    { value: "60", label: "60 minutes" },
                  ]}
                  hasError={!!errors.duration}
                  errorMessage={errors.duration?.message || ""}
                />
              )}
            />
          </Grid>
          <Grid width={"23%"}>
            <CustomLabel label="Time Zone" isRequired />
            <Controller
              control={control}
              name={"timezone"}
              render={({ field }) => (
                <CustomSelect
                  // isDisabled={!getValues("date")}
                  {...field}
                  borderRadius={"8px"}
                  placeholder={"Select timezone"}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    setTimeZone(e.target.value);
                  }}
                  value={field.value || ""}
                  items={AllTimeZone}
                  hasError={!!errors.timezone}
                  errorMessage={errors.timezone?.message || ""}
                />
              )}
            />
          </Grid>
          <Grid width={"100%"}>
            <CustomLabel label="Select Available Time Slot :" />
            <Grid
              sx={{
                padding: "16px",
                borderRadius: "12px",
                border: `solid 1px ${theme.palette.grey[300]}`,
                width: "100%",
                maxHeight: "508px",
                overflow: "auto",
                flexDirection: "column",
              }}
              container
              rowGap={2}
            >
              {renderSlotSection(allSlots)}
            </Grid>
          </Grid>
        </Grid>
        <Grid container justifyContent={"flex-end"}>
          <Button type="submit" variant="contained">
            Save
          </Button>
        </Grid>
      </Grid>
    </form>
  );
}

export default ReassignNurseDialog;
