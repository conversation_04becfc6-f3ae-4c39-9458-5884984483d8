import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MessageIcon from "@mui/icons-material/Message";
import PlaceOutlinedIcon from "@mui/icons-material/PlaceOutlined";
import PodcastsIcon from "@mui/icons-material/Podcasts";
import { Accordion, AccordionSummary, Avatar, Button, IconButton, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { endOfDay, format, startOfDay, subDays } from "date-fns";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomLabel from "@/common-components/custom-label/custom-label";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import Status from "@/common-components/status/status";
import { typographyCss } from "@/common-components/table/common-table-widgets";

import useAuthority from "@/hooks/use-authority";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setIsLoading } from "@/redux/actions/loader-action";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import {
  useAppointmentControllerServiceBroadCastAppointment,
  useAppointmentControllerServiceUpdateAppointmentStatus,
} from "@/sdk/queries";
import { AppointmentControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";
import { toCamelCase } from "@/utils/toCamelCase";

import GroupIcon from "../../../../assets/image_svg/icons/group.svg";
import { Appointment } from "../scheduling-list-table";
import ReassignNurseDialog from "./reassign-nurse-dialog";

export type ExtandedAppointment = Appointment & { status: Appointment["status"] | "IN_EXAM" };

type Allstatus =
  | "PENDING"
  | "ACCEPTED"
  | "REJECTED"
  | "CONFIRMED"
  | "REQUESTED"
  | "CANCELLED"
  | "NO_SHOW"
  | "CHECKED_IN"
  | "IN_PROGRESS"
  | "COMPLETED"
  | "SCHEDULED"
  | "RESCHEDULED"
  | "BROADCAST"
  | "REVOKE"
  | undefined;

type AppointmentDetailsDrawerType = {
  appointmentDetails: Appointment;
  refetch?: (startDate?: string, endDate?: string, filterOption?: "ALL" | "UPCOMING" | "PAST" | "REQUESTED") => void;
  onClose: () => void;
  selectedDate?: Date;
};

const AppointmentDetailsDrawer = (props: AppointmentDetailsDrawerType) => {
  const { selectedDate, appointmentDetails, refetch, onClose } = props;
  const xTenantId = GetTenantId();
  const RoleType = useAuthority();

  const { mutateAsync: broadcastRequestMutateAsync } = useAppointmentControllerServiceBroadCastAppointment({
    onError: (error) => {
      const message =
        (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while broadcasting appointment";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: (data) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: (data?.message || `Successfully broadcasted appointment.`) as string,
        })
      );
    },
  });

  const { data: dataAppt, isLoading } = useQuery({
    enabled: !!appointmentDetails?.uuid,
    queryKey: ["appt-by-id", appointmentDetails?.uuid],
    queryFn: () =>
      AppointmentControllerService.getAppointmentById({
        appointmentId: appointmentDetails?.uuid || "",
        xTenantId: xTenantId || "",
      }),
  });

  let appointmentByIddata: Appointment = (dataAppt?.data as Appointment) || {};
  useEffect(() => {
    appointmentByIddata = dataAppt?.data as Appointment;
  }, [dataAppt]);

  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setIsLoading(isLoading));
  }, [isLoading, dataAppt]);

  const [openDialog, setOpendialog] = useState(false);
  const [openConfirmationForBroadcast, setOpenConfirmationForBroadcast] = useState(false);
  const [openConfirmationForRevoke, setOpenConfirmationForRevoke] = useState(false);
  const [selectedAction, setSelectedAction] = useState<"Broadcast" | "Assign" | "Reassign Nurse" | "Revoke">("Assign");

  const handleBraodCast = () => {
    setSelectedAction("Broadcast");
    setOpenConfirmationForBroadcast(true);
  };

  const confirmBroadcast = async () => {
    try {
      await broadcastRequestMutateAsync({
        xTenantId: xTenantId,
        requestBody: {
          uuid: appointmentDetails?.uuid || "",
          status: "BROADCAST" as Allstatus,
        },
      });
      setOpenConfirmationForBroadcast(false);

      if (refetch) {
        const prevDay = subDays(new Date(), 1);
        const today = new Date();
        refetch(startOfDay(prevDay).toISOString(), endOfDay(today).toISOString(), "REQUESTED");
      }
      onClose();
    } catch (error) {
      console.error("Error broadcasting appointment:", error);
    }
  };

  const handleRevoke = async () => {
    setSelectedAction("Revoke");
    setOpenConfirmationForRevoke(true);
  };

  const handleAssignNurse = () => {
    setSelectedAction("Reassign Nurse");
    setOpendialog(true);
  };

  const confirmRevoke = async () => {
    try {
      await broadcastRequestMutateAsync({
        xTenantId: xTenantId,
        requestBody: {
          uuid: appointmentDetails?.uuid || "",
          status: "REVOKE" as Allstatus,
        },
      });
      setOpenConfirmationForRevoke(false);

      if (refetch) {
        const prevDay = subDays(new Date(), 1);
        const today = new Date();
        refetch(startOfDay(prevDay).toISOString(), endOfDay(today).toISOString(), "REQUESTED");
      }
      onClose();
    } catch (error) {
      console.error("Error revoking broadcast:", error);
    }
  };

  // const [markAsNoShowBy, setMarkAsNoShow] = useState<"PATIENT" | "NURSE">()
  const [loadingType, setLoadingType] = useState<"PATIENT" | "NURSE" | null>(null);

  const { mutateAsync, isPending } = useAppointmentControllerServiceUpdateAppointmentStatus({
    onError: (error) => {
      const message =
        (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while updating status";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: (data) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: (data?.message || `Successfully updated appointment status as Cancel.`) as string,
        })
      );
    },
  });

  const confirmMarkAsNoShow = async (name: "PATIENT" | "NURSE") => {
    if (appointmentByIddata?.uuid) {
      setLoadingType(name);
      await mutateAsync({
        requestBody: { uuid: appointmentByIddata.uuid || "", status: "NO_SHOW", noshow: name },
      });
      if (refetch) {
        const prevDay = subDays(new Date(), 1);
        const today = new Date();
        refetch(startOfDay(prevDay).toISOString(), endOfDay(today).toISOString());
      }
      onClose();
    }
  };

  // const handleNoShowStatus = (name: "PATIENT" | "NURSE") => {
  //   setMarkAsNoShow(name)
  //   confirmMarkAsNoShow()
  // }

  return (
    <Grid container flexDirection={"column"} height={"100%"}>
      <Grid container flexDirection={"column"} rowGap={3}>
        <Grid width={"100%"} container justifyContent={"space-between"}>
          <Grid container flexDirection={"column"} rowGap={0.5}>
            <Typography fontSize={"18px"} fontWeight={550}>
              {appointmentByIddata?.purpose || "-"}
            </Typography>{" "}
            <Typography variant="bodySmall" sx={{ color: "#515C5F" }}>
              {appointmentDetails?.startTime && appointmentDetails?.endTime
                ? `${format(new Date(appointmentDetails.startTime), "EEEE, d MMM - h:mm a")} – ${format(new Date(appointmentDetails.endTime), "h:mm a")}`
                : "-"}
            </Typography>
          </Grid>
          <Typography sx={typographyCss} variant="bodySmall" fontWeight={550}>
            <Status status={appointmentDetails.status || ""} width="120px" />
          </Typography>
        </Grid>
        <Grid container columnGap={3}>
          <Grid container columnGap={1} rowGap={0.5}>
            <PlaceOutlinedIcon fontSize="small" />
            <Grid container columnGap={1} flexDirection={"column"} rowGap={0.5}>
              <Typography variant="bodySmall" fontWeight={550}>
                {appointmentByIddata.mode ? toCamelCase(appointmentByIddata.mode) : "-"}
              </Typography>{" "}
              <Typography variant="bodySmall" sx={{ color: "#515C5F" }}>
                {appointmentByIddata.address?.line1}, {appointmentByIddata.address?.line2}
                {appointmentByIddata.address?.city}, &nbsp;
                {appointmentByIddata.address?.state},&nbsp;
                {appointmentByIddata.address?.country}&nbsp;
              </Typography>
            </Grid>
          </Grid>
        </Grid>
        <Accordion sx={{ border: "1px solid #E8EBEC", borderRadius: "8px", boxShadow: "none" }} defaultExpanded={true}>
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls="panel1-content"
            id="panel1-header"
            sx={{ border: "1px solid #E8EBEC", height: "15px", borderRadius: "8px", boxShadow: "none" }}
          >
            <Grid component="span" container alignItems={"center"} justifyContent={"center"}>
              <IconButton>
                <img src={GroupIcon} alt="Group Icon" style={{ fontSize: "14px" }} />
              </IconButton>
              <Typography fontSize={"14px"}>
                {appointmentByIddata.patientName && appointmentByIddata.nurseName ? "2 guests" : "1 guest"}
              </Typography>
            </Grid>
          </AccordionSummary>
          <Grid container gap={2} flexDirection={"column"} ml={2} paddingY={2}>
            <Grid display={"flex"} justifyContent={"space-between"}>
              <Grid container columnGap={1} rowGap={0.5}>
                <Avatar src={appointmentByIddata?.patientAvatar} />
                <Grid container flexDirection={"column"} rowGap={0.5}>
                  <Typography variant="bodySmall" fontWeight={550}>
                    {appointmentByIddata.patientName}
                  </Typography>{" "}
                  <Typography variant="bodySmall" sx={{ color: "#515C5F" }}>
                    Patient
                  </Typography>
                </Grid>
              </Grid>
              {appointmentByIddata.status !== "NO_SHOW" &&
                appointmentByIddata.status !== "REQUESTED" &&
                appointmentByIddata.status !== "REJECTED" && (
                  <Grid>
                    <Button
                      onClick={() => confirmMarkAsNoShow("PATIENT")}
                      loading={loadingType == "PATIENT" ? isPending : false}
                      sx={{
                        border: "1px solid #B6C1C4",
                        marginRight: "20px",
                        color: "black",
                        borderRadius: "10px",
                        fontSize: "14px",
                      }}
                    >
                      Mark as no show
                    </Button>
                  </Grid>
                )}
            </Grid>

            {appointmentByIddata.nurseId && (
              <Grid display={"flex"} justifyContent={"space-between"}>
                <Grid container columnGap={1} rowGap={0.5}>
                  <Avatar src={appointmentByIddata?.nurseAvatar} />
                  <Grid container flexDirection={"column"} rowGap={0.5}>
                    <Typography variant="bodySmall" fontWeight={550}>
                      {appointmentByIddata.nurseName}
                    </Typography>{" "}
                    <Typography variant="bodySmall" sx={{ color: "#515C5F" }}>
                      Nurse
                    </Typography>
                  </Grid>
                </Grid>
                {appointmentByIddata.status !== "NO_SHOW" &&
                  appointmentByIddata.status !== "REQUESTED" &&
                  appointmentByIddata.status !== "REJECTED" && (
                    <Grid>
                      <Button
                        sx={{
                          border: "1px solid #B6C1C4",
                          marginRight: "20px",
                          color: "black",
                          borderRadius: "10px",
                          fontSize: "14px",
                        }}
                        onClick={() => confirmMarkAsNoShow("NURSE")}
                        loading={loadingType == "NURSE" ? isPending : false}
                      >
                        Mark as no show
                      </Button>
                    </Grid>
                  )}
              </Grid>
            )}
          </Grid>
        </Accordion>
        {appointmentByIddata.status === "CANCELLED" && (
          <Grid container columnGap={1} rowGap={0.5}>
            <Grid container flexDirection={"column"} rowGap={0.5} maxWidth={"510px"}>
              <Typography variant="bodySmall" fontWeight={550} sx={{ color: "#515C5F" }}>
                Cancellation reason
              </Typography>
              <Typography variant="bodySmall">{appointmentByIddata.cancelReason}</Typography>{" "}
            </Grid>
          </Grid>
        )}
        <Grid container columnGap={1} rowGap={0.5} flexWrap={"nowrap"}>
          <Grid>
            <MessageIcon sx={{ color: theme.palette.grey[700] }} />
          </Grid>
          <Grid container rowGap={0.5} flexDirection={"column"} maxWidth={"510px"}>
            <CustomLabel label="Patient Message:"></CustomLabel>
            <Typography variant="bodySmall">{appointmentByIddata?.reason || "-"}</Typography>{" "}
          </Grid>
        </Grid>

        <Grid container mt={3} fontSize={"14px"} gap={1}>
          <Typography fontWeight={550}>Consultation Notes:</Typography>
          <Typography>
            {typeof appointmentByIddata?.clinicalNote === "string"
              ? appointmentByIddata.clinicalNote
              : (appointmentByIddata?.clinicalNote?.note ?? "-")}
          </Typography>
        </Grid>
      </Grid>
      <Grid container flex={1} width={"100%"} mb={2} position={"absolute"} bottom={0} right={"10px"} bgcolor={"white"}>
        <Grid
          container
          height={"100%"}
          columnGap={2}
          width={"100%"}
          alignItems={"flex-end"}
          justifyContent={"flex-end"}
        >
          {!appointmentByIddata?.broadcast &&
            appointmentByIddata.status === "REQUESTED" &&
            new Date(appointmentByIddata?.endTime) > new Date() &&
            !RoleType.isProvider && (
              <>
                <Button
                  onClick={handleBraodCast}
                  startIcon={<PodcastsIcon />}
                  disabled={selectedDate && selectedDate < new Date()}
                  variant="contained"
                >
                  Broadcast
                </Button>

                <Button
                  variant="outlined"
                  onClick={handleAssignNurse}
                  disabled={selectedDate && selectedDate < new Date()}
                >
                  Reassign Nurse
                </Button>
              </>
            )}
          {appointmentByIddata.broadcast &&
            appointmentByIddata.status === "REQUESTED" &&
            new Date(appointmentByIddata?.endTime) > new Date() && (
              <Button variant="outlined" onClick={handleRevoke}>
                Revoke Broadcast
              </Button>
            )}
        </Grid>
        {/* but they are diabled */}
        {/* Reassign nurse dialog */}
        <CustomDialog
          buttonName={["Confirm"]}
          borderRadius="12px"
          open={openDialog}
          title={
            selectedAction === "Broadcast"
              ? "Broadcast Appointment"
              : selectedAction === "Assign"
                ? "Assign Nurse"
                : "Reassign Nurse"
          }
          onClose={() => setOpendialog(false)}
        >
          <ReassignNurseDialog
            onCloseDrawer={onClose}
            appointmentDetails={appointmentByIddata as Appointment}
            action={selectedAction}
            refetch={refetch}
            setOpenReassignAppt={setOpendialog}
          />
        </CustomDialog>
        <ConfirmationPopUp
          open={openConfirmationForBroadcast}
          confirmButtonName="Broadcast"
          onClose={() => setOpenConfirmationForBroadcast(false)}
          onConfirm={() => confirmBroadcast()}
          message={`Do you really want to broadcast the appointment?`}
          title={`Braodcast Appointment`}
          subtitle={"Are you sure you want to broadcast the following appointment?"}
          rowData={[
            appointmentByIddata?.purpose || "",
            appointmentByIddata?.mode ? toCamelCase(appointmentByIddata?.mode) : "",
            appointmentByIddata?.patientName || "",
            appointmentByIddata?.startTime ? format(new Date(appointmentByIddata.startTime), "MM-dd-yyyy") : "-",
          ]}
          header={[{ header: "Purpose" }, { header: "Type" }, { header: "Patient" }, { header: "Date" }]}
        />
        <ConfirmationPopUp
          open={openConfirmationForRevoke}
          confirmButtonName="Revoke Broadcast"
          onClose={() => setOpenConfirmationForRevoke(false)}
          onConfirm={() => confirmRevoke()}
          message={`Do you really want to revoke broadcast the appointment?`}
          title={`Revoke Broadcast`}
          subtitle={"Are you sure you want to revoke broadcast for following appointment?"}
          rowData={[
            appointmentByIddata?.purpose || "",
            appointmentByIddata?.mode ? toCamelCase(appointmentByIddata?.mode) : "",
            appointmentByIddata?.patientName || "",
            appointmentByIddata?.startTime ? format(new Date(appointmentByIddata.startTime), "MM-dd-yyyy") : "-",
          ]}
          header={[{ header: "Purpose" }, { header: "Type" }, { header: "Patient" }, { header: "Date" }]}
        />
      </Grid>
    </Grid>
  );
};

export default AppointmentDetailsDrawer;
