import React, { useState } from "react";

import { Tab, Tabs } from "@mui/material";
import { Grid } from "@mui/system";

import { CustomTabPanel, a11yProps } from "@/common-components/custom-tab/custom-tab";

import useAuthority from "@/hooks/use-authority";

import { Appointment } from "../scheduling-list-table";
import AppointmentDetailsDrawer from "./appointment-details-drawer";
import InfusionDetails from "./infusion-details";

type AppointmentDetailsDrawerType = {
  appointmentDetails: Appointment;
  refetch?:
    | ((startDate?: string, endDate?: string, filterOption?: "ALL" | "UPCOMING" | "PAST" | "REQUESTED") => void)
    | (() => void);
  onClose: () => void;
  selectedDate?: Date;
};

const AppointmentDetailsTabs = (props: AppointmentDetailsDrawerType) => {
  const { selectedDate, appointmentDetails, refetch, onClose } = props;

  const tabLabels = appointmentDetails.mode == "HOME_VISIT" ? ["Overview", "Infusion Details"] : ["Overview"];
  const { isProvider } = useAuthority();
  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    event;
  };

  return (
    <Grid height={"100%"} width={"100%"} maxWidth={"100%"} overflow={"auto"}>
      <Grid height={"100%"} borderRadius={"8px"} container flexDirection={"column"}>
        <Grid>
          <Grid sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs value={value} onChange={handleChange}>
              {tabLabels
                .filter((label) => !(isProvider && label === "Locations"))
                .map((item, index) => (
                  <Tab sx={{ textTransform: "none", fontWeight: 550 }} key={index} label={item} {...a11yProps(0)} />
                ))}
            </Tabs>
          </Grid>
          <Grid flex={1}>
            {tabLabels
              .filter((label) => !(isProvider && label === "Locations"))
              .map((item, index) => (
                <CustomTabPanel key={index} value={value} index={index}>
                  {item === "Overview" && (
                    <AppointmentDetailsDrawer
                      selectedDate={selectedDate}
                      refetch={refetch}
                      onClose={onClose}
                      appointmentDetails={appointmentDetails}
                    />
                  )}
                  {item === "Infusion Details" && <InfusionDetails />}
                </CustomTabPanel>
              ))}
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default AppointmentDetailsTabs;
