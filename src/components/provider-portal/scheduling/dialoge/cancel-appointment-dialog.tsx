import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import { Button, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import { ErrorResponseEntity } from "@/models/response/error-response";
import { setIsLoading } from "@/redux/actions/loader-action";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useAppointmentControllerServiceUpdateAppointmentStatus } from "@/sdk/queries";
import { Appointment } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

type CancelAppointmentDialogType = {
  appointmentDetails: Appointment;
  onClose: () => void;
  selectedAction: string;
  refetch?: () => void;
};

const CancelAppointmentDialog = (props: CancelAppointmentDialogType) => {
  const { appointmentDetails, onClose, refetch, selectedAction } = props;
  const dispatch = useDispatch();
  const [cancellationReason, setCancellationReason] = useState("");

  const { mutateAsync, isPending } = useAppointmentControllerServiceUpdateAppointmentStatus({
    onError: (error) => {
      const message =
        (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while updating status";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: (data) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: (data?.message || `Successfully updated appointment status as Cancel.`) as string,
        })
      );
    },
  });

  useEffect(() => {
    dispatch(setIsLoading(isPending));
  }, [dispatch, isPending]);

  const onSubmit = async () => {
    if (selectedAction === "Cancel Appointment") {
      if (appointmentDetails.uuid && cancellationReason) {
        await mutateAsync({
          requestBody: { uuid: appointmentDetails.uuid || "", reason: cancellationReason, status: "CANCELLED" },
          xTenantId: GetTenantId(),
        });
      }
    }
    if (selectedAction === "Mark As No Show") {
      if (appointmentDetails.uuid) {
        await mutateAsync({
          requestBody: { uuid: appointmentDetails.uuid || "", reason: cancellationReason, status: "NO_SHOW" },
          xTenantId: GetTenantId(),
        });
      }
    }
    refetch && refetch();
    onClose();
  };

  return (
    <Grid flexDirection={"column"} container width={"350px"}>
      {selectedAction === "Cancel Appointment" && (
        <>
          <CustomLabel label="Cancellation Reason" isRequired />
          <CustomInput
            placeholder={"Enter Cancellation Reason"}
            name={"reason"}
            multiline
            rows={2}
            value={cancellationReason}
            onChange={(e) => setCancellationReason(e.target.value)}
          />
        </>
      )}
      {selectedAction === "Mark As No Show" && (
        <Typography variant="bodySmall">Do you really want to update the appointment status as "No Show"?</Typography>
      )}
      <Grid width={"100%"} container justifyContent={"flex-end"} pt={"16px"}>
        <Button onClick={onSubmit} variant="contained" disabled={!cancellationReason}>
          Confirm
        </Button>
      </Grid>
    </Grid>
  );
};

export default CancelAppointmentDialog;
