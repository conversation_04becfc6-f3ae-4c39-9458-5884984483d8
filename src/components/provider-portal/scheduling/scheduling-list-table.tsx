import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import PodcastsIcon from "@mui/icons-material/Podcasts";
import {
  Button,
  ButtonBase,
  IconButton,
  Link,
  MenuItem,
  MenuList,
  Popover,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import {
  addDays,
  addMinutes,
  endOfDay,
  format,
  isAfter,
  isBefore,
  isSameDay,
  parseISO,
  startOfDay,
  subDays,
  subMinutes,
  subSeconds,
} from "date-fns";
import { toZonedTime } from "date-fns-tz";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomDrawer from "@/common-components/custom-drawer/custom-drawer";
import Status from "@/common-components/status/status";

import { ContentObject } from "@/models/response/response-content-entity";
import { useAppointmentControllerServiceUpdateAppointmentStatus } from "@/sdk/queries";
import { DATE_FORMAT_DD_MM_YY } from "@/services/common/date-formatter";

import Paginator from "../../../common-components/paginator/paginator";
import { AlertSeverity } from "../../../common-components/snackbar-alert/snackbar-alert";
import { heading, tableCellCss, typographyCss } from "../../../common-components/table/common-table-widgets";
import { TableHeaders } from "../../../common-components/table/table-models";
import { ErrorResponseEntity } from "../../../models/response/error-response";
import { setIsLoading } from "../../../redux/actions/loader-action";
import { setSnackbarOn } from "../../../redux/actions/snackbar-action";
import { Address, ClinicalNote } from "../../../sdk/requests";
import { theme } from "../../../utils/theme";
import { toCamelCase } from "../../../utils/toCamelCase";
import AppointmentDetailsTabs from "./dialoge/appointment-details-tabs";
import CancelAppointmentDialog from "./dialoge/cancel-appointment-dialog";
import ReassignNurseDialog from "./dialoge/reassign-nurse-dialog";

function SchedulingListTable(props: SchedulingListTableProps) {
  const {
    appointmentListData,
    refetch,
    getPage,
    getSize,
    getDates,
    listFilterOption,
    startDate,
    endDate,
    sortByTime,
    sortDirectionByDate,
    sortDirectionByStatus,
    handleSorting,
  } = props;
  sortByTime;

  const dispatch = useDispatch();
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState<number>(0);

  const [selectedAppt, setSelectedAppt] = useState<Appointment | null>({} as Appointment);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [getAllAppointmentData, setGetAllAppointmentData] = useState<Appointment[] | undefined>(
    appointmentListData?.content
  );
  const [openApptDetailsDrawer, setOpenApptDetailsDrawer] = useState(false);

  useEffect(() => {
    setGetAllAppointmentData(appointmentListData?.content);
    setTotalPages(appointmentListData?.page?.totalPages as number);
    setTotalElements(appointmentListData?.page?.totalElements as number);
  }, [appointmentListData]);

  const [openCancelAppointment, setOpenCancelAppointment] = useState(false);
  const [openReassignAppt, setOpenReassignAppt] = useState(false);

  const handlePreviousDate = () => {
    setSelectedDate((prevDate) => {
      updateSelectedDates(subDays(prevDate, 1));
      return subDays(prevDate, 1);
    });
  };

  const handleNextDate = () => {
    setSelectedDate((prevDate) => {
      updateSelectedDates(addDays(prevDate, 1));
      return addDays(prevDate, 1);
    });
  };

  const [anchorEl, setAnchorEl] = useState<Element | null>(null);
  const [selectedAction, setSelectedAction] = useState<
    "Reschedule" | "Cancel Appointment" | "Reassign Nurse" | "Mark As No Show"
  >("Reschedule");

  const toggleMoreMenu = (el?: Element) => {
    setAnchorEl(el || null);
  };

  const getTodaysDate = () => {
    setSelectedDate(new Date());
  };

  const handleOnClickPurpose = (appt: Appointment) => {
    setOpenApptDetailsDrawer(true);
    setSelectedAppt(appt);
    setSelectedDate(new Date(appt.endTime));
  };

  const { mutateAsync, isPending } = useAppointmentControllerServiceUpdateAppointmentStatus({
    onError: (error) => {
      const message =
        (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while updating status";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: (data) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: (data?.message || `Successfully updated appointment status as Cancel.`) as string,
        })
      );
    },
  });

  const confirmMarkAsNoShow = async () => {
    if (selectedAction === "Mark As No Show") {
      if (selectedAppt?.uuid) {
        await mutateAsync({
          requestBody: { uuid: selectedAppt.uuid || "", status: "NO_SHOW", noshow: markAsNoShowBy },
        });
      }
    }
    setOpenCancelAppointment(false);
    handleRefetch(startDate, endDate, listFilterOption);
  };

  useEffect(() => {
    dispatch(setIsLoading(isPending));
  }, [dispatch, isPending]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    getSize(recordsPerPage, startOfDay(selectedDate).toISOString(), endOfDay(selectedDate).toISOString());
    setSize(recordsPerPage);
  };

  const handlePageChange = (event: ChangeEvent<unknown> | null, page: number) => {
    setPage(page);

    if (event) {
      getPage(page, size, startOfDay(selectedDate).toISOString(), endOfDay(selectedDate).toISOString());
    }
  };

  const [isBackArraowDisabled, setIsBackArraowDisabled] = useState(false);
  const [isForwardArraowDisabled, setIsForwardArraowDisabled] = useState(false);
  const [anchorElNested, setAnchorElNested] = useState<Element | null>(null);
  const [markAsNoShowBy, setMarkAsNoShow] = useState<"PATIENT" | "NURSE">("PATIENT");

  const toggleMoreMenuNested = (el?: Element) => {
    setAnchorElNested(el || null);
  };

  const updateArrowStates = (selectedDate: string | number | Date, status: string) => {
    const currentDate = new Date(); // Current date and time
    const selectedDateTime = new Date(selectedDate);

    let isBackArrowDisabled = false;
    let isForwardArrowDisabled = false;

    // Arrow disabling logic
    if (status === "UPCOMING") {
      isBackArrowDisabled = isBefore(selectedDateTime, currentDate); // Disable back if selected date is in the past
    } else if (status === "PAST") {
      isForwardArrowDisabled =
        isAfter(selectedDateTime, currentDate) || isSameDay(startOfDay(selectedDateTime), startOfDay(currentDate)); // Disable forward if selected date is in the future
    }

    // Start and end date in UTC
    let startDateUTC, endDateUTC;

    if (status === "UPCOMING" && isSameDay(selectedDateTime, currentDate)) {
      // For "upcoming" and today, use current time as start date
      startDateUTC = currentDate.toISOString();
      endDateUTC = endOfDay(selectedDateTime).toISOString();
    } else if (status === "UPCOMING" && isBefore(selectedDateTime, currentDate)) {
      startDateUTC = currentDate.toISOString();
      endDateUTC = endOfDay(currentDate).toISOString();
    } else if (status === "PAST" && isSameDay(selectedDateTime, currentDate)) {
      // For "past" and today, use current time minus 15 seconds as end date
      startDateUTC = startOfDay(selectedDateTime).toISOString();
      endDateUTC = subSeconds(currentDate, 15).toISOString();
    } else if (status === "PAST" && isAfter(selectedDateTime, currentDate)) {
      // For "past" and today, use current time minus 15 seconds as end date
      startDateUTC = startOfDay(currentDate).toISOString();
      endDateUTC = endOfDay(currentDate).toISOString();
    } else {
      // Default case
      startDateUTC = startOfDay(selectedDateTime).toISOString();
      endDateUTC = endOfDay(selectedDateTime).toISOString();
    }

    // Update states
    setIsBackArraowDisabled(isBackArrowDisabled);
    setIsForwardArraowDisabled(isForwardArrowDisabled);

    return { startDateUTC, endDateUTC };
  };

  const updateSelectedDates = (date: string | number | Date) => {
    if (startDate && endDate) {
      return;
    }

    const { startDateUTC, endDateUTC } = updateArrowStates(date, listFilterOption);

    const validStartDate =
      startDateUTC && typeof startDateUTC === "string" && startDateUTC.includes("T")
        ? startDateUTC
        : new Date(startDateUTC).toISOString();

    const validEndDate =
      endDateUTC && typeof endDateUTC === "string" && endDateUTC.includes("T")
        ? endDateUTC
        : new Date(endDateUTC).toISOString();

    getDates(validStartDate, validEndDate);
    setSelectedDate(new Date(validStartDate));

    if (listFilterOption === "REQUESTED") {
      setIsBackArraowDisabled(false);
      setIsForwardArraowDisabled(false);
    }
  };

  const handleRefetch = (
    startDateParam?: string,
    endDateParam?: string,
    filterOption?: "ALL" | "UPCOMING" | "PAST" | "REQUESTED"
  ) => {
    if (startDateParam && endDateParam) {
      let formattedStartDate = startDateParam;
      if (!startDateParam.includes("T")) {
        if (startDateParam.match(/^\d{2}-\d{2}-\d{4}$/)) {
          const [month, day, year] = startDateParam.split("-");
          if (isSameDay(new Date(`${year}-${month}-${day}`), new Date(endDateParam))) {
            // If dates are the same, set start date to previous day at 18:30 UTC
            formattedStartDate = new Date(
              new Date(`${year}-${month}-${day}T18:30:00.000Z`).getTime() - 86400000
            ).toISOString();
          } else {
            // If dates are different, use current UTC time
            formattedStartDate = new Date(
              `${year}-${month}-${day}T${new Date().toISOString().split("T")[1]}`
            ).toISOString();
          }
        } else {
          formattedStartDate = new Date(startDateParam).toISOString();
        }
      }

      let formattedEndDate = endDateParam;
      if (!endDateParam.includes("T")) {
        if (endDateParam.match(/^\d{2}-\d{2}-\d{4}$/)) {
          const [month, day, year] = endDateParam.split("-");
          formattedEndDate = new Date(`${year}-${month}-${day}T23:59:59.999Z`).toISOString();
        } else {
          formattedEndDate = endOfDay(new Date(endDateParam)).toISOString();
        }
      }

      // If start and end dates are the same day, always use start of day for start date
      if (isSameDay(new Date(formattedStartDate), new Date(formattedEndDate))) {
        formattedStartDate = startOfDay(new Date(formattedStartDate)).toISOString();
      }

      refetch(formattedStartDate, formattedEndDate, filterOption);
      return;
    }

    const currentDate = new Date();
    let start, end;

    if (selectedDate) {
      // When using a date picker, always use start of day for the start date
      start = startOfDay(selectedDate).toISOString();

      if (listFilterOption === "PAST" && isSameDay(selectedDate, currentDate)) {
        end = subMinutes(currentDate, 1).toISOString();
      } else if (listFilterOption === "UPCOMING" && isSameDay(selectedDate, currentDate)) {
        // For UPCOMING on today, adjust start time to current time
        start = addMinutes(currentDate, 1).toISOString();
        end = endOfDay(selectedDate).toISOString();
      } else {
        end = endOfDay(selectedDate).toISOString();
      }
    } else {
      start = startOfDay(currentDate).toISOString();
      end = endOfDay(currentDate).toISOString();
    }

    refetch(start, end, filterOption);
  };

  useEffect(() => {
    if (!startDate) {
      updateSelectedDates(selectedDate);
    }

    if (listFilterOption === "PAST") {
      setactionMenu(["Cancel Appointment", "Mark As No Show"]);
    } else {
      setactionMenu(["Reschedule", "Cancel Appointment", "Reassign Nurse", "Mark As No Show"]);
    }
  }, [listFilterOption, startDate]);

  const [actionMenu, setactionMenu] = useState([
    "Reschedule",
    "Cancel Appointment",
    "Reassign Nurse",
    "Mark As No Show",
  ]);

  const TimeZoneIANA: Record<string, string> = {
    IST: "Asia/Kolkata", // Indian Standard Time (UTC+05:30)
    SGT: "Asia/Singapore", // Singapore Standard Time (UTC+08:00)
    CST: "America/Chicago", // Central Standard Time (UTC-06:00)
    EST: "America/New_York", // Eastern Standard Time (UTC-05:00)
    MST: "America/Denver", // Mountain Standard Time (UTC-07:00)
    PST: "America/Los_Angeles", // Pacific Standard Time (UTC-08:00)
    HST: "Pacific/Honolulu", // Hawaiian Standard Time (UTC-10:00)
    AKST: "America/Anchorage", // Alaska Standard Time (UTC-09:00)
    AST: "Asia/Riyadh", // Arabian Standard Time (UTC+03:00)
    EDT: "America/New_York", // Eastern Daylight Time (UTC-04:00)
    PDT: "America/Los_Angeles", // Pacific Daylight Time (UTC-07:00)
    CDT: "America/Chicago", // Central Daylight Time (UTC-05:00)
    ADT: "America/Halifax", // Atlantic Daylight Time (UTC-03:00)
    MDT: "America/Denver", // Mountain Daylight Time (UTC-06:00)
    AKDT: "America/Anchorage", // Alaska Daylight Time (UTC-08:00)
    UTC: "UTC", // Coordinated Universal Time
  };

  // Modified function to make sure column sorting works correctly
  useEffect(() => {
    // When sorting changes, ensure dates are properly handled, especially for PAST filter
    if (startDate && endDate && isSameDay(new Date(startDate), new Date(endDate))) {
      if (listFilterOption === "PAST") {
        // For PAST filter with same day, always use start of day
        const formattedStartDate = startOfDay(new Date(startDate)).toISOString();
        if (formattedStartDate !== startDate) {
          // Only refetch if needed to avoid extra API calls
          refetch(formattedStartDate, endDate, listFilterOption);
        }
      }
    }
  }, [sortDirectionByDate, sortDirectionByStatus]);

  // Modify the TableHead cell's onClick to directly use table sorting
  const handleColumnSort = (header: string) => {
    // Call parent component's handleSorting with the correct column name
    handleSorting(header);

    // For PAST filter with same-day dates, ensure proper date handling
    if (startDate && endDate && isSameDay(new Date(startDate), new Date(endDate)) && listFilterOption === "PAST") {
      const formattedStartDate = startOfDay(new Date(startDate)).toISOString();
      if (formattedStartDate !== startDate) {
        // Delay refetch slightly to allow parent component's state updates
        setTimeout(() => {
          refetch(formattedStartDate, endDate, listFilterOption);
        }, 50);
      }
    }
  };

  return (
    <Grid container gap={1} borderRadius={"4px"} border={`1px solid ${theme.palette.grey[300]}`}>
      {!startDate && !endDate && (
        <Grid container alignItems={"center"} gap={2} justifyContent={"center"} margin={"10px 0px 0px 10px"}>
          <Button
            sx={{
              height: "30px",
              width: "20px",
              border: "1px solid #006D8F",
              borderRadius: "8px",
            }}
            onClick={getTodaysDate}
          >
            Today
          </Button>
          <Grid container alignContent={"center"} justifyContent={"center"}>
            <IconButton
              onClick={handlePreviousDate}
              disabled={listFilterOption !== "REQUESTED" && isBackArraowDisabled}
            >
              <ChevronLeftIcon fontSize="small" />
            </IconButton>
            <Typography
              variant="bodyMedium"
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {format(selectedDate, "dd MMMM   yyyy")}
            </Typography>
            <IconButton onClick={handleNextDate} disabled={listFilterOption !== "REQUESTED" && isForwardArraowDisabled}>
              <ChevronRightIcon fontSize="small" />
            </IconButton>
          </Grid>
        </Grid>
      )}
      {startDate && endDate && (
        <Grid container alignItems={"center"} gap={2} justifyContent={"center"} margin={"10px 0px 0px 10px"}>
          <Typography variant="bodySmall" fontWeight={"550"}>
            {format(startDate, DATE_FORMAT_DD_MM_YY)}
          </Typography>
          &nbsp;-&nbsp;
          <Typography variant="bodySmall" fontWeight={"550"}>
            {format(endDate, DATE_FORMAT_DD_MM_YY)}
          </Typography>
        </Grid>
      )}
      {/* //table */}
      <Grid width={"100%"}>
        <TableContainer sx={{ maxHeight: "78vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {mockHeaders
                  .filter((header) => !(listFilterOption === "REQUESTED" && header.header === "Actions"))
                  .map((header, index) => (
                    <TableCell
                      key={index}
                      sx={{
                        ...heading,
                        minWidth: header.minWidth ? header.minWidth : "inherit",
                        maxWidth: header.maxWidth ? header.maxWidth : "inherit",
                      }}
                      align="left"
                    >
                      <Grid container flexDirection={"column"} alignContent={"flex-start"}>
                        {/* <Typography variant="bodySmall">{header.header}</Typography> */}
                        {header.header === "Date" ? (
                          <Link
                            style={{
                              color: "#667085",
                              textDecoration: "none",
                              cursor: "pointer",
                            }}
                            onClick={() => handleColumnSort(header.header)}
                          >
                            <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                              {header.header}
                              <Typography mt={0.3}>
                                {sortDirectionByDate == "asc" ? (
                                  <ArrowUpwardIcon fontSize="small" />
                                ) : (
                                  <ArrowDownwardIcon fontSize="small" />
                                )}
                              </Typography>
                            </Typography>
                          </Link>
                        ) : header.header === "Status" ? (
                          <Link
                            style={{
                              color: "#667085",
                              textDecoration: "none",
                              cursor: "pointer",
                            }}
                            onClick={() => handleColumnSort(header.header)}
                          >
                            <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                              {header.header}
                              <Typography>
                                {sortDirectionByStatus == "asc" ? (
                                  <ArrowUpwardIcon fontSize="small" />
                                ) : (
                                  <ArrowDownwardIcon fontSize="small" />
                                )}
                              </Typography>
                            </Typography>
                          </Link>
                        ) : (
                          <Grid
                            container
                            flexDirection={"column"}
                            alignContent={header.header === "Actions" ? `flex-end` : "flex-start"}
                          >
                            <Typography variant="bodySmall">{header.header}</Typography>
                          </Grid>
                        )}
                      </Grid>
                    </TableCell>
                  ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {getAllAppointmentData && getAllAppointmentData.length > 0 ? (
                getAllAppointmentData.map((appt, index) => (
                  <TableRow key={index}>
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <ButtonBase
                          sx={{ display: "flex", justifyContent: "flex-start" }}
                          onClick={() => handleOnClickPurpose(appt)}
                        >
                          <Typography color="primary" fontWeight={550} variant="bodySmall">
                            {appt.purpose || "-"}
                          </Typography>

                          {appt.broadcast && (
                            <Grid
                              container
                              bgcolor={"#B1000F"}
                              justifyContent={"center"}
                              width={"35px"}
                              borderRadius={"16px"}
                              ml={1}
                              pt={0.4}
                              pb={0.4}
                            >
                              <PodcastsIcon sx={{ width: "15px", height: "15px", color: theme.palette.common.white }} />
                            </Grid>
                          )}
                        </ButtonBase>
                      </Grid>
                    </TableCell>
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <Grid container flexDirection={"column"} sx={{ cursor: "pointer" }}>
                          <Typography sx={typographyCss} variant="bodySmall">
                            {appt.mode ? toCamelCase(appt.mode) : ""}
                          </Typography>
                        </Grid>
                      </Grid>
                    </TableCell>
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <Typography sx={typographyCss} variant="bodySmall">
                          {appt.patientName || "-"}
                        </Typography>
                      </Grid>
                    </TableCell>

                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <Typography sx={typographyCss} variant="bodySmall">
                          {appt.nurseName || "-"}
                        </Typography>
                      </Grid>
                    </TableCell>
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <Typography sx={typographyCss} variant="bodySmall">
                          {appt.endTime && appt.timezone
                            ? format(
                                toZonedTime(parseISO(appt.endTime), TimeZoneIANA[appt.timezone]),
                                DATE_FORMAT_DD_MM_YY
                              )
                            : "-"}
                        </Typography>
                      </Grid>
                    </TableCell>

                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection="column">
                        {appt.startTime
                          ? (() => {
                              try {
                                const date = new Date(appt.startTime);
                                const hour = date.getHours();
                                const minutes = date.getMinutes().toString().padStart(2, "0");
                                const period = hour < 12 ? "AM" : "PM";
                                const formattedHour = hour % 12 || 12;
                                return `${formattedHour}:${minutes} ${period}`;
                              } catch (error) {
                                return "-";
                              }
                            })()
                          : "-"}
                        &nbsp; - &nbsp;
                        {appt.endTime
                          ? (() => {
                              try {
                                const date = new Date(appt.endTime);
                                const hour = date.getHours();
                                const minutes = date.getMinutes().toString().padStart(2, "0");
                                const period = hour < 12 ? "AM" : "PM";
                                const formattedHour = hour % 12 || 12;
                                return `${formattedHour}:${minutes} ${period}`;
                              } catch (error) {
                                return "-";
                              }
                            })()
                          : "-"}
                      </Grid>
                    </TableCell>

                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <Typography sx={typographyCss} variant="bodySmall">
                          {appt.duration || "-"}
                        </Typography>
                      </Grid>
                    </TableCell>
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <Status status={appt.status || ""} width="120px" />
                      </Grid>
                    </TableCell>

                    {listFilterOption !== "REQUESTED" && (
                      <TableCell sx={{ ...heading }}>
                        <Grid container columnGap={1.2} flexWrap={"nowrap"}>
                          <IconButton
                            disabled={
                              appt.status == "CANCELLED" || appt.status == "NO_SHOW" || appt.status == "COMPLETED"
                            }
                            style={{ padding: 5 }}
                            onClick={(e) => {
                              toggleMoreMenu(e.currentTarget);
                              setSelectedAppt(appt);
                            }}
                          >
                            <MoreVertIcon />
                          </IconButton>
                          <Popover
                            open={Boolean(anchorEl)}
                            anchorEl={anchorEl}
                            onClose={() => {
                              toggleMoreMenu();
                            }}
                            anchorOrigin={{
                              vertical: "top",
                              horizontal: "left",
                            }}
                            transformOrigin={{
                              vertical: "top",
                              horizontal: "right",
                            }}
                          >
                            <MenuList autoFocusItem={!!anchorEl} disablePadding>
                              {actionMenu
                                .filter(
                                  (v) =>
                                    listFilterOption === "PAST" ||
                                    (appt.status !== "REQUESTED" && v !== "Mark As No Show")
                                )
                                .map((v) => (
                                  <MenuItem
                                    key={v}
                                    selected={v === selectedAction}
                                    onClick={(e) => {
                                      setSelectedAction(
                                        v as "Reschedule" | "Cancel Appointment" | "Reassign Nurse" | "Mark As No Show"
                                      );

                                      if (v === "Mark As No Show") {
                                        toggleMoreMenuNested(e.currentTarget);
                                      } else {
                                        setAnchorEl(null);
                                      }

                                      if (v === "Cancel Appointment") {
                                        setOpenCancelAppointment(true);
                                      }

                                      if (v === "Reassign Nurse" || v === "Reschedule") {
                                        setOpenReassignAppt(true);
                                      }
                                    }}
                                    disabled={
                                      v === "Mark As No Show" &&
                                      ["COMPLETED", "CANCELLED", "REQUESTED", "UPCOMING"].includes(appt.status || "")
                                    }
                                  >
                                    {v}
                                  </MenuItem>
                                ))}
                            </MenuList>
                          </Popover>
                          <Popover
                            open={Boolean(anchorElNested)}
                            anchorEl={anchorElNested}
                            onClose={() => {
                              toggleMoreMenuNested();
                            }}
                            anchorOrigin={{
                              vertical: "top",
                              horizontal: "left",
                            }}
                            transformOrigin={{
                              vertical: "top",
                              horizontal: "right",
                            }}
                          >
                            <MenuList autoFocusItem={!!anchorElNested} disablePadding>
                              {["Patient", "Nurse"].map((v) => (
                                <MenuItem
                                  key={v}
                                  selected={v === selectedAction}
                                  onClick={() => {
                                    setMarkAsNoShow(v === "Patient" ? "PATIENT" : "NURSE");
                                    setAnchorElNested(null);
                                    setAnchorEl(null);
                                    setOpenCancelAppointment(true);
                                  }}
                                >
                                  {v}{" "}
                                </MenuItem>
                              ))}
                            </MenuList>
                          </Popover>
                        </Grid>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={10} align="center">
                    <Typography variant="bodySmall" fontWeight={550}>
                      No records found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
            {selectedAppt && (
              <CustomDialog
                buttonName={["Confirm"]}
                open={selectedAction === "Cancel Appointment" && openCancelAppointment}
                title={selectedAction}
                onClose={() => setOpenCancelAppointment(false)}
              >
                <CancelAppointmentDialog
                  appointmentDetails={selectedAppt as Appointment}
                  onClose={() => setOpenCancelAppointment(false)}
                  refetch={() => {
                    handleRefetch(startDate, endDate, listFilterOption);
                    setSelectedAppt(null);
                  }}
                  selectedAction={selectedAction}
                />
              </CustomDialog>
            )}
            <ConfirmationPopUp
              open={selectedAction === "Mark As No Show" && openCancelAppointment}
              onClose={() => setOpenCancelAppointment(false)}
              onConfirm={() => confirmMarkAsNoShow()}
              message={`Do you really want to mark it as no show?`}
              title={`Mark As No Show By ${toCamelCase(markAsNoShowBy)}`}
              subtitle={"Are you sure you want to to mark it as no show?"}
              confirmButtonName="Confirm"
              rowData={[
                selectedAppt?.purpose || "",
                selectedAppt?.mode ? toCamelCase(selectedAppt?.mode) : "",
                selectedAppt?.patientName || "",
                selectedAppt?.startTime ? format(new Date(selectedAppt.startTime), "MM-dd-yyyy") : "-",
              ]}
              header={[{ header: "Purpose" }, { header: "Type" }, { header: "Patient" }, { header: "Date" }]}
            />

            {/* Reassign nurse dialog */}
            <CustomDialog
              buttonName={["Confirm"]}
              borderRadius="12px"
              open={openReassignAppt}
              title={selectedAction}
              onClose={() => setOpenReassignAppt(false)}
            >
              <ReassignNurseDialog
                appointmentDetails={selectedAppt as Appointment}
                action={selectedAction}
                setOpenReassignAppt={setOpenReassignAppt}
                refetch={() => handleRefetch(startDate, endDate, listFilterOption)}
              />
            </CustomDialog>

            {selectedAppt && (
              <CustomDrawer
                anchor={"right"}
                open={openApptDetailsDrawer}
                title={"Appointment Details"}
                drawerWidth="600px"
                onClose={() => {
                  setOpenApptDetailsDrawer(false);
                  setSelectedAppt({} as Appointment);
                }}
              >
                <AppointmentDetailsTabs
                  selectedDate={selectedDate}
                  refetch={() => handleRefetch(startDate, endDate, listFilterOption)}
                  onClose={() => {
                    setOpenApptDetailsDrawer(false);
                    setSelectedAppt({} as Appointment);
                  }}
                  appointmentDetails={selectedAppt as Appointment}
                />
              </CustomDrawer>
            )}
          </Table>
          <Paginator
            page={page}
            defaultSize={size}
            totalPages={totalPages}
            totalRecord={totalElements}
            onRecordsPerPageChange={handleRecordsPerPageChange}
            onPageChange={handlePageChange}
          />
        </TableContainer>
      </Grid>
    </Grid>
  );
}

export default SchedulingListTable;

export type Appointment = {
  uuid?: string;
  purpose: string;
  patientId?: string;
  nurseId: string;
  external: boolean;
  startTime: string;
  endTime: string;
  duration: number;
  timezone:
    | "PST"
    | "EST"
    | "CST"
    | "MST"
    | "AST"
    | "HST"
    | "EDT"
    | "PDT"
    | "CDT"
    | "ADT"
    | "MDT"
    | "IST"
    | "AKDT"
    | "AKST";
  patientName?: string;
  patientMrn?: string;
  patientEmail?: string;
  patientPhone?: string;
  nurseName?: string;
  mode?: "HOME_VISIT" | "TELE_VISIT";
  status?:
    | "PENDING"
    | "ACCEPTED"
    | "REJECTED"
    | "CONFIRMED"
    | "REQUESTED"
    | "CANCELLED"
    | "NO_SHOW"
    | "CHECKED_IN"
    | "IN_EXAM"
    | "COMPLETED"
    | "SCHEDULED"
    | "RESCHEDULED";
  rescheduleReason?: string;
  cancelReason?: string;
  slotOpen?: boolean;
  created?: string;
  archive?: boolean;
  reason?: string;
  address?: Address;
  broadcastNurseName?: string;
  patientAvatar?: string;
  broadcastBy?: string;
  broadcast?: boolean;
  nurseAvatar?: string;
  clinicalNote?: ClinicalNote;
};

interface SchedulingListTableProps {
  listFilterOption: "ALL" | "UPCOMING" | "PAST" | "REQUESTED";
  appointmentListData: ContentObject<Appointment[]>;
  startDate: string;
  endDate: string;
  refetch: (startDate: string, endDate: string, filterOption?: "ALL" | "UPCOMING" | "PAST" | "REQUESTED") => void;
  getPage: (page: number, size: number, startDate: string, endDate: string) => void;
  getSize: (size: number, startDate: string, endDate: string) => void;
  getDates: (startDate: string, endDate: string) => void;
  sortByTime: string;
  sortDirectionByDate: string;
  sortDirectionByStatus: string;
  handleSorting: (column: string) => void;
}
const mockHeaders: TableHeaders[] = [
  { header: "Purpose", minWidth: "100px" },
  { header: "Type" },
  { header: "Patient Name" },
  { header: "Nurse" },
  { header: "Date" },
  { header: "Time" },
  { header: "Duration" },
  { header: "Status" },
  { header: "Actions" },
];
