import { useEffect, useMemo, useState } from "react";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";

// import { Navigate, useNavigate } from "react-router-dom";

import {
  Button,
  ButtonBase,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Skeleton,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
// import { formatInTimeZone, toZonedTime } from "date-fns-tz";
import * as yup from "yup";

import { errorStyle } from "@/common-components/custom-input/widgets/custom-input-styles";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import useAuthority from "@/hooks/use-authority";
import useApiFeedback from "@/hooks/useApiFeedback";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { RootState } from "@/redux/store";
import { useAppointmentControllerServiceBookAppointmentRequest } from "@/sdk/queries";
import { AllTimezones, getStartAndEndOfDayUTC2 } from "@/services/common/date-formatter";

import CustomAutoComplete from "../../../common-components/custom-auto-complete/custom-auto-complete";
import CustomLabel from "../../../common-components/custom-label/custom-label";
import CustomSelect from "../../../common-components/custom-select/customSelect";
import DatePicker from "../../../common-components/date-picker-field/date-picker-field";
import { Options, OptionsInterface } from "../../../constants/options";
import { ContentObject } from "../../../models/response/response-content-entity";
import {
  AvailabilityControllerService,
  PatientControllerService,
  ProviderControllerService,
  // timezone,
  User,
} from "../../../sdk/requests";
import { GetTenantId } from "../../../services/common/get-tenant-id";
import { theme } from "../../../utils/theme";
import { PatientDetailsInScheduleAppointment } from "./patient-details-in-schedule-appt";

export interface SlotType {
  duration: number;
  status: string;
  startTime: string;
  endTime: string;
  timeZone: string;
  slots: string;
}

const appointmentSchema = yup.object().shape({
  mode: yup.string(),
  purpose: yup.string().required("Appointment Title is Required"),
  patient: yup.string().required("Patient Name is Required"),
  nurse: yup.string().required("Nurse Name is Required"),
  date: yup.string().required("Date is Required"),
  startTime: yup.string().required("Please select available slot"),
  endTime: yup.string(),
  duration: yup.string().required("Duration is Required"),
  timezone: yup.string().required("Time zone is required"),
});
type ScheduleAppointmentDialogProps = {
  refetch: () => void;
  // selectedAppointment?: Appointment;
  selectedAction?: string;
  patient?: PatientDetails;
  onClose?: () => void;
  // onSuccessCallback?: () => void;
  setScheduleAppointmentDialog: (isOpen: boolean) => void;
};

export const selectAptBtnStyles = (isActive: boolean) => ({
  backgroundColor: isActive ? theme.palette.primary.light : "initial",
  color: isActive ? theme.palette.common.white : "initial",
  borderRadius: "4px",
  "&:hover": {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.common.white,
  },
  "&:active": {
    boxShadow: "none",
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.common.white,
  },
  "&:focus": {
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.common.white,
    bgcolor: "brown",
  },
});

export type TimeSlot = {
  startTime: string;
  endTime: string;
};

function ScheduleAppointmentDialog(props: ScheduleAppointmentDialogProps) {
  const {
    // selectedAction,
    // selectedAppointment,
    patient,
    // onClose,
    refetch,
    setScheduleAppointmentDialog,
  } = props;
  const dispatch = useDispatch();
  const xtenantId = GetTenantId();
  const [getllAllPatient, setGetAllPatient] = useState<PatientDetails[]>();
  const [selectedPatient, setSelectedPatient] = useState<PatientDetails | undefined>(patient || undefined);
  const [getNurse, setNurse] = useState<OptionsInterface[]>([]);

  const [patientValue, setPatientValue] = useState<string | null>("");
  const [nurseValue, setNurseValue] = useState("");
  const [patientOptions, setPatientOptions] = useState<Options>([]);
  const [durationTime, setDurationTime] = useState("");
  const [timeZone, setTimeZone] = useState("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot>();

  const [allSlots, setAllSlots] = useState<SlotType[]>([]);
  const [isLoadingScheduleAppointment, setIsLoadingScheduleAppointment] = useState(false);

  const initialValues = {
    mode: "teleVisit",
    purpose: "",
    patient: "",
    nurse: "",
    date: "",
    startTime: "",
    endTime: "",
    duration: "",
    timezone: "",
  };

  const {
    control,
    setValue,
    watch,
    formState: { errors },
    handleSubmit,
  } = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(appointmentSchema),
  });

  const Check = (patient: PatientDetails) => {
    return (
      <Grid
        container
        onClick={() => {
          setSelectedPatient(patient);
        }}
      >
        <PatientDetailsInScheduleAppointment
          handleOnChangePatient={() => {
            setValue("patient", patient.uuid || "");
          }}
          forDropDown
          minWidth={"100%"}
          isChangePatientRequired={selectedPatient?.patientName.length === 0}
          patient={patient}
          selectedFromDropDown={(patient: PatientDetails | undefined) => {
            setSelectedPatient(patient);
          }}
        />
      </Grid>
    );
  };

  const createPatientDropDowOptions = () => {
    const optArr =
      (getllAllPatient &&
        getllAllPatient
          .filter((patient) => patient.uuid)
          .map((patient: PatientDetails) => {
            return {
              key: patient.uuid || "",
              value: `${patient.patientName}`,
              child: Check(patient),
              info: (patient.nurseId as unknown as string) || "",
              info2: patient.nurseName,
            };
          })) ||
      [];

    setPatientOptions(optArr);
  };

  useEffect(() => {
    createPatientDropDowOptions();
  }, [getllAllPatient]);

  const handleOnChangePatient = () => {
    setSelectedPatient(undefined);
    setValue("patient", "");
    setPatientValue("");
  };

  const handleSelectSlot = (val: TimeSlot) => {
    setSelectedSlot(val);
    setValue("startTime", val.startTime, { shouldValidate: true });
    setValue("endTime", val.endTime);
  };

  //check role who have logedIn
  const getRole = useAuthority();
  const isProvider = getRole.isProvider;
  const { data: ProviderProfileUuid } = useSelector((state: RootState) => state.providerProfileReducer);

  //getListOfPatient
  const { data, isSuccess } = useQuery({
    enabled: !!xtenantId,
    queryKey: ["list-of-patients", xtenantId],
    queryFn: () =>
      PatientControllerService.getPatientList({
        xTenantId: xtenantId,
        providerId: (isProvider ? ProviderProfileUuid?.uuid : "") as string,
        status: true,
        size: 200,
      }),
  });
  useEffect(() => {
    if (isSuccess) {
      const response = (data as unknown as AxiosResponse).data as ContentObject<PatientDetails[]>;
      const patientData = response?.content;
      setGetAllPatient(patientData);
    }
  }, [data, isSuccess]);

  //getAll nurse

  const { data: nurseData, isSuccess: nurseIsSuccess } = useQuery({
    queryKey: ["list-of-nurse"],
    queryFn: () =>
      ProviderControllerService.getAllProviders({
        role: "NURSE",
        // xTenantId: xtenantId
      }),
  });

  useEffect(() => {
    if (nurseIsSuccess) {
      const response = (nurseData as unknown as AxiosResponse).data as ContentObject<User[]>;

      const userDate = response?.content || [];

      const transformedNurses = userDate
        .filter((user) => !!user.uuid)
        .map((user) => ({
          key: user.uuid || "",
          value: `${user.firstName} ${user.lastName}`,
        }));

      setNurse(transformedNurses);
    }
  }, [nurseIsSuccess, nurseData]);

  const AllTimeZone = [
    { label: "IST", value: "Asia/Calcutta" }, // Indian Standard Time (UTC+05:30)
    { label: "SGT", value: "Asia/Singapore" }, // Alaska Standard Time (UTC-09:00)
    { label: "CST", value: "America/Chicago" }, // Central Standard Time (UTC-06:00)
    { label: "EST", value: "America/New_York" }, // Eastern Standard Time (UTC-05:00)
    { label: "MST", value: "America/Denver" }, // Mountain Standard Time (UTC-07:00)
    { label: "PST", value: "America/Los_Angeles" }, // Pacific Standard Time (UTC-08:00)
    { label: "HST", value: "Pacific/Honolulu" }, // Hawaii Standard Time (UTC-10:00)
    { label: "AKST", value: "America/Anchorage" }, // Alaska Standard Time (UTC-09:00)
  ];

  const timezoneAliasMap: Record<string, string> = {
    "Asia/Kolkata": "Asia/Calcutta",
    "US/Pacific": "America/Los_Angeles",
    "US/Eastern": "America/New_York",
    "US/Central": "America/Chicago",
    "US/Mountain": "America/Denver",
    "US/Alaska": "America/Anchorage",
    "US/Hawaii": "Pacific/Honolulu",
  };

  const normalizeTimeZone = (tz: string) => {
    return timezoneAliasMap[tz] || tz;
  };

  useEffect(() => {
    const rawTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const normalizedTimeZone = normalizeTimeZone(rawTimeZone);

    const matchedTimeZone = AllTimeZone.find((tz) => tz.value === normalizedTimeZone);
    if (matchedTimeZone) {
      setValue("timezone", matchedTimeZone.value);
      setTimeZone(matchedTimeZone.value);
    }
  }, [setValue]);

  const { startUTC, endUTC } = useMemo(() => {
    return getStartAndEndOfDayUTC2(selectedDate, timeZone);
  }, [selectedDate, timeZone]);

  const {
    data: slotData,
    isSuccess: isSuccessSlot,
    isLoading: isLoadingSlot,
  } = useQuery({
    queryKey: [nurseValue, durationTime, selectedDate, timeZone],
    enabled: Boolean(nurseValue && durationTime && selectedDate && timeZone),
    queryFn: () => {
      const duration = Number(durationTime);

      //**
      // const startDateTime = isSameDay(startOfDay(selectedDate), startOfDay(new Date()))
      //   ? addMinutes(new Date(), BUFFER_MINUTE)
      //   : startOfDay(new Date(selectedDate));

      //current time ? local to UTC : start or end of selected timezone
      //start 01-18-2025T00:00:00 and end 01-18-2025T23:59:00
      // console.log("selectedDate 01-18-2025T00:00:00", selectedDate)
      // const startDateInZone = startOfDay(selectedDate);
      // const startDateTime = isSameDay(startOfDay(selectedDate), startOfDay(new Date()))
      //   ? addMinutes(new Date(), BUFFER_MINUTE)
      //   : startDateInZone;
      // const endOfDayDate = startOfDay(addDays(new Date(selectedDate), 1));
      // const toTimeZoned = toZonedTime(startDateTime, timeZone)
      // const toTimeEndZoned = toZonedTime(endOfDayDate, timeZone)

      // const formatStartTimeZone = formatInTimeZone(startDateTime, timeZone, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
      // const formatEndTimeZone = formatInTimeZone(endOfDayDate, timeZone, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")

      const selectedPatientSchemaType = getllAllPatient?.find((ele) => ele.uuid === patientValue)?.schemaType || "";
      return AvailabilityControllerService.getProviderSlots({
        duration: duration,
        startDate: startUTC,
        endDate: endUTC,
        providerUuid: nurseValue,
        page: 0,
        size: 101,
        xTenantId: selectedPatientSchemaType == "INTERNAL" ? xtenantId : "eamata",
      });
    },
  });

  useEffect(() => {
    if (isSuccessSlot && slotData?.data?.content) {
      const response = slotData.data.content as SlotType[];

      setAllSlots(response);
    }
  }, [isSuccessSlot, slotData, startUTC, endUTC]);

  const handleLocalTimeZone = (slot: string) => {
    const zonedDate = toZonedTime(slot, timeZone);
    return format(zonedDate, "hh:mm a");
  };

  const renderSlotSection = (slots: SlotType[]) => (
    <Grid flexDirection={"column"} container>
      <Grid container columnGap={1} rowGap={1}>
        {slots.length === 0 && isSuccessSlot ? (
          <Typography variant="bodySmall" color={theme.palette.grey[500]}>
            No available time slots
          </Typography>
        ) : slots.length > 0 ? (
          slots.map((slot, index) => (
            <ButtonBase
              key={index}
              onClick={() => handleSelectSlot(slot)}
              sx={selectAptBtnStyles(selectedSlot === slot)}
            >
              <Grid
                width="68px"
                height="26px"
                border={`1px solid ${theme.palette.grey[500]}`}
                borderRadius="4px"
                container
                justifyContent={"center"}
                alignContent={"center"}
              >
                <Typography variant="bodySmall">{handleLocalTimeZone(slot.startTime)}</Typography>
              </Grid>
            </ButtonBase>
          ))
        ) : (
          <Typography variant="bodySmall" color={theme.palette.grey[500]}>
            -
          </Typography>
        )}
      </Grid>
    </Grid>
  );

  //post api for Schedule appointment
  const queryClient = useQueryClient();

  const handleSelect = () => {
    queryClient.invalidateQueries({ queryKey: ["get-all-appointment"], exact: false });
  };

  // const navigate = useNavigate();

  const {
    data: scheduleData,
    mutateAsync: mutateAsyncScheduleAppt,
    isSuccess: isSuccessScheduleAppt,
    isError: isErrorScheduleAppt,
    error: errorScheduleAppt,
    // isPending: isPendingScheduleAppt,
  } = useAppointmentControllerServiceBookAppointmentRequest({
    onSuccess: handleSelect,
  });

  useApiFeedback(
    isErrorScheduleAppt,
    errorScheduleAppt,
    isSuccessScheduleAppt,
    (scheduleData?.message || "Schedule Appointment successfully!") as string
  );

  const checkExternal = getllAllPatient?.find((ele) => ele.uuid === patientValue)?.schemaType || "";

  const clearSelectedTimeSlot = () => {
    setSelectedSlot(undefined);
    setValue("startTime", "");
    setValue("endTime", "");
  };

  const onSubmit = async (value: FieldValues) => {
    const AllTimezone: AllTimezones =
      value.timezone === "Asia/Calcutta"
        ? "IST"
        : value.timezone === "Asia/Singapore"
          ? "SGT"
          : value.timezone === "America/Chicago"
            ? "CST"
            : value.timezone === "America/New_York"
              ? "EST"
              : value.timezone === "America/Denver"
                ? "MST"
                : value.timezone === "America/Los_Angeles"
                  ? "PST"
                  : value.timezone === "Pacific/Honolulu"
                    ? "HST"
                    : value.timezone === "America/Anchorage"
                      ? "AKST"
                      : undefined;

    try {
      setIsLoadingScheduleAppointment(true);
      const modeEnum = value.mode === "homeVisit" ? "HOME_VISIT" : "TELE_VISIT";
      await mutateAsyncScheduleAppt({
        requestBody: {
          mode: modeEnum,
          purpose: value.purpose,
          patientId: value.patient || "",
          nurseId: nurseValue || "",
          startTime: selectedSlot?.startTime || "",
          endTime: selectedSlot?.endTime || "",
          duration: value.duration || "",
          timezone: AllTimezone,
          external: checkExternal == "INTERNAL" ? false : true,
        },
        xTenantId: xtenantId,
      });
      setScheduleAppointmentDialog(false);
      refetch();
      // navigate('/provider/tasks')
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message,
        })
      );
    } finally {
      setIsLoadingScheduleAppointment(false);
    }
  };

  const getModeValue = watch("mode");
  const DurationValue =
    getModeValue === "homeVisit"
      ? [
          { label: "30 minutes", value: "30" },
          { label: "60 minutes", value: "60" },
          { label: "90 minutes", value: "90" },
          { label: "120 minutes", value: "120" },
        ]
      : [
          { label: "15 minutes", value: "15" },
          { label: "30 minutes", value: "30" },
          { label: "45 minutes", value: "45" },
          { label: "60 minutes", value: "60" },
        ];

  const [selectedMode, setSelectedMode] = useState("teleVisit");

  const appointmentPurposeHome = [
    { label: "Nurse Visit", value: "Nurse Visit" },
    { label: "Consultation", value: "Consultation" },
  ];

  const appointmentPurposeTel = [
    { label: "Nurse Assistance", value: "Nurse Assistance" },
    { label: "Consultation", value: "Consultation" },
  ];

  return (
    <Grid width={"650px"} minHeight={"50%"}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid marginBottom={3} pt={2} borderTop={`1px solid ${theme.palette.grey[300]}`}>
          <CustomLabel label="Appointment Type" />
          <FormControl component="fieldset">
            <Controller
              control={control}
              name="mode"
              render={({ field }) => (
                <RadioGroup
                  row
                  aria-labelledby="appointment-type-radio-group"
                  {...field}
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(e.target.value), setValue("mode", e.target.value);
                    setSelectedMode(e.target.value);
                  }}
                >
                  <FormControlLabel value="teleVisit" control={<Radio />} label="Tele Visit" />
                  <FormControlLabel value="homeVisit" control={<Radio />} label="Home Visit" />
                </RadioGroup>
              )}
            />
          </FormControl>
        </Grid>

        <Grid
          gap={2}
          container
          height={"100%"}
          flexDirection={"column"}
          justifyContent={"space-between"}
          overflow={"auto"}
          flexWrap={"nowrap"}
        >
          <Grid>
            <CustomLabel label={"Appointment Purpose"} isRequired />
            <Controller
              control={control}
              name="purpose"
              render={({ field }) => (
                <CustomSelect
                  {...field}
                  value={field.value || ""}
                  placeholder={"Enter Appointment Purpose"}
                  hasError={!!errors.purpose}
                  errorMessage={errors.purpose?.message}
                  items={selectedMode === "teleVisit" ? appointmentPurposeTel : appointmentPurposeHome}
                />
              )}
            />
          </Grid>
          {!selectedPatient?.patientName && (
            <Grid>
              <CustomLabel label={"Patient"} isRequired />
              <Controller
                control={control}
                name="patient"
                render={({ field }) => (
                  <CustomAutoComplete
                    {...field}
                    options={patientOptions}
                    onChange={(value: string | "") => {
                      // clearSelectedTimeSlot();
                      field.onChange(value);
                      setPatientValue(value);
                      const selectedPatient: OptionsInterface =
                        patientOptions.find((patient) => patient.key === value) || ({} as OptionsInterface);
                      const isNursePresent = getNurse.some((nurseOpt) => selectedPatient.info === nurseOpt.key);
                      if (!isNursePresent) {
                        setNurse((prev) => {
                          const updatedArr = [
                            ...prev,
                            { key: selectedPatient.info, value: selectedPatient.info2 },
                          ] as OptionsInterface[];

                          return updatedArr;
                        });
                      }

                      if (selectedPatient) {
                        setValue("nurse", selectedPatient?.info || "", { shouldValidate: true });
                        setNurseValue(selectedPatient?.info || "");
                      }
                    }}
                    placeholder="Select Patient"
                    value={patientValue || ""}
                    hasError={!!errors.patient}
                    errorMessage={errors.patient?.message}
                  />
                )}
              />
            </Grid>
          )}
          {selectedPatient?.patientName && (
            <PatientDetailsInScheduleAppointment
              forDropDown={false}
              isChangePatientRequired={true}
              minWidth="450px"
              handleOnChangePatient={handleOnChangePatient}
              patient={selectedPatient}
            />
          )}
          <Grid>
            <CustomLabel label={"Nurse :"} />
            <Typography>{selectedPatient?.nurseName}</Typography>
          </Grid>
          <Grid container width={"100%"} gap={2}>
            <Grid width={"48%"}>
              <CustomLabel label="Date" isRequired />
              <Controller
                control={control}
                name={`date`}
                render={({ field }) => (
                  <DatePicker
                    {...field}
                    bgWhite={false}
                    disablePast
                    value={field.value || ""}
                    onDateChange={(e) => {
                      field.onChange(e);
                      setSelectedDate(e);
                      clearSelectedTimeSlot();
                    }}
                    hasError={!!errors.date}
                    errorMessage={errors.date?.message || ""}
                  />
                )}
              />
            </Grid>{" "}
            <Grid width={"23%"}>
              <CustomLabel label="Time Duration" isRequired />
              <Controller
                control={control}
                name={"duration"}
                render={({ field }) => (
                  <CustomSelect
                    isDisabled={!selectedDate}
                    {...field}
                    borderRadius={"8px"}
                    placeholder={"Select Time Duration"}
                    onChange={(e) => {
                      const newDuration = e.target.value as string;
                      field.onChange(newDuration);
                      setDurationTime(newDuration);
                      setValue("duration", newDuration);
                      clearSelectedTimeSlot();
                    }}
                    value={field.value || ""}
                    items={DurationValue}
                    hasError={!!errors.duration}
                    errorMessage={errors.duration?.message || ""}
                  />
                )}
              />
            </Grid>
            <Grid width={"23%"}>
              <CustomLabel label="Time Zone" isRequired />
              <Controller
                control={control}
                name={"timezone"}
                render={({ field }) => (
                  <CustomSelect
                    // isDisabled={!selectedDate}
                    {...field}
                    borderRadius={"8px"}
                    placeholder={"Select timezone"}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      setTimeZone(e.target.value);
                      clearSelectedTimeSlot();
                    }}
                    value={field.value || ""}
                    items={AllTimeZone}
                    hasError={!!errors.timezone}
                    errorMessage={errors.timezone?.message || ""}
                  />
                )}
              />
            </Grid>
          </Grid>
          <Grid>
            <CustomLabel label="Select Available Time Slot :" isRequired />
            {isLoadingSlot ? (
              <Skeleton
                variant="rectangular"
                animation="wave"
                width={"100%"}
                height={"100px"}
                sx={{ borderRadius: "12px" }}
              />
            ) : (
              <Grid
                sx={{
                  padding: "16px",
                  borderRadius: "12px",
                  border: `solid 1px ${theme.palette.grey[300]}`,
                  width: "100%",
                  maxHeight: "508px",
                  overflow: "auto",
                  flexDirection: "column",
                  // display: "flex",
                  // justifyContent: "center",
                  // alignItems: "center"
                }}
                container
                rowGap={2}
              >
                {renderSlotSection(allSlots)}
              </Grid>
            )}
            {errors.startTime && (
              <Grid>
                <Typography textAlign={"start"} sx={errorStyle} variant="caption">
                  {errors.startTime ? errors.startTime.message : ""}
                </Typography>
              </Grid>
            )}
          </Grid>
          <Grid height={"8%"} borderTop={`1px solid ${theme.palette.grey[300]}`}>
            <Grid container p={1} justifyContent={"flex-end"}>
              <Button variant="contained" type="submit" loading={isLoadingScheduleAppointment}>
                <Typography>Save</Typography>
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </form>
    </Grid>
  );
}

export default ScheduleAppointmentDialog;

export interface PatientDetails {
  uuid: string;
  patientName: string;
  mrn: string;
  schema: string | null;
  phone: string;
  email: string;
  birthDate: string | null;
  gender: "MALE" | "FEMALE" | "OTHER";
  nurseId: string;
  schemaType: "INTERNAL" | "EXTERNAL";
  nurseName: string;
}
