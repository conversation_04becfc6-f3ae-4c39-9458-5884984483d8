import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useSearchParams } from "react-router-dom";

import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import ListAltOutlinedIcon from "@mui/icons-material/ListAltOutlined";
import MenuOpenIcon from "@mui/icons-material/MenuOpen";
import {
  Button,
  Collapse,
  FormControl,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import { Grid, useMediaQuery } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import {
  addDays,
  addMinutes,
  addMonths,
  addWeeks,
  endOfDay,
  endOfWeek,
  format,
  isSameDay,
  startOfDay,
  startOfWeek,
  subDays,
  subMinutes,
  subMonths,
  subWeeks,
} from "date-fns";

import CustomAccordion from "@/common-components/accordion/accordion";
import CustomCheckBox, { CheckedArray } from "@/common-components/custom-check-box/custom-check-box";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import { AppointmentModeEnum } from "@/constants/appointments-const";
import useAuthority from "@/hooks/use-authority";
import { ProviderRole } from "@/models/provider/provider-modal";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setIsLoading } from "@/redux/actions/loader-action";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { RootState } from "@/redux/store";
import { GetTenantId } from "@/services/common/get-tenant-id";

import CustomAutoComplete from "../../../common-components/custom-auto-complete/custom-auto-complete";
import CustomDialog from "../../../common-components/custom-dialog/custom-dialog";
import CustomLabel from "../../../common-components/custom-label/custom-label";
import CustomSelect from "../../../common-components/custom-select/customSelect";
import CustomSelectorSq from "../../../common-components/custom-selector-sq/custom-selector-sq";
import DateCalender from "../../../common-components/date-calender/date-calender";
import DatePicker from "../../../common-components/date-picker-field/date-picker-field";
import { ContentObject } from "../../../models/response/response-content-entity";
import {
  AppointmentControllerService,
  Patient,
  PatientControllerService,
  Provider,
  ProviderControllerService,
} from "../../../sdk/requests";
import { theme } from "../../../utils/theme";
import SchedulingCalendarView from "./calender-view-appointment";
import ScheduleAppointmentDialog, { PatientDetails } from "./schedule-appointment-dialog";
import SchedulingListTable, { Appointment } from "./scheduling-list-table";

function ScheduleAppointment() {
  const [searchParams, setSearchParams] = useSearchParams();

  const [scheduleIconsParam, setScheduleIconParam] = useSearchParams();

  const createNew = searchParams.get("createNew");

  useEffect(() => {
    if (createNew) {
      setScheduleAppointmentDialog(true);
    }
  }, []);

  const below1230 = useMediaQuery("(max-width:1230px)");
  const below1370 = useMediaQuery("(max-width:1370px)");
  const xTenantId = GetTenantId();
  const [selectedDatesFromList, setSelectedDatesFromList] = useState<{ startDate: string; endDate: string }>();

  const schedulingListFilterOptions = ["UPCOMING", "PAST", "REQUESTED"];
  const calenderFilterOptions = ["All", "Request"];
  const [listFilterOption, setListFilterOption] = useState<"ALL" | "UPCOMING" | "PAST" | "REQUESTED">(
    "UPCOMING"
    // (searchParams.get("tab") as "ALL" | "UPCOMING" | "PAST" | "REQUESTED") || schedulingListFilterOptions[0]
  );
  (searchParams.get("tab") as "ALL" | "UPCOMING" | "PAST" | "REQUESTED") || schedulingListFilterOptions[0];
  const [calendarFilterOption, setCalendarFilterOptions] = useState(calenderFilterOptions[0]);
  const [selectedIcon, setSelectedIcon] = useState<string | null>(scheduleIconsParam.get("icon") || "list");

  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [isFilterOpen, setIsFilterOpen] = useState(true);
  const [timeFrame, setTimeFrame] = useState("month");

  const [selectedNurse, setSelectedNurse] = useState("");

  const [searchPatientString, setSearchPatientString] = useState("");
  const [patientsOptions, setPatientsOptions] = useState<{ key: string; value: string }[]>([]);
  const [searchNurseString, setSearchNurseString] = useState("");
  const [nurseOptions, setNurseOptions] = useState<{ key: string; value: string }[]>([{ key: "", value: "" }]);

  /* Filter variables */
  const [selectedPatient, setSelectedPatient] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<AppointmentModeEnum>("" as AppointmentModeEnum);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [nurseType, setNurseType] = useState("INTERNAL");

  const [scheduleAppointmentDialog, setScheduleAppointmentDialog] = useState(false);
  const [selectedStatusName, setSelectedStatusName] = useState<string[]>([]);

  const [isLoadingData, setIsLoadingData] = useState(false);
  const [getAllAppointmentData, setGetAllAppointmentData] = useState<ContentObject<Appointment[]>>(
    {} as ContentObject<Appointment[]>
  );

  const dispatch = useDispatch();
  const location = useLocation();

  const [sortBy, setSortBy] = useState("");
  const [sortDirectionByDate, setSortDirections] = useState("desc");
  const [sortByTime, setSortbyTime] = useState("desc");
  const [sortDirectionByStatus, setSortDirectionByStatus] = useState("desc");
  const [sortDirection, setSortDirection] = useState("desc");

  //check role who have logedIn
  const getRole = useAuthority();
  const isProvider = getRole.isProvider;
  const [weekRange, setWeekRange] = useState("");

  useEffect(() => {
    if (listFilterOption) {
      setSearchParams((prev) => {
        prev.set("tab", listFilterOption.toString());
        return prev;
      });
    }
  }, [listFilterOption]);

  useEffect(() => {
    if (selectedIcon) {
      setScheduleIconParam((prev) => {
        prev.set("icon", selectedIcon.toString());
        return prev;
      });
    }
  }, [selectedIcon, listFilterOption]);

  // const { data: ProfileUuidData } = useSelector((state: RootState) => state.profileReducer);
  const { data: ProviderProfileUuid } = useSelector((state: RootState) => state.providerProfileReducer);

  const getStatusArray = (filterOption: string): CheckedArray[] => {
    return filterOption === "All"
      ? [
          { key: "SCHEDULED", checked: false, value: "Scheduled" },
          { key: "RESCHEDULED", checked: false, value: "Re-scheduled" },
          { key: "IN_PROGRESS", checked: false, value: "In-Progress" },
          { key: "CANCELLED", checked: false, value: "Cancelled" },
          { key: "COMPLETED", checked: false, value: "Completed" },
          { key: "NO_SHOW", checked: false, value: "No Show" },
          { key: "CHECKED_IN", checked: false, value: "Checked In" },

          { key: "REQUESTED", checked: false, value: "Pending" },
          { key: "REJECTED", checked: false, value: "Rejected" },
        ]
      : [
          { key: "CANCELLED", checked: false, value: "Cancelled" },
          { key: "REJECTED", checked: false, value: "Rejected" },
        ];
  };

  const [statusArray, setStatusArray] = useState<CheckedArray[]>(getStatusArray(calendarFilterOption));

  useEffect(() => {
    setStatusArray(getStatusArray(calendarFilterOption));
  }, [calendarFilterOption]);

  const [selectedAppointmentTypefilter, setSelectedAppointmentTypefilter] = useState<string | null>(null);

  useEffect(() => {
    setSelectedNurse(location?.state?.nurseId);
    if (location?.state?.nurseId) {
      setViewFilters(true);
      getAllAppointment({
        endDate: endOfDay(new Date()).toISOString(),
        listFilterOption,
        nurseType,
        selectedNurse: location?.state?.nurseId || "",
        selectedPatient,
        selectedStatus,
        startDate: startOfDay(new Date()).toISOString(),
        page: 0,
        size: 10,
        sortDirectionByDate,
        sortByTime,
        sortDirectionByStatus,
      });
    }
  }, [location?.state]);

  const handleCheckedStatus = (updatedArray: CheckedArray[]) => {
    setStatusArray(updatedArray);

    const selectedStatuses = updatedArray
      .filter((status) => status.checked)
      .map((status) => status.key)
      .filter(isAppointmentStatus);
    setSelectedStatusName(selectedStatuses);
  };

  const isAppointmentStatus = (status: string) => {
    return statusArray.map((s) => s.key).includes(status);
  };

  const toggleFilter = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  const handleIconClick = (icon: string) => {
    setSelectedIcon(icon);
  };

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);
  };

  const [viewFilters, setViewFilters] = useState(false);

  const handleOnClickFilters = () => {
    setViewFilters((prev) => !prev);
  };

  const handleClearFilters = () => {
    setSelectedPatient("");
    setNurseType("");
    setSelectedStatus("" as AppointmentModeEnum);
    setStartDate("");
    setEndDate("");
    setSelectedNurse("");
    getAllAppointment({
      selectedPatient: "",
      selectedStatus: "" as AppointmentModeEnum,
      startDate: selectedDatesFromList?.startDate ? selectedDatesFromList?.startDate : "",
      endDate: selectedDatesFromList?.endDate ? selectedDatesFromList?.endDate : "",
      nurseType: "",
      selectedNurse: "",
      page: 0,
      size: 10,
      listFilterOption,
      sortDirectionByDate,
      sortByTime,
      sortDirectionByStatus,
    });
  };

  //getPatient
  const [getAllPatient, setGetAllPatient] = useState<{ key: string; value: string }[]>([]);
  const xtenantId = GetTenantId();
  const { data, isSuccess } = useQuery({
    enabled: !!xtenantId,
    queryKey: ["list-of-patients", xtenantId],
    queryFn: () =>
      PatientControllerService.getPatientList({
        xTenantId: xtenantId,
      }),
  });

  useEffect(() => {
    if (isSuccess) {
      const response = (data as unknown as AxiosResponse).data as ContentObject<PatientDetails[]>;
      const patientData = response?.content;
      const transformedPatient = patientData
        .filter((user) => !!user.uuid)
        .map((user) => ({
          key: user.uuid || "",
          value: `${user.patientName}` || "",
        }));
      setGetAllPatient(transformedPatient);
    }
  }, [data, isSuccess]);

  const getListOfProviderOrNurse = async (role: ProviderRole) => {
    const schemaTypeVal = nurseType;
    const xTenantIdVal =
      role === ProviderRole.PROVIDER
        ? xTenantId
        : role === ProviderRole.NURSE && schemaTypeVal === "EXTERNAL"
          ? "eamata"
          : xTenantId;

    try {
      const res = await ProviderControllerService.getAllProviders({
        page: 0,
        size: 100,
        sortBy: "modified",
        sortDirection: "desc",
        role,
        status: true,
        archive: false,
        xTenantId: xTenantIdVal,
        searchString: role === ProviderRole.NURSE ? searchNurseString : "",
      });

      const data = (res as unknown as AxiosResponse).data as ContentObject<Provider[]>;
      const options = data?.content.map((item) => {
        return {
          key: item.uuid || "",
          value: `${item.firstName} ${item.lastName}`,
        };
      });

      if (role === ProviderRole.NURSE) {
        setNurseOptions(options);
      }
    } finally {
      //
    }
  };

  //Get list of patients
  const { data: dataPatientList, isSuccess: isSuccessPatientList } = useQuery({
    enabled: !!xtenantId,
    queryKey: [xtenantId, searchPatientString],
    queryFn: () =>
      PatientControllerService.getAllPatient({
        page: 0,
        size: 200,
        archive: false,
        status: true,
        xTenantId: xtenantId,
        searchString: searchPatientString,
      }),
  });

  useEffect(() => {
    if (isSuccessPatientList) {
      const response = (dataPatientList as unknown as AxiosResponse).data as ContentObject<Patient[]>;

      const patientData = response?.content;

      const options = patientData.map((patient) => {
        return {
          key: patient.uuid || "",
          value: `${patient.firstName} ${patient.lastName}`,
        };
      });
      setPatientsOptions(options);
    }
  }, [dataPatientList, isSuccessPatientList]);

  useEffect(() => {
    getListOfProviderOrNurse(ProviderRole.NURSE);
  }, [nurseType]);

  const handleClickOnSearch = () => {
    getAllAppointment({
      // endDate: endDate
      //   ? listFilterOption === "PAST" && isSameDay(endDate, new Date())
      //     ? new Date().toISOString()
      //     : endOfDay(new Date(endDate)).toISOString()
      //   : selectedDatesFromList?.endDate || "",
      endDate: endDate
        ? listFilterOption === "PAST" && isSameDay(endDate, new Date())
          ? new Date(new Date().getTime() - 60000).toISOString()
          : endOfDay(new Date(endDate)).toISOString()
        : selectedDatesFromList?.endDate || "",
      nurseType,
      page: 0,
      selectedNurse,
      selectedPatient,
      selectedStatus,
      size: 10,
      startDate:
        isSameDay(startDate, endDate) && startDate
          ? startOfDay(new Date(startDate)).toISOString()
          : isSameDay(startOfDay(startDate), startOfDay(new Date()))
            ? addMinutes(new Date(), 1).toISOString()
            : startDate
              ? new Date(startDate).toISOString()
              : selectedDatesFromList?.startDate
                ? selectedDatesFromList?.startDate
                : "",
      listFilterOption,
      sortDirectionByDate,
      sortByTime,
      sortDirectionByStatus,
    });
  };

  const handleSorting = (column: string) => {
    if (column == "Date") {
      setSortBy("startDate");
      setSortDirections((prev) => (prev === "desc" ? "asc" : "desc"));
    } else if (column === "Time") {
      setSortBy("startDate");
      setSortbyTime((prev) => (prev === "desc" ? "asc" : "desc"));
    } else if (column === "Status") {
      setSortBy("status");
      setSortDirectionByStatus((prev) => (prev === "desc" ? "asc" : "desc"));
    }
  };
  useEffect(() => {
    if (sortBy == "startDate") {
      setSortDirection(sortDirectionByDate);
    } else if (sortBy === "startDate") {
      setSortDirection(sortByTime);
    } else if (sortBy === "status") {
      setSortDirection(sortDirectionByStatus);
    }
  }, [sortBy, sortDirection, handleSorting]);

  const getAllAppointment = async (
    filters?:
      | {
          selectedPatient: string;
          selectedStatus: AppointmentModeEnum;
          startDate: string;
          endDate: string;
          nurseType: string;
          selectedNurse: string;
          page: number;
          size: number;
          listFilterOption: "ALL" | "UPCOMING" | "PAST" | "REQUESTED" | undefined;
          sortByTime: string;
          sortDirectionByStatus: string;
          sortDirectionByDate: string;
          patientId?: string;
        }
      | undefined
  ) => {
    setIsLoadingData(true);
    try {
      let startDateFormatted = filters?.startDate || "";
      let endDateFormatted = filters?.endDate || "";

      if (startDateFormatted && !startDateFormatted.includes("T")) {
        if (startDateFormatted.match(/^\d{2}-\d{2}-\d{4}$/)) {
          const [month, day, year] = startDateFormatted.split("-");
          startDateFormatted = new Date(`${year}-${month}-${day}T00:00:00.000Z`).toISOString();
        } else {
          startDateFormatted = new Date(startDateFormatted).toISOString();
        }
      }

      if (endDateFormatted && !endDateFormatted.includes("T")) {
        if (endDateFormatted.match(/^\d{2}-\d{2}-\d{4}$/)) {
          const [month, day, year] = endDateFormatted.split("-");
          endDateFormatted = new Date(`${year}-${month}-${day}T23:59:59.999Z`).toISOString();
        } else {
          endDateFormatted = new Date(endOfDay(new Date(endDateFormatted))).toISOString();
        }
      }

      let response = await AppointmentControllerService.getAllAppointments({
        startDate: filters
          ? startDateFormatted ||
            (selectedDate
              ? isSameDay(selectedDate, new Date())
                ? endDateFormatted && isSameDay(new Date(startDateFormatted), new Date(endDateFormatted))
                  ? new Date(new Date(startDateFormatted + "T18:30:00.000Z").getTime() - 86400000).toISOString()
                  : new Date(startDateFormatted + "T" + new Date().toISOString().split("T")[1]).toISOString()
                : startOfDay(selectedDate).toISOString()
              : undefined)
          : selectedDate
            ? isSameDay(selectedDate, new Date())
              ? new Date().toISOString()
              : startOfDay(selectedDate).toISOString()
            : undefined,
        endDate: filters
          ? endDateFormatted || (selectedDate ? endOfDay(selectedDate).toISOString() : undefined)
          : selectedDate
            ? endOfDay(selectedDate).toISOString()
            : undefined,
        filter: filters ? filters?.listFilterOption : listFilterOption,
        nurseId: filters ? filters?.selectedNurse : selectedNurse,
        patientId: filters ? filters?.selectedPatient : selectedPatient,
        mode: filters ? filters?.selectedStatus : selectedStatus,
        page: filters ? filters?.page : 0,
        size: filters ? filters?.size : 10,
        sortBy: sortBy,
        sortDirection: sortDirection,
        xTenantId: xTenantId,
        providerId: (isProvider ? ProviderProfileUuid?.uuid : "") as string,
      });

      let data = (response as unknown as AxiosResponse).data as ContentObject<Appointment[]>;

      setGetAllAppointmentData(data);

      // Store the date range used for this query if none was explicitly provided
      if (!filters?.startDate && !filters?.endDate && selectedDate) {
        const start = filters?.startDate || "";
        const end = filters?.endDate || "";
        setSelectedDatesFromList({
          startDate:
            start ||
            (isSameDay(selectedDate, new Date()) ? new Date().toISOString() : startOfDay(selectedDate).toISOString()),
          endDate: end || endOfDay(selectedDate).toISOString(),
        });
      }
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message || "Something went wrong",
        })
      );
    } finally {
      setIsLoadingData(false);
    }
  };

  useEffect(() => {
    let effectiveStartDate = startDate
      ? startOfDay(new Date(startDate)).toISOString()
      : selectedDatesFromList?.startDate || "";
    let effectiveEndDate = endDate ? endOfDay(new Date(endDate)).toISOString() : selectedDatesFromList?.endDate || "";

    if (listFilterOption === "PAST") {
      const now = new Date();
      const currentEndDate = effectiveEndDate ? new Date(effectiveEndDate) : now;
      if (currentEndDate >= startOfDay(now)) {
        effectiveEndDate = subMinutes(now, 1).toISOString();
      }
      if (effectiveStartDate && new Date(effectiveStartDate) >= startOfDay(now)) {
        if (new Date(effectiveStartDate) >= new Date(effectiveEndDate)) {
          effectiveStartDate = startOfDay(new Date(effectiveEndDate)).toISOString();
        }
      }
    } else if (listFilterOption === "UPCOMING") {
      const now = new Date();
      if (effectiveStartDate && isSameDay(new Date(effectiveStartDate), now)) {
        effectiveStartDate = addMinutes(now, 1).toISOString();
      }
    }

    if (sortBy) {
      getAllAppointment({
        startDate: effectiveStartDate,
        endDate: effectiveEndDate,
        nurseType,
        selectedNurse,
        selectedPatient,
        selectedStatus,
        page: 0,
        size: getAllAppointmentData?.size || 10,
        listFilterOption,
        sortDirectionByDate,
        sortByTime,
        sortDirectionByStatus,
      });
    }
  }, [
    sortBy,
    sortDirection,
    listFilterOption,
    sortDirectionByDate,
    sortByTime,
    sortDirectionByStatus,
    selectedDatesFromList,
  ]);

  useEffect(() => {
    dispatch(setIsLoading(isLoadingData));
  }, [dispatch, isLoadingData]);

  useEffect(() => {
    setStartDate("");
    setEndDate("");
    setNurseType("");
    setSelectedNurse("");
    setSearchNurseString("");
    setSelectedPatient("");
    setSearchPatientString("");
    setSelectedStatus("" as AppointmentModeEnum);
  }, [listFilterOption]);

  const handleResetTodayDate = () => {
    setSelectedDate(new Date());
  };

  const handlePreviousDate = () => {
    if (!selectedDate) {
      return;
    }
    if (timeFrame == "month") {
      setSelectedDate(subMonths(selectedDate, 1));
    } else if (timeFrame == "week") {
      setSelectedDate(subWeeks(selectedDate, 1));
    } else {
      setSelectedDate(subDays(selectedDate, 1));
    }
  };

  const handleNextDate = () => {
    if (!selectedDate) {
      return;
    }
    if (timeFrame == "month") {
      setSelectedDate(addMonths(selectedDate, 1));
    } else if (timeFrame == "week") {
      setSelectedDate(addWeeks(selectedDate, 1));
    } else {
      setSelectedDate(addDays(selectedDate, 1));
    }
  };

  const startOfWeekDay = startOfWeek(selectedDate || "", { weekStartsOn: 1 });
  const endOfWeekDay = endOfWeek(selectedDate || "", { weekStartsOn: 1 });

  const updateWeekRange = () => {
    if (timeFrame === "week") {
      const startMonthYear = format(startOfWeekDay, "MMM yyyy");
      const endMonthYear = format(endOfWeekDay, "MMM yyyy");

      if (startOfWeekDay.getMonth() !== endOfWeekDay.getMonth()) {
        setWeekRange(`${startMonthYear} - ${endMonthYear}`);
      } else {
        setWeekRange(startMonthYear);
      }
    }
  };

  useEffect(() => {
    updateWeekRange();
  }, [selectedDate, timeFrame]);

  return (
    <Grid
      height={"100%"}
      p={0.5}
      width={"100%"}
      maxWidth={"100%"}
      overflow={"auto"}
      display={"flex"}
      flexDirection={"row"}
      gap={2}
      bgcolor={"#F3F0F1"}
    >
      {selectedIcon === "calendar" && isFilterOpen && (
        <Grid
          position={"relative"}
          overflow={"auto"}
          container
          paddingTop={"20px"}
          flexDirection={"column"}
          width={below1230 ? "290px" : below1370 ? "310px" : "380px"}
          border={`1px solid ${theme.palette.grey[100]}`}
          height={"100%"}
          borderRadius={2}
          bgcolor={"white"}
        >
          <Grid paddingLeft={1.5} display={"flex"} gap={1} onClick={toggleFilter} sx={{ cursor: "pointer" }}>
            <MenuOpenIcon color="primary" />
            <Typography fontWeight={500} color={theme.palette.primary.main}>
              {"Filter"}
            </Typography>
          </Grid>

          <Grid mt={2}>
            <DateCalender value={selectedDate} onChange={handleDateChange} />
          </Grid>
          <Grid padding={"10px 13px"} marginTop={45}>
            <Grid mt={2}>
              <CustomLabel label={"Search By Patient"} />
              <CustomAutoComplete
                options={getAllPatient}
                onChange={(options) => setSelectedPatient(options)}
                value={selectedPatient}
                placeholder={"Search & Select Patient"}
                hasStartSearchIcon
              />
            </Grid>
            {!getRole.isProvider && (
              <Grid mt={3} columnGap={7}>
                <CustomLabel label={"Search By Nurse"} />
                <CustomAutoComplete
                  options={nurseOptions}
                  value={selectedNurse}
                  onChange={(options) => setSelectedNurse(options)}
                  placeholder={"Search & Select Nurse"}
                  hasStartSearchIcon
                  // onDebounceCall={(value) => setSelectedNurse(value)}
                  // onInputEmpty={() => setSelectedNurse("")}
                />
              </Grid>
            )}

            <Grid mt={2}>
              <CustomAccordion title="Appointment Type ">
                <Grid>
                  <FormControl>
                    <RadioGroup
                      aria-labelledby="demo-radio-buttons-group-label"
                      name="radio-buttons-group"
                      onChange={(e) => setSelectedAppointmentTypefilter(e.target.value)}
                      value={selectedAppointmentTypefilter || null}
                    >
                      <FormControlLabel value="HOME_VISIT" control={<Radio />} label="Home visit" />
                      <FormControlLabel value="TELE_VISIT" control={<Radio />} label="Tele visit" />
                    </RadioGroup>
                  </FormControl>
                  <Typography
                    display={"flex"}
                    sx={{ cursor: "pointer" }}
                    justifyContent={"flex-end"}
                    onClick={() => setSelectedAppointmentTypefilter(null)}
                  >
                    Clear
                  </Typography>
                </Grid>
              </CustomAccordion>
            </Grid>
            <Grid mt={2}>
              <CustomAccordion title="Status">
                <Grid>
                  <CustomCheckBox
                    options={statusArray}
                    onChange={handleCheckedStatus}
                    initialVisibleItems={4}
                    maxHeight="310px"
                  />
                </Grid>
              </CustomAccordion>
            </Grid>
          </Grid>
        </Grid>
      )}

      <Grid
        // border={`1px solid ${theme.palette.grey[300]}`}
        // boxShadow={`0px 0px 16px 0px #021D2614`}
        height={"100%"}
        borderRadius={"8px"}
        container
        flexDirection={"column"}
        p={0.5}
        width={"100%"}
        bgcolor={"white"}
      >
        <Grid height={"100%"} width={"100%"}>
          <Grid container justifyContent={"space-between"} rowGap={2}>
            {selectedIcon === "list" ? (
              <Grid container alignItems={"center"} columnGap={2} rowGap={2}>
                <CustomSelectorSq
                  onSelect={(filterField) => {
                    setListFilterOption(filterField as "ALL" | "UPCOMING" | "PAST" | "REQUESTED");
                    setSearchParams({ tab: filterField });
                  }}
                  options={schedulingListFilterOptions}
                  selectedValue={listFilterOption || ""}
                  widthOfBtn="110px"
                />
                {!viewFilters && (
                  <IconButton onClick={() => setViewFilters(true)}>
                    <Grid container border={"1px solid #B6C1C4"} p={1} borderRadius={2}>
                      <FilterAltOutlinedIcon />
                    </Grid>
                  </IconButton>
                )}
                {viewFilters && (
                  <Grid alignContent={"flex-end"} columnGap={2} rowGap={2} container justifyContent={"flex-end"}>
                    <Button onClick={handleOnClickFilters} variant="outlined">
                      <Typography fontWeight={550} variant="bodySmall" sx={{ padding: "6px 0px" }}>
                        {viewFilters ? "Hide Filters" : "Filters"}
                      </Typography>
                    </Button>
                    <Grid container width={"fit-content"} justifyContent={"flex-end"}>
                      <Button variant="contained" onClick={handleClickOnSearch}>
                        <Typography sx={{ padding: "8px 0px" }} fontWeight={550} variant="bodySmall">
                          Search
                        </Typography>
                      </Button>
                    </Grid>
                    <Grid container width={"fit-content"} justifyContent={"flex-end"}>
                      <Button variant="outlined" onClick={handleClearFilters}>
                        <Typography sx={{ padding: "8px 0px" }} fontWeight={550} variant="bodySmall">
                          Clear Filters
                        </Typography>
                      </Button>
                    </Grid>
                  </Grid>
                )}
              </Grid>
            ) : (
              <>
                <Grid display={"flex"} gap={2} justifyContent={"center"} alignItems={"center"}>
                  {!isFilterOpen && (
                    <Grid display={"flex"} gap={5} alignItems={"center"}>
                      <Grid display={"flex"} gap={1} onClick={toggleFilter} sx={{ cursor: "pointer" }}>
                        <MenuOpenIcon color="primary" />
                        <Typography fontWeight={500} color={theme.palette.primary.main}>
                          {"Filter"}
                        </Typography>
                      </Grid>
                    </Grid>
                  )}
                  <CustomSelectorSq
                    onSelect={(filterField) => setCalendarFilterOptions(filterField)}
                    options={calenderFilterOptions}
                    selectedValue={calendarFilterOption || ""}
                    widthOfBtn="90px"
                  />
                  <Grid>
                    <Button
                      onClick={handleResetTodayDate}
                      sx={{
                        // height: "40px",
                        // width: "20px",
                        border: "1px solid #006D8F",
                        borderRadius: "4px",
                      }}
                    >
                      Today
                    </Button>
                  </Grid>
                  <Grid
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    {/* Left Arrow Button */}
                    <IconButton aria-label="Previous" onClick={handlePreviousDate}>
                      <ChevronLeftIcon fontSize="small" />
                    </IconButton>
                    {selectedDate && (
                      <Typography variant="bodyMedium" fontWeight={550} fontSize={"14px"}>
                        {timeFrame === "month"
                          ? format(selectedDate, "MMM yyyy")
                          : timeFrame === "week"
                            ? weekRange
                            : format(selectedDate, "dd MMM yyyy")}
                      </Typography>
                    )}

                    {/* Right Arrow Button */}
                    <IconButton aria-label="Next" onClick={handleNextDate}>
                      <ChevronRightIcon fontSize="small" />
                    </IconButton>
                  </Grid>
                </Grid>
              </>
            )}

            <Grid container alignItems={"center"} columnGap={2} justifyContent={"flex-end"}>
              {selectedIcon === "calendar" && (
                <Grid width={"100px"}>
                  <CustomSelect
                    borderRadius={"8px"}
                    items={[
                      { value: "day", label: "Day" },
                      { value: "week", label: "Week" },
                      { value: "month", label: "Month" },
                    ]}
                    name=""
                    onChange={(e) => setTimeFrame(e.target.value)}
                    placeholder=""
                    value={timeFrame}
                  />
                </Grid>
              )}
              <Grid container alignItems="center" border="1px solid #E8EBEC" borderRadius={"8px"} p={0.6} gap={0.8}>
                {[
                  { icon: ListAltOutlinedIcon, value: "list" },
                  { icon: CalendarMonthOutlinedIcon, value: "calendar" },
                ].map(({ icon: Icon, value }) => (
                  <Typography
                    key={value}
                    p={0.8}
                    borderRadius="7px"
                    display="flex"
                    bgcolor={selectedIcon === value ? "#006D8F" : "transparent"}
                    onClick={() => handleIconClick(value)}
                  >
                    <Icon
                      fontSize="small"
                      sx={{
                        color: selectedIcon === value ? "white" : "#006D8F",
                      }}
                    />
                  </Typography>
                ))}
              </Grid>

              <Grid onClick={() => setScheduleAppointmentDialog(true)}>
                <Button variant="contained" startIcon={<AddOutlinedIcon sx={{ fontWeight: 650 }} />}>
                  <Typography fontWeight={550}>Schedule Appointment</Typography>
                </Button>
              </Grid>
            </Grid>
          </Grid>
          <Collapse in={viewFilters && selectedIcon !== "calendar"} timeout={500} sx={{ marginBottom: "10px" }}>
            <Grid container mt={2} columnGap={2} rowGap={2} padding={2}>
              <Grid sx={filterGrid}>
                <CustomLabel isRequired label="Start Date" />

                <DatePicker
                  bgWhite
                  disableFuture={listFilterOption === "PAST"}
                  disablePast={listFilterOption === "UPCOMING"}
                  value={startDate}
                  onDateChange={function (selectedDate: string): void {
                    setStartDate(selectedDate);
                  }}
                />
              </Grid>
              <Grid sx={filterGrid}>
                <CustomLabel isRequired label="End Date" />
                <DatePicker
                  bgWhite
                  value={endDate}
                  disablePast={listFilterOption === "UPCOMING"}
                  disableFuture={listFilterOption === "PAST"}
                  onDateChange={function (selectedDate: string): void {
                    setEndDate(selectedDate);
                  }}
                />
              </Grid>

              <Grid sx={filterGrid}>
                <CustomLabel label={"Select Nurse Type"} />
                <CustomSelect
                  placeholder={"Select Nurse Type"}
                  items={[
                    {
                      value: "INTERNAL",
                      label: `${"Provider Group Nurses"}`,
                    },
                    { value: "EXTERNAL", label: "Eamata Nurses" },
                  ]}
                  onChange={(e) => setNurseType(e.target.value)}
                  name={"nurseType"}
                  value={nurseType}
                />
              </Grid>
              <Grid sx={filterGrid}>
                <CustomLabel label={"Search By Nurse"} />
                <CustomAutoComplete
                  hasStartSearchIcon
                  placeholder="Search Nurse"
                  hideArrow
                  value={selectedNurse}
                  options={nurseOptions}
                  onChange={function (selectedValue: string): void {
                    setSelectedNurse(selectedValue);
                  }}
                  onDebounceCall={(value) => setSearchNurseString(value)}
                  onInputEmpty={() => setSearchNurseString("")}
                />
              </Grid>
              <Grid sx={filterGrid}>
                <CustomLabel label={"Search By Patient"} />
                <CustomAutoComplete
                  hasStartSearchIcon
                  placeholder="Search Patient"
                  hideArrow
                  value={selectedPatient}
                  options={patientsOptions}
                  onChange={function (selectedValue: string): void {
                    setSelectedPatient(selectedValue);
                  }}
                  onDebounceCall={(value) => setSearchPatientString(value)}
                  onInputEmpty={() => setSearchPatientString("")}
                />
              </Grid>
              <Grid sx={filterGrid}>
                {" "}
                <CustomLabel label={"Appointment Type"} />
                <CustomSelect
                  placeholder={"Select Type"}
                  name={""}
                  enableDeselect
                  value={selectedStatus}
                  items={[
                    { label: "Tele Visit", value: AppointmentModeEnum.TELE_VISIT },
                    { label: "Home Visit", value: AppointmentModeEnum.HOME_VISIT },
                  ]}
                  bgWhite
                  onChange={function (e: SelectChangeEvent<string>): void {
                    setSelectedStatus(e.target.value as AppointmentModeEnum);
                  }}
                />
              </Grid>
            </Grid>
          </Collapse>
          {selectedIcon === "list" && (
            <Grid marginTop={3}>
              <SchedulingListTable
                handleSorting={handleSorting}
                sortDirectionByDate={sortDirectionByDate}
                sortByTime={sortByTime}
                sortDirectionByStatus={sortDirectionByStatus}
                appointmentListData={getAllAppointmentData}
                listFilterOption={listFilterOption}
                startDate={startDate}
                endDate={endDate}
                refetch={(startDate, endDate, filterOption?: "ALL" | "UPCOMING" | "PAST" | "REQUESTED") => {
                  let formattedStartDate = startDate;
                  let formattedEndDate = endDate;

                  if (startDate && !startDate.includes("T")) {
                    try {
                      if (startDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
                        const [month, day, year] = startDate.split("-");
                        formattedStartDate = new Date(`${year}-${month}-${day}T00:00:00.000Z`).toISOString();
                      } else {
                        formattedStartDate = new Date(startDate).toISOString();
                      }
                    } catch (error) {
                      console.error("Error formatting start date:", error);
                    }
                  }

                  if (endDate && !endDate.includes("T")) {
                    try {
                      if (endDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
                        const [month, day, year] = endDate.split("-");
                        formattedEndDate = new Date(`${year}-${month}-${day}T23:59:59.999Z`).toISOString();
                      } else {
                        formattedEndDate = endOfDay(new Date(endDate)).toISOString();
                      }
                    } catch (error) {
                      console.error("Error formatting end date:", error);
                    }
                  }

                  getAllAppointment({
                    endDate: formattedEndDate,
                    startDate: formattedStartDate,
                    nurseType,
                    selectedNurse,
                    selectedPatient,
                    selectedStatus,
                    page: 0,
                    size: 10,
                    listFilterOption: filterOption ? filterOption : listFilterOption,
                    sortByTime,
                    sortDirectionByDate,
                    sortDirectionByStatus,
                  });
                }}
                getPage={(page: number, size: number, startDateVal: string, endDateVal: string) => {
                  let start = startDateVal;
                  let end = endDateVal;

                  if (startDateVal && !startDateVal.includes("T")) {
                    try {
                      if (startDateVal.match(/^\d{2}-\d{2}-\d{4}$/)) {
                        const [month, day, year] = startDateVal.split("-");
                        start = new Date(`${year}-${month}-${day}T00:00:00.000Z`).toISOString();
                      } else {
                        start = new Date(startDateVal).toISOString();
                      }
                    } catch (error) {
                      console.error("Error formatting start date:", error);
                    }
                  }

                  if (endDateVal && !endDateVal.includes("T")) {
                    try {
                      if (endDateVal.match(/^\d{2}-\d{2}-\d{4}$/)) {
                        const [month, day, year] = endDateVal.split("-");
                        end = new Date(`${year}-${month}-${day}T23:59:59.999Z`).toISOString();
                      } else {
                        end = new Date(endDateVal).toISOString();
                      }
                    } catch (error) {
                      console.error("Error formatting end date:", error);
                    }
                  }

                  const endFormatted =
                    endDate && listFilterOption === "PAST"
                      ? subMinutes(new Date(), 1).toISOString()
                      : endDate
                        ? endOfDay(new Date(endDate)).toISOString()
                        : end;

                  const startFormatted =
                    startDate && isSameDay(startOfDay(startDate), startOfDay(new Date()))
                      ? addMinutes(new Date(), 1).toISOString()
                      : startDate
                        ? new Date(startDate).toISOString()
                        : start;

                  getAllAppointment({
                    endDate: endFormatted,
                    startDate: startFormatted,
                    nurseType,
                    selectedNurse,
                    selectedPatient,
                    selectedStatus,
                    page,
                    size,
                    listFilterOption,
                    sortDirectionByDate,
                    sortByTime,
                    sortDirectionByStatus,
                  });
                }}
                getSize={function (size: number, startDateVal: string, endDateVal: string): void {
                  // Ensure date values are properly formatted as ISO strings
                  let start = startDateVal;
                  let end = endDateVal;

                  // Format the dates if needed
                  if (startDateVal && !startDateVal.includes("T")) {
                    try {
                      if (startDateVal.match(/^\d{2}-\d{2}-\d{4}$/)) {
                        const [month, day, year] = startDateVal.split("-");
                        start = new Date(`${year}-${month}-${day}T00:00:00.000Z`).toISOString();
                      } else {
                        start = new Date(startDateVal).toISOString();
                      }
                    } catch (error) {
                      console.error("Error formatting start date:", error);
                    }
                  }

                  if (endDateVal && !endDateVal.includes("T")) {
                    try {
                      if (endDateVal.match(/^\d{2}-\d{2}-\d{4}$/)) {
                        const [month, day, year] = endDateVal.split("-");
                        end = new Date(`${year}-${month}-${day}T23:59:59.999Z`).toISOString();
                      } else {
                        end = new Date(endDateVal).toISOString();
                      }
                    } catch (error) {
                      console.error("Error formatting end date:", error);
                    }
                  }

                  const endFormatted =
                    endDate && listFilterOption === "PAST"
                      ? subMinutes(new Date(), 1).toISOString()
                      : endDate
                        ? endOfDay(new Date(endDate)).toISOString()
                        : end;

                  const startFormatted =
                    startDate && isSameDay(startOfDay(startDate), startOfDay(new Date()))
                      ? addMinutes(new Date(), 1).toISOString()
                      : startDate
                        ? new Date(startDate).toISOString()
                        : start;

                  getAllAppointment({
                    endDate: endFormatted,
                    startDate: startFormatted,
                    nurseType,
                    selectedNurse,
                    selectedPatient,
                    selectedStatus,
                    page: 0,
                    size,
                    listFilterOption,
                    sortDirectionByDate,
                    sortByTime,
                    sortDirectionByStatus,
                  });
                }}
                getDates={(startDate: string, endDate: string) => {
                  let formattedStartDate = startDate;
                  let formattedEndDate = endDate;

                  if (startDate && !startDate.includes("T")) {
                    try {
                      if (startDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
                        const [month, day, year] = startDate.split("-");
                        formattedStartDate = new Date(`${year}-${month}-${day}T00:00:00.000Z`).toISOString();
                      } else {
                        formattedStartDate = new Date(startDate).toISOString();
                      }
                    } catch (error) {
                      console.error("Error formatting start date:", error);
                    }
                  }

                  if (endDate && !endDate.includes("T")) {
                    try {
                      if (endDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
                        const [month, day, year] = endDate.split("-");
                        formattedEndDate = new Date(`${year}-${month}-${day}T23:59:59.999Z`).toISOString();
                      } else {
                        formattedEndDate = endOfDay(new Date(endDate)).toISOString();
                      }
                    } catch (error) {
                      console.error("Error formatting end date:", error);
                    }
                  }

                  setSelectedDatesFromList({ startDate: formattedStartDate, endDate: formattedEndDate });

                  const startDateAdjusted =
                    listFilterOption === "UPCOMING" && isSameDay(startOfDay(formattedStartDate), startOfDay(new Date()))
                      ? addMinutes(new Date(), 1).toISOString()
                      : startOfDay(new Date(formattedStartDate)).toISOString();

                  const endDateAdjusted =
                    listFilterOption === "PAST" && isSameDay(startOfDay(formattedEndDate), startOfDay(new Date()))
                      ? subMinutes(new Date(), 1).toISOString()
                      : endOfDay(new Date(formattedStartDate)).toISOString();

                  getAllAppointment({
                    endDate: endDateAdjusted,
                    startDate: startDateAdjusted,
                    nurseType,
                    selectedNurse,
                    selectedPatient,
                    selectedStatus,
                    page: 0,
                    size: 10,
                    listFilterOption,
                    sortDirectionByDate,
                    sortByTime,
                    sortDirectionByStatus,
                  });
                }}
              />
            </Grid>
          )}
          {selectedIcon === "calendar" && (
            <Grid marginTop={3}>
              <SchedulingCalendarView
                ProviderProfileUuid={ProviderProfileUuid}
                selectedAppointmentType={selectedAppointmentTypefilter}
                selectedStatusName={selectedStatusName}
                selectedpatient={selectedPatient}
                selectedNurse={selectedNurse}
                calenderFilterOptions={calendarFilterOption}
                timeFrame={timeFrame}
                setTimeFrame={setTimeFrame}
                // setSelectedDate={setSelectedDate}
                selectedDate={selectedDate as Date}
              />
            </Grid>
          )}
        </Grid>
        <CustomDialog
          buttonName={["save"]}
          borderRadius="15px"
          open={scheduleAppointmentDialog}
          title="Schedule Appointment"
          onClose={() => setScheduleAppointmentDialog(false)}
          sx={{
            "& .MuiDialog-paper": {
              overflow: "visible",
            },
          }}
        >
          <ScheduleAppointmentDialog
            setScheduleAppointmentDialog={setScheduleAppointmentDialog}
            refetch={getAllAppointment}
          />
        </CustomDialog>
      </Grid>
    </Grid>
  );
}

export default ScheduleAppointment;

export const filterGrid = {
  width: "355px",
};
