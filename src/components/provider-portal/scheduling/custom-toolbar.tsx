// import React from "react";
// import { ToolbarProps } from "react-big-calendar";
import { useEffect, useState } from "react";

import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { Button, IconButton, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { endOfWeek, format, startOfWeek } from "date-fns";

interface CalendarTimeDateType {
  handlePreviousDate: () => void;
  handleNextDate: () => void;
  selectedDate: Date | null;
  handleResetTodayDate: () => void;
  timeFrame: string;
}
const CustomToolbar = (props: CalendarTimeDateType) => {
  const { handleNextDate, handlePreviousDate, selectedDate, handleResetTodayDate, timeFrame } = props;

  const startOfWeekDay = startOfWeek(selectedDate || "", { weekStartsOn: 1 });
  const endOfWeekDay = endOfWeek(selectedDate || "", { weekStartsOn: 1 });

  const [weekRange, setWeekRange] = useState("");

  const updateWeekRange = () => {
    if (timeFrame === "week") {
      const startMonthYear = format(startOfWeekDay, "MMM yyyy");
      const endMonthYear = format(endOfWeekDay, "MMM yyyy");

      if (startOfWeekDay.getMonth() !== endOfWeekDay.getMonth()) {
        setWeekRange(`${startMonthYear} - ${endMonthYear}`);
      } else {
        setWeekRange(startMonthYear);
      }
    }
  };

  useEffect(() => {
    updateWeekRange();
  }, [selectedDate, timeFrame]);

  return (
    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} marginBottom={"10px"}>
      <Grid display={"flex"} justifyContent={"center"} alignItems={"center"}>
        <Grid>
          <Button
            onClick={handleResetTodayDate}
            sx={{
              height: "30px",
              width: "20px",
              border: "1px solid #006D8F",
              borderRadius: "4px",
            }}
          >
            Today
          </Button>
        </Grid>
        <Grid
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {/* Left Arrow Button */}
          <IconButton aria-label="Previous" onClick={handlePreviousDate}>
            <ChevronLeftIcon fontSize="small" />
          </IconButton>
          {selectedDate && (
            <Typography variant="bodyMedium" fontWeight={550} fontSize={"14px"}>
              {timeFrame === "month"
                ? format(selectedDate, "MMM yyyy")
                : timeFrame === "week"
                  ? weekRange
                  : format(selectedDate, "dd MMM yyyy")}
            </Typography>
          )}

          {/* Right Arrow Button */}
          <IconButton aria-label="Next" onClick={handleNextDate}>
            <ChevronRightIcon fontSize="small" />
          </IconButton>
        </Grid>
      </Grid>
      <Grid></Grid>
    </Grid>
  );
};

export default CustomToolbar;
