import MailOutlineOutlinedIcon from "@mui/icons-material/MailOutlineOutlined";
import PhoneAndroidOutlinedIcon from "@mui/icons-material/PhoneAndroidOutlined";
import { ButtonBase, Divider, Tooltip, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { format, isValid, parseISO } from "date-fns";

// import { formatDateWithAgeNewWithoutdateandGen } from "../../../utils/time-date-formatter";
import AgeCalculator from "../../../common-components/age-calculator/age-calculator";
import { theme } from "../../../utils/theme";
import { PatientDetails as Patients } from "./schedule-appointment-dialog";

type PatientDetailsProps = {
  patient?: Patients;
  handleOnChangePatient: () => void;
  isChangePatientRequired: boolean;

  selectedFromDropDown?: (patient: Patients | undefined) => void;
  forDropDown: boolean;
  minWidth: string;
};

export const PatientDetailsInScheduleAppointment = (props: PatientDetailsProps) => {
  const { patient, handleOnChangePatient, isChangePatientRequired, selectedFromDropDown, forDropDown, minWidth } =
    props;

  const onClickOfPatient = (patient: Patients | undefined) => {
    selectedFromDropDown && selectedFromDropDown(patient);
  };
  const detailsBox = () => {
    return (
      <Grid
        flex={1}
        padding={forDropDown ? "7px" : "8px"}
        borderRadius="8px"
        columnGap={1}
        bgcolor="#f4f4f4"
        minWidth={minWidth}
        container
        width={"100%"}
        height={"56px"}
        justifyContent={"space-between"}
      >
        <Grid
          container
          width={forDropDown ? "600px" : "600px"}
          minWidth={forDropDown ? "220px" : "340px"}
          maxWidth={forDropDown ? "100%" : "70%"}
          flexDirection={"column"}
        >
          <Grid container columnGap={1} width={"100%"} flexDirection={"row"}>
            {/* {!forDropDown && ( */}
            <>
              <Typography variant={forDropDown ? "bodySmall" : "bodySmall"} color={theme.palette.grey[900]}>
                {patient?.mrn}
              </Typography>
              <Divider orientation="vertical" sx={{ height: "20px" }} />
            </>
            {/* )} */}
            <Typography variant="bodySmall" fontWeight={"700"}>
              {`${patient?.patientName} `}
            </Typography>
            <Divider orientation="vertical" sx={{ height: "20px" }} />

            {!forDropDown && (
              <>
                <Typography variant={forDropDown ? "bodySmall" : "bodySmall"} color={theme.palette.grey[700]}>
                  {patient?.birthDate && isValid(parseISO(patient.birthDate))
                    ? format(parseISO(patient.birthDate), "MM-dd-yyyy")
                    : "N/A"}
                </Typography>

                <Divider orientation="vertical" sx={{ height: "20px" }} />
                <Typography variant={forDropDown ? "bodySmall" : "bodySmall"} color={theme.palette.grey[700]}>
                  {patient?.birthDate ? (
                    <AgeCalculator birthdate={format(new Date(patient?.birthDate || ""), "MM-dd-yyyy") || ""} />
                  ) : (
                    "-"
                  )}
                  {" yrs"}&nbsp;
                </Typography>
                <Divider orientation="vertical" sx={{ height: "20px" }} />
                <Typography variant={forDropDown ? "bodySmall" : "bodySmall"} color={theme.palette.grey[700]}>
                  {(patient?.gender && patient?.gender[0]) || "-"}
                </Typography>
              </>
            )}

            {forDropDown && (
              <>
                <Typography variant="bodySmall">
                  {`${patient?.birthDate ? format(parseISO(patient.birthDate), "MM-dd-yyyy") : "-"} `}
                </Typography>
                <Divider orientation="vertical" sx={{ height: "20px" }} />
                <Typography variant={forDropDown ? "bodySmall" : "bodySmall"} color={theme.palette.grey[700]}>
                  {patient?.birthDate ? (
                    <AgeCalculator
                      birthdate={
                        patient?.birthDate && isValid(parseISO(patient.birthDate))
                          ? format(parseISO(patient.birthDate), "MM-dd-yyyy")
                          : "NNNNN"
                      }
                    />
                  ) : (
                    "-"
                  )}
                  {" yrs"}&nbsp;
                </Typography>

                <Divider orientation="vertical" sx={{ height: "20px" }} />

                <Typography variant={forDropDown ? "bodySmall" : "bodySmall"} color={theme.palette.grey[700]}>
                  {(patient?.gender && patient?.gender[0]) || "-"}
                </Typography>
              </>
            )}
          </Grid>
          <Grid container columnGap={forDropDown ? 1 : 2} flexDirection={"row"}>
            {/* Phone Section */}
            <Grid
              container
              width={forDropDown ? "24%" : "29%"}
              columnGap={"4px"}
              alignItems="center"
              justifyContent="flex-start"
              flexDirection={"row"}
            >
              <PhoneAndroidOutlinedIcon style={{ width: "15px", height: "14px" }} />
              <Typography variant={forDropDown ? "bodySmall" : "bodySmall"} color={"#727272"}>
                {patient?.phone}
              </Typography>
            </Grid>

            {/* Email Section */}
            <Grid container flex={1} columnGap={"3px"} alignItems="center" justifyContent="flex-start">
              <MailOutlineOutlinedIcon style={{ width: "14px", height: "14px" }} />
              <Tooltip title={patient?.email} arrow>
                <Typography
                  color={"#727272"}
                  variant={"bodySmall"}
                  noWrap
                  // border={2}
                  sx={{
                    // maxWidth: forDropDown ? "200px" : "200px",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    ":hover": {
                      cursor: "pointer",
                    },
                  }}
                >
                  {patient?.email}
                </Typography>
              </Tooltip>
            </Grid>
          </Grid>
        </Grid>
        {isChangePatientRequired && (
          <Grid container width={"90px"} justifyContent={"center"} alignContent={"center"}>
            <ButtonBase onClick={() => handleOnChangePatient()}>
              <Typography fontSize={"11px"} color={theme.palette.primary.light}>
                {"Change Patient"}
              </Typography>
            </ButtonBase>
          </Grid>
        )}
      </Grid>
    );
  };

  return (
    <>
      {forDropDown && (
        <ButtonBase onClick={() => onClickOfPatient(patient)} disableRipple>
          {detailsBox()}
        </ButtonBase>
      )}
      {!forDropDown && <>{detailsBox()}</>}
    </>
  );
};
