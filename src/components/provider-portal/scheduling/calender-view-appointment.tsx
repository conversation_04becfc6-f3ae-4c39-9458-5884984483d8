import React, { useEffect, useState } from "react";
import { Calendar, dateFnsLocalizer } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { useDispatch } from "react-redux";

import HomeIcon from "@mui/icons-material/Home";
import VideocamIcon from "@mui/icons-material/Videocam";
import { Divider, Icon, Typography } from "@mui/material";
import { Grid, useMediaQuery } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import {
  endOfDay,
  endOfMonth,
  endOfWeek,
  format,
  getDay,
  parse,
  startOfDay,
  startOfMonth,
  startOfWeek,
} from "date-fns";
import { enUS } from "date-fns/locale";

import CustomDrawer from "@/common-components/custom-drawer/custom-drawer";

import useAuthority from "@/hooks/use-authority";
import { setIsLoading } from "@/redux/actions/loader-action";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { toCamelCase } from "@/utils/toCamelCase";

import PersonImage from "../../../assets/image_svg/icons/person.svg";
import Stethescope from "../../../assets/image_svg/icons/stethoscope.svg";
import { AppointmentControllerService, Provider, status, timezone } from "../../../sdk/requests";
import "./calendar-style.css";
// import CustomToolbar from "./custom-toolbar";
import AppointmentDetailsTabs from "./dialoge/appointment-details-tabs";
import { Appointment } from "./scheduling-list-table";

type CalendarEventType = {
  uuid?: string;
  purpose: string;
  patientId: string;
  nurseId: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  timezone: timezone;
  patientName?: string;
  patientMrn?: string;
  patientEmail?: string;
  patientPhone?: string;
  nurseName?: string;
  mode?: "HOME_VISIT" | "TELE_VISIT";
  status?: status;
};

interface CalendarValue {
  calenderFilterOptions: string;
  selectedNurse: string;
  selectedpatient: string;
  timeFrame: string;

  setTimeFrame: (timeFrame: string) => void;
  selectedStatusName: String[];
  selectedAppointmentType: string | null;
  selectedDate: Date;
  ProviderProfileUuid: Provider | null;
}
type View = "month" | "week" | "day";

function SchedulingCalendarView(props: CalendarValue) {
  const {
    selectedAppointmentType,
    selectedStatusName,
    timeFrame,
    setTimeFrame,
    selectedNurse,
    selectedpatient,
    calenderFilterOptions,
    selectedDate,
    ProviderProfileUuid,
  } = props;
  const dispatch = useDispatch();
  const below1300 = useMediaQuery("(max-width :1300px)");
  const below1500 = useMediaQuery("(max-width :1500px)");

  const [eventsWithShortDuration, setEventsWithShortDuration] = useState<string[] | undefined>([]);
  // const [selectedDate, setSelectedDate] = useState(new Date());
  setTimeFrame;
  const [eventsWithLessThanOneHourDuration, setEventsWithLessThanOneHourDuration] = useState<string[] | undefined>([]);
  const [slotLessThanFifteenMin, setSlotLessThanFifteenMin] = useState<string[] | undefined>([]);

  const [startDate, setStartDate] = useState<Date | null>();
  const [endDate, setEndDate] = useState<Date | null>();
  const [slotsData, setSlotData] = useState<CalendarEventType[]>();
  const [coloredAppointments, setColoredAppointments] = useState<(CalendarEventType & { color: string })[]>([]);
  const role = useAuthority();

  const handleTimeDiffLessThanHrs = () => {
    const shortEvents = slotsData
      ?.filter((event) => {
        const startTime = new Date(event.startTime);
        const endTime = new Date(event.endTime);
        return endTime.getTime() - startTime.getTime() <= 3600000; // 1 hour
      })
      .map((event) => event.uuid)
      // Removed undefined
      .filter((uuid): uuid is string => uuid !== undefined);
    const shortLessThanHourEvents = slotsData
      ?.filter((event) => {
        const startTime = new Date(event.startTime);
        const endTime = new Date(event.endTime);
        return endTime.getTime() - startTime.getTime() <= 2700000; // 58 minutes
      })
      .map((event) => event.uuid)
      // Removed undefined
      .filter((uuid): uuid is string => uuid !== undefined);

    // 15min slots
    const shortLessThanFifteenMin = slotsData
      ?.filter((event) => {
        const startTime = new Date(event.startTime);
        const endTime = new Date(event.endTime);
        return endTime.getTime() - startTime.getTime() <= 900000; // 15 minutes
      })
      .map((event) => event.uuid)
      // Removed undefined
      .filter((uuid): uuid is string => uuid !== undefined);

    setSlotLessThanFifteenMin(shortLessThanFifteenMin);
    setEventsWithLessThanOneHourDuration(shortLessThanHourEvents);
    setEventsWithShortDuration(shortEvents);
  };

  useEffect(() => {
    handleTimeDiffLessThanHrs();
  }, [slotsData, timeFrame]);

  const locales = {
    "en-US": enUS,
  };

  const localizer = dateFnsLocalizer({
    format,
    parse,
    startOfWeek: () => startOfWeek(new Date(), { weekStartsOn: 1 }),
    getDay,
    locales,
  });

  const statusColors: Record<string, string> = {
    SCHEDULED: "#004AB1",
    CANCELLED: "red",
    COMPLETED: "#016A1C",
    PENDING: "#943C00",
    CHECKED_IN: "#D614FF",
    RESCHEDULED: "#943C00",
    IN_PROGRESS: "#004AB1",
    NO_SHOW: "#525E6F",
    CHECK_IN: "#6565EE",
    BROADCAST: "#B1000F",
    BROADCAST_EVENT: "#006D8F",
    BROADCAST_ACCEPTED: "#364144",
    REQUESTED: "green",
  };

  useEffect(() => {
    const addColorsToAppointments = () => {
      if (!slotsData) return;
      const updatedAppointments = slotsData?.map((appointment) => {
        const color = appointment.status ? statusColors[appointment.status] || "black" : "black";
        return {
          ...appointment,
          color,
        };
      });
      setColoredAppointments(updatedAppointments);
    };

    if (slotsData && slotsData?.length > 0) {
      addColorsToAppointments();
    }
  }, [slotsData]);

  const handleNavigate = (newDate: Date | null, view: string) => {
    switch (view) {
      case "month":
        // Start and end dates of the full month
        const startDateofMonth = startOfMonth(newDate || "");
        const endDateofMonth = endOfMonth(newDate || "");
        setStartDate(startDateofMonth);
        setEndDate(endDateofMonth);
        break;

      case "week":
        const startDateOfWeek = startOfWeek(newDate || "", { weekStartsOn: 1 }); // Monday
        const endDateOfWeek = endOfWeek(newDate || "", { weekStartsOn: 1 }); // sunday
        setStartDate(startDateOfWeek);
        setEndDate(endDateOfWeek);
        break;

      case "day":
        // Start and end of the same day
        const startDateOfDay = startOfDay(newDate || "");
        const endDateOfDay = endOfDay(newDate || "");
        setStartDate(startDateOfDay);
        setEndDate(endDateOfDay);
        break;

      default:
        setStartDate(newDate);
        setEndDate(newDate);
    }
  };

  useEffect(() => {
    handleNavigate(selectedDate, timeFrame);
  }, [timeFrame, selectedDate]);

  //useState for dialog open
  const [openAppointmentDetailDialog, setOpenAppointmentDetailDialog] = useState(false);
  // useState for setApptDetail
  const [selectedAppt, setSelectedAppt] = useState<CalendarEventType>();

  const handleOnClickPurpose = (appt: CalendarEventType) => {
    setOpenAppointmentDetailDialog(true);
    setSelectedAppt(appt);
  };
  const xTenantId = GetTenantId();

  const { data, isSuccess, isLoading } = useQuery({
    queryKey: [
      "get-all-appointment",
      calenderFilterOptions,
      selectedpatient,
      selectedNurse,
      selectedStatusName,
      selectedAppointmentType,
      timeFrame,
      startDate,
      endDate,
      selectedDate,
    ],
    enabled: Boolean(selectedDate && startDate && endDate),
    queryFn: () => {
      if (!startDate || !endDate) {
        return undefined;
      }
      return AppointmentControllerService.getAppointmentList({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        patientUuid: selectedpatient,
        nurseId: selectedNurse,
        filter: calenderFilterOptions === "All" ? "ALL" : calenderFilterOptions === "Request" ? "REQUESTED" : undefined,
        type: selectedAppointmentType || undefined,
        // status: selectedStatusName as status[],
        status: selectedStatusName[0] as status,
        xTenantId: xTenantId,
        providerId: role.isProvider ? ProviderProfileUuid?.uuid : "",
      });
    },
  });

  // Function to refetch calendar data - can be passed to other components
  const refetchCalendarData = () => {
    if (startDate && endDate) {
      AppointmentControllerService.getAppointmentList({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        patientUuid: selectedpatient,
        nurseId: selectedNurse,
        filter: calenderFilterOptions === "All" ? "ALL" : calenderFilterOptions === "Request" ? "REQUESTED" : undefined,
        type: selectedAppointmentType || undefined,
        status: selectedStatusName[0] as status,
        xTenantId: xTenantId,
        providerId: role.isProvider ? ProviderProfileUuid?.uuid : "",
      }).then((response) => {
        // Process response similar to the useQuery effect
        if (response && response.data) {
          const responseData = response.data as unknown as CalendarEventType[];
          const allSlotsData = responseData.map((item) => ({
            ...item,
            startTime: new Date(item.startTime),
            endTime: new Date(item.endTime),
          }));
          setSlotData(allSlotsData);
        }
      });
    }
  };

  useEffect(() => {
    if (isSuccess) {
      const response = data?.data as unknown as CalendarEventType[];
      const allSlotsData = response.map((item) => ({
        ...item,
        startTime: new Date(item.startTime),
        endTime: new Date(item.endTime),
      }));
      setSlotData(allSlotsData);
    }
  }, [isSuccess, data]);

  useEffect(() => {
    dispatch(setIsLoading(isLoading));
  }, [dispatch, isLoading]);

  return (
    <>
      <Calendar
        defaultView="month"
        localizer={localizer}
        events={slotsData}
        startAccessor="startTime"
        endAccessor="endTime"
        style={{ height: 790 }}
        view={timeFrame as View}
        date={selectedDate}
        showMultiDayTimes={false}
        allDayMaxRows={2}
        // popupOffset={{ x: 30, y: 30 }}
        // key={slotsData?.length}
        formats={{
          timeGutterFormat: "HH:mm",
        }}
        // onDrillDown={(date, view) => {  // on click of view more in month view it will redirect to day view
        //   if (view === "day") {
        //     setTimeFrame("day");
        //     setSelectedDate(date);
        //   }
        // }}
        timeslots={2} // timeslots * step = 30 min gap
        step={15}
        popup={true} // Enables "+ N more"  open dialog for events
        eventPropGetter={(event) => {
          if (!event.uuid) return {};
          return {};
        }}
        dayLayoutAlgorithm={"no-overlap"}
        components={{
          // toolbar: (toolbarProps) => (
          //   <CustomToolbar
          //     {...toolbarProps}
          //     handleResetTodayDate={handleResetTodayDate}
          //     handlePreviousDate={handlePreviousDate}
          //     handleNextDate={handleNextDate}
          //     selectedDate={selectedDate}
          //     timeFrame={timeFrame}
          //   />
          // ),
          toolbar: () => null,
          event: ({ event }) => {
            //For Month View Event
            if (timeFrame === "month") {
              return (
                <Grid
                  data-tip={event.mode || event.status}
                  sx={{
                    // overflow: "auto",
                    display: "flex",
                    justifyContent: "space-between",
                    minHeight: "100%",
                    background: event.status === String("CANCELLED") ? "#FFF2F3" : "#F2F7FF",
                    borderRadius: "4px",
                    paddingLeft: "3px",
                    alignItems: "center",
                    paddingY: "4px",
                    paddingRight: "5px",
                  }}
                  onClick={() => handleOnClickPurpose(event)}
                >
                  <Grid display={"flex"} width={"100%"}>
                    <Grid
                      sx={{
                        marginRight: "8px",
                        width: "3px",
                        background: coloredAppointments?.[0]?.color,
                        borderRadius: "13px",
                      }}
                    />
                    <Grid container gap={"3px"} flexDirection={"column"}>
                      <Grid display={"flex"} alignItems={"center"} sx={{ wordBreak: "break-word" }} gap={0.4}>
                        <Typography
                          color={"#041D25"}
                          fontFamily={"Roboto"}
                          variant="bodyMedium"
                          fontWeight={600}
                          fontSize={below1300 ? "12px" : "12px"}
                          sx={{
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: below1300 ? "80px" : below1500 ? "100px" : "unset",
                          }}
                        >
                          {event.purpose}
                        </Typography>
                      </Grid>

                      <Grid container flexDirection={"column"} rowGap={0.1}>
                        <Grid display={"flex"} alignItems={"center"} gap={0.4}>
                          <Icon
                            sx={{
                              alignItems: "center",
                              display: "flex",
                              width: "7px",
                              height: "7px",
                            }}
                          >
                            <img src={Stethescope} width={"100%"} />
                          </Icon>
                          <Typography variant="bodySmall" sx={{ color: "black", fontSize: "9px" }}>
                            {event.nurseName}{" "}
                          </Typography>
                        </Grid>
                        <Grid display={"flex"} alignItems={"center"} gap={0.4}>
                          <Icon
                            sx={{
                              alignItems: "center",
                              display: "flex",
                              width: "7px",
                              height: "7px",
                            }}
                          >
                            <img src={PersonImage} width={"100%"} />
                          </Icon>
                          <Typography variant="bodySmall" sx={{ color: "black", fontSize: "9px" }}>
                            {event.patientName}{" "}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                  {!below1300 && (
                    <Grid
                      sx={{
                        width: "30%",
                        display: "flex",
                        alignItems: "flex-end",
                        justifyContent: timeFrame !== "month" ? "center" : "space-between",
                        flexDirection: "column",
                      }}
                    >
                      {event.mode == "HOME_VISIT" ? (
                        <HomeIcon fontSize="small" sx={{ color: "#078EB9" }} />
                      ) : (
                        <VideocamIcon sx={{ color: "#B1000F" }} fontSize="small" />
                      )}
                      {timeFrame == "month" && (
                        <Typography color="black" fontSize={"9px"}>
                          {new Date(event.startTime).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: true,
                          })}
                          {/* {event.start.getTime().} */}
                        </Typography>
                      )}
                    </Grid>
                  )}
                </Grid>
              );
              //For Day View Event ===>>>
            } else if (timeFrame === "day") {
              return (
                <Grid
                  onClick={() => handleOnClickPurpose(event)}
                  sx={{
                    overflow: "auto",
                    display: "flex",
                    justifyContent: "space-between",
                    minHeight: "100%",
                    background: "#F2F7FF",
                    borderRadius: "4px",
                    marginLeft: eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "5px" : "",
                    marginBottom: eventsWithShortDuration?.includes(event.uuid as string) ? "5px" : "",
                    padding: eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "0px" : "6px",
                    flexDirection: eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "row" : "",
                    alignItems: eventsWithLessThanOneHourDuration?.includes(event.uuid as string)
                      ? "center"
                      : "flex-start",
                  }}
                >
                  <Divider
                    orientation="vertical"
                    flexItem={true}
                    sx={{
                      background: coloredAppointments?.[0]?.color,
                      width: "3px",
                      marginRight: "5px",
                      borderRadius: "15px",
                      marginY: eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "2px" : "",
                    }}
                  />
                  <Grid display={"flex"} width={"100%"}>
                    {/* <Grid
                      sx={{
                        marginRight: "8px",
                        width: "4px",
                        background: event.appoitmentStatus ? event.color : "",
                        borderRadius: "13px",

                      }}
                    /> */}
                    <Grid
                      container
                      gap={"3px"}
                      // border={"2px solid red"}
                      flexDirection={
                        eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "row" : "column"
                      }
                      alignItems={eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "center" : ""}
                      justifyContent={eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "center" : ""}
                      columnGap={eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? 2 : 0}
                    >
                      <Grid display={"flex"} alignItems={"center"} sx={{ wordBreak: "break-word" }} gap={0.4}>
                        <Typography
                          color={"#041D25"}
                          fontFamily={"Roboto"}
                          variant="bodyMedium"
                          fontWeight={600}
                          fontSize={below1300 ? "12px" : "12px"}
                          sx={{
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: below1300 ? "180px" : below1500 ? "380px" : "unset",
                          }}
                        >
                          {event.purpose}
                        </Typography>
                      </Grid>

                      <Grid
                        container
                        flexDirection={
                          eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "row" : "column"
                        }
                        mt={0.5}
                        mb={0.3} // border={"2px solid red"}
                        columnGap={1}
                        rowGap={0.4}
                      >
                        <Grid display={"flex"} alignItems={"center"} gap={0.4}>
                          <Icon
                            sx={{
                              alignItems: "center",
                              display: "flex",
                              width: "7px",
                              height: "7px",
                            }}
                          >
                            <img src={Stethescope} width={"100%"} />
                          </Icon>
                          <Typography variant="bodySmall" sx={{ color: "black", fontSize: "8px" }}>
                            {event.nurseName}{" "}
                          </Typography>
                        </Grid>
                        <Grid display={"flex"} alignItems={"center"} gap={0.4}>
                          <Icon
                            sx={{
                              alignItems: "center",
                              display: "flex",
                              width: "7px",
                              height: "7px",
                            }}
                          >
                            <img src={PersonImage} width={"100%"} />
                          </Icon>
                          <Typography variant="bodySmall" sx={{ color: "black", fontSize: "8px" }}>
                            {event.patientName}{" "}
                          </Typography>
                        </Grid>
                      </Grid>

                      {!eventsWithLessThanOneHourDuration?.includes(event.uuid as string) && (
                        <Grid
                          display={"flex"}
                          alignItems={"center"}
                          mt={timeFrame == "day" && eventsWithShortDuration?.includes(event.uuid as string) ? 0 : 0.4}
                        >
                          <Typography
                            color={coloredAppointments?.[0]?.color}
                            variant="bodySmall"
                            fontSize={"10px"}
                            fontWeight={1000}
                          >
                            {toCamelCase(event.status || "")}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
                  <Grid
                    sx={{
                      // border: "2px solid red",
                      width: "auto",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      flexDirection: "row",
                      gap: "50px",
                      paddingRight: eventsWithShortDuration?.includes(event.uuid as string) ? "10px" : "",
                    }}
                  >
                    {eventsWithLessThanOneHourDuration?.includes(event.uuid as string) && (
                      <Grid
                        display={"flex"}
                        alignItems={"center"}
                        mt={timeFrame == "day" && eventsWithShortDuration?.includes(event.uuid as string) ? 0 : 0.4}
                      >
                        <Typography
                          color={coloredAppointments[0].color}
                          variant="bodySmall"
                          fontSize={"10px"}
                          fontWeight={1000}
                          display={"flex"}
                          sx={{
                            whiteSpace: "nowrap",
                          }}
                        >
                          {toCamelCase(event.status || "").trim()}
                        </Typography>
                      </Grid>
                    )}

                    {eventsWithShortDuration?.includes(event.uuid as string) ? (
                      event.mode === "TELE_VISIT" ? (
                        React.cloneElement(<VideocamIcon sx={{ color: "#B1000F" }} fontSize="small" />, {
                          style: { fontSize: "14px" },
                        })
                      ) : (
                        React.cloneElement(<HomeIcon sx={{ color: "#078EB9" }} fontSize="small" />, {
                          style: { fontSize: "14px" },
                        })
                      )
                    ) : event.mode === "TELE_VISIT" ? (
                      <VideocamIcon sx={{ color: "#B1000F" }} fontSize="small" />
                    ) : event.mode === "HOME_VISIT" ? (
                      <HomeIcon sx={{ color: "#078EB9" }} fontSize="small" />
                    ) : null}
                  </Grid>
                </Grid>
              );
              //For Week View Event
            } else if (timeFrame === "week") {
              return (
                <Grid
                  onClick={() => handleOnClickPurpose(event)}
                  sx={{
                    overflow: "auto",
                    display: "flex",
                    justifyContent: "space-between",
                    minHeight: "100%",
                    background: "#F2F7FF",
                    borderRadius: "4px",

                    marginLeft: eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "5px" : "",
                    marginBottom: eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "5px" : "",
                    padding: eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "0px" : "5px",
                    flexDirection: eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "row" : "",
                    alignItems: eventsWithLessThanOneHourDuration?.includes(event.uuid as string)
                      ? "center"
                      : "flex-start",
                  }}
                >
                  <Divider
                    orientation="vertical"
                    flexItem={true}
                    sx={{
                      background: coloredAppointments?.[0]?.color,
                      width: "4px",
                      marginRight: "4px",
                      borderRadius: "15px",
                      marginY: eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "2px" : "",
                    }}
                  />
                  <Grid display={"flex"} width={"100%"}>
                    <Grid
                      container
                      gap={"3px"}
                      flexDirection={
                        eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "row" : "column"
                      }
                      alignItems={eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "center" : ""}
                      justifyContent={
                        eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? "flex-start" : ""
                      }
                      columnGap={eventsWithLessThanOneHourDuration?.includes(event.uuid as string) ? 2 : 0}
                    >
                      <Grid display={"flex"} alignItems={"center"} sx={{ wordBreak: "break-word" }} gap={0.4}>
                        {/* <Typography color={"black"} variant="bodyMedium" fontWeight={600} fontSize={"10px"}>
                          {event.purpose}
                        </Typography> */}
                        <Typography
                          color={"black"}
                          variant="bodyMedium"
                          fontWeight={600}
                          fontSize={"12px"}
                          sx={{
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: below1300 ? "80px" : below1500 ? "100px" : "unset",
                          }}
                        >
                          {event.purpose}
                        </Typography>
                      </Grid>
                      {!eventsWithLessThanOneHourDuration?.includes(event.uuid as string) && (
                        <Grid container flexDirection={"row"} columnGap={1} rowGap={0.4} mt={0.5}>
                          <Grid display={"flex"} alignItems={"center"} gap={0.4}>
                            <Icon
                              sx={{
                                alignItems: "center",
                                display: "flex",
                                width: "7px",
                                height: "7px",
                              }}
                            >
                              <img src={Stethescope} width={"100%"} />
                            </Icon>
                            <Typography variant="bodySmall" sx={{ color: "black", fontSize: "8px" }}>
                              {event.nurseName}{" "}
                            </Typography>
                          </Grid>
                          <Grid display={"flex"} alignItems={"center"} gap={0.4}>
                            <Icon
                              sx={{
                                alignItems: "center",
                                display: "flex",
                                width: "7px",
                                height: "7px",
                              }}
                            >
                              <img src={PersonImage} width={"100%"} />
                            </Icon>
                            <Typography variant="bodySmall" sx={{ color: "black", fontSize: "8px" }}>
                              {event.patientName}{" "}
                            </Typography>
                          </Grid>
                        </Grid>
                      )}

                      {!slotLessThanFifteenMin?.includes(event.uuid as string) && (
                        <Grid
                          display={"flex"}
                          alignItems={"center"}
                          mt={eventsWithShortDuration?.includes(event.uuid as string) ? 0.4 : 0.4}
                        >
                          <Typography
                            color={coloredAppointments[0].color}
                            variant="bodySmall"
                            fontSize={"10px"}
                            fontWeight={1000}
                          >
                            {toCamelCase(event.status || "")}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
                  <Grid
                    sx={{
                      width: "30%",
                      display: "flex",
                      alignItems: "flex-end",
                      justifyContent: "center",
                      flexDirection: "column",
                      paddingRight: eventsWithShortDuration?.includes(event.uuid as string) ? "10px" : "",
                    }}
                  >
                    {eventsWithShortDuration?.includes(event.uuid as string) ? (
                      event.mode === "TELE_VISIT" ? (
                        React.cloneElement(<VideocamIcon sx={{ color: "#B1000F" }} fontSize="small" />, {
                          style: { fontSize: "14px" },
                        })
                      ) : (
                        React.cloneElement(<HomeIcon sx={{ color: "#078EB9" }} fontSize="small" />, {
                          style: { fontSize: "14px" },
                        })
                      )
                    ) : event.mode === "TELE_VISIT" ? (
                      <VideocamIcon sx={{ color: "#B1000F" }} fontSize="small" />
                    ) : event.mode === "HOME_VISIT" ? (
                      <HomeIcon sx={{ color: "#078EB9" }} fontSize="small" />
                    ) : null}
                  </Grid>
                </Grid>
              );
            }
          },
        }}
      />

      <CustomDrawer
        anchor={"right"}
        open={openAppointmentDetailDialog}
        title={"Appointment Details"}
        onClose={() => setOpenAppointmentDetailDialog(false)}
        drawerWidth="470px"
      >
        <AppointmentDetailsTabs
          appointmentDetails={selectedAppt as unknown as Appointment}
          onClose={() => setOpenAppointmentDetailDialog(false)}
          refetch={refetchCalendarData}
        />
      </CustomDrawer>
    </>
  );
}

export default SchedulingCalendarView;
