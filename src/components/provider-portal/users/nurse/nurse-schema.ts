import * as yup from "yup";

import {
  addressLine1Max128ErrorMsg,
  addressLine1RequiredErrorMsg,
  cityMax64ErrorMsg,
  cityRequiredErrorMsg,
  cityStateRegexErrorMsg,
  countryRequiredErrorMsg,
  ehrRequiredErrorMsg,
  emailRegexErrorMsg,
  emailRequiredErrorMsg,
  firstNameOrSurnameRegexErrorMsg,
  firstNameRequiredErrorMsg,
  genderRequiredErrorMessage,
  lastNameRequiredErrorMsg,
  lessThan255ErrorMsg,
  licenseStatesRequiredErrorMsg,
  npiRegexErrorMsg,
  npiRequiredErrorMsg,
  phoneRegexErrorMsg,
  phoneRequiredErrorMsg,
  practiceIdRequiredErrorMsg,
  stateRequiredErrorMsg,
  zipCodeRegexErrorMsg,
  zipCodeRequiredErrorMsg,
} from "@/constants/error-messages";
import { cityStateRgex, emailRegExp, nameRegex, npiRegExp, phoneRegex, zipCodeRegex } from "@/utils/regex";

export const manualEntryFormSchema = yup.object().shape({
  optionSelected: yup.string(),
  gender: yup.string().when("optionSelected", (optionSelected, schema) => {
    if (optionSelected[0] === "manualEntry") {
      return schema.required(genderRequiredErrorMessage);
    } else {
      return schema.notRequired();
    }
  }),
  firstName: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string().required(firstNameRequiredErrorMsg).matches(nameRegex, firstNameOrSurnameRegexErrorMsg);
    } else {
      return yup.string();
    }
  }),
  lastName: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string().required(lastNameRequiredErrorMsg).matches(nameRegex, firstNameOrSurnameRegexErrorMsg);
    } else {
      return yup.string();
    }
  }),
  npi: yup.string().when("optionalSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string().required(npiRequiredErrorMsg).matches(npiRegExp, npiRegexErrorMsg);
    } else {
      return yup.string();
    }
  }),
  phone: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup
        .string()
        .required(phoneRequiredErrorMsg)
        .transform((value) => (value === "" ? null : value))
        .matches(phoneRegex, phoneRegexErrorMsg);
    } else {
      return yup.string();
    }
  }),
  prefix: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string().transform((value) => (value === "" ? null : value));
    } else {
      return yup.string().notRequired();
    }
  }),
  email: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string().required(emailRequiredErrorMsg).matches(emailRegExp, emailRegexErrorMsg);
    } else {
      return yup.string();
    }
  }),
  address: yup.object().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.object().shape({
        line1: yup.string().max(128, addressLine1Max128ErrorMsg).required(addressLine1RequiredErrorMsg),
        line2: yup.string().max(128, addressLine1Max128ErrorMsg),
        city: yup
          .string()
          .matches(cityStateRgex, cityStateRegexErrorMsg)
          .max(64, cityMax64ErrorMsg)
          .required(cityRequiredErrorMsg),
        state: yup
          .string()
          .matches(cityStateRgex, cityStateRegexErrorMsg)
          .max(50, lessThan255ErrorMsg)
          .required(stateRequiredErrorMsg),
        zipcode: yup.string().required(zipCodeRequiredErrorMsg).matches(zipCodeRegex, zipCodeRegexErrorMsg),
        country: yup.string().required(countryRequiredErrorMsg),
      });
    } else {
      return yup.object().notRequired();
    }
  }),
  avatar: yup.string(),
  licenses: yup.array().when("optionSelected", (optionSelected, schema) => {
    if (Array.isArray(optionSelected) && optionSelected[0] === "manualEntry") {
      return schema
        .of(
          yup.object().shape({
            licenseNumber: yup.string().required("License number is required"),
            licensedState: yup.string().required("Licensed state is required"),
            licenseExpiryDate: yup
              .date()
              .typeError("License expiry date is required")
              .required("License expiry date is required"),
          })
        )
        .min(1, licenseStatesRequiredErrorMsg);
    } else {
      return schema.notRequired();
    }
  }),
  status: yup.string(),
  // =>> Ehr field
  ehr: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(ehrRequiredErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  practiceId: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(practiceIdRequiredErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrFirstName: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(firstNameRequiredErrorMsg).matches(nameRegex, firstNameOrSurnameRegexErrorMsg);
    } else {
      return yup.string();
    }
  }),
  ehrLastName: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(lastNameRequiredErrorMsg).matches(nameRegex, firstNameOrSurnameRegexErrorMsg);
    } else {
      return yup.string();
    }
  }),
  ehrGender: yup.string().when("optionSelected", (optionSelected, schema) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return schema.required(genderRequiredErrorMessage);
    } else {
      return schema.notRequired();
    }
  }),
  ehrEmail: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(emailRequiredErrorMsg).matches(emailRegExp, emailRegexErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrPhone: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup
        .string()
        .required(phoneRequiredErrorMsg)
        .transform((value) => (value === "" ? null : value))
        .matches(phoneRegex, phoneRegexErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrPrefix: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup
        .string()
        .required(phoneRequiredErrorMsg)
        .transform((value) => (value === "" ? null : value));
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrNpi: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(npiRequiredErrorMsg).matches(npiRegExp, npiRegexErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrAddress: yup.object().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.object().shape({
        line1: yup.string().max(128, addressLine1Max128ErrorMsg).required(addressLine1RequiredErrorMsg),
        line2: yup.string().max(128, addressLine1Max128ErrorMsg),
        city: yup
          .string()
          .matches(cityStateRgex, cityStateRegexErrorMsg)
          .max(64, cityMax64ErrorMsg)
          .required(cityRequiredErrorMsg),
        state: yup
          .string()
          .matches(cityStateRgex, cityStateRegexErrorMsg)
          .max(50, lessThan255ErrorMsg)
          .required(stateRequiredErrorMsg),
        zipcode: yup.string().required(zipCodeRequiredErrorMsg).matches(zipCodeRegex, zipCodeRegexErrorMsg),
        country: yup.string().required(countryRequiredErrorMsg),
      });
    } else {
      return yup.object().notRequired();
    }
  }),
  ehrLicenses: yup.array().when("optionSelected", (optionSelected, schema) => {
    if (Array.isArray(optionSelected) && optionSelected[0] === "enrollFromEHR") {
      return schema
        .of(
          yup.object().shape({
            licenseNumber: yup.string().required("License number is required"),
            licensedState: yup.string().required("Licensed state is required"),
            licenseExpiryDate: yup
              .date()
              .typeError("License expiry date is required")
              .required("License expiry date is required"),
          })
        )
        .min(1, licenseStatesRequiredErrorMsg);
    } else {
      return schema.notRequired();
    }
  }),
});
