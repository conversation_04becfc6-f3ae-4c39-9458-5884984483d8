import React, { useState } from "react";
import { Form<PERSON>rovider, useFieldArray, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  Grid2 as Grid,
  IconButton,
  InputLabel,
  LinearProgress,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import { InputPhoneNumber } from "@/components/ui/Form/InputPhoneNumber";
import Select from "@/components/ui/Form/Select";
import { UploadImage } from "@/components/ui/Form/UploadImage";
import { Gender } from "@/constants/roles";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useProviderControllerServiceCreateProvider } from "@/sdk/queries";
import {
  EhrControllerService,
  LicenseStateControllerService,
  Provider,
  ProviderControllerService,
} from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { splitPhoneNumber } from "@/services/common/phone-formatter";
import { stateList } from "@/utils/StateList";
import { getNumbersOnly } from "@/utils/format/string";
import { theme } from "@/utils/theme";

import { manualEntryFormSchema } from "./nurse-schema";

type NurseFormProps = {
  nurse: Provider | null;
  role: string;
  isEdit?: boolean;
  handleDrawerClose: () => void;
};

const NurseForm = (props: NurseFormProps) => {
  const { nurse, handleDrawerClose, isEdit, role } = props;
  const isNurse = role === "Nurse";
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const xTenantId = GetTenantId();
  const [valueRadio, setValueRadio] = useState("manualEntry");

  const [integrateNurseFromEhr, setIntegrateNurseFromEhr] = useState<Provider>();
  const [isLoading, setIsLoading] = useState(false);
  const handleChangeRadioBtn = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selecetedValue = e.target.value;
    formMethods.setValue("optionSelected", selecetedValue);
    setValueRadio(selecetedValue);
  };

  const initialValues = {
    optionSelected: valueRadio,
    firstName: nurse?.firstName || "",
    lastName: nurse?.lastName || "",
    npi: nurse?.npi || "",
    email: nurse?.email || "",
    phone: nurse?.phone ? splitPhoneNumber(nurse?.phone)?.number : "",
    prefix: nurse?.phone ? splitPhoneNumber(nurse?.phone)?.countryCode : "+1",
    address: {
      line1: nurse?.address?.line1 || "",
      line2: nurse?.address?.line2 || "",
      city: nurse?.address?.city || "",
      state: nurse?.address?.state || "",
      zipcode: nurse?.address?.zipcode || "",
      country: nurse?.address?.country || "USA",
    },
    avatar: "",
    licenses: nurse?.providerLicenseDetails?.map((v) => {
      const expiryDate = v.expiryDate ? new Date(v.expiryDate) : "";
      return {
        licenseNumber: v.licenseNumber || "",
        licensedState: v.licensedStates?.length ? `${v.licensedStates[0].state}` : "",
        licenseExpiryDate: expiryDate > new Date() ? expiryDate : "",
      };
    }) || [{ licenseNumber: "", licensedState: "", licenseExpiryDate: "" as unknown as Date }],
    status: !isEdit ? "active" : nurse?.active ? "active" : "inactive",
    gender: nurse?.gender || "",
    ehr: "",
    practiceId: "",
    ehrFirstName: integrateNurseFromEhr?.firstName || "",
    ehrLastName: integrateNurseFromEhr?.lastName || "",
    ehrGender: integrateNurseFromEhr?.gender || "",
    ehrEmail: integrateNurseFromEhr?.email || "",
    ehrPrefix: "+1",
    ehrPhone: integrateNurseFromEhr?.phone || "",
    ehrNpi: integrateNurseFromEhr?.npi || "",
    ehrAddress: {
      line1: integrateNurseFromEhr?.address.line1 || "",
      line2: integrateNurseFromEhr?.address.line2 || "",
      city: integrateNurseFromEhr?.address.city || "",
      state: integrateNurseFromEhr?.address.state || "",
      country: integrateNurseFromEhr?.address.country || "USA",
      zipcode: integrateNurseFromEhr?.address.zipcode || "",
    },
    ehrLicenses: integrateNurseFromEhr?.providerLicenseDetails?.map((v) => ({
      licenseNumber: v.licenseNumber || "",
      licensedState: v.licensedStates?.length ? `${v.licensedStates[0].state}` : "",
      licenseExpiryDate: v.expiryDate ? new Date(v.expiryDate) : new Date(),
    })) || [{ licenseNumber: "", licensedState: "", licenseExpiryDate: "" as unknown as Date }],
  };

  const { data: licensedStateOptions } = useQuery({
    queryKey: ["list-of-licensed-states"],
    queryFn: async () => {
      const response = await LicenseStateControllerService.getAllLicensedStates({
        page: 0,
        size: 100,
      });

      const { content } = response.data as ContentObject<{ id: number; country: "US"; state: string }[]>;

      return content?.map((item) => {
        return {
          key: item.state.toString(),
          value: item.state,
          info: item.id.toString(),
        };
      });
    },
  });

  const getIdOfState = (state: string) => licensedStateOptions?.find((item) => item.value === state)?.info || "";

  const { data: profile, isLoading: loadingProfile } = useQuery({
    queryKey: ["provider", nurse?.uuid],
    queryFn: async () => {
      const response = (await ProviderControllerService.getProviderById({
        providerUuid: nurse?.uuid || "",
      })) as AxiosResponse<Provider>;

      return response.data as Provider;
    },
    enabled: Boolean(nurse?.uuid) && isEdit,
  });

  const formMethods = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(manualEntryFormSchema),
  });

  const {
    fields: licenseFields,
    append: appendLicenses,
    remove: removeLicenses,
  } = useFieldArray({
    name: "licenses",
    control: formMethods.control,
  });

  const {
    fields: ehrLicenseFields,
    append: appendEhrLicenses,
    remove: removeEhrLicenses,
  } = useFieldArray({
    name: "ehrLicenses",
    control: formMethods.control,
  });

  const handleOnSuccess = () => {
    const title = isNurse ? "Nurse" : "Provider";

    queryClient.invalidateQueries({ queryKey: ["list-of-provider"] });

    if (isEdit) {
      queryClient.invalidateQueries({ queryKey: ["nurse", nurse?.uuid] });
    }

    dispatch(
      setSnackbarOn({
        severity: AlertSeverity.SUCCESS,
        message: isEdit ? `${title} updated successfully!` : `${title} added successfully!`,
      })
    );
    handleDrawerClose();
  };

  const addNurseService = useProviderControllerServiceCreateProvider({
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const editNurseService = useMutation({
    mutationFn: ({ user, avatar }: { user: Provider; avatar?: string }) =>
      Promise.all([
        ProviderControllerService.updateProvider({
          requestBody: { ...user, uuid: nurse?.uuid },
          xTenantId: xTenantId,
        }),
        avatar !== "" &&
          ProviderControllerService.changeAvatar({
            requestBody: {
              newAvatar: avatar?.startsWith("data:image")
                ? avatar.replace(/data:image\/(jpeg|png);base64,/, "")
                : avatar,
            },
            providerUuid: nurse?.uuid || "",
            xTenantId: xTenantId,
          }),
      ]),
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const getEhrData = async () => {
    setIsLoading(true);
    try {
      let res = await EhrControllerService.getPractitionerByProviderId({
        practitionerId: formMethods.getValues("practiceId") || "",
      });
      if (res) {
        setIntegrateNurseFromEhr({
          ...res,
          address: res.address || { line1: "", line2: "", city: "", state: "", country: "", zipcode: "" },
        });

        formMethods.setValue("ehrFirstName", res?.firstName || "");
        formMethods.setValue("ehrLastName", res?.lastName || "");
        formMethods.setValue("ehrGender", res?.gender || "");
        formMethods.setValue("ehrEmail", res?.email || "");
        formMethods.setValue("ehrPhone", res?.phone ? getNumbersOnly(res?.phone) : "");
        formMethods.setValue("ehrNpi", res?.npi || "");
        formMethods.setValue("ehrAddress", {
          line1: res.address.line1 || "",
          line2: res.address.line2 || "",
          city: res.address.city || "",
          state: res.address.state || "",
          country: res.address.country || "USA",
          zipcode: res.address.zipcode || "",
        });
        formMethods.setValue(
          "ehrLicenses",
          res?.providerLicenseDetails?.map((v) => ({
            licenseNumber: v.licenseNumber || "",
            licensedState: v.licensedStates?.length ? `${v.licensedStates[0].state}` : "",
            licenseExpiryDate: v.expiryDate ? new Date(v.expiryDate) : new Date(),
          })) || [{ licenseNumber: "", licensedState: "", licenseExpiryDate: "" as unknown as Date }]
        );
      }
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message || "Something went wrong",
        })
      );
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (values: unknown) => {
    let payload = {} as Provider;
    const formValues = values as typeof initialValues;

    if (formValues.optionSelected === "manualEntry") {
      payload = {
        chatbotTone: nurse?.chatbotTone || "CASUAL",
        introduction: nurse?.introduction,
        firstName: formValues.firstName,
        lastName: formValues.lastName,
        npi: formValues.npi,
        email: formValues.email,
        phone: `${formValues.prefix}${formValues.phone}`,
        active: formValues.status === "active",
        address: {
          line1: formValues.address.line1,
          line2: formValues.address.line2,
          city: formValues.address.city,
          country: formValues.address.country,
          state: formValues.address.state,
          zipcode: formValues.address.zipcode,
        },
        providerLicenseDetails: formValues.licenses.map(
          (item: { licenseExpiryDate?: Date | string; licensedState?: string; licenseNumber?: string }) => {
            return {
              licenseNumber: item.licenseNumber || "",
              expiryDate: item?.licenseExpiryDate || "",
              licensedStates: [
                {
                  state: item.licensedState,
                  id: item?.licensedState ? +getIdOfState(item?.licensedState) : "",
                },
              ],
            };
          }
        ),
        gender: formValues.gender,
        role: isNurse ? "NURSE" : "PROVIDER",
      } as Provider;
    } else {
      payload = {
        ehrId: integrateNurseFromEhr?.ehrId || "",
        chatbotTone: integrateNurseFromEhr?.chatbotTone || "CASUAL",
        introduction: integrateNurseFromEhr?.introduction || "",
        firstName: formValues.ehrFirstName || "",
        lastName: formValues.ehrLastName || "",
        gender: formValues.ehrGender || "",
        npi: formValues.ehrNpi || "",
        email: formValues.ehrEmail || "",
        phone: formValues.ehrPhone ? `${formValues.ehrPrefix}${formValues.ehrPhone}` : "",
        active: Boolean(integrateNurseFromEhr?.active),
        address: {
          line1: formValues.ehrAddress?.line1 || "",
          line2: formValues.ehrAddress?.line2 || "",
          city: formValues.ehrAddress?.city || "",
          country: formValues.ehrAddress?.country || "USA",
          state: formValues.ehrAddress?.state || "",
          zipcode: formValues.ehrAddress?.zipcode || "",
        },
        providerLicenseDetails: formValues.ehrLicenses.map(
          (item: { licenseExpiryDate?: Date; licensedState?: string; licenseNumber?: string }) => {
            return {
              licenseNumber: item.licenseNumber || "",
              expiryDate: item?.licenseExpiryDate || "",
              licensedStates: [
                {
                  state: item.licensedState,
                  id: item?.licensedState ? +getIdOfState(item?.licensedState) : "",
                },
              ],
            };
          }
        ),
        role: isNurse ? "NURSE" : "PROVIDER",
      } as Provider;
    }

    if (isEdit) {
      editNurseService.mutate({ user: payload, avatar: formValues.avatar });
    } else {
      addNurseService.mutate({ requestBody: payload });
    }
  };

  if (loadingProfile) {
    return (
      <DrawerBody>
        <LinearProgress />
      </DrawerBody>
    );
  }

  return (
    <DrawerBody padding={3} offset={80}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          {isEdit && (
            <Grid size={{ xs: 12, sm: 2 }}>
              <UploadImage name="avatar" defaultImage={profile?.avatar as string} isLoading={loadingProfile} />
            </Grid>
          )}
          {!isEdit && (
            <Grid size={12}>
              <FormControl sx={{ paddingBottom: 1 }}>
                <RadioGroup
                  row
                  aria-labelledby="demo-controlled-radio-buttons-group"
                  name="controlled-radio-buttons-group"
                  value={valueRadio}
                  onChange={handleChangeRadioBtn}
                >
                  <FormControlLabel value="manualEntry" control={<Radio />} label="Manual Entry" />
                  <FormControlLabel value="enrollFromEHR" control={<Radio />} label="Enroll From EHR" />
                </RadioGroup>
              </FormControl>
              <Divider />
            </Grid>
          )}
          {formMethods.getValues("optionSelected") == "manualEntry" && (
            <Stack spacing={3} paddingTop={2}>
              <Grid container spacing={2}>
                <Grid container size={isEdit ? 10 : 12}>
                  <Grid size={6}>
                    <Input name="firstName" isRequired />
                  </Grid>
                  <Grid size={6}>
                    <Input name="lastName" isRequired />
                  </Grid>
                  <Grid size={6}>
                    <Input
                      name="email"
                      type="email"
                      isRequired
                      disabled={Boolean(profile?.emailVerified)}
                      isVerified={Boolean(profile?.emailVerified)}
                    />
                  </Grid>
                  <Grid size={6}>
                    <InputPhoneNumber isRequired />
                  </Grid>
                  <Grid size={6}>
                    <Input
                      name="npi"
                      label="NPI"
                      placeholder="Enter NPI Number"
                      isRequired
                      disabled={Boolean(profile?.emailVerified)}
                      inputProps={{ maxLength: 10 }}
                    />
                  </Grid>
                  <Grid size={6}>
                    <Select name="gender" options={Gender} placeholder="Select Gender" isRequired />
                  </Grid>
                  {isEdit && (
                    <Grid size={6}>
                      <Select
                        name="status"
                        options={[
                          { value: "active", label: "Active" },
                          { value: "inactive", label: "Inactive" },
                        ]}
                        placeholder="Select Status"
                        isRequired
                      />
                    </Grid>
                  )}
                </Grid>
              </Grid>
              <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
                <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                  <Typography variant="medium">Address</Typography>
                </Box>
                <Grid container spacing={2} sx={{ padding: 2 }}>
                  <Grid container spacing={2}>
                    <Grid size={6}>
                      <Input name="address.line1" label="Line 1" isRequired />
                    </Grid>
                    <Grid size={6}>
                      <Input name="address.line2" label="Line 2" />
                    </Grid>
                    <Grid size={6}>
                      <Select
                        name="address.state"
                        label="State"
                        options={stateList.map((item) => ({ label: item.value, value: item.key }))}
                        placeholder="Select State"
                        isRequired
                      />
                    </Grid>
                    <Grid size={6}>
                      <Input name="address.city" label="City" isRequired />
                    </Grid>
                    <Grid size={6}>
                      <Input name="address.country" label="Country" isRequired disabled />
                    </Grid>
                    <Grid size={6}>
                      <Input name="address.zipcode" label="Zip Code" isRequired />
                    </Grid>
                  </Grid>
                </Grid>
              </Box>
              <Box>
                <Typography variant="medium">License Details</Typography>
                <Box sx={{ backgroundColor: "#EEFBFF", padding: 1.5, marginTop: 1.5 }}>
                  {licenseFields.map((license, index) => (
                    <Box sx={{ display: "flex", gap: 1, marginBottom: 2 }} key={license.id}>
                      <Grid size="grow">
                        <Grid container spacing={2}>
                          <Grid size={4}>
                            <Input
                              name={`licenses.${index}.licenseNumber`}
                              label="Licensed Number"
                              placeholder="Enter License Number"
                              isRequired
                            />
                          </Grid>
                          <Grid size={4}>
                            <Select
                              name={`licenses.${index}.licensedState`}
                              label="Licensed State"
                              options={
                                licensedStateOptions
                                  ? licensedStateOptions
                                      .map((item) => ({ label: item.value, value: item.key }))
                                      .sort((a, b) => a.label.localeCompare(b.label))
                                  : []
                              }
                              placeholder="Select State"
                              isRequired
                            />
                          </Grid>
                          <Grid size={4}>
                            <Datepicker
                              name={`licenses.${index}.licenseExpiryDate`}
                              label="License Expiry Date"
                              placeholder="Select Expiry Date"
                              isRequired
                              minDate={new Date()}
                            />
                          </Grid>
                        </Grid>
                      </Grid>
                      <Grid size="auto">
                        <InputLabel sx={{ mb: 1 }}>&nbsp;</InputLabel>
                        <IconButton onClick={() => removeLicenses(index)} disabled={licenseFields.length <= 1}>
                          <DeleteOutlineOutlinedIcon />
                        </IconButton>
                      </Grid>
                    </Box>
                  ))}
                  <Button
                    sx={{
                      "&.MuiButton-outlined": {
                        borderColor: "#078EB9",
                        color: "#078EB9",
                      },
                      "&.MuiButton-outlined:hover": {
                        backgroundColor: "transparent",
                        opacity: 0.8,
                      },
                    }}
                    variant="outlined"
                    startIcon={<AddOutlinedIcon />}
                    onClick={() =>
                      appendLicenses({
                        licensedState: "",
                        licenseExpiryDate: "" as unknown as Date,
                        licenseNumber: "",
                      })
                    }
                  >
                    Add License Details
                  </Button>
                </Box>
              </Box>
            </Stack>
          )}
          {formMethods.getValues("optionSelected") == "enrollFromEHR" && (
            <Grid container sx={{ width: "100%" }}>
              <Grid container size={12} gap={1} mt={2}>
                <Grid size={5.9}>
                  <Select
                    name="ehr"
                    label="EHR"
                    options={[{ value: "EpicHealth", label: "EpicHealth" }]}
                    placeholder="Select EHR"
                    isRequired
                  />
                </Grid>
                <Grid size={5.9}>
                  <Input name="practiceId" label="Practice ID" isRequired />
                </Grid>
              </Grid>
              <Grid size={12} sx={{ display: "flex", justifyContent: "flex-end" }} my={2}>
                <Button
                  variant="contained"
                  onClick={() => getEhrData()}
                  loading={isLoading}
                  disabled={!(formMethods.watch("practiceId") && formMethods.watch("ehr"))}
                >
                  <Typography variant="bodySmall">Fetch Details</Typography>
                </Button>
              </Grid>

              {integrateNurseFromEhr && (
                <Grid size={12}>
                  <Grid>
                    <Typography variant="bodyMedium" fontWeight={600}>
                      Result
                    </Typography>
                    <Divider sx={{ my: 1.5 }} />
                  </Grid>
                  <Stack spacing={2}>
                    <Grid container spacing={2}>
                      <Grid size={6}>
                        <Input
                          label="First Name"
                          name="ehrFirstName"
                          isRequired
                          disabled={Boolean(integrateNurseFromEhr.firstName)}
                        />
                      </Grid>
                      <Grid size={6}>
                        <Input
                          label="Last Name"
                          name="ehrLastName"
                          isRequired
                          disabled={Boolean(integrateNurseFromEhr.lastName)}
                        />
                      </Grid>
                      <Grid size={6}>
                        <Input
                          disabled={!!integrateNurseFromEhr.email}
                          name="ehrEmail"
                          isRequired
                          placeholder="Enter Email"
                          value={integrateNurseFromEhr?.email}
                          label="Email"
                        />
                      </Grid>
                      <Grid size={6}>
                        <InputPhoneNumber
                          namePrefix="ehrPrefix"
                          namePhone="ehrPhone"
                          isRequired
                          placeholder="Enter Phone Number"
                        />
                      </Grid>
                      <Grid size={6}>
                        <Input
                          name="ehrNpi"
                          disabled={!!integrateNurseFromEhr.npi}
                          isRequired
                          placeholder="Enter Npi"
                          value={integrateNurseFromEhr?.npi}
                          label="NPI"
                        />
                      </Grid>
                      <Grid size={6}>
                        <Select
                          label="Gender"
                          name="ehrGender"
                          options={Gender}
                          placeholder="Select Gender"
                          isRequired
                        />
                      </Grid>
                    </Grid>
                    <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
                      <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                        <Typography variant="medium">Address</Typography>
                      </Box>
                      <Grid container spacing={2} sx={{ padding: 2 }}>
                        <Grid container spacing={2}>
                          <Grid size={6}>
                            <Input name="ehrAddress.line1" label="Line 1" isRequired />
                          </Grid>
                          <Grid size={6}>
                            <Input name="ehrAddress.line2" label="Line 2" />
                          </Grid>
                          <Grid size={6}>
                            <Autocomplete
                              name="ehrAddress.state"
                              label="State"
                              options={stateList.map((item) => ({ label: item.value, value: item.key }))}
                              placeholder="Select State"
                              isRequired
                            />
                          </Grid>
                          <Grid size={6}>
                            <Input name="ehrAddress.city" label="City" isRequired />
                          </Grid>
                          <Grid size={6}>
                            <Input name="ehrAddress.country" label="Country" isRequired disabled />
                          </Grid>
                          <Grid size={6}>
                            <Input name="ehrAddress.zipcode" label="Zip Code" isRequired />
                          </Grid>
                        </Grid>
                      </Grid>
                    </Box>
                    <Box>
                      <Typography variant="medium">License Details</Typography>
                      <Box sx={{ backgroundColor: "#EEFBFF", padding: 1.5, marginTop: 1.5 }}>
                        {ehrLicenseFields.map((license, index) => (
                          <Box sx={{ display: "flex", gap: 1, marginBottom: 2 }} key={license.id}>
                            <Grid size="grow">
                              <Grid container spacing={2}>
                                <Grid size={4}>
                                  <Input
                                    name={`ehrLicenses.${index}.licenseNumber`}
                                    label="Licensed Number"
                                    placeholder="Enter License Number"
                                    isRequired
                                  />
                                </Grid>
                                <Grid size={4}>
                                  <Autocomplete
                                    name={`ehrLicenses.${index}.licensedState`}
                                    label="Licensed State"
                                    options={
                                      licensedStateOptions
                                        ? licensedStateOptions
                                            .map((item) => ({ label: item.value, value: item.key }))
                                            .sort((a, b) => a.label.localeCompare(b.label))
                                        : []
                                    }
                                    placeholder="Select State"
                                    isRequired
                                  />
                                </Grid>
                                <Grid size={4}>
                                  <Datepicker
                                    name={`ehrLicenses.${index}.licenseExpiryDate`}
                                    label="License Expiry Date"
                                    placeholder="Select Expiry Date"
                                    isRequired
                                    minDate={new Date()}
                                  />
                                </Grid>
                              </Grid>
                            </Grid>
                            <Grid size="auto">
                              <InputLabel sx={{ mb: 1 }}>&nbsp;</InputLabel>
                              <IconButton
                                onClick={() => removeEhrLicenses(index)}
                                disabled={ehrLicenseFields.length <= 1}
                              >
                                <DeleteOutlineOutlinedIcon />
                              </IconButton>
                            </Grid>
                          </Box>
                        ))}
                        <Button
                          sx={{
                            "&.MuiButton-outlined": {
                              borderColor: "#078EB9",
                              color: "#078EB9",
                            },
                            "&.MuiButton-outlined:hover": {
                              backgroundColor: "transparent",
                              opacity: 0.8,
                            },
                          }}
                          variant="outlined"
                          startIcon={<AddOutlinedIcon />}
                          onClick={() =>
                            appendEhrLicenses({
                              licensedState: "",
                              licenseExpiryDate: "" as unknown as Date,
                              licenseNumber: "",
                            })
                          }
                        >
                          Add License Details
                        </Button>
                      </Box>
                    </Box>
                  </Stack>
                </Grid>
              )}
            </Grid>
          )}
          <DrawerFooter>
            <Button variant="contained" type="submit" loading={addNurseService.isPending || editNurseService.isPending}>
              {isEdit ? `Save ${role}` : `Add ${role}`}
            </Button>
          </DrawerFooter>
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default NurseForm;
