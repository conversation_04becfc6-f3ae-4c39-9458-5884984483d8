import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSearchParams } from "react-router-dom";

import AddIcon from "@mui/icons-material/Add";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import RestoreIcon from "@mui/icons-material/Restore";
import {
  Button,
  IconButton,
  Link,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelect from "@/common-components/custom-select/customSelect";
import CustomSelectorSq from "@/common-components/custom-selector-sq/custom-selector-sq";
import Paginator from "@/common-components/paginator/paginator";
import Status from "@/common-components/status/status";
import {
  heading,
  iconStyles,
  linkCss,
  tableCellCss,
  typographyCss,
} from "@/common-components/table/common-table-widgets";
import { TableHeaders } from "@/common-components/table/table-models";

import { useDrawer } from "@/components/providers/DrawerProvider";
import DrawerBody from "@/components/ui/DrawerBody";
import ProfileNurse from "@/components/ui/Profile/ProfileNurse";
import ProfileStaff from "@/components/ui/Profile/ProfileStaff";
import { Roles, RolesOfAdminUsers, RolesOfPGUsers } from "@/constants/roles";
import useAuthority from "@/hooks/use-authority";
import useApiFeedback from "@/hooks/useApiFeedback";
import { ContentObject } from "@/models/response/response-content-entity";
import { setIsLoading } from "@/redux/actions/loader-action";
import {
  useProviderControllerServiceUpdateProviderArchiveStatus,
  useUserControllerServiceUpdateUserArchiveStatus,
} from "@/sdk/queries";
import { Provider, ProviderControllerService, User, UserControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { formatDateOnly } from "@/utils/format/date";
import { theme } from "@/utils/theme";

import NurseForm from "../nurse/nurse-form";
import StaffForm from "./staff-form";

type StaffListProps = {
  roleType: string;
  listType: string;
};

const StaffList = ({ roleType, listType }: StaffListProps) => {
  const { isProvider } = useAuthority();
  const headers: TableHeaders[] = !isProvider
    ? [
      { header: "Name" },
      { header: "Email" },
      { header: "Address" },
      { header: "Phone Number" },
      { header: "Status" },
      { header: "Actions" },
    ]
    : [
      { header: "Name" },
      { header: "Email" },
      { header: "Address" },
      { header: "Phone Number" },
      { header: "Status" },
    ];
  const headersNurse: TableHeaders[] = !isProvider
    ? [
      { header: "Name" },
      { header: "Email" },
      { header: "Address" },
      { header: "Phone Number" },
      { header: "NPI" },
      { header: "Licensed State" },
      { header: "License Number" },
      { header: "License Exp." },
      { header: "Status" },
      { header: "Actions" },
    ]
    : [
      { header: "Name" },
      { header: "Email" },
      { header: "Address" },
      { header: "Phone Number" },
      { header: "NPI" },
      { header: "Licensed State" },
      { header: "License Number" },
      { header: "License Exp." },
      { header: "Status" },
    ];
  const { open: openDrawer, close: closeDrawer } = useDrawer();
  const [searchParams, setSearchParams] = useSearchParams();

  const [headersArr, setHeaderArr] = useState(headers);
  const xTenantId = GetTenantId();
  const [totalPages, setTotalPages] = useState(0);
  const [selectedStaff, setSelectedStaff] = useState<User>();
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [openWarningPopUp, setOpenWarningPopUp] = useState(false);
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [searchString, setSearchstring] = useState<string>("");
  const [sortBy, setSortBy] = useState("");
  const [sortDirections, setSortDirections] = useState("desc");
  const [sortDirection, setSortDirection] = useState("desc");
  const [sortDirectionByStatus, setSortDirectionByStatus] = useState("desc");
  const [rows, setRows] = useState<User[]>([]);
  const [rowsNurse, setRowsNurse] = useState<Provider[]>([]);
  const [totalElements, setTotalElements] = useState<number>(0);
  const dispatch = useDispatch();
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);
  const [status] = useState<boolean | undefined>(undefined);
  const [archive] = useState<boolean | undefined>(undefined);
  const [selectedFilterOpt, setSelectedFilterOpt] = useState(
    searchParams.get("role") || (listType === "Staff" ? "Provider Group Admin" : "Nurse")
  );

  useEffect(() => {
    if (selectedFilterOpt) {
      setSearchParams((prev) => {
        prev.set("role", selectedFilterOpt.toString());
        return prev;
      });
    } else {
      const role = listType === "Staff" ? "Provider Group Admin" : "Nurse";

      setSelectedFilterOpt(role);
      setSearchParams((prev) => {
        prev.set("role", role);
        return prev;
      });
    }
  }, [selectedFilterOpt]);

  const [nurseType, setNurseType] = useState("INTERNAL");

  useEffect(() => {
    if (selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider") {
      setHeaderArr(
        headersNurse.filter((item) => {
          if (isEamataNurse) {
            return item.header !== "Actions";
          }
          return item;
        })
      );
    } else {
      setHeaderArr(headers);
    }
  }, [selectedFilterOpt, nurseType]);

  const handleOnClickLinkStaff = (user: User) => {
    setSelectedStaff(user);
    handleDrawer.details("View Staff", "Staff", user as User);
  };

  const handleOnClickProvider = (user: Provider) => {
    setSelectedProvider(user);
    handleDrawer.details(`View ${selectedFilterOpt}`, "Nurse", user as Provider);
  };

  const getRole = (role: string) => {
    if (role === "Front Desk") {
      return Roles.FRONTDESK;
    } else if (role === "Biller") {
      return Roles.BILLER;
    } else if (role === "Provider Group Admin") {
      return Roles.PROVIDER_GROUP_ADMIN;
    } else if (role === "Site Admin") {
      return Roles.SITE_ADMIN;
    } else if (role === "Staff") {
      return Roles.NURSE;
    } else if (role === "All") {
      return Roles.PROVIDER;
    } else if (role === "Nurse") {
      return Roles.NURSE;
    } else if (role === "Provider") {
      return Roles.PROVIDER;
    }
    return Roles.BILLER;
  };

  const { data, isLoading, isSuccess, refetch, isRefetching } = useQuery({
    queryKey: ["list-of-staff", page, size, searchString, selectedFilterOpt, sortDirection, sortBy],
    enabled: listType === "Staff",

    queryFn: () =>
      UserControllerService.getAllUsers({
        page,
        size,
        sortBy,
        sortDirection,
        status,
        archive,
        role: getRole(selectedFilterOpt),
        roleType:
          selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
            ? "PROVIDER"
            : (roleType as "PATIENT" | "STAFF" | "PROVIDER") || "STAFF",
        searchString,
        xTenantId,
      }),
  });

  const {
    data: dataGetProvider,
    isSuccess: isSuccessGetProvider,
    refetch: refetchGetProvider,
    isLoading: isLoadingProviderList,
  } = useQuery({
    queryKey: ["list-of-provider", page, size, searchString, selectedFilterOpt, nurseType, sortBy, sortDirection],
    enabled: !!(listType === "Providers"),
    queryFn: () =>
      ProviderControllerService.getAllProviders({
        page,
        size,
        sortBy,
        sortDirection,
        status,
        archive,
        role: getRole(selectedFilterOpt) as
          | "SUPER_ADMIN"
          | "FRONTDESK"
          | "BILLER"
          | "NURSE"
          | "PATIENT"
          | "ANONYMOUS"
          | "PROVIDER_GROUP_ADMIN"
          | "SITE_ADMIN"
          | "PROVIDER",
        searchString,
        xTenantId: getRole(selectedFilterOpt) === Roles.PROVIDER ? xTenantId : isEamataNurse ? "eamata" : xTenantId,
      }),
  });

  useEffect(() => {
    if (isSuccess) {
      const response = (data as unknown as AxiosResponse).data as ContentObject<User[]>;

      const userData = response?.content;
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);

      const tablePayload = userData?.map((user) => {
        return {
          uuid: user?.uuid,
          firstName: user.firstName,
          lastName: user.lastName,
          address: {
            line1: user.address?.line1,
            line2: user.address?.line2,
            city: user.address?.city,
            state: user.address?.state,
            country: user.address?.country,
            zipcode: user.address?.zipcode,
          },

          phone: user?.phone,
          active: user?.active,
          email: user?.email,
          archive: user.archive,
          role: user?.role,
          gender: user?.gender,
          locationId: user?.locationId,
          locationName: user?.locationName,
        } as User;
      });

      setRows(tablePayload);
    }
  }, [data, isSuccess]);

  useEffect(() => {
    if (isSuccessGetProvider) {
      const response = (dataGetProvider as unknown as AxiosResponse).data as ContentObject<Provider[]>;

      const userData = response?.content;
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);

      const tablePayload = userData?.map((user) => {
        return {
          uuid: user?.uuid,
          firstName: user.firstName,
          lastName: user.lastName,
          address: {
            line1: user.address?.line1,
            line2: user.address?.line2,
            city: user.address?.city,
            state: user.address?.state,
            country: user.address?.country,
            zipcode: user.address?.zipcode,
          },
          phone: user?.phone,
          active: user?.active,
          email: user?.email,
          archive: user.archive,
          role: user?.role,
          gender: user.gender,
          npi: user.npi,
          providerLicenseDetails: user.providerLicenseDetails,
          introduction: user.introduction,
          chatbotTone: user.chatbotTone,
        } as Provider;
      });

      setRowsNurse(tablePayload);
    }
  }, [dataGetProvider, isSuccessGetProvider]);

  const {
    mutateAsync: mutateAsyncArchive,
    isError: isErrorArchive,
    error: errorArchive,
    isSuccess: isSuccessArchive,
    data: dataArchive,
    isPending: isPendingArchive,
  } = useUserControllerServiceUpdateUserArchiveStatus();

  const {
    mutateAsync: mutateAsyncArchiveProvider,

    isError: isErrorArchiveProvider,
    error: errorArchiveProvider,
    isSuccess: isSuccessArchiveProvider,
    data: dataArchiveProvider,
    isPending: isPendingArchiveProvider,
  } = useProviderControllerServiceUpdateProviderArchiveStatus();

  const confirmDelete = async () => {
    if (selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider") {
      await mutateAsyncArchiveProvider({
        providerId: selectedProvider?.uuid || "",
        status: true,
        xTenantId: xTenantId,
      });
      refetchGetProvider();
    } else {
      await mutateAsyncArchive({
        userId: selectedStaff?.uuid || "",
        status: true,
        xTenantId: xTenantId,
      });
      refetch();
    }

    setOpenConfirmDeletePopUp(false);
  };

  const confirmRestore = async () => {
    if (selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider") {
      await mutateAsyncArchiveProvider({
        providerId: selectedProvider?.uuid || "",
        status: false,
        xTenantId: xTenantId,
      });
      refetchGetProvider();
    } else {
      await mutateAsyncArchive({
        userId: selectedStaff?.uuid || "",
        status: false,
        xTenantId: xTenantId,
      });
      refetch();
    }

    setOpenConfirmRestorePopUp(false);
  };

  useApiFeedback(
    isErrorArchive,
    errorArchive,
    isSuccessArchive,
    (dataArchive?.message || "User archive status updated!") as string
  );
  useApiFeedback(
    isErrorArchiveProvider,
    errorArchiveProvider,
    isSuccessArchiveProvider,
    (dataArchiveProvider?.message || "User archive status updated!") as string
  );

  const handlePageChange = (event: ChangeEvent<unknown> | null, page: number) => {
    event;
    setPage(page);
  };

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  useEffect(() => {
    dispatch(
      setIsLoading(isLoading || isPendingArchive || isLoadingProviderList || isRefetching || isPendingArchiveProvider)
    );
  }, [dispatch, isLoading, isPendingArchive, isLoadingProviderList, isRefetching, isPendingArchiveProvider]);

  const handleSorting = (column: string) => {
    if (column == "Name") {
      setSortBy("firstName");
      setSortDirections((prev) => (prev === "desc" ? "asc" : "desc"));
    } else if (column === "Status") {
      setSortBy("active");
      setSortDirectionByStatus((prev) => (prev === "desc" ? "asc" : "desc"));
    }
  };
  useEffect(() => {
    if (sortBy == "firstName") {
      setSortDirection(sortDirections);
    } else if (sortBy === "active") {
      setSortDirection(sortDirectionByStatus);
    }
  }, [sortBy, sortDirection, handleSorting]);

  const handleDrawer = {
    staffForm: (action: string, user?: User) => {
      openDrawer({
        title: `${action} Staff`,
        component: (
          <StaffForm
            isEdit={Boolean(user)}
            staffData={user || null}
            handleDrawerClose={closeDrawer}
            xTenantId={xTenantId}
            roleOptions={listType === "Staff" ? RolesOfPGUsers : RolesOfAdminUsers}
          />
        ),
      });
    },
    nurseForm: (title: string, role: string, user?: Provider) => {
      openDrawer({
        title: `${title} ${role}`,
        component: (
          <NurseForm nurse={user || null} role={role} handleDrawerClose={closeDrawer} isEdit={Boolean(user)} />
        ),
      });
    },
    details: (title: string, role: string, user?: User | Provider) => {
      openDrawer({
        title: title,
        component: (
          <DrawerBody>
            {role === "Nurse" ? (
              <ProfileNurse providerId={user?.uuid || ""} />
            ) : (
              <ProfileStaff staffId={user?.uuid || ""} xTenantId={xTenantId} />
            )}
          </DrawerBody>
        ),
      });
    },
  };

  const isEamataNurse = getRole(selectedFilterOpt) === Roles.NURSE && nurseType === "EXTERNAL";

  return (
    <Grid height={"100%"}>
      <Grid
        border={`1px solid ${theme.palette.grey[300]}`}
        boxShadow={`0px 0px 16px 0px #021D2614`}
        height={"100%"}
        borderRadius={"8px"}
        container
        flexDirection={"column"}
      >
        <Grid container p={2} justifyContent={"space-between"} rowGap={2}>
          <Grid container alignItems={"center"} columnGap={2}>
            <CustomSelectorSq
              widthOfBtn={"200px"}
              options={
                listType === "Staff"
                  ? ["Provider Group Admin", "Site Admin", "Front Desk", "Biller"]
                  : ["Nurse", "Provider"]
              }
              onSelect={(selectedOption) => {
                setPage(0);
                setSize(10);
                setSelectedFilterOpt(selectedOption);
              }}
              selectedValue={selectedFilterOpt}
            />
          </Grid>
          <Grid container columnGap={2} width={"600px"} justifyContent={"flex-end"}>
            {selectedFilterOpt === "Nurse" && (
              <Grid width={"150px"}>
                <CustomSelect
                  placeholder={"Select Nurse"}
                  items={[
                    { value: "EXTERNAL", label: "eAmata" },
                    { value: "INTERNAL", label: "Provider Group" },
                  ]}
                  name={"nurseType"}
                  value={nurseType}
                  onChange={function (e: SelectChangeEvent<string>): void {
                    setNurseType(e.target.value);
                  }}
                />
              </Grid>
            )}
            <Grid>
              <CustomInput
                hasStartSearchIcon
                placeholder={"Search by Name"}
                name={"searchString"}
                value={searchString}
                onDebounceCall={(debouncedValue) => setSearchstring(debouncedValue)}
                onInputEmpty={() => setSearchstring("")}
              />
            </Grid>
            {!isProvider && (
              <Grid>
                <Button
                  sx={{ minWidth: "150px" }}
                  startIcon={<AddIcon />}
                  onClick={() => {
                    if (["Nurse", "Provider"].includes(selectedFilterOpt)) {
                      handleDrawer.nurseForm("Add", selectedFilterOpt);
                    } else {
                      handleDrawer.staffForm("Add");
                    }
                  }}
                  variant="contained"
                  type="submit"
                >
                  <Typography variant="bodySmall">
                    {selectedFilterOpt === "Nurse"
                      ? "Add Nurse	"
                      : selectedFilterOpt === "Provider"
                        ? "Add Provider"
                        : selectedFilterOpt === "All"
                          ? "Add Provider"
                          : "Add Staff"}
                  </Typography>
                </Button>
              </Grid>
            )}
          </Grid>
        </Grid>

        {/* Table */}
        <Grid width={"100%"}>
          <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
            <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
              <TableHead>
                <TableRow>
                  {headersArr.map((header, index) => (
                    <TableCell
                      sx={{
                        ...heading,
                        minWidth: header.minWidth ? header.minWidth : "inherit",
                        maxWidth: header.maxWidth ? header.maxWidth : "inherit",
                      }}
                      align="left"
                      key={index}
                    >
                      {header.header === "Name" ? (
                        <Link
                          style={{
                            color: "#667085",
                            textDecoration: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => handleSorting(header.header)}
                        >
                          <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                            {header.header}
                            <Typography mt={0.3}>
                              {sortDirections == "asc" ? (
                                <ArrowUpwardIcon fontSize="small" />
                              ) : (
                                <ArrowDownwardIcon fontSize="small" />
                              )}
                            </Typography>
                          </Typography>
                        </Link>
                      ) : header.header === "Status" ? (
                        <Link
                          style={{
                            color: "#667085",
                            textDecoration: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => handleSorting(header.header)}
                        >
                          <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                            {header.header}
                            <Typography>
                              {sortDirectionByStatus == "asc" ? (
                                <ArrowUpwardIcon fontSize="small" />
                              ) : (
                                <ArrowDownwardIcon fontSize="small" />
                              )}
                            </Typography>
                          </Typography>
                        </Link>
                      ) : (
                        <Grid
                          container
                          flexDirection={"column"}
                          alignContent={header.header === "Actions" ? `flex-end` : "flex-start"}
                        >
                          <Typography variant="bodySmall">{header.header}</Typography>
                        </Grid>
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {selectedFilterOpt !== "Nurse" && selectedFilterOpt !== "Provider" && (
                  <>
                    {rows.length > 0 ? (
                      rows.map((user: User, index: number) => (
                        <>
                          <TableRow key={index}>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Link
                                  style={{
                                    ...linkCss,
                                  }}
                                  onClick={() => handleOnClickLinkStaff(user)}
                                >
                                  <Typography fontWeight={550} variant="bodySmall">
                                    {user.firstName + " " + user.lastName}
                                  </Typography>
                                </Link>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user.email || "-"}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user?.address?.line1
                                    ? `${user?.address?.line1 || "-"},  ${user?.address?.line2 || "-"},
								 ${user?.address?.city || "-"}, ${user?.address?.state || "-"},
								  ${user?.address?.country || "-"}, ${user?.address?.zipcode || "-"}`
                                    : "-"}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user.phone}
                                </Typography>
                              </Grid>
                            </TableCell>

                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Status status={`${user.active ? "ACTIVE" : "INACTIVE"}`} width="74px" />
                              </Grid>
                            </TableCell>
                            {!isProvider && (
                              <TableCell sx={{ ...heading }} align="right">
                                <Grid container justifyContent={"flex-end"} columnGap={1.2} flexWrap={"nowrap"}>
                                  <IconButton
                                    sx={{ padding: "0px 5px" }}
                                    aria-label="edit"
                                    onClick={() => {
                                      setSelectedStaff(user);
                                      handleDrawer.staffForm("Edit", user);
                                    }}
                                  >
                                    <EditOutlinedIcon sx={iconStyles} />
                                  </IconButton>
                                  {!user.archive ? (
                                    <IconButton
                                      aria-label="delete"
                                      onClick={() => {
                                        setSelectedStaff(user);
                                        setOpenConfirmDeletePopUp(true);
                                      }}
                                      sx={{ padding: "0px" }}
                                    >
                                      <ArchiveOutlinedIcon sx={iconStyles} />
                                    </IconButton>
                                  ) : (
                                    <IconButton
                                      aria-label="delete"
                                      onClick={() => {
                                        setSelectedStaff(user);
                                        setOpenConfirmRestorePopUp(true);
                                      }}
                                      sx={{ padding: "0px" }}
                                    >
                                      <RestoreIcon sx={iconStyles} />
                                    </IconButton>
                                  )}
                                </Grid>
                              </TableCell>
                            )}
                          </TableRow>
                        </>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider" ? 9 : 6}
                          align="center"
                        >
                          <Typography variant="bodySmall" fontWeight={550}>
                            No records found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </>
                )}
                {(selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider") && (
                  <>
                    {rowsNurse.length > 0 ? (
                      rowsNurse.map((user: Provider, index: number) => (
                        <>
                          <TableRow key={index}>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Link
                                  style={{
                                    ...linkCss,
                                  }}
                                  onClick={() => handleOnClickProvider(user)}
                                >
                                  <Typography fontWeight={550} variant="bodySmall">
                                    {user.firstName + " " + user.lastName}
                                  </Typography>
                                </Link>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user.email || "-"}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user?.address?.line1
                                    ? `${user?.address?.line1 || "-"},  ${user?.address?.line2 || "-"},
								 ${user?.address?.city || "-"}, ${user?.address?.state || "-"},
								  ${user?.address?.country || "-"}, ${user?.address?.zipcode || "-"}`
                                    : "-"}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user?.phone
                                    ? `${user.phone.replace(/^\+1(\d{3})(\d{3})(\d{4})$/, "+1$1-$2-$3").replace(/^(\d{3})(\d{3})(\d{4})$/, "+1$1-$2-$3")}`
                                    : "-"}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user.npi}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                {user.providerLicenseDetails?.map((val) => (
                                  <>
                                    {val.licensedStates?.map((v) => (
                                      <>
                                        {v.state}
                                        <br />
                                      </>
                                    ))}
                                  </>
                                ))}
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                {user.providerLicenseDetails?.map((item) => (
                                  <>
                                    <Typography sx={typographyCss} variant="bodySmall">
                                      {item.licenseNumber || "-"}{" "}
                                    </Typography>
                                  </>
                                ))}
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                {user.providerLicenseDetails?.map((item) => (
                                  <>
                                    <Typography sx={typographyCss} variant="bodySmall">
                                      {formatDateOnly(item.expiryDate, "-")}
                                    </Typography>
                                  </>
                                ))}
                              </Grid>
                            </TableCell>

                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Status status={`${user.active ? "ACTIVE" : "INACTIVE"}`} width="74px" />
                              </Grid>
                            </TableCell>
                            {!isProvider && !isEamataNurse && (
                              <TableCell sx={{ ...heading }} align="right">
                                <Grid container justifyContent={"flex-end"} columnGap={1.2} flexWrap={"nowrap"}>
                                  <IconButton
                                    sx={{ padding: "0px 5px" }}
                                    aria-label="edit"
                                    onClick={() => {
                                      setSelectedProvider(user);
                                      if (listType === "Staff") {
                                        handleDrawer.staffForm("Edit", user);
                                      } else {
                                        handleDrawer.nurseForm("Edit", selectedFilterOpt, user);
                                      }
                                    }}
                                  >
                                    <EditOutlinedIcon sx={iconStyles} />
                                  </IconButton>
                                  {!user.archive ? (
                                    <IconButton
                                      aria-label="delete"
                                      onClick={() => {
                                        setSelectedProvider(user);
                                        setOpenConfirmDeletePopUp(true);
                                      }}
                                      sx={{ padding: "0px" }}
                                    >
                                      <ArchiveOutlinedIcon sx={iconStyles} />
                                    </IconButton>
                                  ) : (
                                    <IconButton
                                      aria-label="delete"
                                      onClick={() => {
                                        setSelectedProvider(user);
                                        setOpenConfirmRestorePopUp(true);
                                      }}
                                      sx={{ padding: "0px" }}
                                    >
                                      <RestoreIcon sx={iconStyles} />
                                    </IconButton>
                                  )}
                                </Grid>
                              </TableCell>
                            )}
                          </TableRow>
                        </>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider" ? 10 : 6}
                          align="center"
                        >
                          <Typography variant="bodySmall" fontWeight={550}>
                            No records found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <Grid container>
            <Paginator
              page={page}
              totalPages={totalPages}
              totalRecord={totalElements}
              onRecordsPerPageChange={handleRecordsPerPageChange}
              onPageChange={handlePageChange}
              defaultSize={size}
            />
          </Grid>
        </Grid>

        <ConfirmationPopUp
          open={openConfirmDeletePopUp}
          confirmButtonName="Archive"
          rowData={[
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? `${selectedProvider?.firstName} ${selectedProvider?.lastName}` || ""
              : `${selectedStaff?.firstName} ${selectedStaff?.lastName}` || "",
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? selectedProvider?.email || ""
              : selectedStaff?.email || "",
          ]}
          header={[{ header: "Name" }, { header: "Email" }]}
          title={`Archive Item`}
          subtitle={"Are you sure you want to archive the following item?"}
          onClose={() => setOpenConfirmDeletePopUp(false)}
          onConfirm={() => confirmDelete()}
          message={`Do you really want to archive ${selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
            ? `${selectedProvider?.firstName} ${selectedProvider?.lastName}`
            : `${selectedStaff?.firstName} ${selectedStaff?.lastName}` || "this user"
            } ?`}
        />
        <ConfirmationPopUp
          title={`Restore Item`}
          confirmButtonName="Restore"
          subtitle={"Are you sure you want to restore the following items?"}
          open={openConfirmRestorePopUp}
          onClose={() => setOpenConfirmRestorePopUp(false)}
          onConfirm={() => confirmRestore()}
          message={`Do you really want to restore ${selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
            ? `${selectedProvider?.firstName} ${selectedProvider?.lastName}`
            : `${selectedStaff?.firstName} ${selectedStaff?.lastName}` || "this user"
            } ?`}
          rowData={[
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? `${selectedProvider?.firstName} ${selectedProvider?.lastName}` || ""
              : `${selectedStaff?.firstName} ${selectedStaff?.lastName}` || "",
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? selectedProvider?.email || ""
              : selectedStaff?.email || "",
          ]}
          header={[{ header: "Name" }, { header: "Email" }]}
        />

        <ConfirmationPopUp
          open={openWarningPopUp}
          onClose={() => setOpenWarningPopUp(false)}
          message={`This provider group is inactive, you can not add location for it.`}
          title={""}
          rowData={[]}
          header={[]}
        />
      </Grid>
    </Grid>
  );
};

export default StaffList;
