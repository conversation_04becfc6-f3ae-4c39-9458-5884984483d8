import { <PERSON><PERSON><PERSON><PERSON>, FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { Button, Grid2 as Grid, LinearProgress, Stack, Typography } from "@mui/material";
import { Box } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Input } from "@/components/ui/Form/Input";
import { InputPhoneNumber } from "@/components/ui/Form/InputPhoneNumber";
import Select from "@/components/ui/Form/Select";
import { UploadImage } from "@/components/ui/Form/UploadImage";
import { Gender, Roles } from "@/constants/roles";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useUserControllerServiceAddUser } from "@/sdk/queries";
import { Location, LocationControllerService, User, UserControllerService } from "@/sdk/requests";
import { splitPhoneNumber } from "@/services/common/phone-formatter";
import { stateList } from "@/utils/StateList";
import { theme } from "@/utils/theme";

import { staffFormSchema } from "./staff-schema";

export interface StaffFormProps {
  staffData: User | null;
  isEdit?: boolean;
  isView?: boolean;
  xTenantId: string;
  roleOptions: {
    value: string;
    label: string;
  }[];
  handleDrawerClose: () => void;
}

const StaffForm = (props: StaffFormProps) => {
  const { isEdit, xTenantId, handleDrawerClose, staffData, roleOptions } = props;
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const initialValues = {
    role: staffData?.role || "",
    firstName: staffData?.firstName || "",
    lastName: staffData?.lastName || "",
    status: staffData?.active ? "active" : "inactive",
    email: staffData?.email || "",
    phone: staffData?.phone ? splitPhoneNumber(staffData?.phone)?.number : "",
    prefix: staffData?.phone ? splitPhoneNumber(staffData?.phone)?.countryCode : "+1",
    address: {
      line1: staffData?.address?.line1 || "",
      line2: staffData?.address?.line2 || "",
      city: staffData?.address?.city || "",
      state: staffData?.address?.state || "",
      zipcode: staffData?.address?.zipcode || "",
      country: "USA",
    },
    gender: staffData?.gender || "",
    locationId: staffData?.locationId || "",
    avatar: "",
  };

  const formMethods = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(staffFormSchema),
  });

  const { data: profile, isLoading: loadingProfile } = useQuery({
    queryKey: ["profile", staffData?.uuid],
    queryFn: async () => {
      const response = (await UserControllerService.getUser({
        userId: String(staffData?.uuid),
        xTenantId: xTenantId,
      })) as AxiosResponse<User>;

      return response.data as User;
    },
    enabled: !!staffData?.uuid,
  });

  const { data: locations } = useQuery({
    queryKey: ["autocomplete-locations", xTenantId],
    queryFn: async () => {
      const response = await LocationControllerService.getAllLocations({
        page: 0,
        size: 100,
        status: true,
        archive: false,
        sortBy: "modified",
        sortDirection: "desc",
        xTenantId: xTenantId,
      });

      const { content } = response.data as ContentObject<Location[]>;

      return content ? content?.map((item) => ({ value: String(item.uuid), label: String(item.name) })) : [];
    },
    enabled: !!xTenantId && formMethods.watch("role") === Roles.SITE_ADMIN,
  });

  const handleOnSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["list-of-staff"] });
    if (isEdit) {
      queryClient.invalidateQueries({ queryKey: ["profile", staffData?.uuid] });
    }
    dispatch(
      setSnackbarOn({
        severity: AlertSeverity.SUCCESS,
        message: isEdit ? "User updated successfully!" : "User added successfully!",
      })
    );
    handleDrawerClose();
  };

  const addUserService = useUserControllerServiceAddUser({
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const editUserService = useMutation({
    mutationFn: ({ user, avatar }: { user: User; avatar?: string }) =>
      Promise.all([
        UserControllerService.updateUser({
          requestBody: { ...user, uuid: staffData?.uuid },
          xTenantId: xTenantId,
        }),
        avatar !== "" &&
          UserControllerService.changeAvatar3({
            requestBody: {
              newAvatar: avatar?.startsWith("data:image")
                ? avatar.replace(/data:image\/(jpeg|png);base64,/, "")
                : avatar,
            },
            userUuid: staffData?.uuid || "",
            xTenantId: xTenantId,
          }),
      ]),
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const onSubmit = (values: FieldValues) => {
    const formValues = values as typeof initialValues;
    const payload = {
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      email: formValues.email,
      role: formValues.role,
      address: {
        line1: formValues.address?.line1,
        line2: formValues.address?.line2,
        city: formValues.address?.city,
        state: formValues.address?.state,
        country: formValues.address?.country,
        zipcode: formValues.address?.zipcode,
      },
      gender: formValues.gender,
      locationId: formValues.locationId || "",
      phone: `${formValues.prefix}${formValues.phone}`,
      roleType: "STAFF",
      active: !isEdit || formValues.status === "active",
    } as User;

    if (isEdit) {
      editUserService.mutate({ user: payload, avatar: formValues.avatar });
    } else {
      addUserService.mutate({ requestBody: payload, xTenantId: xTenantId });
    }
  };

  if (loadingProfile) {
    return (
      <DrawerBody>
        <LinearProgress />
      </DrawerBody>
    );
  }

  return (
    <DrawerBody padding={3} offset={80}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            <Grid container spacing={2}>
              {isEdit && (
                <Grid size={{ xs: 12, sm: 2 }}>
                  <UploadImage name="avatar" defaultImage={profile?.avatar as string} isLoading={loadingProfile} />
                </Grid>
              )}
              <Grid container size={isEdit ? 10 : 12}>
                <Grid size={6}>
                  <Input name="firstName" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input name="lastName" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input name="email" isRequired disabled={isEdit} />
                </Grid>
                <Grid size={6}>
                  <InputPhoneNumber isRequired />
                </Grid>
                <Grid size={6}>
                  <Select name="role" options={roleOptions} placeholder="Select Staff Role" isRequired />
                </Grid>
                <Grid size={6}>
                  <Select name="gender" options={Gender} placeholder="Select Gender" isRequired />
                </Grid>
                {isEdit && (
                  <Grid size={6}>
                    <Select
                      name="status"
                      options={[
                        { value: "active", label: "Active" },
                        { value: "inactive", label: "Inactive" },
                      ]}
                      placeholder="Select Status"
                      isRequired
                    />
                  </Grid>
                )}
                {formMethods.watch("role") === Roles.SITE_ADMIN && (
                  <Grid size={6}>
                    <Autocomplete
                      name="locationId"
                      label="Location"
                      options={locations || []}
                      placeholder="Select Location"
                      isRequired
                    />
                  </Grid>
                )}
              </Grid>
            </Grid>
            <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
              <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                <Typography variant="medium">Address</Typography>
              </Box>
              <Grid container spacing={2} sx={{ padding: 2 }}>
                <Grid size={6}>
                  <Input name="address.line1" label="Line 1" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input name="address.line2" label="Line 2" />
                </Grid>
                <Grid size={6}>
                  <Select
                    name="address.state"
                    label="State"
                    options={stateList.map((item) => ({ label: item.key, value: item.value }))}
                    placeholder="Select State"
                    isRequired
                  />
                </Grid>
                <Grid size={6}>
                  <Input name="address.city" label="City" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input name="address.country" label="Country" isRequired disabled />
                </Grid>
                <Grid size={6}>
                  <Input name="address.zipcode" label="Zip Code" isRequired />
                </Grid>
              </Grid>
            </Box>
          </Stack>
          <DrawerFooter>
            <Button variant="contained" type="submit" loading={addUserService.isPending || editUserService.isPending}>
              {isEdit ? `Save Staff` : `Add Staff`}
            </Button>
          </DrawerFooter>
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default StaffForm;
