import React, { useState } from "react";
import { useSearchParams } from "react-router-dom";

import { Tab, Tabs } from "@mui/material";
import { Grid } from "@mui/system";

import { CustomTabPanel, a11yProps } from "@/common-components/custom-tab/custom-tab";

import StaffList from "./staff-users/staff-list";

const tabLabels = ["Providers", "Staff"];

const UsersTab = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [value, setValue] = useState(Number(searchParams.get("tab")) || 0);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSearchParams({ tab: newValue.toString() });
    setValue(newValue);
  };

  return (
    <Grid width="100%" height="100%" p={2}>
      <Grid height="100%" borderRadius="8px" container flexDirection="column">
        <Grid>
          <Grid sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs value={value} onChange={handleChange}>
              {tabLabels?.map((item, index) => (
                <Tab sx={{ textTransform: "none", fontWeight: 550 }} key={index} label={item} {...a11yProps(0)} />
              ))}
            </Tabs>
          </Grid>
          <Grid flex={1}>
            {tabLabels.map((item, index) => (
              <CustomTabPanel key={index} value={value} index={index}>
                {item === "Providers" && <StaffList roleType="" listType="Providers" />}
                {item === "Staff" && <StaffList roleType="" listType="Staff" />}
              </CustomTabPanel>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default UsersTab;
