import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, Divider, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import CustomSelect from "@/common-components/custom-select/customSelect";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { ProviderGroup, ProviderGroupControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

const BillingTab = () => {
  const xTenantId = GetTenantId();

  const { data: profile, refetch: refetchProfile } = useQuery({
    queryKey: ["my-profile"],
    queryFn: async () => {
      const response = await ProviderGroupControllerService.getProviderGroupBySchema({ xTenantId: xTenantId });

      return response.data as ProviderGroup;
    },
  });

  type BillingCycle = "NO_BILLING" | "ROLLING_30_DAYS" | "CALENDAR_MONTH";

  const [billingCycle, setBillingCycle] = useState<string>();

  const [timeThreshold, setTimeThreshold] = useState<number>();

  useEffect(() => {
    if (profile?.billingCycle) {
      setBillingCycle(profile.billingCycle);
      setSelectedCycle(profile.billingCycle);
    }
  }, [profile?.billingCycle, profile?.monitoringThreshold]);

  useEffect(() => {
    if (profile?.monitoringThreshold) {
      setTimeThreshold(profile.monitoringThreshold);
    }
  }, [profile?.monitoringThreshold]);

  const dispatch = useDispatch();

  const [selectedCycle, setSelectedCycle] = useState<string>();

  const updateBillingCycle = useMutation({
    mutationFn: async (billingCycle: string) => {
      await ProviderGroupControllerService.updateProviderGroupConfiguration({
        xTenantId: xTenantId,
        requestBody: {
          billingCycle: billingCycle as BillingCycle,
          monitoringThreshold: timeThreshold,
          uuid: profile?.uuid,
        },
      });
    },
    onSuccess: (response) => {
      setSelectedCycle(billingCycle);
      refetchProfile();
      const message = (response as unknown as AxiosResponse)?.data?.message || "Billing cycle updated successfully!";
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
      refetchProfile();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const updateTimeThreshold = useMutation({
    mutationFn: async (monitoringThreshold: number) => {
      await ProviderGroupControllerService.updateProviderGroupConfiguration({
        xTenantId: xTenantId,
        requestBody: {
          monitoringThreshold: monitoringThreshold,
          billingCycle: billingCycle as BillingCycle,
          uuid: profile?.uuid,
        },
      });
      refetchProfile();
    },
    onSuccess: (response) => {
      refetchProfile();
      const message = (response as unknown as AxiosResponse)?.data?.message || "Time threshold updated successfully!";
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
      refetchProfile();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const [showSetTimeModal, setShowSetTimeModal] = useState(false);

  const [minutes, setMinutes] = useState("");
  const [seconds, setSeconds] = useState("");

  const handleSave = () => {
    const totalTimeInSeconds = parseInt(minutes || "0") * 60 + parseInt(seconds || "0");
    console.log("Threshold set to (sec):", totalTimeInSeconds);
    updateTimeThreshold.mutate(totalTimeInSeconds);
    setShowSetTimeModal(false);
  };

  const formatTimeThreshold = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;

    if (mins === 0) return `${secs}s`;
    if (secs === 0) return `${mins}m`;
    return `${mins}m ${secs}s`;
  };

  const [confirmModalOpen, setConfirmModalOpen] = useState(false);

  const handleConfirm = () => {
    if (billingCycle) {
      setBillingCycle(billingCycle);
      updateBillingCycle.mutate(billingCycle);
      setConfirmModalOpen(false);
    }
  };

  return (
    <Grid>
      <Typography sx={styles.sectionTitle}>General</Typography>

      <Grid container sx={styles.container}>
        <Grid size={10}>
          <Typography variant="h6" sx={styles.heading}>
            Billing Cycle
          </Typography>

          <Typography sx={{ ...styles.bodyText, mt: 1 }}>
            This setting will apply to all billing-related processes for your provider group.
          </Typography>

          <Typography sx={{ ...styles.bodyText, mt: 0.5 }}>
            Note: by selecting "Recurring Every 30 Days" billing starts on each patient’s onboarding date and repeats
            every 30 days.
          </Typography>
        </Grid>
        <Grid size={2}>
          <CustomSelect
            placeholder={"Select Billing Cycle"}
            items={[
              { value: "ROLLING_30_DAYS", label: "Recurring Every 30 Days" },
              { value: "CALENDAR_MONTH", label: "Recurring Every Calendar Month" },
            ]}
            name={"billingCycle"}
            value={selectedCycle || ""}
            onChange={(value) => {
              setBillingCycle(value?.target?.value as unknown as string);
              setConfirmModalOpen(true);
            }}
          />
        </Grid>
      </Grid>

      <Grid container sx={styles.container} mt={2}>
        <Grid size={9}>
          <Typography variant="h6" sx={styles.heading}>
            Monitoring Time Threshold
          </Typography>

          <Typography sx={{ ...styles.bodyText, mt: 1 }}>
            Minimum time required to start tracking for patient monitoring.
          </Typography>

          <Typography sx={{ ...styles.bodyText, mt: 0.5 }}>
            Time below this threshold will not be counted or eligible for monitoring claims.
          </Typography>
        </Grid>
        <Grid size={3} container alignItems={"center"} justifyContent={"flex-end"}>
          <Typography variant="h6" sx={{ ...styles.bodyText, mr: 1 }}>
            Current : {formatTimeThreshold(timeThreshold || 0)}
          </Typography>
          <Button variant="contained" sx={{ height: "34px" }} onClick={() => setShowSetTimeModal(true)}>
            Set Time
          </Button>
        </Grid>
      </Grid>
      <CustomDialog
        open={showSetTimeModal}
        onClose={() => setShowSetTimeModal(false)}
        title="Set Time Threshold"
        description="(Minimum time before tracking begins)"
        width="448px"
        height="192px"
        borderRadius="12px"
        titleAlign="left"
        showDivider={false}
        buttonName={[]}
        padding={"0"}
      >
        <Box display="flex" gap={2} mt={3} alignItems="center" justifyContent="center" mb={3}>
          <Box display="flex" flexDirection="column">
            <CustomLabel label="Minutes"></CustomLabel>
            <CustomInput
              value={minutes}
              onChange={(e) => setMinutes(e.target.value)}
              placeholder={"00"}
              name={"Minutes"}
            />
          </Box>

          <Typography fontSize={18} fontWeight={400} mt={2}>
            :
          </Typography>

          <Box display="flex" flexDirection="column">
            <CustomLabel label="seconds"></CustomLabel>
            <CustomInput
              value={seconds}
              onChange={(e) => setSeconds(e.target.value)}
              placeholder={"00"}
              name={"Seconds"}
            />
          </Box>
        </Box>
        <Divider
        // sx={{ my: "10px" }}
        />
        <Box display="flex" justifyContent="flex-end" mt={2} mr={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSave}
            sx={{ borderRadius: 2, textTransform: "none" }}
          >
            Save
          </Button>
        </Box>
      </CustomDialog>
      <Dialog
        open={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        maxWidth="sm"
        PaperProps={{
          sx: {
            borderRadius: "16px",
            padding: "24px",
            minWidth: "420px",
          },
        }}
      >
        <DialogTitle sx={{ p: 0 }} width={"448px"}>
          <Typography fontSize={20} fontWeight={500}>
            Confirm Update Billing Cycle
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ p: 0, mt: 1 }}>
          <Typography fontSize={16} fontWeight={400} color="text.secondary">
            Are you sure you want to update your provider group’s <br /> billing cycle to{" "}
            <strong>
              {selectedCycle === "ROLLING_30_DAYS" ? "Recurring Every 30 Days" : "Recurring Every Calendar Month"}
            </strong>
            ?
          </Typography>
        </DialogContent>

        <DialogActions sx={{ p: 0, mt: 3, justifyContent: "flex-end" }}>
          <Box display="flex" gap={2}>
            <Button
              onClick={() => setConfirmModalOpen(false)}
              variant="outlined"
              sx={{ borderRadius: 2, textTransform: "none", px: 3 }}
            >
              Cancel
            </Button>
            <Button onClick={handleConfirm} variant="contained" sx={{ borderRadius: 2, textTransform: "none", px: 3 }}>
              Confirm
            </Button>
          </Box>
        </DialogActions>
      </Dialog>
    </Grid>
  );
};

export default BillingTab;

const styles = {
  sectionTitle: {
    fontFamily: "Roboto",
    fontWeight: 500,
    fontSize: 18,
    color: "#101828",
    marginBottom: 2,
  },
  container: {
    border: 1,
    borderRadius: 2,
    borderColor: "#EAECF0",
    pt: 2,
    pr: 3,
    pb: 2,
    pl: 3,
    justifyContent: "space-between",
    alignItems: "center",
    boxShadow: "0px 0px 16px 0px #021D2614",
  },
  heading: {
    fontWeight: 700,
    color: "#101828",
  },
  bodyText: {
    fontSize: 14,
    fontWeight: 400,
    color: "#515C5F",
  },
};
