import * as yup from "yup";

import {
  addressLine1Max128ErrorMsg,
  addressLine1RequiredErrorMsg,
  cityMax64ErrorMsg,
  cityRequiredErrorMsg,
  cityStateRegexErrorMsg,
  countryRequiredErrorMsg,
  emailRegexErrorMsg,
  emailRequiredErrorMsg,
  lessThan255ErrorMsg,
  nameRequiredErrorMsg,
  phoneRegexErrorMsg,
  phoneRequiredErrorMsg,
  stateRequiredErrorMsg,
  zipCodeRegexErrorMsg,
  zipCodeRequiredErrorMsg,
} from "../../../constants/error-messages";
import { cityStateRgex, emailRegExp, phoneRegex, zipCodeRegex } from "../../../utils/regex";

export const manualEntryFormSchema = yup.object().shape({
  name: yup.string().required(nameRequiredErrorMsg),
  phone: yup.string().required(phoneRequiredErrorMsg).matches(phoneRegex, phoneRegexErrorMsg),
  prefix: yup.string(),
  email: yup.string().required(emailRequiredErrorMsg).matches(emailRegExp, emailRegexErrorMsg),
  status: yup.string().oneOf(["active", "inactive"], "Invalid status"),

  address: yup.object().shape({
    line1: yup.string().max(128, addressLine1Max128ErrorMsg).required(addressLine1RequiredErrorMsg),
    line2: yup.string().max(128, addressLine1Max128ErrorMsg),
    city: yup
      .string()
      .matches(cityStateRgex, cityStateRegexErrorMsg)
      .max(64, cityMax64ErrorMsg)
      .required(cityRequiredErrorMsg),
    state: yup
      .string()
      .matches(cityStateRgex, cityStateRegexErrorMsg)
      .max(50, lessThan255ErrorMsg)
      .required(stateRequiredErrorMsg),
    zipcode: yup.string().required(zipCodeRequiredErrorMsg).matches(zipCodeRegex, zipCodeRegexErrorMsg),
    country: yup.string().required(countryRequiredErrorMsg),
  }),
});
