import { Form<PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { <PERSON><PERSON>, Stack, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQueryClient } from "@tanstack/react-query";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
// import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Input } from "@/components/ui/Form/Input";
import { InputPhoneNumber } from "@/components/ui/Form/InputPhoneNumber";
import Select from "@/components/ui/Form/Select";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useLocationControllerServiceCreateLocation, useLocationControllerServiceUpdateLocation } from "@/sdk/queries";
import { Location } from "@/sdk/requests";
import { splitPhoneNumber } from "@/services/common/phone-formatter";
import { stateList } from "@/utils/StateList";

import { manualEntryFormSchema } from "./location-schema";

export interface LocationDataProp {
  locationData: Location | null;
  isEdit?: boolean;
  xTenantId: string;
  handleDrawerClose: () => void;
}

interface LocationFormValues {
  name: string;
  phone: string;
  prefix?: string;
  address: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    zipcode: string;
    country: string;
  };
  email: string;
  status?: string;
}

const LocationForm = (props: LocationDataProp) => {
  const { handleDrawerClose, locationData, isEdit, xTenantId } = props;
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const initialValues = {
    name: locationData?.name || "",
    phone: locationData?.phone ? splitPhoneNumber(locationData?.phone)?.number : "",
    prefix: locationData?.phone ? splitPhoneNumber(locationData?.phone)?.countryCode : "+1",
    address: {
      line1: locationData?.address?.line1 || "",
      line2: locationData?.address?.line2 || "",
      city: locationData?.address?.city || "",
      state: locationData?.address?.state || "",
      zipcode: locationData?.address?.zipcode || "",
      country: locationData?.address?.country || "USA",
    },
    email: locationData?.email || "",
    status: locationData?.active ? "active" : "inactive",
  };

  const formMethods = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(manualEntryFormSchema),
  });

  const handleOnSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["list-of-locations"] });
    dispatch(
      setSnackbarOn({
        severity: AlertSeverity.SUCCESS,
        message: isEdit ? "Location updated successfully!" : "Location added successfully!",
      })
    );
    handleDrawerClose();
  };

  const addLocationService = useLocationControllerServiceCreateLocation({
    onSuccess: handleOnSuccess,
    onError: (error) => {
      const errorMessage = (error as { body: { message: string } }).body.message;
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: errorMessage }));
    },
  });

  const editLocationService = useLocationControllerServiceUpdateLocation({
    onSuccess: handleOnSuccess,
    onError: (error) => {
      const errorMessage = (error as { body: { message: string } }).body.message;
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: errorMessage }));
    },
  });

  const onSubmit = async (values: LocationFormValues) => {
    const payload: Location = {
      uuid: locationData?.uuid || "",
      name: values.name,
      phone: `${values.prefix}${values.phone}`,
      email: values.email,
      active: !isEdit || values.status === "active",
      address: {
        line1: values.address.line1,
        line2: values.address.line2,
        city: values.address.city,
        state: values.address.state,
        country: values.address.country,
        zipcode: values.address.zipcode,
      },
    };
    if (isEdit) {
      editLocationService.mutate({ requestBody: payload, xTenantId });
    } else {
      addLocationService.mutate({ requestBody: payload, xTenantId });
    }
  };

  return (
    <DrawerBody padding={3} offset={80}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            <Grid container spacing={2}>
              <Grid size={6}>
                <Input name="name" label="Name" isRequired />
              </Grid>
              <Grid size={6}>
                <InputPhoneNumber isRequired />
              </Grid>
              <Grid size={6}>
                <Input name="email" label="Email" isRequired disabled={isEdit} />
              </Grid>
              {isEdit && (
                <Grid size={6}>
                  <Select
                    name="status"
                    label="Status"
                    options={[
                      { value: "active", label: "Active" },
                      { value: "inactive", label: "Inactive" },
                    ]}
                    isRequired
                  />
                </Grid>
              )}
              <Grid size={6}>
                <Input name="address.line1" label="Address Line 1" isRequired />
              </Grid>
              <Grid size={6}>
                <Input name="address.line2" label="Address Line 2" />
              </Grid>
              <Grid size={6}>
                <Select
                  name="address.state"
                  label="State"
                  options={stateList.map((item) => ({ label: item.key, value: item.value }))}
                  isRequired
                />
              </Grid>
              <Grid size={6}>
                <Input name="address.city" label="City" isRequired />
              </Grid>
              <Grid size={6}>
                <Input name="address.zipcode" label="Zip Code" isRequired />
              </Grid>
            </Grid>
          </Stack>
          <DrawerFooter>
            <Button
              variant="contained"
              type="submit"
              disabled={addLocationService.isPending || editLocationService.isPending}
            >
              <Typography>{isEdit ? "Save" : "Add Location"}</Typography>
            </Button>
          </DrawerFooter>
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default LocationForm;
