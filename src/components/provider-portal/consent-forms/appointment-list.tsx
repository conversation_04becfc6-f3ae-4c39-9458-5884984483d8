import { Grid } from "@mui/system";

import CustomSelectorSq from "../../../common-components/custom-selector-sq/custom-selector-sq";
import { theme } from "../../../utils/theme";

const AppointmentList = () => {
  return (
    <>
      <Grid height={"100%"}>
        <Grid
          border={`1px solid ${theme.palette.grey[300]}`}
          boxShadow={`0px 0px 16px 0px #021D2614`}
          height={"100%"}
          borderRadius={"8px"}
          container
          flexDirection={"column"}
        >
          <Grid container p={2} justifyContent={"space-between"} rowGap={2}>
            <Grid container alignItems={"center"} columnGap={2}>
              <CustomSelectorSq
                widthOfBtn={"100px"}
                options={["Upcoming", "Past"]}
                onSelect={() => {}}
                selectedValue={""}
              />
            </Grid>
            <Grid container columnGap={2} width={"600px"} justifyContent={"flex-end"}></Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  );
};

export default AppointmentList;
