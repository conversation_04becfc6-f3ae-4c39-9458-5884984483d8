import { ChangeEvent, useEffect, useState } from "react";
import { <PERSON>, FormProvider, Resolver, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import { Close } from "@mui/icons-material";
import AddIcon from "@mui/icons-material/Add";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import {
  Box,
  Button,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Paper,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Too<PERSON><PERSON>,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";
import { capitalize } from "lodash";
import * as yup from "yup";

import CustomAutoComplete from "@/common-components/custom-auto-complete/custom-auto-complete";
import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import CustomSelect from "@/common-components/custom-select/customSelect";
import CustomSelectorSq from "@/common-components/custom-selector-sq/custom-selector-sq";
import DatePicker from "@/common-components/date-picker-field/date-picker-field";
import Paginator from "@/common-components/paginator/paginator";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import { heading, iconStyles, tableCellCss } from "@/common-components/table/common-table-widgets";

import useAuthority from "@/hooks/use-authority";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { RootState } from "@/redux/store";
import {
  Patient,
  PatientControllerService,
  PatientVital,
  Provider,
  ProviderControllerService,
  Task,
  TaskControllerService,
  UserControllerService,
} from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { formatDateNewFormat } from "@/utils/format/date";

// Import the constant for localStorage key
const SELECTED_VITAL_TYPE_KEY = "selectedVitalType";

type ExtendedTask = Task & {
  patientId?: string;
  patientName?: string;
  assignedName?: string;
  assignedExternal?: boolean;
  assignedByName?: string;
  assignedByExternal?: boolean;
  completedDate?: string;
  created?: string;
  assignedTo?: string;
};

type filterStatus = "Pending" | "Completed";

// Task form schema
const taskFormSchema = yup.object({
  taskTitle: yup.string().required("Task title is required"),
  selectedPatient: yup.string().required("Patient is required"),
  selectedAssignee: yup.string().required("Assignee is required"),
  selectedType: yup.string().required("Type is required"),
  taskPriority: yup.string().oneOf(["HIGH", "MEDIUM", "LOW"], "Priority is required").required("Priority is required"),
  dueDate: yup.string().required("Due date is required"),
  taskNote: yup.string().optional(),
});

type TaskFormValues = {
  taskTitle: string;
  selectedPatient: string;
  selectedAssignee: string;
  selectedType: string;
  taskPriority: string;
  dueDate: string;
  taskNote: string;
};

// headers
const getHeaders = (status: filterStatus) => {
  const baseHeaders = ["Tasks", "Priority", "Patient Name", "Assigned to", "Type", "Assigned Date & Time", "Due Date"];
  return [...baseHeaders, status === "Pending" ? "Action" : "Completed Date"];
};

const convertIstMmDdYyyyToUtc = (istDateStr: string): string => {
  if (!istDateStr) return "";
  const [month, day, year] = istDateStr.split("-").map(Number);
  const targetUtcMidnight = Date.UTC(year, month - 1, day);
  return new Date(targetUtcMidnight).toISOString();
};

function TasksTab() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchTask, setSearchTask] = useState("");
  const [tasks, setTasks] = useState<ExtendedTask[]>([]);
  const [viewFilters, setViewFilters] = useState(false);
  const [openAddTaskDialog, setOpenAddTaskDialog] = useState(false);
  const { isProvider } = useAuthority();

  // Get the current logged-in user profile data based on role
  const { data: currentUserData } = useSelector((state: RootState) =>
    isProvider ? state.providerProfileReducer : state.profileReducer
  );

  const [nurseType, setNurseType] = useState("EAMATA NURSE");
  // Add a new loading state
  // const [_isNurseTypeLoading, setIsNurseTypeLoading] = useState(false);

  // Add state for edit functionality
  const [isEdit, setIsEdit] = useState(false);
  const [selectedEditTask, setSelectedEditTask] = useState<ExtendedTask | null>(null);

  // Pagination state
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState<number>(0);
  const [size, setSize] = useState(10);

  const [statusFilter, setStatusFilter] = useState<filterStatus>("Pending");

  // Resolve task states
  const [selectedTask, setSelectedTask] = useState<ExtendedTask | null>(null);
  const [openResolveTaskDialog, setOpenResolveTaskDialog] = useState(false);
  const [resolveNote, setResolveNote] = useState("");
  // Add state for patient vital data
  const [taskVitalData, setTaskVitalData] = useState<PatientVital | null>(null);
  const [loadingVitalData, setLoadingVitalData] = useState(false);
  // Add state for detailed task data
  const [taskDetailedData, setTaskDetailedData] = useState<ExtendedTask | null>(null);

  // Options for dropdowns
  const [patientsOptions, setPatientsOptions] = useState<{ key: string; value: string }[]>([]);
  const [nurseOptions, setNurseOptions] = useState<{ key: string; value: string }[]>([]);

  // Add new state for filter
  const [filterPriority, setFilterPriority] = useState<string>("");
  const [filterTaskType, setFilterTaskType] = useState<string>("");
  const [filterAssignedDate, setFilterAssignedDate] = useState<string>("");
  const [filterDueDate, setFilterDueDate] = useState<string>("");
  const [filterAssignee, setFilterAssignee] = useState<string | undefined>(undefined);

  // Add new state for task detail loading
  const [taskDetailLoading, setTaskDetailLoading] = useState(false);

  const methods = useForm<TaskFormValues>({
    resolver: yupResolver(taskFormSchema) as Resolver<TaskFormValues>,
    defaultValues: {
      taskTitle: "",
      selectedPatient: "",
      selectedAssignee: "",
      taskPriority: "",
      dueDate: "",
      taskNote: "",
    },
  });

  const {
    handleSubmit,
    reset,
    control,
    setValue,
    watch,
    formState: { errors },
  } = methods;

  // CSS styles
  const typographyCss = {
    fontFamily: "Roboto",
    fontWeight: 400,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "0%",
    color: "#212D30",
  };

  // API call to get tasks
  const { data, isLoading, isSuccess, isRefetching, refetch } = useQuery({
    queryKey: [
      "tasks",
      page,
      size,
      searchTask,
      statusFilter,
      filterAssignee,
      filterPriority,
      filterTaskType,
      filterAssignedDate,
      filterDueDate,
      currentUserData,
    ],
    queryFn: () => {
      const apiParams: {
        xTenantId: string;
        sortBy?: string;
        sortDirection?: string;
        page: number;
        size: number;
        searchString?: string;
        status?: "PENDING" | "COMPLETED" | "DISCARDED";
        assignedTo?: string;
        priority?: "HIGH" | "MEDIUM" | "LOW" | "CRITICAL";
        type?: string;
        createdDateStart?: string;
        dueDate?: string;
        assignedDate?: string;
      } = {
        xTenantId: GetTenantId(),

        page,
        size,
        sortBy: "modified",
        assignedTo: currentUserData?.userId,
        sortDirection: "desc",
        status: statusFilter.toUpperCase() as "PENDING" | "COMPLETED" | "DISCARDED",
      };

      if (searchTask && searchTask.trim() !== "") {
        apiParams.searchString = searchTask;
      }

      if (filterAssignee && filterAssignee.trim() !== "") {
        apiParams.assignedTo = filterAssignee;
      }

      if (
        filterPriority &&
        filterPriority.trim() !== "" &&
        ["HIGH", "MEDIUM", "LOW", "CRITICAL"].includes(filterPriority)
      ) {
        apiParams.priority = filterPriority as "HIGH" | "MEDIUM" | "LOW" | "CRITICAL";
      }

      if (filterTaskType && filterTaskType.trim() !== "") {
        apiParams.type = filterTaskType;
      }

      if (filterDueDate && filterDueDate.trim() !== "") {
        apiParams.dueDate = convertIstMmDdYyyyToUtc(filterDueDate);
      }

      if (filterAssignedDate && filterAssignedDate.trim() !== "") {
        apiParams.assignedDate = convertIstMmDdYyyyToUtc(filterAssignedDate);
        apiParams.createdDateStart = new Date(filterAssignedDate).toISOString();
      }

      return TaskControllerService.getAllTasks(apiParams);
    },
    enabled: currentUserData?.userId !== undefined || true,
  });

  const { data: getUserIdByProviderData, isSuccess: isSuccessGetUserIdByProviderData } = useQuery({
    enabled: !!watch("selectedAssignee"),
    queryKey: [GetTenantId(), watch("selectedAssignee"), nurseType],
    queryFn: () =>
      ProviderControllerService.getUserIdByProvider({
        userId: watch("selectedAssignee"),
        xTenantId: nurseType == "EAMATA NURSE" ? "eamata" : GetTenantId(),
      }),
  });

  // Get patients list
  const {
    data: dataPatientList,
    isSuccess: isSuccessPatientList,
    isLoading: isPatientLoading,
  } = useQuery({
    queryKey: [openAddTaskDialog, isSuccessGetUserIdByProviderData && watch("selectedAssignee"), nurseType],
    queryFn: () =>
      PatientControllerService.getAllPatient({
        page: 0,
        size: 200,
        archive: false,
        status: true,
        nurseId:
          nurseType == "EAMATA NURSE" || nurseType == "PROVIDER GROUP NURSE"
            ? (getUserIdByProviderData?.data?.uuid as string)
            : undefined,
        providerId: nurseType === "PROVIDER" ? (getUserIdByProviderData?.data?.uuid as string) : undefined,
        xTenantId: nurseType == "EAMATA NURSE" ? "eamata" : GetTenantId(),
      }),
  });

  useEffect(() => {
    setPatientsOptions([]);
  }, [isPatientLoading]);

  // Get nurses list
  const getListOfNurses = async () => {
    const res = await UserControllerService.getAllUsers({
      page: 0,
      size: 100,
      sortBy: "modified",
      sortDirection: "desc",
      role:
        nurseType == "EAMATA NURSE" || nurseType == "PROVIDER GROUP NURSE"
          ? "NURSE"
          : nurseType == "PROVIDER"
            ? "PROVIDER"
            : undefined,
      roleType:
        nurseType == "EAMATA NURSE" || nurseType == "PROVIDER GROUP NURSE"
          ? "PROVIDER"
          : nurseType == "PROVIDER"
            ? "PROVIDER"
            : undefined,
      status: true,
      archive: false,
      xTenantId: nurseType == "EAMATA NURSE" ? "eamata" : GetTenantId(),
    });

    const data = (res as unknown as AxiosResponse).data as ContentObject<Provider[]>;

    const options = data.content.map((item) => ({
      key: item.uuid || "",
      value: `${item.firstName} ${item.lastName}`,
    }));
    // setIsNurseTypeLoading(false);
    setNurseOptions(options);
  };

  // Process data when API returns successfully
  useEffect(() => {
    if (isSuccess && data) {
      const response = (data as unknown as AxiosResponse).data as ContentObject<ExtendedTask[]>;
      const tasksData = response?.content;
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);
      setTasks(tasksData || []);
    }
  }, [data, isSuccess]);

  useEffect(() => {
    if (isSuccessPatientList) {
      const response = (dataPatientList as unknown as AxiosResponse).data as ContentObject<Patient[]>;
      const patientData = response?.content;
      const options = patientData.map((patient) => ({
        key: patient.uuid || "",
        value: `${patient.firstName} ${patient.lastName}`,
      }));
      setPatientsOptions(options);
    }
  }, [dataPatientList, isSuccessPatientList]);

  useEffect(() => {
    getListOfNurses();
  }, [nurseType]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handlePageChange = (_event: ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage);
  };

  const handleFilterChange = (filterField: string) => {
    setStatusFilter(filterField as filterStatus);
    setPage(0);
    setSize(10);
    setViewFilters(false);
    handleClearFilters();
  };

  // Format date and time
  const formatDateTime = (dateString: string | null | undefined) => {
    if (!dateString) return "-";
    return format(new Date(dateString), " dd MMM yyyy, h:mm a");
  };

  // Format date
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "-";
    return format(new Date(dateString), " dd MMM yyyy");
  };

  // Check if a date is past due
  const isPastDue = (dateString: string | null | undefined) => {
    if (!dateString) return false;
    const dueDate = new Date(dateString);
    dueDate.setHours(0, 0, 0, 0);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return dueDate < today;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "HIGH":
        return { text: "High", color: "#CE0718" };
      case "MEDIUM":
        return { text: "Medium", color: "#D66F00" };
      case "LOW":
        return { text: "Low", color: "#049B22" };

      case "MODERATE":
        return { text: "Medium", color: "#D66F00" };
      case "CRITICAL":
        return { text: "High", color: "#CE0718" };

      default:
        return { text: priority, color: "gray" };
    }
  };

  const handleOnClickFilters = () => {
    setViewFilters((prev) => !prev);
  };

  useEffect(() => {
    // setIsNurseTypeLoading(true);
    setNurseType(watch("selectedType"));
  }, [watch("selectedType")]);

  const handleClearFilters = () => {
    // Clear the filters
    setFilterAssignee(undefined);
    setFilterPriority("");
    setFilterTaskType("");
    setFilterAssignedDate("");
    setFilterDueDate("");
    setValue("selectedType", "");
    setValue("selectedAssignee", "");
    refetch();
  };

  // const handleClickOnSearch = () => {
  //   // Refetch data with current filters
  //   refetch();
  // };

  const handleCloseTaskDialog = () => {
    setOpenAddTaskDialog(false);
    setIsEdit(false);
    setSelectedEditTask(null);
    reset();
  };

  // Helper function to populate form with data from the task list
  const populateFormWithListData = (task: ExtendedTask) => {
    setValue("taskTitle", task.title || "");
    setValue("selectedPatient", task.patientId || task.patientUuid || "");
    setValue("selectedAssignee", task.assignTo || "");
    setValue("taskPriority", task.priority || "");

    if (task.dueDate) {
      const date = new Date(task.dueDate);
      setValue("dueDate", format(date, "MM-dd-yyyy"), { shouldValidate: true });
    }

    setValue("taskNote", task.note || "");
  };

  // Update function to handle edit button click to fetch task details
  const handleEditTask = async (task: ExtendedTask) => {
    try {
      // Set initial states
      setSelectedEditTask(task);
      setIsEdit(true);
      setOpenAddTaskDialog(true);
      setTaskDetailLoading(true);

      try {
        // Fetch task details by UUID
        const response = await TaskControllerService.getTaskByUuid({
          taskUuid: task.uuid || "",
          xTenantId: GetTenantId(),
        });

        // Process response with proper types
        const responseData = response.data;
        if (responseData) {
          // Extract task detail from response
          const taskDataContent = responseData;

          if (taskDataContent) {
            const taskDetail = taskDataContent;

            // Populate form with task data from API
            setValue("taskTitle", taskDetail.title?.toString() || "");
            setValue("selectedPatient", taskDetail.patientId?.toString() || taskDetail.patientUuid?.toString() || "");
            setValue("selectedAssignee", taskDetail.assignTo?.toString() || "");
            setValue("taskPriority", taskDetail.priority?.toString() || "");
            setValue("taskNote", taskDetail.description?.toString() || "");

            setValue(
              "selectedType",
              taskDetail?.role === "NURSE"
                ? taskDetail?.assignedExternal === true
                  ? "EAMATA NURSE"
                  : "PROVIDER GROUP NURSE"
                : taskDetail?.roleType === "STAFF"
                  ? "STAFF"
                  : "PROVIDER"
            );

            // Format and set the due date
            if (taskDetail.dueDate && typeof taskDetail.dueDate === "string") {
              const date = new Date(taskDetail.dueDate);
              setValue("dueDate", format(date, "MM-dd-yyyy"), { shouldValidate: true });
            }

            // Update selected task with detailed data
            setSelectedEditTask({
              ...task,
              ...taskDetail,
            });
          } else {
            // Fallback to list data if API doesn't return expected data
            populateFormWithListData(task);
          }
        } else {
          // Fallback to list data if API doesn't return expected structure
          populateFormWithListData(task);
        }
      } catch (error) {
        // Handle error and use the list item data as fallback
        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.ERROR,
            message: "Error fetching task details. Using available data.",
          })
        );
        populateFormWithListData(task);
      }
    } finally {
      setTaskDetailLoading(false);
    }
  };

  // Handle task resolution
  const handleResolveClick = async (task: ExtendedTask) => {
    setSelectedTask(task);
    setResolveNote("");
    setTaskVitalData(null);
    setTaskDetailedData(null);
    setLoadingVitalData(true);
    setOpenResolveTaskDialog(true);

    try {
      // Fetch task details including vital data
      const response = await TaskControllerService.getTaskByUuid({
        taskUuid: task.uuid || "",
        xTenantId: selectedTask?.assignedExternal ? "eamata" : GetTenantId(),
      });

      if (response.data) {
        setTaskDetailedData(response.data as unknown as Task);

        // Set vital data if available
        if (response.data.patientVital) {
          setTaskVitalData(response.data.patientVital as PatientVital);
        }
      }
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: "Error fetching task details",
        })
      );
    } finally {
      setLoadingVitalData(false);
    }
  };

  const resolveTask = async () => {
    if (!selectedTask) return;

    try {
      await TaskControllerService.updateTaskStatus({
        xTenantId: GetTenantId(),
        taskUuid: selectedTask.uuid || "",
        status: "COMPLETED" as "PENDING" | "COMPLETED" | "DISCARDED",
        note: resolveNote,
      });

      // Show success message
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: "Task resolved successfully",
        })
      );

      // Close dialog and refresh
      setOpenResolveTaskDialog(false);
      setSelectedTask(null);
      refetch();
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message || "Error resolving task",
        })
      );
    }
  };

  // Handle View Vitals button click to navigate to patient vitals tab
  const handleViewVitalsClick = () => {
    if (selectedTask && taskVitalData) {
      const patientId = selectedTask.patientUuid || selectedTask.patientId;
      const vitalName = taskVitalData.vitalName;

      if (patientId && vitalName) {
        localStorage.setItem(SELECTED_VITAL_TYPE_KEY, vitalName);
        setOpenResolveTaskDialog(false);
        navigate(`/provider/patients/${patientId}/profile?tab=vitals`);
      } else {
        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.ERROR,
            message: "Missing patient information or vital data",
          })
        );
      }
    }
  };

  const handleNavigateToCreateAppointment = () => {
    if (selectedTask) {
      navigate(`/provider/scheduling?tab=UPCOMING&icon=list&createNew=true`);
    }
  };

  const handleDisabled = () => {
    if (
      watch("taskTitle") !== "" &&
      watch("selectedPatient") !== "" &&
      watch("selectedAssignee") !== "" &&
      watch("taskPriority") !== "" &&
      watch("dueDate") !== "" &&
      watch("selectedType") !== ""
    ) {
      return false;
    }
    return true;
  };

  type VitalType =
    | "Blood Glucose"
    | "Heart Rate"
    | "Weight"
    | "ECG"
    | "HRV"
    | "Oxygen Saturation"
    | "Stress"
    | "Blood Pressure";

  const getVitalUnit = (vitalType: VitalType | string): string => {
    const unitMap: Record<VitalType, string> = {
      "Blood Glucose": "mg/dL",
      "Heart Rate": "bpm",
      Weight: "kg",
      ECG: "bpm",
      HRV: "ms",
      "Oxygen Saturation": "%",
      Stress: "%",
      "Blood Pressure": "mmHg",
    };

    return unitMap[vitalType as VitalType] || "";
  };

  const onSubmitTask = async (data: TaskFormValues) => {
    try {
      if (isEdit && selectedEditTask) {
        // Handle edit task submission
        await TaskControllerService.updateTask({
          xTenantId: GetTenantId(),
          // xTenantId:
          //   nurseType === "EAMATA NURSE"
          //     ? "eamata"
          //     : nurseType === "PROVIDER GROUP NURSE" || nurseType === "STAFF" || nurseType === "PROVIDER"
          //       ? GetTenantId()
          //       : undefined,
          requestBody: {
            uuid: selectedEditTask.uuid,
            type: selectedEditTask.type || "MANUAL",
            title: data.taskTitle,
            priority: data.taskPriority as "HIGH" | "MEDIUM" | "LOW",
            patientUuid: data.selectedPatient,
            assignTo: data.selectedAssignee,
            dueDate: convertIstMmDdYyyyToUtc(data.dueDate),
            description: data.taskNote,
            status: selectedEditTask.status as "PENDING" | "COMPLETED" | "DISCARDED",
            assignedExternal:
              nurseType === "EAMATA NURSE"
                ? true
                : nurseType === "PROVIDER GROUP NURSE" || nurseType === "STAFF" || nurseType === "PROVIDER"
                  ? false
                  : true,
            role:
              nurseType == "EAMATA NURSE" || nurseType == "PROVIDER GROUP NURSE"
                ? "NURSE"
                : nurseType == "PROVIDER"
                  ? "PROVIDER"
                  : undefined,
            roleType: nurseType == "PROVIDER" ? "PROVIDER" : nurseType == "STAFF" ? "STAFF" : undefined,
          },
        });

        // Show success message
        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.SUCCESS,
            message: "Task updated successfully",
          })
        );
      } else {
        // Handle new task submission (existing code)
        await TaskControllerService.addTask({
          xTenantId: GetTenantId(),
          requestBody: {
            type: "MANUAL",
            title: data.taskTitle,
            priority: data.taskPriority as "HIGH" | "MEDIUM" | "LOW",
            patientUuid: data.selectedPatient,
            assignTo: data.selectedAssignee,
            dueDate: convertIstMmDdYyyyToUtc(data.dueDate),
            description: data.taskNote,
            status: "PENDING",
            role:
              nurseType == "EAMATA NURSE" || nurseType == "PROVIDER GROUP NURSE"
                ? "NURSE"
                : nurseType == "PROVIDER"
                  ? "PROVIDER"
                  : undefined,
            roleType: nurseType == "PROVIDER" ? "PROVIDER" : nurseType == "STAFF" ? "STAFF" : undefined,
            assignedExternal:
              nurseType === "EAMATA NURSE"
                ? true
                : nurseType === "PROVIDER GROUP NURSE" || nurseType === "STAFF" || nurseType === "PROVIDER"
                  ? false
                  : true,
          },
        });

        // Show success message
        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.SUCCESS,
            message: "Task created successfully",
          })
        );
      }

      setOpenAddTaskDialog(false);
      setIsEdit(false);
      setSelectedEditTask(null);
      reset();

      refetch();
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message || "Error processing task",
        })
      );
    }
  };

  const [openTaskDetailsDialog, setOpenTaskDetailsDialog] = useState(false);
  const [taskDetails, setTaskDetails] = useState<Task>({} as Task);
  const fetchTaskDetails = async (taskUuid: string) => {
    try {
      const response = await TaskControllerService.getTaskByUuid({
        taskUuid: taskUuid || "",
        xTenantId: GetTenantId(),
      });
      return response.data;
    } catch (error) {
      return null;
    }
  };

  const handleTaskClick = async (task: Task) => {
    const data = await fetchTaskDetails(task.uuid || "");
    if (data) {
      setTaskDetails(data as Task);
    } else {
      setTaskDetails({} as Task);
    }
    setOpenTaskDetailsDialog(true);
  };

  return (
    <Grid
      border={"1px solid #EAECF0"}
      borderRadius={2}
      margin={2}
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Grid mb={1} px={2} pt={2}>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid>
            <CustomSelectorSq
              options={["Pending", "Completed"]}
              onSelect={handleFilterChange}
              selectedValue={statusFilter || "Pending"}
              widthOfBtn="100px"
            />
          </Grid>
          <Grid container alignItems="center" justifyContent="flex-end" spacing={1}>
            <Grid display="flex" alignItems="center">
              <Grid mr={2}>
                {viewFilters ? (
                  <Grid columnGap={2} rowGap={2} container sx={{ width: "120px" }} mr={3}>
                    <Button onClick={handleOnClickFilters} variant="outlined">
                      <Typography fontWeight={550} variant="bodySmall" sx={{ padding: "6px 0px" }}>
                        Hide Filters
                      </Typography>
                    </Button>
                  </Grid>
                ) : (
                  <IconButton onClick={() => setViewFilters(true)}>
                    <Grid container border={"1px solid #B6C1C4"} p={1} borderRadius={2}>
                      <FilterAltOutlinedIcon />
                    </Grid>
                  </IconButton>
                )}
              </Grid>

              <CustomInput
                placeholder="Search Tasks"
                name="task"
                hasStartSearchIcon={true}
                value={searchTask}
                onDebounceCall={(searchString) => setSearchTask(searchString)}
                onInputEmpty={() => setSearchTask("")}
              />
            </Grid>
            <Grid>
              <Button startIcon={<AddIcon />} onClick={() => setOpenAddTaskDialog(true)} variant="contained">
                <Typography variant="bodySmall">Add New Task</Typography>
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Collapse in={viewFilters} timeout={500} sx={{ marginBottom: "10px" }}>
        <Grid
          container
          mt={2}
          columnGap={2}
          rowGap={2}
          padding={2}
          size={12}
          sx={{
            border: "1px solid #EAECF0",
            backgroundColor: "#F2F7F9",
            margin: "19px",
            borderRadius: "10px",
          }}
        >
          <Grid size={3}>
            <CustomLabel label="Priority" />
            <CustomSelect
              placeholder="Filter by Priority"
              name="priority"
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value)}
              items={[
                { value: "HIGH", label: "High" },
                { value: "MEDIUM", label: "Medium" },
                { value: "LOW", label: "Low" },
              ]}
              enableDeselect
            />
          </Grid>
          <Grid size={3}>
            <CustomLabel label="Task Type" />
            <CustomSelect
              placeholder="Filter by Task Type"
              name="taskType"
              value={filterTaskType}
              onChange={(e) => setFilterTaskType(e.target.value)}
              items={[
                { value: "APPOINMENT", label: "Appoinment" },
                { value: "MANUAL", label: "Manual" },
                { value: "ALERT", label: "Alert" },
              ]}
              enableDeselect
            />
          </Grid>
          <Grid size={3}>
            <CustomLabel label="Assigned Date" />
            <DatePicker bgWhite value={filterAssignedDate} onDateChange={(date) => setFilterAssignedDate(date)} />
          </Grid>
          <Grid size={3}>
            <CustomLabel label="Due Date" />
            <DatePicker bgWhite value={filterDueDate} onDateChange={(date) => setFilterDueDate(date)} />
          </Grid>

          {currentUserData?.role !== "PROVIDER" && (
            <>
              <Grid size={3}>
                {/* Changed size to item and added item */}
                <Controller
                  name="selectedType"
                  control={control}
                  render={({ field }) => (
                    <Grid container size={12} direction="column">
                      {" "}
                      {/* This inner grid helps align label and autocomplete */}
                      <CustomLabel label="Assign Type" isRequired />
                      <CustomAutoComplete
                        placeholder="Select Type"
                        bgWhite={true}
                        options={["eAmata Nurse", "Provider Group Nurse", "Staff", "Provider"].map((item) => ({
                          key: item.toUpperCase(),
                          value: item,
                        }))}
                        value={field.value}
                        onChange={(value) => field.onChange(value)}
                      />
                    </Grid>
                  )}
                />
              </Grid>

              <Grid size={3} ml={1}>
                <Controller
                  name="selectedAssignee"
                  control={control}
                  render={({ field }) => (
                    <Grid container size={12} direction="column">
                      <CustomLabel label="Assign to" isRequired />
                      <CustomAutoComplete
                        placeholder="Select Assignee"
                        bgWhite={true}
                        options={nurseOptions}
                        value={field.value}
                        onChange={(selectedValue) => {
                          setFilterAssignee(selectedValue);
                          field.onChange(selectedValue);
                        }}
                      />
                    </Grid>
                  )}
                />
              </Grid>
            </>
          )}
          <Grid container justifyContent="flex-end" mt={3} spacing={1}>
            {/* <Grid>
              <Button variant="contained" onClick={handleClickOnSearch} sx={{ marginLeft: "16px" }}>
                <Typography fontWeight={550} variant="bodySmall">
                  Search
                </Typography>
              </Button>
            </Grid> */}
            <Grid>
              <Button variant="outlined" onClick={handleClearFilters}>
                <Typography fontWeight={550} variant="bodySmall">
                  Clear Filters
                </Typography>
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Collapse>
      <Grid sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
        <TableContainer sx={{ overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {getHeaders(statusFilter).map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    <Typography fontWeight={550} variant="bodySmall" color="#667085" sx={{ fontStyle: "Roboto" }}>
                      {header}
                    </Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading || isRefetching ? (
                [...Array(5)].map((_, index) => (
                  <TableRow key={index}>
                    {[...Array(8)].map((_, cellIndex) => (
                      <TableCell key={cellIndex}>
                        <Skeleton variant="text" width={100} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : tasks.length > 0 ? (
                tasks.map((task, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography
                        sx={{
                          ...typographyCss,
                          color: "#0078D7",
                          cursor: "pointer",
                          fontWeight: 550,
                          fontSize: "14px",
                        }}
                        variant="bodySmall"
                        onClick={() => {
                          handleTaskClick(task);
                        }}
                      >
                        {task.title}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={{ ...typographyCss, color: getPriorityColor(task.priority).color }}>
                        {getPriorityColor(task.priority).text}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{task.patientName || "-"}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{task.assignedName || "-"}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{capitalize(task.type) || "-"}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{formatDateTime(task.created)}</Typography>
                    </TableCell>
                    <TableCell>
                      {/* check if the due date is past due */}
                      {isPastDue(task.dueDate) ? (
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <ErrorOutlineIcon sx={{ color: "#B1000F", mr: 0.5, fontSize: "1rem" }} />
                          <Typography sx={{ ...typographyCss, color: "#B1000F" }}>
                            {formatDate(task.dueDate)}
                          </Typography>
                        </Box>
                      ) : (
                        // if not past due, show the due date
                        <Typography sx={typographyCss}>{formatDate(task.dueDate)}</Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {statusFilter === "Pending" ? (
                        <Grid container>
                          {(task?.assignedTo == currentUserData?.userId ||
                            task?.assignedTo == currentUserData?.uuid) && (
                            <Typography
                              sx={{
                                color: "#0078D7",
                                cursor: "pointer",
                                fontWeight: 550,
                                fontSize: "14px",
                                marginRight: "8px",
                                display: "flex",

                                // border right
                                alignItems: "center",
                                borderRight: "1px solid #EAECF0",
                                paddingRight: "8px",
                              }}
                              variant="bodySmall"
                              onClick={() => handleResolveClick(task)}
                            >
                              Resolve
                            </Typography>
                          )}

                          {task?.type == "MANUAL" && (
                            <Tooltip title="Edit" placement="top">
                              <IconButton
                                sx={{ padding: "0px 5px" }}
                                aria-label="edit"
                                onClick={() => handleEditTask(task)}
                              >
                                <EditOutlinedIcon sx={iconStyles} />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Grid>
                      ) : (
                        <Typography sx={typographyCss}>{formatDateTime(task.completedDate)}</Typography>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    <Typography>No tasks found</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <Grid
          container
          justifyContent={"flex-end"}
          sx={{ borderTop: "1px solid #EAECF0", borderBottom: "1px solid #EAECF0" }}
        >
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElements}
            onPageChange={handlePageChange}
            onRecordsPerPageChange={handleRecordsPerPageChange}
          />
        </Grid>
      </Grid>
      <FormProvider {...methods}>
        <CustomDialog
          buttonName={["save"]}
          borderRadius="15px"
          open={openAddTaskDialog}
          title={isEdit ? "Edit Task" : "Add New Task"}
          onClose={handleCloseTaskDialog}
          sx={{
            "& .MuiDialog-paper": {
              overflow: "visible",
              paddingTop: "20px",
            },
          }}
        >
          {taskDetailLoading ? (
            <Box
              display="flex"
              flexDirection="column"
              justifyContent="center"
              alignItems="center"
              minHeight="300px"
              width="700px"
              padding={2}
            >
              <CircularProgress size={40} />
            </Box>
          ) : (
            <form onSubmit={handleSubmit(onSubmitTask)}>
              <Grid container spacing={2} pt={2} width={"700px"}>
                <Grid container size={12}>
                  <Controller
                    name="taskTitle"
                    control={control}
                    render={({ field }) => (
                      <Grid container size={12} direction="column">
                        <CustomLabel label="Task Title" isRequired />
                        <CustomInput
                          name="taskTitle"
                          placeholder="Enter Task Title"
                          value={field.value}
                          onChange={(e) => field.onChange(e.target.value)}
                          hasError={!!errors.taskTitle}
                          errorMessage={errors.taskTitle?.message}
                        />
                      </Grid>
                    )}
                  />
                </Grid>

                <Grid container size={12} display="flex" alignItems="center" spacing={2}>
                  {" "}
                  {/* Added spacing for separation */}
                  <Grid size={6}>
                    {" "}
                    {/* Changed size to item and added item */}
                    <Controller
                      name="selectedType"
                      control={control}
                      render={({ field }) => (
                        <Grid container size={12} direction="column">
                          {" "}
                          {/* This inner grid helps align label and autocomplete */}
                          <CustomLabel label="Assign Type" isRequired />
                          <CustomAutoComplete
                            placeholder="Select Roles Type"
                            options={["eAmata Nurse", "Provider Group Nurse", "Staff", "Provider"].map((item) => ({
                              key: item.toUpperCase(),
                              value: item,
                            }))}
                            value={field.value}
                            onChange={(value) => field.onChange(value)}
                          />
                        </Grid>
                      )}
                    />
                  </Grid>
                  <Grid size={6}>
                    {" "}
                    {/* Changed size to item and added item */}
                    <Controller
                      name="selectedAssignee"
                      control={control}
                      render={({ field }) => (
                        <Grid container size={12} direction="column">
                          <CustomLabel label="Assign to" isRequired />
                          <CustomAutoComplete
                            placeholder="Select Assignee"
                            options={nurseOptions}
                            // loading={isNurseTypeLoading}
                            value={field.value}
                            onChange={(value) => field.onChange(value)}
                            hasError={!!errors.selectedAssignee}
                            errorMessage={errors.selectedAssignee?.message}
                          />
                        </Grid>
                      )}
                    />
                  </Grid>
                </Grid>

                <Grid size={12}>
                  <Controller
                    name="selectedPatient"
                    control={control}
                    render={({ field }) => (
                      <Grid container size={12} direction="column">
                        <CustomLabel label="Patient Name" isRequired />
                        <CustomAutoComplete
                          placeholder="Select Patient Name"
                          loading={isPatientLoading}
                          options={patientsOptions}
                          value={field.value}
                          onChange={(value) => field.onChange(value)}
                          hasError={!!errors.selectedPatient}
                          errorMessage={errors.selectedPatient?.message}
                        />
                      </Grid>
                    )}
                  />
                </Grid>

                <Grid size={6}>
                  <Controller
                    name="taskPriority"
                    control={control}
                    render={({ field }) => (
                      <Grid container size={12} direction="column">
                        <CustomLabel label="Priority" isRequired />
                        <CustomSelect
                          name="taskPriority"
                          placeholder="Assign Priority"
                          value={field.value}
                          onChange={(e) => field.onChange(e.target.value)}
                          items={[
                            { value: "HIGH", label: "High" },
                            { value: "MEDIUM", label: "Medium" },
                            { value: "LOW", label: "Low" },
                          ]}
                          hasError={!!errors.taskPriority}
                          errorMessage={errors.taskPriority?.message}
                        />
                      </Grid>
                    )}
                  />
                </Grid>
                <Grid size={6}>
                  <Controller
                    name="dueDate"
                    control={control}
                    render={({ field }) => (
                      <Grid container size={12} direction="column">
                        <CustomLabel label="Due Date" isRequired />
                        <DatePicker
                          value={field.value}
                          onDateChange={(date) => field.onChange(date)}
                          disablePast
                          bgWhite
                          hasError={!!errors.dueDate}
                          errorMessage={errors.dueDate?.message}
                        />
                      </Grid>
                    )}
                  />
                </Grid>

                <Grid size={12}>
                  <Controller
                    name="taskNote"
                    control={control}
                    render={({ field }) => (
                      <Grid container size={12} direction="column">
                        <CustomLabel label="Description" />
                        <CustomInput
                          name="taskNote"
                          placeholder="Enter Description"
                          value={field.value}
                          onChange={(e) => field.onChange(e.target.value)}
                          multiline
                          rows={3}
                        />
                      </Grid>
                    )}
                  />
                </Grid>

                <Grid width={"100%"} borderTop={"1px solid #EAECF0"} pt={2}>
                  <Grid container justifyContent="flex-end">
                    <Button
                      disabled={handleDisabled()}
                      sx={{
                        width: "100px",
                        height: "40px",
                        borderRadius: "8px",
                        backgroundColor: "#006D8F",
                        color: "white",
                      }}
                      variant="contained"
                      type="submit"
                    >
                      Save
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </form>
          )}
        </CustomDialog>
      </FormProvider>

      {/* Resolve Task Dialog */}
      <Dialog
        open={openResolveTaskDialog}
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: "15px",
            width: "700px",
            p: 2,
          },
        }}
      >
        <DialogTitle sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", p: 2 }}>
          <Typography variant="h6" pl={0} fontWeight={600}>
            Resolve Task - Alert
          </Typography>
          <IconButton onClick={() => setOpenResolveTaskDialog(false)}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <Divider />
        <DialogContent sx={{ p: 2 }}>
          {loadingVitalData ? (
            <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
              <CircularProgress size={30} />
            </Box>
          ) : (
            taskDetailedData && (
              <>
                <Paper
                  elevation={0}
                  sx={{
                    bgcolor: "#F0F7FF",
                    p: 2,
                    borderRadius: "8px",
                    mb: 2,
                  }}
                >
                  <Grid container spacing={2}>
                    <Grid size={8}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Task
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {taskDetailedData.title || selectedTask?.title}
                      </Typography>
                    </Grid>
                    <Grid size={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Priority
                      </Typography>
                      <Typography
                        variant="body1"
                        fontWeight={500}
                        color={getPriorityColor(taskDetailedData.priority || selectedTask?.priority).color}
                      >
                        {getPriorityColor(taskDetailedData.priority || selectedTask?.priority).text}
                      </Typography>
                    </Grid>

                    <Grid size={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Patient Name
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {taskDetailedData.patientName || selectedTask?.patientName || "-"}
                      </Typography>
                    </Grid>
                    <Grid size={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Assigned to
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {taskDetailedData.assignedName || selectedTask?.assignedName || "-"}
                      </Typography>
                    </Grid>
                    <Grid size={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Assigned Date
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {formatDateTime(taskDetailedData?.created || selectedTask?.created)}
                      </Typography>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Vital information display (if available) */}
                {taskVitalData ? (
                  <Paper
                    elevation={0}
                    sx={{
                      bgcolor: "#FEF3F2",
                      p: 2,
                      borderRadius: "8px",
                      mb: 2,
                      border: "1px solid #FEE4E2",
                    }}
                  >
                    <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                      <Typography variant="subtitle1" fontWeight={600} mr={1}>
                        {taskVitalData.vitalName} Reading
                      </Typography>
                      <ErrorOutlineIcon sx={{ color: "#B1000F", fontSize: "1.2rem" }} />
                    </Box>

                    <Grid container spacing={2}>
                      <Grid size={4}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Reading
                        </Typography>
                        <Typography variant="body1" fontWeight={600} color="#B1000F">
                          {taskVitalData.value1} {taskVitalData.unit || "mmHg"}
                        </Typography>
                      </Grid>
                      <Grid size={4}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Device
                        </Typography>
                        <Typography variant="body1" fontWeight={500}>
                          {taskVitalData.integrationType || "Assigned Device"}
                        </Typography>
                      </Grid>
                      <Grid size={4}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Recorded Date
                        </Typography>
                        <Typography variant="body1" fontWeight={500}>
                          {taskVitalData.recordedDate ? formatDateTime(taskVitalData.recordedDate) : "-"}
                        </Typography>
                      </Grid>

                      {(taskVitalData.minRange !== null || taskVitalData.maxRange !== null) && (
                        <Grid size={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Normal Range
                          </Typography>
                          <Typography variant="body1" fontWeight={500}>
                            {taskVitalData.minRange !== null && taskVitalData.maxRange !== null
                              ? `${taskVitalData.minRange} - ${taskVitalData.maxRange}`
                              : taskVitalData.minRange !== null
                                ? `Min: ${taskVitalData.minRange}`
                                : `Max: ${taskVitalData.maxRange}`}
                          </Typography>
                        </Grid>
                      )}

                      {taskVitalData.note && (
                        <Grid size={12}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Vital Note
                          </Typography>
                          <Typography variant="body1" fontWeight={500} sx={{ whiteSpace: "pre-wrap" }}>
                            {String(taskVitalData?.note?.name)}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </Paper>
                ) : null}

                <Box sx={{ mb: 1 }}>
                  <Grid container alignItems="center">
                    <Typography variant="subtitle2" fontWeight={500} sx={{ mr: 0.5 }}>
                      Note
                    </Typography>
                    <Typography color="error" variant="subtitle2">
                      *
                    </Typography>
                  </Grid>
                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    placeholder="Enter Note"
                    value={resolveNote}
                    onChange={(e) => setResolveNote(e.target.value)}
                    sx={{ mt: 0.5 }}
                  />
                </Box>
              </>
            )
          )}
        </DialogContent>
        <Divider />
        <DialogActions sx={{ p: 2, display: "flex", justifyContent: "space-between" }}>
          <Box>
            <Button
              variant="outlined"
              // onClick={() => setOpenResolveTaskDialog(false)}
              sx={{ borderColor: "#D0D5DD", color: "#344054" }}
            >
              Chat with Patient
            </Button>
          </Box>
          <Box>
            {taskVitalData && (
              <Button
                variant="outlined"
                sx={{ mr: 1, borderColor: "#D0D5DD", color: "#344054" }}
                onClick={handleViewVitalsClick}
              >
                View Vitals
              </Button>
            )}
            {taskDetailedData?.type === "APPOINTMENT" && (
              <Button
                variant="contained"
                sx={{ mr: 1, borderColor: "#D0D5DD" }}
                onClick={handleNavigateToCreateAppointment}
              >
                Create Appointment
              </Button>
            )}
            <Button
              variant="contained"
              startIcon={<CheckIcon />}
              onClick={resolveTask}
              disabled={!resolveNote.trim()}
              sx={{
                bgcolor: "#006D8F",
                "&:hover": {
                  bgcolor: "#005a75",
                },
              }}
            >
              Resolve
            </Button>
          </Box>
        </DialogActions>
      </Dialog>

      {/* Task Details Dialog */}
      {tasks.length > 0 && (
        <Dialog open={openTaskDetailsDialog} fullWidth>
          <Grid container justifyContent="space-between" alignItems="center" sx={{ borderBottom: "1px solid #D0D5DD" }}>
            <DialogTitle>Task Details - {taskDetails?.patientVital ? "Alert" : "Others"}</DialogTitle>
            <Box sx={{ padding: "16px 24px" }}>
              <IconButton onClick={() => setOpenTaskDetailsDialog(false)} size="small">
                <Close sx={{ fontSize: "28  px" }} />
              </IconButton>
            </Box>
          </Grid>
          <DialogContent>
            <Paper
              elevation={0}
              sx={{
                bgcolor: "#F0F7FF",
                p: 2,
                borderRadius: "8px",
                mb: 2,
                mt: 2,
                // border: "1px solid #D0D5DD",
              }}
            >
              <Grid container pt={1}>
                <Grid container size={12} mb={2}>
                  <Grid size={8}>
                    <Typography fontSize={14} variant="subtitle2" color="text.secondary">
                      Task
                    </Typography>
                    <Typography variant="body1" fontWeight={500}>
                      {taskDetails?.title}
                    </Typography>
                  </Grid>

                  <Grid size={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Priority
                    </Typography>
                    <Typography variant="body1" fontWeight={500} color={getPriorityColor(tasks[0].priority)?.color}>
                      {getPriorityColor(taskDetails?.priority)?.text}
                    </Typography>
                  </Grid>
                </Grid>
                <Grid container size={12} mb={2}>
                  <Grid size={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Patient Name
                    </Typography>
                    <Typography variant="body1" fontWeight={500}>
                      {taskDetails?.patientName}
                    </Typography>
                  </Grid>
                  <Grid size={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Assigned to
                    </Typography>
                    <Typography variant="body1" fontWeight={500}>
                      {taskDetails?.assignedName}
                    </Typography>
                  </Grid>
                  <Grid size={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Assigned Date & Time
                    </Typography>
                    <Typography variant="body1" fontWeight={500}>
                      {formatDateNewFormat(taskDetails.assignedDate)}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>

              {taskDetails?.patientVital && (
                <>
                  <Divider sx={{ mt: 1, borderColor: "#D0D5DD" }} />
                  <Grid container size={12} mb={2} mt={2}>
                    <Grid size={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        {taskDetails?.patientVital?.vitalName} Reading
                      </Typography>
                      <Typography variant="body1" fontWeight={600} color="error">
                        {taskDetails?.patientVital?.value1}
                        {taskDetails?.patientVital?.value2 ? `/${taskDetails?.patientVital?.value2}` : ""}{" "}
                        {getVitalUnit(taskDetails?.patientVital?.vitalName)}
                      </Typography>
                    </Grid>
                    <Grid size={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Device
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        Assigned Device
                      </Typography>
                    </Grid>
                    <Grid size={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Recorded Date
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {formatDateNewFormat(taskDetails?.patientVital?.created)}
                      </Typography>
                    </Grid>
                  </Grid>
                  <Divider sx={{ mb: 1, borderColor: "#D0D5DD" }} />
                </>
              )}

              <Grid size={12} mb={2}>
                <Typography variant="subtitle2" color="text.secondary">
                  Description
                </Typography>
                <Typography variant="body1" fontWeight={500}>
                  {taskDetails?.description ?? "-"}
                </Typography>
              </Grid>

              {taskDetails?.status !== "PENDING" && (
                <Grid container size={12} spacing={2}>
                  <Grid size={8}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Resolution Note
                    </Typography>
                    <Typography
                      variant="body1"
                      fontWeight={500}
                      sx={{
                        wordBreak: "break-word",
                        overflowWrap: "break-word",
                        hyphens: "auto",
                      }}
                    >
                      {taskDetails.note}
                    </Typography>
                  </Grid>

                  <Grid size={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Completion Date
                    </Typography>
                    <Typography variant="body1" fontWeight={500}>
                      {formatDateNewFormat(taskDetails.completionDate)}
                    </Typography>
                  </Grid>
                </Grid>
              )}
            </Paper>
          </DialogContent>
          <Divider sx={{ borderColor: "#D0D5DD" }} />
          <DialogActions sx={{ p: 2, display: "flex", justifyContent: "end" }}>
            <Box>
              <Button
                variant="outlined"
                sx={{ mr: 1, borderColor: "#D0D5DD", color: "#344054" }}
                onClick={() => {
                  navigate(`/provider/patients/${taskDetails?.patientUuid}/profile?tab=vitals`);
                }}
              >
                View Vitals
              </Button>
            </Box>
          </DialogActions>
        </Dialog>
      )}
    </Grid>
  );
}

export default TasksTab;
