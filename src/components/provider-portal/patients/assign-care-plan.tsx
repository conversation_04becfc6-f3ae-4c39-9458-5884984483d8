import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { Button, Collapse, Paper, Typography } from "@mui/material";
import { Box, Grid } from "@mui/system";
import { GridRowId } from "@mui/x-data-grid";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import * as yup from "yup";

import StaticSeletionShow from "@/common-components/custom-select-vk/Custom-select";
import CustomSelect from "@/common-components/custom-select/customSelect";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import VitalReferenceRange from "@/common-components/vital-reference-range/VitalReferenceRange";

import CP_BMI from "@/assets/image_svg/care-plan/cp-bmi.svg";
import CP_DIET from "@/assets/image_svg/care-plan/cp-diet.svg";
import CP_EXERCISE from "@/assets/image_svg/care-plan/cp-exercise.svg";
import DrawerFooter from "@/components/ui/DrawerFooter";
import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { usePatientCarePlanControllerServiceBulkAssignCarePlans } from "@/sdk/queries";
import { BulkCarePlanRequest, CarePlan, CarePlanControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";

import { transformVitalRanges } from "../carePlan/careplan-form";

const AssigncarePlanYup = yup.object().shape({
  carePlanName: yup.string(),
  startDate: yup.string().required("Start Date is required"),
});

interface AssingPatient {
  selectedCheckboxPatient: GridRowId[];
  handleCloseDrawer: () => void;
}

function AssignCarePlan(props: AssingPatient) {
  const { selectedCheckboxPatient, handleCloseDrawer } = props;
  const [getCareplanNames, setCarePlanNames] = useState<{ value: string; label: string }[]>([]);
  const [selectedCarePlanData, setSelectedCarePlanData] = useState<CarePlan>();
  const xTenantId = GetTenantId();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const [nurseType, setNurseType] = useState<string>("INTERNAL");
  const [endDate, setEndDate] = useState<string>("");

  const categoryIcons: Record<string, string> = {
    weight: CP_BMI,
    exercise: CP_EXERCISE,
    diet: CP_DIET,
    bloodPressure: CP_BMI,
    // Add more mappings...
  };

  interface VitalRange {
    rangeType?:
      | "NORMAL"
      | "LOW_MODERATE"
      | "HIGH_MODERATE"
      | "CRITICAL"
      | "NORMAL_SYSTOLIC"
      | "NORMAL_DIASTOLIC"
      | "LOW_MODERATE_SYSTOLIC"
      | "HIGH_MODERATE_SYSTOLIC"
      | "LOW_MODERATE_DIASTOLIC"
      | "HIGH_MODERATE_DIASTOLIC"
      | "CRITICAL_SYSTOLIC"
      | "CRITICAL_DIASTOLIC";
    min?: number;
    max?: number;
  }

  const initialValues = {
    carePlanName: "",
    startDate: "",
    applicableforGender: [] as string[],
    applicableforAgeCriteria: "",
    applicableforAge: "",
    applicableforConditions: "",
  };

  const formMethod = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(AssigncarePlanYup),
  });

  useEffect(() => {
    if (selectedCarePlanData) {
      formMethod.setValue("carePlanName", selectedCarePlanData.uuid || "");
    }
  }, [selectedCarePlanData]);

  // Calculate end date whenever startDate or selectedCarePlanData changes
  useEffect(() => {
    const startDate = formMethod.watch("startDate");
    if (startDate && selectedCarePlanData?.duration && selectedCarePlanData?.durationUnit) {
      const start = new Date(startDate);
      const duration = selectedCarePlanData.duration;
      let calculatedEnd = new Date(start);
      if (selectedCarePlanData.durationUnit === "DAY") {
        calculatedEnd.setDate(start.getDate() + duration);
      } else if (selectedCarePlanData.durationUnit === "WEEK") {
        calculatedEnd.setDate(start.getDate() + duration * 7);
      } else if (selectedCarePlanData.durationUnit === "MONTH") {
        calculatedEnd.setMonth(start.getMonth() + duration);
      } else if (selectedCarePlanData.durationUnit === "YEAR") {
        calculatedEnd.setFullYear(start.getFullYear() + duration);
      }
      setEndDate(
        calculatedEnd.toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        })
      );
    } else {
      setEndDate("");
    }
  }, [formMethod.watch("startDate"), selectedCarePlanData]);

  const StatusIndicator = ({ color, label }: { color: string; label: string }) => (
    <Box style={{ display: "flex", alignItems: "center", gap: "8px" }}>
      <Box style={{ width: 16, height: 16, backgroundColor: color, borderRadius: 4 }}></Box>
      <Typography>{label}</Typography>
    </Box>
  );

  const { data: GetCarePlanName, isSuccess: isSuccessCarePlan } = useQuery({
    queryKey: ["carePlanName", nurseType],
    queryFn: () =>
      CarePlanControllerService.getAllCarePlans1({
        status: true,
        archive: false,
        xTenantId: nurseType === "EXTERNAL" ? "eAmata" : GetTenantId(),
      }),
  });

  useEffect(() => {
    if (isSuccessCarePlan && GetCarePlanName) {
      const response = GetCarePlanName?.data?.content as CarePlan[];
      const titleName = response?.map((title) => ({
        value: title.uuid ?? "",
        label: title.title ?? "",
      }));
      setCarePlanNames(titleName);
    }
  }, [isSuccessCarePlan, GetCarePlanName]);

  const carePlanName = formMethod.watch("carePlanName");

  const { data: getAllCarePlanData, isSuccess } = useQuery({
    queryKey: ["carePlanName", carePlanName],
    enabled: !!carePlanName,
    queryFn: () =>
      CarePlanControllerService.getCarePlanById({
        carePlanId: carePlanName as string,
        xTenantId: nurseType === "EXTERNAL" ? "eAmata" : xTenantId,
      }),
  });

  useEffect(() => {
    if (isSuccess || getAllCarePlanData) {
      const res = getAllCarePlanData?.data as unknown as CarePlan;
      setSelectedCarePlanData(res);
    }
  }, [isSuccess]);

  const handleOnSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["list-of-patients"] });

    dispatch(
      setSnackbarOn({
        severity: AlertSeverity.SUCCESS,
        message: "Assign successfully!",
      })
    );
    handleCloseDrawer();
  };

  const bulkAssignPatient = usePatientCarePlanControllerServiceBulkAssignCarePlans({
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const onSubmit = async () => {
    const startDate = formMethod.getValues("startDate");
    const payload = {
      carePlanId: selectedCarePlanData?.uuid as string,
      startDate: format(startDate, "yyyy-MM-dd"),
      patientIds: selectedCheckboxPatient as string[],
      globalCarePlan: selectedCarePlanData?.globalCarePlan,
    };
    await bulkAssignPatient.mutate({
      requestBody: payload as unknown as BulkCarePlanRequest,
      xTenantId: xTenantId,
    });
  };

  return (
    <Grid
      sx={{
        width: "100%",
        height: "100%",
        padding: "30px 25px",
        overflowY: "auto", // Added to make the grid scrollable
      }}
    >
      <FormProvider {...formMethod}>
        <form onSubmit={formMethod.handleSubmit(onSubmit)}>
          <Grid size={12} display={"flex"} justifyContent={"space-between"} gap={2}>
            <Grid size={3}>
              <Typography
                mb={2}
                variant="body2"
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: "14px",
                  lineHeight: "14.06px",
                  letterSpacing: "0%",
                  color: "#515C5F",
                }}
              >
                Care Plan Type
              </Typography>
              <CustomSelect
                name="nurseType"
                placeholder="Select Provider Group"
                value={nurseType}
                items={[
                  { value: "INTERNAL", label: "Provider Group" },
                  { value: "EXTERNAL", label: "eAmata" },
                ]}
                onChange={(e) => {
                  setNurseType(e.target.value);
                  formMethod.setValue("carePlanName", "");
                }}
              />
            </Grid>

            <Grid container size={8}>
              <Autocomplete
                name="carePlanName"
                label="Care Plan"
                options={
                  getCareplanNames.length > 0
                    ? getCareplanNames
                    : [{ value: "", label: "No Care Plans Available", disabled: true }]
                }
                placeholder="Select Care Plan"
                isRequired
              />
            </Grid>
            <Grid
              container
              size={8}
              spacing={2}
              sx={{
                alignItems: "flex-start",
                "& > .MuiGrid-item": {
                  paddingTop: "0 !important",
                },
              }}
            >
              <Grid size={6}>
                <Datepicker name="startDate" label="Start Date" minDate={new Date()} isRequired />
              </Grid>
              <Grid size={6}>
                <Input placeholder="End Date" name="endDate" value={endDate} label="End Date" disabled />
              </Grid>
            </Grid>
          </Grid>
          <Collapse in={!!selectedCarePlanData?.title} timeout={300} sx={{ marginBottom: "10px" }}>
            <Grid
              mt={5}
              mb={10}
              padding={"20px 20px"}
              bgcolor={"#F2F7F9"}
              borderTop={"1px solid #E8EBEC"}
              display={"flex"}
              pt={2}
              container
              flexDirection={"column"}
              gap={2.5}
              borderRadius={2}
            >
              <Grid size={5}>
                <Typography fontSize={"18px"} fontWeight={500} color="#515C5F">
                  Applicable For
                </Typography>
              </Grid>

              <Grid container size={12} columnSpacing={2} alignItems="flex-start">
                <Grid size={3}>
                  <StaticSeletionShow
                    label={"Gender"}
                    options={
                      selectedCarePlanData?.gender === "MALE"
                        ? ["Male"]
                        : selectedCarePlanData?.gender === "FEMALE"
                          ? ["Female"]
                          : ["Male", "Female"]
                    }
                    selectedValue={
                      selectedCarePlanData?.gender === "MALE"
                        ? ["Male"]
                        : selectedCarePlanData?.gender === "FEMALE"
                          ? ["Female"]
                          : ["Male", "Female"]
                    }
                    onSelect={() => {}}
                  />
                </Grid>
                <Grid size={3}>
                  <StaticSeletionShow
                    label={"Age Criteria"}
                    options={[selectedCarePlanData?.ageCriteria + " " + selectedCarePlanData?.age + " yrs" || ""]}
                    selectedValue={[selectedCarePlanData?.ageCriteria + " " + selectedCarePlanData?.age + " yrs" || ""]}
                    onSelect={() => {}}
                  />
                </Grid>
                <Grid size={6}>
                  <StaticSeletionShow
                    label={"Diagnosis Codes"}
                    options={selectedCarePlanData?.diagnosisCodes || []}
                    selectedValue={selectedCarePlanData?.diagnosisCodes || []}
                    onSelect={() => {}}
                  />
                </Grid>
              </Grid>

              <Grid
                container
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                borderTop={"1px solid #CDD7DA"}
              >
                <Grid
                  container
                  size={12}
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  mb={3}
                  mt={3}
                >
                  <Grid>
                    <Typography
                      fontFamily="Roboto"
                      fontWeight={500}
                      fontSize="18px"
                      lineHeight="21.6px"
                      letterSpacing="0%"
                      color="#515C5F"
                    >
                      Program Goals
                    </Typography>
                  </Grid>

                  <Grid size={12} container spacing={1} mt={2}>
                    {selectedCarePlanData?.programGoals?.map((goal, index) => (
                      <Grid size={6} key={index}>
                        <Paper
                          elevation={0}
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            p: 2,
                            borderRadius: 2,
                          }}
                        >
                          <Box display="flex" alignItems="center" gap={1.5}>
                            {goal.category && categoryIcons[goal.category] && (
                              <img
                                src={categoryIcons[goal.category]}
                                width="30px"
                                height="30px"
                                alt={`${goal.category} Icon`}
                              />
                            )}
                            <Typography fontWeight="bold">{goal.title}</Typography>
                          </Box>
                          <Typography color="text.secondary">
                            {goal.targetValue} {goal.unit}
                          </Typography>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
              </Grid>
              <Grid
                container
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                borderTop={"1px solid #CDD7DA"}
              >
                <Grid
                  container
                  size={12}
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  mb={3}
                  mt={3}
                >
                  <Grid>
                    <Typography
                      fontFamily="Roboto"
                      fontWeight={500}
                      fontSize="18px"
                      lineHeight="21.6px"
                      letterSpacing="0%"
                      color="#515C5F"
                    >
                      Vital Reference Range
                    </Typography>
                  </Grid>
                  <Grid style={{ display: "flex", alignItems: "center", gap: "16px" }}>
                    <StatusIndicator color="#7FD067" label="Normal" />
                    <StatusIndicator color="#FCB33B" label="Moderate" />
                    <StatusIndicator color="#CE0718" label="Critical" />
                  </Grid>
                </Grid>
                <Grid container spacing={2} size={12}>
                  {selectedCarePlanData?.vitalReferences?.map((vital, index) => (
                    <Grid
                      key={index}
                      size={6}
                      mb={2}
                      sx={{
                        ...(selectedCarePlanData?.vitalReferences &&
                          index === selectedCarePlanData.vitalReferences.length - 1 &&
                          selectedCarePlanData.vitalReferences.length > 3 && {
                            marginLeft: 0,
                          }),
                      }}
                    >
                      <VitalReferenceRange
                        title={vital.vitalType || ""}
                        unit={
                          vital.vitalType === "Blood Glucose"
                            ? "mg/dL"
                            : vital.vitalType === "Heart Rate"
                              ? "bpm"
                              : vital.vitalType === "Weight"
                                ? "kg"
                                : vital.vitalType === "ECG"
                                  ? "bpm"
                                  : vital.vitalType === "HRV"
                                    ? "ms"
                                    : vital.vitalType === "Oxygen Saturation"
                                      ? "%"
                                      : vital.vitalType === "Stress"
                                        ? "%"
                                        : ""
                        }
                        ranges={transformVitalRanges(vital.vitalRanges as VitalRange[])}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>
          </Collapse>

          <DrawerFooter>
            <Button variant="contained" type="submit">
              {"Assign"}
            </Button>
          </DrawerFooter>
        </form>
      </FormProvider>
    </Grid>
  );
}

export default AssignCarePlan;
