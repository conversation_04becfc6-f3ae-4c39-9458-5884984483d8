import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Provider, Resolver, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { Button, Divider, FormControl, FormControlLabel, Radio, RadioGroup, Typography } from "@mui/material";
import { Box, Grid, Stack } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format, formatDate } from "date-fns";

import CustomSelect from "@/common-components/custom-select/customSelect";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
import Autocomplete from "@/components/ui/Form/Autocomplete";
import AutocompleteMultiSelect from "@/components/ui/Form/AutocompleteMultiSelect";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import { InputPhoneNumber } from "@/components/ui/Form/InputPhoneNumber";
import Select from "@/components/ui/Form/Select";
import useApiFeedback from "@/hooks/useApiFeedback";
import { ProviderRole } from "@/models/provider/provider-modal";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { usePatientControllerServiceCreatePatient } from "@/sdk/queries";
import {
  CarePlan,
  CarePlanControllerService,
  EhrControllerService,
  MedicalCode,
  MedicalCodeControllerService,
  Patient,
  Provider,
  ProviderControllerService,
} from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { getNumbersOnly } from "@/utils/format/string";
import { stateList } from "@/utils/statesList";
import { theme } from "@/utils/theme";

import { PatientEnrollSchema } from "./patient-enroll-schema";

interface patientType {
  handleCloseDrawer: () => void;
  isLoadingProviders: boolean;
  providerOptions: { label: string; value: string }[];
  setSearchProviderValue: (data: string) => void;
  refetch: () => void;
}
interface Option {
  label: string;
  value: string;
  disabled?: boolean;
}

const EhrPatientEnroll = (props: patientType) => {
  const { handleCloseDrawer, providerOptions, setSearchProviderValue, refetch } = props;

  const [integrateNurseFromEhr, setIntegrateNurseFromEhr] = useState<Patient[] | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();
  const xtenantId = GetTenantId();

  const [AssignCarePlanBy, setAssignCarePlanBy] = useState("AutoAssignByAI");
  const [nurseType, setNurseType] = useState("INTERNAL");
  const [searchDiagnosisCodes, setSearchDiagnosisCodes] = useState<string>("");
  const [DiagnosisCodes, setDiagnosisCodes] = useState<Option[]>([]);
  const [DiagnosisCodesPageSize, setDiagnosisCodesPageSize] = useState(0);
  const [getCareplanNames, setCarePlanNames] = useState<{ value: string; label: string }[]>([]);
  const [selectedCarePlanData, setSelectedCarePlanData] = useState<CarePlan>();

  const { data: diagnosisCodesData, isLoading: isDiagnosisCodesLoading } = useQuery({
    queryKey: ["diagnosisCodes", searchDiagnosisCodes, DiagnosisCodesPageSize],
    queryFn: () =>
      MedicalCodeControllerService.getMedicalCodes({
        xTenantId: GetTenantId(),
        searchString: searchDiagnosisCodes.toUpperCase(),
        size: 30,
        page: DiagnosisCodesPageSize,
        sortBy: "code",
        sort: "asc",
      }),
  });

  useEffect(() => {
    if (diagnosisCodesData?.data?.content) {
      const response = diagnosisCodesData.data.content as MedicalCode[];
      const diagnosisOptions = response.map((code) => ({
        label: `${code.code}`,
        value: code.code || "",
      }));
      setDiagnosisCodes(diagnosisOptions);
    }
  }, [diagnosisCodesData]);

  const handleSearchDiagnosisCodes = (searchValue: string) => {
    if (searchValue.trim() !== searchDiagnosisCodes) {
      setSearchDiagnosisCodes(searchValue.trim());
    }
  };

  function handleDiagnosisCodesScroll() {
    setDiagnosisCodesPageSize((prev) => prev + 1);
    setDiagnosisCodes((prev) => [...prev, ...DiagnosisCodes]);
  }
  const { data: GetCarePlanName, isSuccess: isSuccessCarePlan } = useQuery({
    queryKey: ["carePlanName", nurseType],
    queryFn: () =>
      CarePlanControllerService.getAllCarePlans1({
        status: true,
        archive: false,
        xTenantId: nurseType === "EXTERNAL" ? "eAmata" : GetTenantId(),
      }),
  });

  useEffect(() => {
    if (isSuccessCarePlan && GetCarePlanName) {
      const response = GetCarePlanName?.data?.content as CarePlan[];
      const titleName = response?.map((title) => ({
        value: title.uuid ?? "",
        label: title.title ?? "",
      }));
      setCarePlanNames(titleName);
    }
  }, [isSuccessCarePlan, GetCarePlanName]);

  const initialValues = {
    schemaType: "",
    firstName: "",
    family: "",
    dateOfBirth: integrateNurseFromEhr ? integrateNurseFromEhr[0]?.birthDate : "",
    patientId: "",
    providerId: "",
    nurseId: "",
    ehrName: integrateNurseFromEhr ? (integrateNurseFromEhr[0].firstName ?? "") : "",
    ehrLastName: integrateNurseFromEhr ? (integrateNurseFromEhr[0].lastName ?? "") : "",
    ehrEmail: integrateNurseFromEhr ? (integrateNurseFromEhr[0].email ?? "") : "",
    Phone: integrateNurseFromEhr ? (integrateNurseFromEhr[0]?.mobileNumber ?? "") : "",
    mrn: integrateNurseFromEhr ? (integrateNurseFromEhr[0]?.mrn ?? "") : "",
    ehrAddress: {
      line1: integrateNurseFromEhr
        ? integrateNurseFromEhr[0]?.address?.line1
          ? integrateNurseFromEhr[0]?.address.line1
          : ""
        : "",
      line2: integrateNurseFromEhr ? integrateNurseFromEhr[0]?.address?.line2 : "",
      city: integrateNurseFromEhr ? integrateNurseFromEhr[0]?.address?.city : "",
      state: integrateNurseFromEhr ? integrateNurseFromEhr[0]?.address?.state : "",
      country: integrateNurseFromEhr ? integrateNurseFromEhr[0]?.address?.country : "",
      zipcode: integrateNurseFromEhr ? integrateNurseFromEhr[0]?.address?.zipcode : "",
    },
    ehrId: integrateNurseFromEhr ? (integrateNurseFromEhr[0].ehrId ?? "") : "",
    selectedCarePlan: "",
    startDate: "",
  };

  interface PatientEnrollFormValues {
    schemaType?: string;
    firstName: string;
    family: string;
    dateOfBirth?: string;
    patientId?: string;
    providerId?: string;
    nurseId?: string;
    ehrName?: string;
    ehrLastName?: string;
    ehrEmail?: string;
    Phone?: string;
    mrn?: string;
    ehrAddress: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      country?: string;
      zipcode?: string;
    };
    ehrId?: string;
    selectedCarePlan?: string;
    StartDate?: string;
  }

  const formMethods = useForm<PatientEnrollFormValues>({
    defaultValues: initialValues,
    context: AssignCarePlanBy === "AutoAssignByAI" ? { isAssignByAi: true } : { isAssignByAi: false },
    resolver: yupResolver(PatientEnrollSchema) as Resolver<PatientEnrollFormValues>,
  });

  const getEhrData = async () => {
    const patientId = formMethods.getValues("patientId");
    const firstName = formMethods.getValues("firstName");
    const family = formMethods.getValues("family");
    const dateOfBirth = formMethods.getValues("dateOfBirth");

    setIsLoading(true);
    try {
      let res = await EhrControllerService.searchPatients({
        birthdate: dateOfBirth ? format(dateOfBirth, "yyyy-MM-dd") : "",
        given: firstName || "",
        family: family || "",
        organisationId: "enRyWnSP963FYDpoks4NHOA3",
        patientId: patientId || "",
      });
      if (res) {
        const allResponse = res as unknown as Patient[];
        setIntegrateNurseFromEhr(allResponse);
        // Set all form values
        formMethods.setValue("ehrName", allResponse[0]?.firstName || "");
        formMethods.setValue("ehrLastName", allResponse[0]?.lastName || "");
        formMethods.setValue("ehrEmail", allResponse[0]?.email || "");
        formMethods.setValue("Phone", allResponse[0].mobileNumber ? getNumbersOnly(allResponse[0].mobileNumber) : "");

        formMethods.setValue("mrn", allResponse[0]?.mrn || "");
        formMethods.setValue("dateOfBirth", allResponse[0]?.birthDate || "");
        formMethods.setValue("ehrId", allResponse[0]?.ehrId || "");

        // Set address fields
        formMethods.setValue("ehrAddress.line1", allResponse[0]?.address?.line1 || "");
        formMethods.setValue("ehrAddress.line2", allResponse[0]?.address?.line2 || "");
        formMethods.setValue("ehrAddress.city", allResponse[0]?.address?.city || "");
        formMethods.setValue("ehrAddress.state", allResponse[0]?.address?.state || "");
        formMethods.setValue("ehrAddress.country", allResponse[0]?.address?.country || "");
        formMethods.setValue("ehrAddress.zipcode", allResponse[0]?.address?.zipcode || "");
      }
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message || "Something went wrong",
        })
      );
    } finally {
      setIsLoading(false);
    }
  };

  // nurse options:

  const [nurseOptions, setNurseOptions] = useState<{ label: string; value: string }[]>([]);
  const [searchNurseValue, setSearchNurseValue] = useState("");

  const {
    data: nurses,
    // isPending: isLoadingNurses,
    isSuccess: isSuccessNurses,
  } = useQuery({
    queryKey: ["nurses", formMethods.watch("schemaType"), searchNurseValue],
    queryFn: async () => {
      const res = await ProviderControllerService.getAllProviders({
        xTenantId: formMethods.watch("schemaType") === "EXTERNAL" ? "eamata" : xtenantId,
        page: 0,
        size: 100,
        sortBy: "modified",
        sortDirection: "desc",
        role: ProviderRole.NURSE,
        status: true,
        archive: false,
        searchString: searchNurseValue,
      });

      const { data: nurseData } = res as AxiosResponse<ContentObject<Provider[]>>;
      const options = nurseData?.content.map((item) => ({
        label: `${item.firstName} ${item.lastName}`,
        value: String(item.uuid),
      }));

      return options;
    },
    enabled: !!formMethods.watch("schemaType"),
  });

  const carePlanName = formMethods.watch("selectedCarePlan");

  const { data: getAllCarePlanData, isSuccess: isDone } = useQuery({
    queryKey: ["carePlanName", carePlanName],
    enabled: !!carePlanName,
    queryFn: () =>
      CarePlanControllerService.getCarePlanById({
        carePlanId: carePlanName as string,
        xTenantId: nurseType === "EXTERNAL" ? "eAmata" : xtenantId,
      }),
  });

  useEffect(() => {
    if (isSuccess || getAllCarePlanData) {
      const res = getAllCarePlanData?.data as unknown as CarePlan;
      setSelectedCarePlanData(res);
    }
  }, [isDone]);

  useEffect(() => {
    if (isSuccessNurses) {
      setNurseOptions(nurses || []);
    }
  }, [nurses, isSuccessNurses]);

  const { mutateAsync, isError, error, isSuccess, data, isPending } = usePatientControllerServiceCreatePatient();

  useApiFeedback(isError, error, isSuccess, (data?.message || "Added ") as string);

  const onSubmit = async (value: FieldValues) => {
    const payload = {
      ehrId: value.ehrId || integrateNurseFromEhr?.[0]?.ehrId || "",
      firstName: (value.ehrName || integrateNurseFromEhr?.[0]?.firstName || "").trim(),
      lastName: (value.ehrLastName || integrateNurseFromEhr?.[0]?.lastName || "").trim(),
      email: value.ehrEmail || integrateNurseFromEhr?.[0]?.email || "",
      mobileNumber: value.Phone ? `+1${value.Phone}` : integrateNurseFromEhr?.[0]?.mobileNumber || "",
      gender: "MALE",
      birthDate: value.dateOfBirth || integrateNurseFromEhr?.[0]?.birthDate || "",
      providerId: value.providerId ? { [value.providerId]: "provider" } : {},
      nurseId: value.nurseId ? { [value.nurseId]: "nurse" } : {},
      schemaType: value.schemaType ?? "",
      ...(AssignCarePlanBy === "AutoAssignByAI" && {
        diagnosisCodes: Array.isArray(value.DiagnosisCodes)
          ? value.DiagnosisCodes.map((code: { value: string }) => code.value)
          : [],
      }),
      mrn: value.mrn || integrateNurseFromEhr?.[0]?.mrn || "",
      address: {
        line1: value.ehrAddress?.line1 || integrateNurseFromEhr?.[0]?.address?.line1 || "",
        line2: value.ehrAddress?.line2 || integrateNurseFromEhr?.[0]?.address?.line2 || "",
        city: value.ehrAddress?.city || integrateNurseFromEhr?.[0]?.address?.city || "",
        state: value.ehrAddress?.state || integrateNurseFromEhr?.[0]?.address?.state || "",
        country: value.ehrAddress?.country || integrateNurseFromEhr?.[0]?.address?.country || "",
        zipcode: value.ehrAddress?.zipcode || integrateNurseFromEhr?.[0]?.address?.zipcode || "",
      },
      ...(AssignCarePlanBy !== "AutoAssignByAI" && {
        patientCarePlanRequest: {
          carePlanId: selectedCarePlanData?.uuid,
          startDate: value.StartDate
            ? new Date(new Date(value.StartDate).getTime() + 86400000).toISOString().split("T")[0]
            : "",
          globalCarePlan: nurseType == "EXTERNAL" ? true : false,
          vitalReferences: selectedCarePlanData?.vitalReferences,
        },
      }),
    };

    await mutateAsync({
      requestBody: payload as Patient,
      xTenantId: xtenantId,
      isAiGenerated: AssignCarePlanBy === "AutoAssignByAI" ? true : false,
    });
    refetch();
    handleCloseDrawer();
  };

  const [radioValue, setRadioValue] = useState("PatientDetails");

  return (
    <DrawerBody padding={3} offset={80}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <Grid container sx={{ width: "100%" }}>
            <Grid>
              <Grid mb={2}>
                <Typography fontSize={"20px"} sx={{ color: "black" }}>
                  Search By
                </Typography>
              </Grid>
              <FormControl>
                {/* <FormLabel sx={{ color: "black", fontSize: "20px" }}>Search by</FormLabel> */}
                <RadioGroup
                  aria-labelledby="demo-radio-buttons-group-label"
                  defaultValue="PatientDetails"
                  name="radio-buttons-group"
                  onChange={(e) => setRadioValue(e.target.value)}
                  sx={{ display: "flex", flexDirection: "row" }}
                >
                  <FormControlLabel value="PatientDetails" control={<Radio />} label="Patient Details" />
                  <FormControlLabel value="Patient-ID" control={<Radio />} label="Patient ID" />
                </RadioGroup>
              </FormControl>
            </Grid>
            <Grid container size={12} gap={1} mt={2}>
              {radioValue == "PatientDetails" ? (
                <Grid size={12} container spacing={4} display={"flex"} flexDirection={"row"}>
                  <Grid size={4}>
                    <Input name="firstName" label="First Name" isRequired placeholder="Enter First Name" />
                  </Grid>
                  <Grid size={4}>
                    <Input name="family" label="Last Name" isRequired placeholder="Enter Last Name" />
                  </Grid>
                  <Grid size={4}>
                    <Datepicker name="dateOfBirth" label="Date of Birth" isRequired />
                  </Grid>
                </Grid>
              ) : (
                <Grid size={5.9}>
                  <Input name="patientId" label="Patient ID" isRequired />
                </Grid>
              )}
            </Grid>
            <Grid size={12} sx={{ display: "flex", justifyContent: "flex-start" }} my={2}>
              <Button
                variant="contained"
                onClick={() => getEhrData()}
                loading={isLoading}
                disabled={
                  !(formMethods.watch("patientId") || (formMethods.watch("family") && formMethods.watch("dateOfBirth")))
                }
              >
                <Typography variant="bodySmall">Search</Typography>
              </Button>
            </Grid>

            {integrateNurseFromEhr && (
              <Grid size={12}>
                <Grid>
                  <Typography variant="bodyMedium" fontWeight={600}>
                    Result
                  </Typography>
                  <Divider sx={{ my: 1.5 }} />
                </Grid>
                <Stack spacing={2}>
                  <Grid container spacing={2}>
                    <Grid size={8}>
                      <Input
                        isRequired
                        name="ehrName"
                        placeholder="Enter First Name"
                        value={`${integrateNurseFromEhr[0]?.firstName} ${integrateNurseFromEhr[0].lastName}` || ""}
                        label="Name"
                        disabled={!!integrateNurseFromEhr[0]?.firstName}
                      />
                    </Grid>
                    <Grid size={4}>
                      <Input
                        name="dateOfBirth"
                        disabled={!!integrateNurseFromEhr[0]?.birthDate}
                        isRequired
                        placeholder="Enter Date of Birth"
                        value={formatDate(integrateNurseFromEhr[0]?.birthDate ?? "", "dd-MM-yyyy")}
                        label="Date of birth"
                      />
                    </Grid>
                    <Grid size={4}>
                      <Input
                        disabled={!!integrateNurseFromEhr[0]?.email}
                        name="ehrEmail"
                        placeholder="Enter Email"
                        value={integrateNurseFromEhr[0]?.email}
                        label="Email"
                        isRequired
                      />
                    </Grid>
                    <Grid size={4}>
                      <InputPhoneNumber
                        isRequired
                        placeholder="Enter Phone Number"
                        disabled={!!integrateNurseFromEhr?.[0]?.mobileNumber}
                        namePhone="Phone"
                        label="Phone Number"
                      />
                    </Grid>
                    <Grid size={4}>
                      <Input
                        name="mrn"
                        label="MRN"
                        value={integrateNurseFromEhr?.[0]?.mrn}
                        disabled={!!integrateNurseFromEhr?.[0]?.mrn}
                      />
                    </Grid>
                  </Grid>

                  {/* <Grid>
                    <Input
                      name="ehrAddress"
                      // disabled={integrateNurseFromEhr?.address !== null}
                      isRequired
                      placeholder="Enter Address"
                      value={
                        integrateNurseFromEhr[0]?.address?.line1
                        // integrateNurseFromEhr[0]??.address
                        // ? `${integrateNurseFromEhr[0]??.address?.line1 || ""} ${integrateNurseFromEhr[0]??.address?.line1 ? "," : ""}  ${integrateNurseFromEhr[0]??.address?.line2 || ""} ${integrateNurseFromEhr[0]??.address?.line2 ? "," : ""} ${integrateNurseFromEhr[0]??.address?.city || ""} ${integrateNurseFromEhr[0]??.address?.city ? "," : ""} ${integrateNurseFromEhr[0]??.address?.state || ""} ${integrateNurseFromEhr[0]??.address?.state ? "," : ""} ${integrateNurseFromEhr[0]??.address?.country || ""} ${integrateNurseFromEhr[0]??.address?.country ? "," : ""}`
                        // : ""
                      }
                      label="Address"
                    />
                  </Grid> */}
                  <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
                    <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                      <Typography variant="medium">Address</Typography>
                    </Box>
                    <Grid container spacing={2} sx={{ padding: 2 }}>
                      <Grid size={6}>
                        <Input
                          name="ehrAddress.line1"
                          label="Line 1"
                          isRequired
                          disabled={!!integrateNurseFromEhr?.[0]?.address?.line1}
                          value={integrateNurseFromEhr?.[0]?.address?.line1}
                        />
                      </Grid>
                      <Grid size={6}>
                        <Input
                          name="ehrAddress.line2"
                          label="Line 2"
                          disabled={!!integrateNurseFromEhr?.[0]?.address?.line2}
                          value={integrateNurseFromEhr?.[0]?.address?.line2 || null}
                        />
                      </Grid>
                      <Grid size={6}>
                        <Autocomplete
                          isRequired
                          name="ehrAddress.state"
                          label="State"
                          options={stateList.map((item) => ({ label: item.label, value: item.value }))}
                          placeholder="Select State"
                          // disabled={!!integrateNurseFromEhr?.[0]?.address?.state}
                          // value={integrateNurseFromEhr?.[0]?.address?.state || ""}
                        />
                      </Grid>
                      <Grid size={6}>
                        <Input
                          name="ehrAddress.city"
                          label="City"
                          isRequired
                          disabled={!!integrateNurseFromEhr?.[0]?.address?.city}
                          value={integrateNurseFromEhr?.[0]?.address?.city || ""}
                        />
                      </Grid>
                      <Grid size={6}>
                        <Input
                          name="ehrAddress.country"
                          label="Country"
                          disabled={!!integrateNurseFromEhr?.[0]?.address?.country}
                          value={integrateNurseFromEhr?.[0]?.address?.country || ""}
                        />
                      </Grid>
                      <Grid size={6}>
                        <Input
                          name="ehrAddress.zipcode"
                          label="Zip Code"
                          isRequired
                          disabled={!!integrateNurseFromEhr?.[0]?.address?.zipcode}
                          value={integrateNurseFromEhr?.[0]?.address?.zipcode || ""}
                        />
                      </Grid>
                    </Grid>
                  </Box>
                  <Grid size={12}>
                    <Grid container spacing={2}>
                      <Grid size={4}>
                        <Autocomplete
                          name="providerId"
                          options={providerOptions || []}
                          label="Primary Provider"
                          placeholder="Select Primary Provider"
                          onSearch={(value) => setSearchProviderValue(value)}
                          // isLoading={isLoadingProviders}
                          isRequired
                        />
                      </Grid>
                      <Grid size={4}>
                        <Select
                          name="schemaType"
                          options={[
                            {
                              value: "INTERNAL",
                              label: "Provider Group Nurses",
                            },
                            { value: "EXTERNAL", label: "Eamata Nurses" },
                          ]}
                          label="Nurse Type"
                          isRequired
                        />
                      </Grid>
                      <Grid size={4}>
                        <Autocomplete
                          name="nurseId"
                          options={nurseOptions || []}
                          label="Nurse"
                          placeholder="Select Nurse"
                          onSearch={(value) => setSearchNurseValue(value)}
                          // isLoading={isLoadingNurses}
                          isRequired
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                </Stack>
              </Grid>
            )}
          </Grid>
          {integrateNurseFromEhr && (
            <Grid mt={3}>
              <Divider />
              <Grid container size={12}>
                <Grid size={12}>
                  <Typography
                    mt={2}
                    mb={2}
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 500,
                      fontSize: "16px",
                      lineHeight: "120%",
                      letterSpacing: "0%",
                    }}
                  >
                    Assign Care Plan
                  </Typography>
                </Grid>
                <Grid size={12}>
                  {/* <FormLabel component="legend">Care Plan Type</FormLabel> */}
                  <RadioGroup
                    row
                    name="carePlanType"
                    defaultValue="AutoAssignByAI"
                    onChange={(e) => {
                      setAssignCarePlanBy(e.target.value);
                    }}
                  >
                    <FormControlLabel value="AutoAssignByAI" control={<Radio />} label="Auto-assign by AI" />
                    <FormControlLabel value="Manual_Input" control={<Radio />} label="Manual Input" />
                  </RadioGroup>
                </Grid>

                {AssignCarePlanBy === "AutoAssignByAI" && (
                  <Grid size={4} mt={2} mb={5}>
                    <AutocompleteMultiSelect
                      isRequired
                      // defaultValue={[""]}
                      name="DiagnosisCodes"
                      placeholder="Select Diagnosis Codes"
                      options={DiagnosisCodes}
                      label={"Diagnosis Codes"}
                      onSearch={handleSearchDiagnosisCodes}
                      isLoading={isDiagnosisCodesLoading}
                      isEndOfListScroll={handleDiagnosisCodesScroll}
                    />
                  </Grid>
                )}

                {AssignCarePlanBy === "Manual_Input" && (
                  <Grid size={12} mt={2} mb={2} container spacing={2}>
                    <Grid size={3}>
                      <Typography
                        mb={2}
                        variant="body2"
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 500,
                          fontSize: "14px",
                          lineHeight: "14.06px",
                          letterSpacing: "0%",
                          color: "#515C5F",
                        }}
                      >
                        Care Plan Type
                      </Typography>
                      <CustomSelect
                        name="nurseType"
                        placeholder="Select Provider Group"
                        value={nurseType}
                        items={[
                          { value: "INTERNAL", label: "Provider Group" },
                          { value: "EXTERNAL", label: "eAmata" },
                        ]}
                        onChange={(e) => {
                          setNurseType(e.target.value);
                          // formMethods.setValue("carePlanName", "");
                        }}
                      />
                    </Grid>

                    <Grid size={6}>
                      <Autocomplete
                        name="selectedCarePlan"
                        options={getCareplanNames}
                        label="Select Care Plan"
                        placeholder="Select Care Plan"
                        isRequired
                      />
                    </Grid>
                    <Grid size={3}>
                      <Datepicker name="StartDate" label="Start Date" minDate={new Date()} isRequired />
                    </Grid>
                    {/* <Grid size={3}>
                    <Input
                      name="endDate"
                      label="End Date"
                      type="date"
                      value={CarePlanEndDate}
                      onChange={(e) => {
                        const val = e.target.value;
                        const newStartDate = new Date(val);
                        setCarePlanStartDate(newStartDate);
                      }}
                    />
                  </Grid> */}
                  </Grid>
                )}
              </Grid>
            </Grid>
          )}
          {integrateNurseFromEhr && (
            <DrawerFooter>
              <Button variant="contained" type="submit" loading={isPending}>
                {"Add Patient"}
              </Button>
            </DrawerFooter>
          )}
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default EhrPatientEnroll;
