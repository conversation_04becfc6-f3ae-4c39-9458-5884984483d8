import * as yup from "yup";

import {
  carePlanRequiredErrorMsg,
  diagnosisRequiredErrorMsg,
  emailRequiredErrorMsg,
  startdateRequiredErrorMsg,
} from "@/constants/error-messages";

export const PatientEnrollSchema = yup.object().shape({
  firstName: yup.string(),
  family: yup.string().notRequired(),
  ehrLastName: yup.string().notRequired(),
  dateOfBirth: yup.string().notRequired(),
  patientId: yup.string().notRequired(),
  ehrName: yup.string().optional(),
  ehrEmail: yup.string().required(emailRequiredErrorMsg),
  Phone: yup.string(),
  schemaType: yup.string().required("NurseType is required"),
  nurseId: yup.string().required("Nurse is required"),
  providerId: yup.string().required(" Provider is required"),
  ehrId: yup.string(),

  ehrAddress: yup.object().shape({
    line1: yup.string(),
    line2: yup.string().optional(),
    city: yup.string().optional(),
    state: yup.string().optional(),
    zipcode: yup.string().optional(),
    country: yup.string().optional(),
  }),
  selectedCarePlan: yup
    .string()
    .required(carePlanRequiredErrorMsg)
    .when("$isAssignByAi", {
      is: false,
      then: (schema) => schema.required(carePlanRequiredErrorMsg),
      otherwise: (schema) => schema.notRequired(),
    }),
  // startDate: yup.string().required("Start Date is required"),
  StartDate: yup
    .string()
    .required(startdateRequiredErrorMsg)
    .when("$isAssignByAi", {
      is: false,
      then: (schema) => schema.required(startdateRequiredErrorMsg),
      otherwise: (schema) => schema.notRequired(),
    }),

  DiagnosisCodes: yup
    .array()
    .of(yup.object().shape({ value: yup.string().required(diagnosisRequiredErrorMsg) }))
    .when("$isAssignByAi", {
      is: true,
      then: (schema) => schema.required(diagnosisRequiredErrorMsg),
      otherwise: (schema) => schema.notRequired(),
    }),
});
