import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { <PERSON>, Button, Collapse, Divider, Grid2 as Grid, Paper, Stack, Typography } from "@mui/material";
import { FormControlLabel, Radio, RadioGroup } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import StaticSeletionShow from "@/common-components/custom-select-vk/Custom-select";
import CustomSelect from "@/common-components/custom-select/customSelect";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import VitalReferenceRange from "@/common-components/vital-reference-range/VitalReferenceRange";

import CP_BMI from "@/assets/image_svg/care-plan/cp-bmi.svg";
import CP_DIET from "@/assets/image_svg/care-plan/cp-diet.svg";
import CP_EXERCISE from "@/assets/image_svg/care-plan/cp-exercise.svg";
import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
import Autocomplete from "@/components/ui/Form/Autocomplete";
import AutocompleteMultiSelect from "@/components/ui/Form/AutocompleteMultiSelect";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import { InputPhoneNumber } from "@/components/ui/Form/InputPhoneNumber";
import Select from "@/components/ui/Form/Select";
import { UploadImage } from "@/components/ui/Form/UploadImage";
import { ProviderRole } from "@/models/provider/provider-modal";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { usePatientControllerServiceCreatePatient } from "@/sdk/queries";
import {
  CarePlan,
  CarePlanControllerService,
  MedicalCode,
  MedicalCodeControllerService,
  Patient,
  PatientControllerService,
  Provider,
  ProviderControllerService,
} from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { stateList } from "@/utils/StateList";
import { getNumbersOnly } from "@/utils/format/string";
import { theme } from "@/utils/theme";

import { transformVitalRanges } from "../carePlan/careplan-form";
import { invitePatientFormSchema } from "./invite-patient-form-schema";

const categoryIcons: Record<string, string> = {
  weight: CP_BMI,
  exercise: CP_EXERCISE,
  diet: CP_DIET,
  bloodPressure: CP_BMI,
  // Add more mappings...
};
interface Option {
  label: string;
  value: string;
  disabled?: boolean;
}

interface InvitePatientFormProps {
  handleCloseDrawer: () => void;
  patientUuid?: string;
  xTenantId: string;
  refetch: () => void;
  isLoadingProviders: boolean;
  providerOptions: { label: string; value: string }[];
  setSearchProviderValue: (data: string) => void;
}

const InvitePatientForm = ({
  handleCloseDrawer,
  patientUuid,
  xTenantId,
  refetch,
  providerOptions,
  setSearchProviderValue,
}: InvitePatientFormProps) => {
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const isEdit = !!patientUuid;

  const [AssignCarePlanBy, setAssignCarePlanBy] = useState("AutoAssignByAI");

  const formMethods = useForm({
    defaultValues: {
      avatar: "",
      schemaType: "INTERNAL",
      address: {
        country: "USA",
      },
    },
    context: {
      isAssignByAi: AssignCarePlanBy === "AutoAssignByAI",
      isEdit,
    },
    resolver: yupResolver(invitePatientFormSchema),
  });

  useEffect(() => {
    formMethods.clearErrors();
    if (AssignCarePlanBy !== "AutoAssignByAI") {
      formMethods.trigger("carePlan");
      formMethods.trigger("StartDate");
    }
    // Remove the DiagnosisCodes trigger from here
  }, [AssignCarePlanBy, formMethods]);

  const {
    data: patient,
    isLoading: isLoadingPatient,
    isSuccess: isSuccessPatient,
  } = useQuery({
    queryKey: ["patient", patientUuid],
    queryFn: async () => {
      const res = (await PatientControllerService.getPatientById({
        patientUuid: String(patientUuid),
        xTenantId,
      })) as AxiosResponse<Patient>;

      const { data: patientData } = res;

      return patientData;
    },
    enabled: !!patientUuid,
  });

  const StatusIndicator = ({ color, label }: { color: string; label: string }) => (
    <Box style={{ display: "flex", alignItems: "center", gap: "8px" }}>
      <Box style={{ width: 16, height: 16, backgroundColor: color, borderRadius: 4 }}></Box>
      <Typography>{label}</Typography>
    </Box>
  );

  const carePlanName = formMethods.watch("carePlan");

  const { data: getAllCarePlanData, isSuccess } = useQuery({
    queryKey: ["carePlanName", carePlanName],
    enabled: !!carePlanName,
    queryFn: () =>
      CarePlanControllerService.getCarePlanById({
        carePlanId: carePlanName as string,
        xTenantId: nurseType === "EXTERNAL" ? "eAmata" : xTenantId,
      }),
  });

  useEffect(() => {
    if (isSuccess || getAllCarePlanData) {
      const res = getAllCarePlanData?.data as unknown as CarePlan;
      setSelectedCarePlanData(res);
    }
  }, [isSuccess]);

  interface VitalRange {
    rangeType?:
      | "NORMAL"
      | "LOW_MODERATE"
      | "HIGH_MODERATE"
      | "CRITICAL"
      | "NORMAL_SYSTOLIC"
      | "NORMAL_DIASTOLIC"
      | "LOW_MODERATE_SYSTOLIC"
      | "HIGH_MODERATE_SYSTOLIC"
      | "LOW_MODERATE_DIASTOLIC"
      | "HIGH_MODERATE_DIASTOLIC"
      | "CRITICAL_SYSTOLIC"
      | "CRITICAL_DIASTOLIC";
    min?: number;
    max?: number;
  }

  useEffect(() => {
    if (isSuccessPatient) {
      formMethods.setValue("firstName", patient.firstName?.trim());
      formMethods.setValue("lastName", patient.lastName);
      formMethods.setValue("mrn", patient.mrn || "");
      formMethods.setValue("email", patient.email);
      formMethods.setValue("applicableforGender", patient.gender || "");
      formMethods.setValue("prefix", "+1");
      formMethods.setValue("phone", patient.mobileNumber ? getNumbersOnly(patient.mobileNumber) : "");
      formMethods.setValue("status", patient.active == undefined ? "" : patient.active ? "active" : "inactive");
      if (patient.birthDate) {
        formMethods.setValue("dob", new Date(patient.birthDate));
      }
      formMethods.setValue("nurseId", patient.nurseId ? Object.keys(patient.nurseId)[0] : "");
      formMethods.setValue("schemaType", patient.schemaType || "INTERNAL");
      formMethods.setValue("providerId", patient.providerId ? Object.keys(patient.providerId)[0] : "");
      formMethods.setValue("address.line1", patient.address?.line1 || "");
      formMethods.setValue("address.line2", patient.address?.line2 || "");
      formMethods.setValue("address.city", patient.address?.city || "");
      formMethods.setValue("address.country", patient.address?.country || "USA");
      formMethods.setValue("address.state", patient.address?.state || "");
      formMethods.setValue("address.zipcode", patient.address?.zipcode || "");
    }
  }, [patient, isSuccessPatient]);

  const [nurseOptions, setNurseOptions] = useState<{ label: string; value: string }[]>([]);
  const [searchNurseValue, setSearchNurseValue] = useState("");
  const [nurseType, setNurseType] = useState("INTERNAL");
  const [getCareplanNames, setCarePlanNames] = useState<{ value: string; label: string }[]>([]);
  const [selectedCarePlanData, setSelectedCarePlanData] = useState<CarePlan>();
  const [searchDiagnosisCodes, setSearchDiagnosisCodes] = useState<string>("");
  const [DiagnosisCodes, setDiagnosisCodes] = useState<Option[]>([]);
  const [DiagnosisCodesPageSize, setDiagnosisCodesPageSize] = useState(0);

  const { data: GetCarePlanName, isSuccess: isSuccessCarePlan } = useQuery({
    queryKey: ["carePlanName", nurseType],
    queryFn: () =>
      CarePlanControllerService.getAllCarePlans1({
        status: true,
        archive: false,
        xTenantId: nurseType === "EXTERNAL" ? "eAmata" : GetTenantId(),
      }),
  });

  useEffect(() => {
    if (isSuccessCarePlan && GetCarePlanName) {
      const response = GetCarePlanName?.data?.content as CarePlan[];
      const titleName = response?.map((title) => ({
        value: title.uuid ?? "",
        label: title.title ?? "",
      }));
      setCarePlanNames(titleName);
    }
  }, [isSuccessCarePlan, GetCarePlanName]);

  const {
    data: nurses,
    // isPending: isLoadingNurses,
    isSuccess: isSuccessNurses,
  } = useQuery({
    queryKey: ["nurses", formMethods.watch("schemaType"), searchNurseValue],
    queryFn: async () => {
      const res = await ProviderControllerService.getAllProviders({
        xTenantId: formMethods.watch("schemaType") === "EXTERNAL" ? "eamata" : xTenantId,
        page: 0,
        size: 100,
        sortBy: "modified",
        sortDirection: "desc",
        role: ProviderRole.NURSE,
        status: true,
        archive: false,
        searchString: searchNurseValue,
      });

      const { data: nurseData } = res as AxiosResponse<ContentObject<Provider[]>>;
      const options = nurseData?.content.map((item) => ({
        label: `${item.firstName} ${item.lastName}`,
        value: String(item.uuid),
      }));

      return options;
    },
    enabled: !!formMethods.watch("schemaType"),
  });

  useEffect(() => {
    if (isSuccessNurses) {
      setNurseOptions(nurses || []);
    }
  }, [nurses, isSuccessNurses]);

  const { data: diagnosisCodesData, isLoading: isDiagnosisCodesLoading } = useQuery({
    queryKey: ["diagnosisCodes", searchDiagnosisCodes, DiagnosisCodesPageSize],
    queryFn: () =>
      MedicalCodeControllerService.getMedicalCodes({
        xTenantId: GetTenantId(),
        searchString: searchDiagnosisCodes.toUpperCase(),
        size: 30,
        page: DiagnosisCodesPageSize,
        type: "ICD10",
        sortBy: "code",
        sort: "asc",
      }),
  });

  useEffect(() => {
    if (diagnosisCodesData?.data?.content) {
      const response = diagnosisCodesData.data.content as MedicalCode[];
      const diagnosisOptions = response.map((code) => ({
        label: `${code.code}:${code.description}`,
        value: code.code || "",
      }));

      if (DiagnosisCodesPageSize === 0) {
        setDiagnosisCodes(diagnosisOptions);
      } else {
        setDiagnosisCodes((prev) => [...prev, ...diagnosisOptions]);
      }
    }
  }, [diagnosisCodesData]);

  const handleOnSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["list-of-patients"] });

    if (isEdit) {
      queryClient.invalidateQueries({ queryKey: ["patient", patientUuid] });
    }

    dispatch(
      setSnackbarOn({
        severity: AlertSeverity.SUCCESS,
        message: isEdit ? "Patient updated successfully!" : "Patient invited successfully!",
      })
    );
    handleCloseDrawer();
  };

  const handleOnError = (error: ErrorResponseEntity) => {
    if (
      error.body.message ==
      "Patient invited successfully, but care plan could not be assigned automatically. Please assign a care plan manually"
    ) {
      dispatch(setSnackbarOn({ severity: AlertSeverity.INFO, message: error.body.message }));
      handleCloseDrawer();
      refetch();
    } else {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    }
  };

  const invitePatientService = usePatientControllerServiceCreatePatient({
    onSuccess: handleOnSuccess,
    onError: handleOnError,
  });

  const editPatientService = useMutation({
    mutationFn: ({ user, avatar }: { user: Patient; avatar?: string }) =>
      Promise.all([
        PatientControllerService.updatePatient({
          requestBody: { ...user, uuid: patientUuid },
          xTenantId: xTenantId,
        }),
        avatar !== "" &&
          PatientControllerService.changeAvatar2({
            requestBody: {
              newAvatar: avatar?.startsWith("data:image")
                ? avatar.replace(/data:image\/(jpeg|png);base64,/, "")
                : avatar,
            },
            patientUuid: patientUuid || "",
            xTenantId: xTenantId,
          }),
      ]),
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const onSubmit = async (values: FieldValues) => {
    let payload = {
      firstName: values.firstName,
      lastName: values.lastName,
      birthDate: values?.dob ? new Date(values.dob).toISOString() : "",
      email: values.email,
      mobileNumber: values.prefix + values.phone,
      active: !isEdit || values.status === "active",
      nurseId: { [values.nurseId]: "" },
      providerId: { [values.providerId]: "" },
      mrn: values.mrn,
      schemaType: values.schemaType,
      ...(AssignCarePlanBy === "AutoAssignByAI" &&
        !isEdit && {
          diagnosisCodes: Array.isArray(values.DiagnosisCodes)
            ? values.DiagnosisCodes.map((code: { value: string }) => code.value)
            : [],
        }),
      gender: values.applicableforGender,
      address: {
        line1: values.address.line1,
        line2: values.address.line2,
        city: values.address.city,
        country: "USA",
        state: values.address.state,
        zipcode: values.address.zipcode,
      },
      ...(AssignCarePlanBy !== "AutoAssignByAI" && {
        patientCarePlanRequest: {
          carePlanId: selectedCarePlanData?.uuid,
          startDate: values.StartDate ? new Date(values.StartDate).toISOString().split("T")[0] : "",
          globalCarePlan: nurseType == "EXTERNAL" ? true : false,
          vitalReferences: selectedCarePlanData?.vitalReferences,
        },
      }),
    } as Patient;

    if (patient?.emergencyContact?.firstName) {
      payload = {
        ...payload,
        emergencyContact: patient.emergencyContact,
      };
    }
    if (isEdit) {
      editPatientService.mutate({ user: payload, avatar: values.avatar });
    } else {
      invitePatientService.mutate({
        requestBody: payload,
        xTenantId: xTenantId,
        isAiGenerated: AssignCarePlanBy === "AutoAssignByAI",
      });
    }
  };

  const handleSearchDiagnosisCodes = (searchValue: string) => {
    if (searchValue.trim() !== searchDiagnosisCodes) {
      setDiagnosisCodesPageSize(0);
      setDiagnosisCodes([]);

      setTimeout(() => {
        setSearchDiagnosisCodes(searchValue.trim());
      }, 1500);
    }
  };

  function handleDiagnosisCodesScroll() {
    setDiagnosisCodesPageSize((prev) => prev + 1);
  }

  return (
    <DrawerBody padding={3} offset={80} isLoading={isLoadingPatient}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            <Stack spacing={2}>
              <Grid container spacing={2}>
                {isEdit && (
                  <Grid size="auto">
                    <Box width="120px">
                      <UploadImage name="avatar" defaultImage={patient?.avatar} isLoading={isLoadingPatient} />
                    </Box>
                  </Grid>
                )}
                <Grid container size={isEdit ? "grow" : 12}>
                  <Grid size={4}>
                    <Input name="firstName" isRequired />
                  </Grid>
                  <Grid size={4}>
                    <Input name="lastName" isRequired />
                  </Grid>
                  <Grid size={4}>
                    <Input name="mrn" label="MRN" disabled={isEdit} />
                  </Grid>
                  <Grid size={4}>
                    <Datepicker
                      name="dob"
                      label="Date of Birth"
                      placeholder="Select Date of Birth"
                      isRequired
                      maxDate={new Date()}
                    />
                  </Grid>
                  <Grid size={4}>
                    <Input name="email" isRequired disabled={patient?.emailVerified} />
                  </Grid>
                  <Grid size={4}>
                    <Select
                      isRequired
                      placeholder="Select gender"
                      name="applicableforGender"
                      label="Gender"
                      // isRequired
                      options={[
                        { label: "Male", value: "MALE" },
                        { label: "Female", value: "FEMALE" },
                        { label: "Other", value: "Other" },
                      ]}
                    />
                  </Grid>
                  <Grid size={4}>
                    <InputPhoneNumber namePhone="phone" isRequired />
                  </Grid>
                  <Grid size={4}>
                    <Select
                      name="schemaType"
                      options={[
                        {
                          value: "INTERNAL",
                          label: "Provider Group Nurses",
                        },
                        { value: "EXTERNAL", label: "Eamata Nurses" },
                      ]}
                      label="Nurse Type"
                      isRequired
                    />
                  </Grid>
                  <Grid size={4}>
                    <Select
                      name="nurseId"
                      options={nurseOptions || []}
                      label="Nurse"
                      placeholder="Select Nurse"
                      onChange={(value) => setSearchNurseValue(value)}
                      isRequired
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid container spacing={2}>
                <Grid size={4}>
                  <Select
                    name="providerId"
                    options={providerOptions}
                    label="Primary Provider"
                    placeholder="Select Primary Provider"
                    onChange={(value) => setSearchProviderValue(value)}
                    isRequired
                  />
                </Grid>
                {isEdit && (
                  <Grid size={4}>
                    <Select
                      name="status"
                      options={[
                        { value: "active", label: "Active" },
                        { value: "inactive", label: "Inactive" },
                      ]}
                      placeholder="Select Status"
                      isRequired
                    />
                  </Grid>
                )}
              </Grid>
            </Stack>
            <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
              <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                <Typography variant="medium">Address</Typography>
              </Box>
              <Grid container spacing={2} sx={{ padding: 2 }}>
                <Grid size={6}>
                  <Input name="address.line1" label="Line 1" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input name="address.line2" label="Line 2" />
                </Grid>
                <Grid size={6}>
                  <Select
                    name="address.state"
                    label="State"
                    options={stateList.map((item) => ({ label: item.key, value: item.value }))}
                    placeholder="Select State"
                    isRequired
                  />
                </Grid>
                <Grid size={6}>
                  <Input name="address.city" label="City" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input name="address.country" label="Country" isRequired disabled />
                </Grid>
                <Grid size={6}>
                  <Input name="address.zipcode" label="Zip Code" isRequired />
                </Grid>
              </Grid>
            </Box>

            {!isEdit && (
              <>
                <Divider />
                <Grid container size={12}>
                  <Grid size={12}>
                    <Typography
                      mb={2}
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "16px",
                        lineHeight: "120%",
                        letterSpacing: "0%",
                      }}
                    >
                      Assign Care Plan
                    </Typography>
                  </Grid>
                  <Grid size={12}>
                    <RadioGroup
                      row
                      name="carePlanType"
                      defaultValue="AutoAssignByAI"
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setAssignCarePlanBy(newValue);
                        if (newValue === "AutoAssignByAI") {
                          formMethods.setValue("carePlan", "");
                          formMethods.setValue("StartDate", "");
                          setSelectedCarePlanData(undefined);
                        } else {
                          formMethods.setValue("DiagnosisCodes", []);
                        }
                      }}
                    >
                      <FormControlLabel value="AutoAssignByAI" control={<Radio />} label="Auto-assign by AI" />
                      <FormControlLabel value="Manual_Input" control={<Radio />} label="Manual Input" />
                    </RadioGroup>
                  </Grid>

                  {AssignCarePlanBy === "AutoAssignByAI" && (
                    <Grid size={12} mt={2} mb={5}>
                      <AutocompleteMultiSelect
                        isRequired
                        name="DiagnosisCodes"
                        placeholder="Select Diagnosis Codes"
                        options={DiagnosisCodes}
                        label={"Diagnosis Codes"}
                        onSearch={handleSearchDiagnosisCodes}
                        isLoading={isDiagnosisCodesLoading}
                        isEndOfListScroll={handleDiagnosisCodesScroll}
                      />
                    </Grid>
                  )}

                  {AssignCarePlanBy === "Manual_Input" && (
                    <Grid size={12} mt={2} mb={2} container spacing={2}>
                      <Grid size={3}>
                        <Typography
                          mb={2}
                          variant="body2"
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 500,
                            fontSize: "14px",
                            lineHeight: "14.06px",
                            letterSpacing: "0%",
                            color: "#515C5F",
                          }}
                        >
                          Care Plan Type
                        </Typography>
                        <CustomSelect
                          name="nurseType"
                          placeholder="Select Provider Group"
                          value={nurseType}
                          items={[
                            { value: "INTERNAL", label: "Provider Group" },
                            { value: "EXTERNAL", label: "eAmata" },
                          ]}
                          onChange={(e) => {
                            setNurseType(e.target.value);
                            // formMethods.setValue("carePlanName", "");
                          }}
                        />
                      </Grid>

                      <Grid size={6}>
                        <Autocomplete
                          name="carePlan"
                          options={getCareplanNames}
                          label="Select Care Plan"
                          placeholder="Select Care Plan"
                          isRequired
                        />
                      </Grid>
                      <Grid size={3}>
                        <Datepicker name="StartDate" label="Start Date" minDate={new Date()} isRequired />
                      </Grid>
                    </Grid>
                  )}
                </Grid>
              </>
            )}

            <Collapse
              in={selectedCarePlanData && AssignCarePlanBy === "Manual_Input"}
              timeout={300}
              sx={{ marginBottom: "10px" }}
            >
              <Grid
                // mt={5}
                mb={10}
                padding={"20px 20px"}
                bgcolor={"#F2F7F9"}
                borderTop={"1px solid #E8EBEC"}
                display={"flex"}
                pt={2}
                container
                flexDirection={"column"}
                gap={2.5}
                borderRadius={2}
              >
                <Grid size={5}>
                  <Typography fontSize={"18px"} fontWeight={500} color="#515C5F">
                    Applicable For
                  </Typography>
                </Grid>

                <Grid container size={12} columnSpacing={2} alignItems="flex-start">
                  <Grid size={3}>
                    <StaticSeletionShow
                      label={"Gender"}
                      options={
                        selectedCarePlanData?.gender === "MALE"
                          ? ["Male"]
                          : selectedCarePlanData?.gender === "FEMALE"
                            ? ["Female"]
                            : ["Male", "Female"]
                      }
                      selectedValue={
                        selectedCarePlanData?.gender === "MALE"
                          ? ["Male"]
                          : selectedCarePlanData?.gender === "FEMALE"
                            ? ["Female"]
                            : ["Male", "Female"]
                      }
                      onSelect={() => {}}
                    />
                  </Grid>
                  <Grid size={3}>
                    <StaticSeletionShow
                      label={"Age Criteria"}
                      options={[selectedCarePlanData?.ageCriteria + " " + selectedCarePlanData?.age + " yrs" || ""]}
                      selectedValue={[
                        selectedCarePlanData?.ageCriteria + " " + selectedCarePlanData?.age + " yrs" || "",
                      ]}
                      onSelect={() => {}}
                    />
                  </Grid>
                  <Grid size={6}>
                    <StaticSeletionShow
                      label={"Diagnosis Codes"}
                      options={selectedCarePlanData?.diagnosisCodes || []}
                      selectedValue={selectedCarePlanData?.diagnosisCodes || []}
                      onSelect={() => {}}
                    />
                  </Grid>
                </Grid>

                <Grid
                  container
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  borderTop={"1px solid #CDD7DA"}
                >
                  <Grid
                    container
                    size={12}
                    display="flex"
                    justifyContent="space-between"
                    alignItems="center"
                    mb={3}
                    mt={3}
                  >
                    <Grid>
                      <Typography
                        fontFamily="Roboto"
                        fontWeight={500}
                        fontSize="18px"
                        lineHeight="21.6px"
                        letterSpacing="0%"
                        color="#515C5F"
                      >
                        Program Goals
                      </Typography>
                    </Grid>

                    <Grid size={12} container spacing={1} mt={2}>
                      {selectedCarePlanData?.programGoals?.map((goal, index) => (
                        <Grid size={6} key={index}>
                          <Paper
                            elevation={0}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                              p: 2,
                              borderRadius: 2,
                            }}
                          >
                            <Box display="flex" alignItems="center" gap={1.5}>
                              {goal.category && categoryIcons[goal.category] && (
                                <img
                                  src={categoryIcons[goal.category]}
                                  width="30px"
                                  height="30px"
                                  alt={`${goal.category} Icon`}
                                />
                              )}
                              <Typography fontWeight="bold">{goal.title}</Typography>
                            </Box>
                            <Typography color="text.secondary">
                              {goal.targetValue} {goal.unit}
                            </Typography>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  </Grid>
                </Grid>
                <Grid
                  container
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  borderTop={"1px solid #CDD7DA"}
                >
                  <Grid
                    container
                    size={12}
                    display="flex"
                    justifyContent="space-between"
                    alignItems="center"
                    mb={3}
                    mt={3}
                  >
                    <Grid>
                      <Typography
                        fontFamily="Roboto"
                        fontWeight={500}
                        fontSize="18px"
                        lineHeight="21.6px"
                        letterSpacing="0%"
                        color="#515C5F"
                      >
                        Vital Reference Range
                      </Typography>
                    </Grid>
                    <Grid style={{ display: "flex", alignItems: "center", gap: "16px" }}>
                      <StatusIndicator color="#7FD067" label="Normal" />
                      <StatusIndicator color="#FCB33B" label="Abnormal" />
                      <StatusIndicator color="#CE0718" label="Critical" />
                    </Grid>
                  </Grid>
                  <Grid container spacing={2} size={12}>
                    {selectedCarePlanData?.vitalReferences?.map((vital, index) => (
                      <Grid
                        key={index}
                        size={6}
                        mb={2}
                        sx={{
                          ...(selectedCarePlanData?.vitalReferences &&
                            index === selectedCarePlanData.vitalReferences.length - 1 &&
                            selectedCarePlanData.vitalReferences.length > 3 && {
                              marginLeft: 0,
                            }),
                        }}
                      >
                        <VitalReferenceRange
                          title={vital.vitalType || ""}
                          unit={
                            vital.vitalType === "Blood Glucose"
                              ? "mg/dL"
                              : vital.vitalType === "Heart Rate"
                                ? "bpm"
                                : vital.vitalType === "Weight"
                                  ? "kg"
                                  : vital.vitalType === "ECG"
                                    ? "bpm"
                                    : vital.vitalType === "HRV"
                                      ? "ms"
                                      : vital.vitalType === "Oxygen Saturation"
                                        ? "%"
                                        : vital.vitalType === "Stress"
                                          ? "%"
                                          : ""
                          }
                          ranges={transformVitalRanges(vital.vitalRanges as VitalRange[])}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
              </Grid>
            </Collapse>
          </Stack>
          <DrawerFooter>
            <Button
              variant="contained"
              type="submit"
              loading={invitePatientService.isPending || editPatientService.isPending}
            >
              {isEdit ? "Save Patient" : "Invite Patient"}
            </Button>
          </DrawerFooter>
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default InvitePatientForm;
