import * as yup from "yup";

import {
  addressLine1Max128ErrorMsg,
  addressLine1RequiredErrorMsg,
  birthDateRequiredErrorMsg,
  carePlanRequiredErrorMsg,
  cityMax64ErrorMsg,
  cityRequiredErrorMsg,
  cityStateRegexErrorMsg,
  countryRequiredErrorMsg,
  diagnosisRequiredErrorMsg,
  emailRegexErrorMsg,
  emailRequiredErrorMsg,
  firstNameOrSurnameRegexErrorMsg,
  firstNameRequiredErrorMsg,
  lastNameRequiredErrorMsg,
  lessThan255ErrorMsg,
  nurseRequiredErrorMsg,
  phoneRegexErrorMsg,
  phoneRequiredErrorMsg,
  providerRequiredErrorMsg,
  startdateRequiredErrorMsg,
  stateRequiredErrorMsg,
  zipCodeRegexErrorMsg,
  zipCodeRequiredErrorMsg,
} from "@/constants/error-messages";
import { cityStateRgex, emailRegExp, nameRegex, phoneRegex, zipCodeRegex } from "@/utils/regex";

export const invitePatientFormSchema = yup.object().shape({
  firstName: yup.string().required(firstNameRequiredErrorMsg).matches(nameRegex, firstNameOrSurnameRegexErrorMsg),
  lastName: yup.string().required(lastNameRequiredErrorMsg).matches(nameRegex, firstNameOrSurnameRegexErrorMsg),
  email: yup.string().required(emailRequiredErrorMsg).matches(emailRegExp, emailRegexErrorMsg),
  applicableforGender: yup.string().required("Gender is required"),
  phone: yup
    .string()
    .required(phoneRequiredErrorMsg)
    .transform((value) => (value === "" ? null : value))
    .matches(phoneRegex, phoneRegexErrorMsg),
  prefix: yup.string().transform((value) => (value === "" ? null : value)),
  status: yup.string(),
  mrn: yup
    .string()
    .optional()
    .test("is-number", "MRN must contain only numbers", (value) => !value || /^\d+$/.test(value)),
  providerId: yup.string().required(providerRequiredErrorMsg),
  address: yup.object().shape({
    line1: yup.string().max(128, addressLine1Max128ErrorMsg).required(addressLine1RequiredErrorMsg),
    line2: yup.string().max(128, addressLine1Max128ErrorMsg),
    city: yup
      .string()
      .max(64, cityMax64ErrorMsg)
      .required(cityRequiredErrorMsg)
      .matches(cityStateRgex, cityStateRegexErrorMsg),
    state: yup
      .string()
      .max(50, lessThan255ErrorMsg)
      .required(stateRequiredErrorMsg)
      .matches(cityStateRgex, cityStateRegexErrorMsg),
    zipcode: yup.string().required(zipCodeRequiredErrorMsg).matches(zipCodeRegex, zipCodeRegexErrorMsg),
    country: yup.string().required(countryRequiredErrorMsg),
  }),
  dob: yup.date().required(birthDateRequiredErrorMsg),
  nurseId: yup.string().required(nurseRequiredErrorMsg),
  schemaType: yup.string(),
  avatar: yup.string(),

  // Validates carePlan field only when AssignCarePlanBy is Manual_Input
  carePlan: yup.string().when(["$isAssignByAi", "$isEdit"], {
    is: (isAssignByAi: boolean, isEdit: boolean) => !isAssignByAi && !isEdit,
    then: (schema) => schema.required(carePlanRequiredErrorMsg),
    otherwise: (schema) => schema.notRequired(),
  }),

  // Validates StartDate field only when AssignCarePlanBy is Manual_Input
  StartDate: yup.string().when(["$isAssignByAi", "$isEdit"], {
    is: (isAssignByAi: boolean, isEdit: boolean) => !isAssignByAi && !isEdit,
    then: (schema) => schema.required(startdateRequiredErrorMsg),
    otherwise: (schema) => schema.notRequired(),
  }),

  // Validates DiagnosisCodes field only when AssignCarePlanBy is AutoAssignByAI
  DiagnosisCodes: yup.array().when(["$isAssignByAi", "$isEdit"], {
    is: (isAssignByAi: boolean, isEdit: boolean) => isAssignByAi && !isEdit,
    then: (schema) =>
      schema
        .of(yup.object().shape({ value: yup.string().required(diagnosisRequiredErrorMsg) }))
        .min(1, diagnosisRequiredErrorMsg)
        .required(diagnosisRequiredErrorMsg),
    otherwise: (schema) => schema.notRequired(),
  }),
});
