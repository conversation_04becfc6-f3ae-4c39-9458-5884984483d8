import { useState } from "react";
import { <PERSON>Val<PERSON>, Form<PERSON>rovider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { Avatar, Box, Button, Divider, Grid2 as Grid, Stack, Typography } from "@mui/material";
import { Container } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQuery } from "@tanstack/react-query";
import { compact } from "lodash";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import { Input } from "@/components/ui/Form/Input";
import Text from "@/components/ui/Text";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useUserControllerServiceChangePassword } from "@/sdk/queries";
import { Provider, ProviderControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import cookieService from "@/services/core/cookie-service";
import { formatDateOnly } from "@/utils/format/date";
import { titleCase } from "@/utils/format/string";
import { theme } from "@/utils/theme";

import EditProfile from "./edit-profile";
import { changePasswordSchema } from "./profile-schema";

const Profile = () => {
  const xTenantIdVal = GetTenantId();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [isEdit, setIsEdit] = useState(false);

  const { data: profile, isLoading: loadingProfile } = useQuery({
    queryKey: ["my-profile"],
    queryFn: async () => {
      const response = await ProviderControllerService.getProfile({ xTenantId: xTenantIdVal });

      return response.data as Provider;
    },
  });

  const initialValues = {
    currentPassword: "",
    newPassword: "",
    confirmNewPassword: "",
  };

  const formChangePassword = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(changePasswordSchema),
  });

  const changePasswordService = useUserControllerServiceChangePassword({
    onSuccess: () => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: "Password Change Successfully",
        })
      );
      cookieService.clearCookies();
      localStorage.clear();
      localStorage.removeItem("redirectURL");
      navigate("/auth/login");
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: error.body.message || "Password Change Failed",
        })
      );
    },
  });

  const onSubmit = async (values: FieldValues) => {
    const payload = {
      oldPassword: values.currentPassword,
      newPassword: values.newPassword,
    };

    changePasswordService.mutate({ requestBody: payload });
  };

  return (
    <Container maxWidth="md" sx={{ p: 2 }}>
      {!isEdit && (
        <Stack spacing={2}>
          <Grid className="bordered-box">
            <Box sx={{ p: 2, borderBottom: "1px solid #EAECF0" }}>
              <Grid container justifyContent="space-between" alignItems="center">
                <Typography variant="large">View Profile</Typography>
                <Button
                  variant="outlined"
                  startIcon={<EditOutlinedIcon />}
                  size="small"
                  onClick={() => setIsEdit(true)}
                  sx={{ backgroundColor: theme.palette.secondary.main, px: 1.5 }}
                >
                  Edit
                </Button>
              </Grid>
            </Box>
            <Stack spacing={4} sx={{ p: 2 }}>
              <Grid container justifyContent="center">
                <Box
                  sx={{
                    p: 2,
                    width: "100%",
                    display: "flex",
                    justifyContent: "center",
                    bgcolor: "#FBFBFB",
                    borderRadius: 2,
                  }}
                >
                  <Box sx={{ width: 96, aspectRatio: "1/1" }}>
                    <Avatar
                      src={profile?.avatar || ""}
                      alt={profile?.firstName || ""}
                      sx={{
                        width: "100%",
                        height: "100%",
                        border: "2px solid",
                        borderColor: "grey.200",
                      }}
                    />
                  </Box>
                </Box>
              </Grid>
              <Grid container rowSpacing={4} columnSpacing={8}>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Stack spacing={1}>
                    <Typography variant="medium" color="#525252">
                      Full Name
                    </Typography>
                    <Text variant="medium" fontWeight={400} color="#333332" isLoading={loadingProfile}>
                      {profile?.firstName} {profile?.lastName}
                    </Text>
                  </Stack>
                </Grid>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Stack spacing={1}>
                    <Typography variant="medium" color="#525252">
                      Email
                    </Typography>
                    <Text variant="medium" fontWeight={400} color="#333332" isLoading={loadingProfile}>
                      {profile?.email}
                    </Text>
                  </Stack>
                </Grid>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Stack spacing={1}>
                    <Typography variant="medium" color="#525252">
                      Phone Number
                    </Typography>
                    <Text variant="medium" fontWeight={400} color="#333332" isLoading={loadingProfile}>
                      {profile?.phone}
                    </Text>
                  </Stack>
                </Grid>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Stack spacing={1}>
                    <Typography variant="medium" color="#525252">
                      NPI
                    </Typography>
                    <Text variant="medium" fontWeight={400} color="#333332" isLoading={loadingProfile}>
                      {profile?.npi}
                    </Text>
                  </Stack>
                </Grid>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Stack spacing={1}>
                    <Typography variant="medium" color="#525252">
                      Address Information
                    </Typography>
                    <Text variant="medium" fontWeight={400} color="#333332" isLoading={loadingProfile}>
                      {compact([
                        profile?.address?.line1,
                        profile?.address?.line2,
                        profile?.address?.city,
                        profile?.address?.state,
                        profile?.address?.country,
                        profile?.address?.zipcode,
                      ]).join(", ")}
                    </Text>
                  </Stack>
                </Grid>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Stack spacing={1}>
                    <Typography variant="medium" color="#525252">
                      Role
                    </Typography>
                    <Text variant="medium" fontWeight={400} color="#333332" isLoading={loadingProfile}>
                      {titleCase(profile?.role)}
                    </Text>
                  </Stack>
                </Grid>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Stack spacing={1}>
                    <Typography variant="medium" color="#525252">
                      Provider Type
                    </Typography>
                    <Text variant="medium" fontWeight={400} color="#333332" isLoading={loadingProfile}>
                      {titleCase(profile?.providerType) || "-"}
                    </Text>
                  </Stack>
                </Grid>
              </Grid>
              <Divider />
              <Stack spacing={2}>
                <Typography variant="large">License Details</Typography>
                <Stack sx={{ p: 2, borderRadius: 2, backgroundColor: "#F2F7F9" }}>
                  {profile?.providerLicenseDetails?.map((license, index) => (
                    <Stack key={license.uuid}>
                      <Grid container justifyContent="space-between">
                        <Grid size={4}>
                          <Stack spacing={1}>
                            <Typography variant="medium" color="#525252">
                              License Number
                            </Typography>
                            <Typography variant="medium" color="#333332">
                              {license.licenseNumber}
                            </Typography>
                          </Stack>
                        </Grid>
                        <Grid size={4}>
                          <Stack spacing={1}>
                            <Typography variant="medium" color="#525252">
                              License State
                            </Typography>
                            <Typography variant="medium" color="#333332">
                              {license.licensedStates && license.licensedStates[0].state}
                            </Typography>
                          </Stack>
                        </Grid>
                        <Grid size={4}>
                          <Stack spacing={1}>
                            <Typography variant="medium" color="#525252">
                              License Expiry Date
                            </Typography>
                            <Typography variant="medium" color="#333332">
                              {formatDateOnly(license.expiryDate)}
                            </Typography>
                          </Stack>
                        </Grid>
                      </Grid>
                      {index !== Number(profile?.providerLicenseDetails?.length) - 1 && <Divider sx={{ my: 2 }} />}
                    </Stack>
                  ))}
                </Stack>
              </Stack>
            </Stack>
          </Grid>
          <Grid className="bordered-box">
            <Box sx={{ p: 2, borderBottom: "1px solid #EAECF0" }}>
              <Typography variant="large">Change Password</Typography>
            </Box>
            <FormProvider {...formChangePassword}>
              <form onSubmit={formChangePassword.handleSubmit(onSubmit)}>
                <Grid container sx={{ p: 2 }} spacing={2}>
                  <Grid size={4}>
                    <Input type="password" name="currentPassword" label="Current Password" isRequired />
                  </Grid>
                  <Grid size={4}>
                    <Input type="password" name="newPassword" label="New Password" isRequired />
                  </Grid>
                  <Grid size={4}>
                    <Input type="password" name="confirmNewPassword" label="Confirm New Password" isRequired />
                  </Grid>
                </Grid>
                <Box sx={{ p: 2, borderTop: "1px solid #EAECF0" }}>
                  <Grid container justifyContent="flex-end">
                    <Button variant="contained" type="submit" loading={changePasswordService.isPending}>
                      Change Password
                    </Button>
                  </Grid>
                </Box>
              </form>
            </FormProvider>
          </Grid>
        </Stack>
      )}
      {isEdit && profile && (
        <Grid className="bordered-box">
          <Box sx={{ p: 2, borderBottom: "1px solid #EAECF0" }}>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="large">Edit Profile</Typography>
              <Button variant="outlined" size="small" onClick={() => setIsEdit(false)} sx={{ px: 1.5 }}>
                Cancel
              </Button>
            </Grid>
          </Box>
          <Box p={2}>
            <EditProfile handleOnSuccess={() => setIsEdit(false)} />
          </Box>
        </Grid>
      )}
    </Container>
  );
};

export default Profile;
