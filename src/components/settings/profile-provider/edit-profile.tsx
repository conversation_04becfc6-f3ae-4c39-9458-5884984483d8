import { <PERSON><PERSON><PERSON><PERSON>, useFieldArray, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import { Box, Button, Grid2 as Grid, IconButton, InputLabel, Stack, Typography } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import { InputPhoneNumber } from "@/components/ui/Form/InputPhoneNumber";
import Select from "@/components/ui/Form/Select";
import { UploadImage } from "@/components/ui/Form/UploadImage";
import { providerTypes } from "@/constants/provider";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { LicenseStateControllerService, Provider, ProviderControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { splitPhoneNumber } from "@/services/common/phone-formatter";
import { stateList } from "@/utils/StateList";
import { theme } from "@/utils/theme";

import { editProfileSchema } from "./profile-schema";

type EditProfileProps = {
  handleOnSuccess: () => void;
};

const EditProfile = ({ handleOnSuccess }: EditProfileProps) => {
  const xTenantIdVal = GetTenantId();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const { data: licensedStateOptions } = useQuery({
    queryKey: ["list-of-licensed-states"],
    queryFn: async () => {
      const response = await LicenseStateControllerService.getAllLicensedStates({
        page: 0,
        size: 100,
      });

      const { content } = response.data as ContentObject<{ id: number; country: "US"; state: string }[]>;

      return content?.map((item) => {
        return {
          key: item.state.toString(),
          value: item.state,
          info: item.id.toString(),
        };
      });
    },
  });

  const getIdOfState = (state: string) => licensedStateOptions?.find((item) => item.value === state)?.info || "";

  const { data: profile, isLoading: loadingProfile } = useQuery({
    queryKey: ["my-profile"],
    queryFn: async () => {
      const response = await ProviderControllerService.getProfile({ xTenantId: xTenantIdVal });

      return response.data as Provider;
    },
  });

  const initialValues = {
    firstName: profile?.firstName || "",
    lastName: profile?.lastName || "",
    npi: profile?.npi || "",
    email: profile?.email || "",
    phone: profile?.phone ? splitPhoneNumber(profile?.phone)?.number : "",
    prefix: profile?.phone ? splitPhoneNumber(profile?.phone)?.countryCode : "+1",
    address: {
      line1: profile?.address?.line1 || "",
      line2: profile?.address?.line2 || "",
      city: profile?.address?.city || "",
      state: profile?.address?.state || "",
      zipcode: profile?.address?.zipcode || "",
      country: profile?.address?.country || "USA",
    },
    avatar: "",
    licenses: profile?.providerLicenseDetails?.map((v) => ({
      licenseNumber: v.licenseNumber || "",
      licensedState: v.licensedStates?.length ? `${v.licensedStates[0].state}` : "",
      licenseExpiryDate: v.expiryDate ? new Date(v.expiryDate) : new Date(),
    })) || [{ licenseNumber: "", licensedState: "", licenseExpiryDate: "" as unknown as Date }],
    providerType: profile?.providerType || "",
  };

  const formMethods = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(editProfileSchema),
  });

  const {
    fields: licenseFields,
    append: appendLicenses,
    remove: removeLicenses,
  } = useFieldArray({
    name: "licenses",
    control: formMethods.control,
  });

  const editNurseService = useMutation({
    mutationFn: ({ user, avatar }: { user: Provider; avatar?: string }) =>
      Promise.all([
        ProviderControllerService.updateProvider({
          requestBody: { ...profile, ...user, uuid: profile?.uuid },
        }),
        avatar !== "" &&
          ProviderControllerService.changeAvatar({
            requestBody: {
              newAvatar: avatar?.startsWith("data:image")
                ? avatar.replace(/data:image\/(jpeg|png);base64,/, "")
                : avatar,
            },
            providerUuid: profile?.uuid || "",
          }),
      ]),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["my-profile"] });
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: "Profile updated successfully",
        })
      );
      handleOnSuccess();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const onSubmit = async (values: unknown) => {
    const formValues = values as typeof initialValues;
    const payload = {
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      npi: formValues.npi,
      email: formValues.email,
      phone: `${formValues.prefix}${formValues.phone}`,
      address: {
        line1: formValues.address.line1,
        line2: formValues.address.line2,
        city: formValues.address.city,
        country: formValues.address.country,
        state: formValues.address.state,
        zipcode: formValues.address.zipcode,
      },
      providerLicenseDetails: formValues.licenses.map(
        (item: { licenseExpiryDate?: Date; licensedState?: string; licenseNumber?: string }) => {
          return {
            licenseNumber: item.licenseNumber || "",
            expiryDate: item?.licenseExpiryDate || "",
            licensedStates: [
              {
                state: item.licensedState,
                id: item?.licensedState ? +getIdOfState(item?.licensedState) : "",
              },
            ],
          };
        }
      ),
      providerType: formValues.providerType,
    } as Provider;

    editNurseService.mutate({ user: payload, avatar: formValues.avatar });
  };

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSubmit)}>
        <Stack spacing={3}>
          <Grid container spacing={2}>
            <Grid size={2}>
              <UploadImage name="avatar" defaultImage={profile?.avatar as string} isLoading={loadingProfile} />
            </Grid>
            <Grid container size={10}>
              <Grid size={6}>
                <Input name="firstName" isRequired />
              </Grid>
              <Grid size={6}>
                <Input name="lastName" isRequired />
              </Grid>
              <Grid size={6}>
                <Input
                  name="email"
                  isRequired
                  disabled={Boolean(profile?.emailVerified)}
                  isVerified={Boolean(profile?.emailVerified)}
                />
              </Grid>
              <Grid size={6}>
                <InputPhoneNumber isRequired />
              </Grid>
              <Grid size={6}>
                <Input
                  name="npi"
                  label="NPI"
                  placeholder="Enter NPI Number"
                  isRequired
                  disabled={Boolean(profile?.emailVerified)}
                  inputProps={{ maxLength: 10 }}
                />
              </Grid>
              <Grid size={6}>
                <Select name="providerType" options={providerTypes} placeholder="Select Provider Type" isRequired />
              </Grid>
            </Grid>
          </Grid>
          <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
            <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
              <Typography variant="medium">Address</Typography>
            </Box>
            <Grid container spacing={2} sx={{ padding: 2 }}>
              <Grid container spacing={2}>
                <Grid size={6}>
                  <Input name="address.line1" label="Line 1" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input name="address.line2" label="Line 2" />
                </Grid>
                <Grid size={6}>
                  <Autocomplete
                    name="address.state"
                    label="State"
                    options={stateList.map((item) => ({ label: item.value, value: item.key }))}
                    placeholder="Select State"
                    isRequired
                  />
                </Grid>
                <Grid size={6}>
                  <Input name="address.city" label="City" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input name="address.country" label="Country" isRequired disabled />
                </Grid>
                <Grid size={6}>
                  <Input name="address.zipcode" label="Zip Code" isRequired />
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Box>
            <Typography variant="medium">License Details</Typography>
            <Box sx={{ backgroundColor: "#EEFBFF", padding: 1.5, marginTop: 1.5 }}>
              {licenseFields.map((license, index) => (
                <Box sx={{ display: "flex", gap: 1, marginBottom: 2 }} key={license.id}>
                  <Grid size="grow">
                    <Grid container spacing={2}>
                      <Grid size={4}>
                        <Input
                          name={`licenses.${index}.licenseNumber`}
                          label="Licensed Number"
                          placeholder="Enter License Number"
                          isRequired
                        />
                      </Grid>
                      <Grid size={4}>
                        <Autocomplete
                          name={`licenses.${index}.licensedState`}
                          label="Licensed State"
                          options={
                            licensedStateOptions
                              ? licensedStateOptions
                                  .map((item) => ({ label: item.value, value: item.key }))
                                  .sort((a, b) => a.label.localeCompare(b.label))
                              : []
                          }
                          placeholder="Select State"
                          isRequired
                        />
                      </Grid>
                      <Grid size={4}>
                        <Datepicker
                          name={`licenses.${index}.licenseExpiryDate`}
                          label="License Expiry Date"
                          isRequired
                          minDate={new Date()}
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid size="auto">
                    <InputLabel sx={{ mb: 1 }}>&nbsp;</InputLabel>
                    <IconButton onClick={() => removeLicenses(index)} disabled={licenseFields.length <= 1}>
                      <DeleteOutlineOutlinedIcon />
                    </IconButton>
                  </Grid>
                </Box>
              ))}
              <Button
                sx={{
                  "&.MuiButton-outlined": {
                    borderColor: "#078EB9",
                    color: "#078EB9",
                  },
                  "&.MuiButton-outlined:hover": {
                    backgroundColor: "transparent",
                    opacity: 0.8,
                  },
                }}
                variant="outlined"
                startIcon={<AddOutlinedIcon />}
                onClick={() =>
                  appendLicenses({
                    licensedState: "",
                    licenseExpiryDate: "" as unknown as Date,
                    licenseNumber: "",
                  })
                }
              >
                Add License Details
              </Button>
            </Box>
          </Box>
        </Stack>
        <Button variant="contained" type="submit" loading={editNurseService.isPending} sx={{ mt: 2 }}>
          Save Profile
        </Button>
      </form>
    </FormProvider>
  );
};

export default EditProfile;
