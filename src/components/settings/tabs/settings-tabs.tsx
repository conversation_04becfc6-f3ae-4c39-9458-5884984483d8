import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useLocation, useSearchParams } from "react-router-dom";

import { Tab, Tabs } from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import DevicesTab from "@/components/provider-portal/Devices/DevicesTab";
import CarePlanTab from "@/components/provider-portal/carePlan/care-plan-tab";

import CustomDrawer from "../../../common-components/custom-drawer/custom-drawer";
import { CustomTabPanel, a11yProps } from "../../../common-components/custom-tab/custom-tab";
import useAuthority from "../../../hooks/use-authority";
import { setIsLoading } from "../../../redux/actions/loader-action";
import { ProviderGroup, ProviderGroupControllerService } from "../../../sdk/requests";
import Roles from "../../provider-portal/Roles/Roles";
import BillingTab from "../../provider-portal/billing/BillingTab";
import ConsentForm from "../../provider-portal/consent-forms/consent-forms-list";
import LocationList from "../../provider-portal/locations/location-list";
import MedicalCodesTab from "../../provider-portal/medical-codes/medical-codes-tab";

const SettingsTabs = () => {
  const { isProvider, isProviderGroupAdmin } = useAuthority();
  const tabLabels = [
    "Locations",
    "Medical Codes",
    "Consent Forms",
    "Care Plans",
    "Device Library",
    ...(isProviderGroupAdmin ? ["Roles"] : []),
    "Billing",
  ];
  const [searchParams, setSearchParams] = useSearchParams();
  const [value, setValue] = useState(Number(searchParams.get("tab")) || 0);

  const location = useLocation();
  const [providerGroup, setProviderGroup] = useState<ProviderGroup>({} as ProviderGroup);
  const dispatch = useDispatch();
  const providerGroupDetails = (location.state?.providerGroup || {}) as ProviderGroup;
  const [openAddPGDialog, setOpenAddPGDialog] = useState(false);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSearchParams({ tab: newValue.toString() });
    setValue(newValue);
  };

  const { data, isSuccess, isLoading, isRefetching } = useQuery({
    queryKey: ["pg-by-id", providerGroupDetails?.uuid],
    queryFn: () =>
      ProviderGroupControllerService.getProviderGroupById({
        providerGroupId: providerGroupDetails.uuid || "",
      }),
    enabled: !!providerGroupDetails?.uuid,
  });

  useEffect(() => {
    if (isSuccess) {
      setProviderGroup((data as unknown as AxiosResponse).data);
    }
  }, [isSuccess, data]);

  useEffect(() => {
    dispatch(setIsLoading(isLoading || isRefetching));
  }, [dispatch, isLoading, isRefetching]);

  return (
    <Grid height={"100%"} p={2} width={"100%"} maxWidth={"100%"} overflow={"auto"}>
      <Grid height={"100%"} borderRadius={"8px"} container flexDirection={"column"}>
        <Grid>
          <Grid sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs value={value} onChange={handleChange}>
              {tabLabels
                .filter((label) => !(isProvider && label === "Locations"))
                .map((item, index) => (
                  <Tab sx={{ textTransform: "none", fontWeight: 550 }} key={index} label={item} {...a11yProps(0)} />
                ))}
            </Tabs>
          </Grid>
          <Grid flex={1}>
            {tabLabels
              .filter((label) => !(isProvider && label === "Locations"))
              .map((item, index) => (
                <CustomTabPanel key={index} value={value} index={index}>
                  {item === "Locations" && <LocationList />}
                  {item === "Consent Forms" && <ConsentForm />}
                  {item === "Care Plans" && <CarePlanTab />}
                  {item === "Medical Codes" && <MedicalCodesTab />}
                  {item === "Device Library" && <DevicesTab />}
                  {item === "Roles" && <Roles />}
                  {item === "Billing" && <BillingTab />}
                </CustomTabPanel>
              ))}
          </Grid>
        </Grid>
      </Grid>
      <CustomDrawer
        anchor={"right"}
        open={openAddPGDialog}
        title={` ${providerGroup?.name ? providerGroup?.name + ": Edit Details" : "Edit Details"}`}
        onClose={() => setOpenAddPGDialog(false)}
      >
        <>HEllo</>
      </CustomDrawer>
    </Grid>
  );
};

export default SettingsTabs;
