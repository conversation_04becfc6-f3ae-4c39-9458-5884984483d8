/* eslint-disable react-refresh/only-export-components */
import React, { ReactNode, createContext, useContext, useEffect, useState } from "react";

import MainDrawer from "../ui/MainDrawer";

interface DrawerContent {
  title?: string;
  component: ReactNode;
}

interface DrawerContextType {
  isOpen: boolean;
  content: DrawerContent | null;
  open: (content: DrawerContent, width?: string) => void;
  close: () => void;
  drawerWidth?: string;
}

const DrawerContext = createContext<DrawerContextType | undefined>(undefined);

export const DrawerProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [content, setContent] = useState<DrawerContent | null>(null);
  const [drawerWidth, setDrawerWidth] = useState<string>("800px");

  useEffect(() => {
    if (content) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [content]);

  const open = (drawerContent: DrawerContent, width?: string) => {
    setContent(drawerContent);
    setDrawerWidth(width || "800px");
  };

  const close = () => {
    setContent(null);
  };

  return (
    <DrawerContext.Provider value={{ isOpen, content, open, close, drawerWidth }}>
      <MainDrawer drawerWidth={drawerWidth} />
      {children}
    </DrawerContext.Provider>
  );
};

export const useDrawer = () => {
  const context = useContext(DrawerContext);

  if (context === undefined) {
    throw new Error("useDrawer must be used within a DrawerProvider");
  }
  return context;
};
