import { useCallback, useEffect, useRef, useState } from "react";
import { Controller, FieldError, FieldValues, Path, useFormContext } from "react-hook-form";

import { useAutocomplete } from "@mui/base/useAutocomplete";
import UnfoldMoreRoundedIcon from "@mui/icons-material/UnfoldMoreRounded";
import { CircularProgress, InputLabel } from "@mui/material";
import { styled } from "@mui/system";

import clsx from "clsx";
import { debounce, startCase } from "lodash";

interface Option {
  label: string;
  value: string;
  disabled?: boolean;
}

interface AutocompleteProps<T extends FieldValues> {
  name: Path<T>;
  options: Option[];
  placeholder?: string;
  rules?: object;
  label?: string;
  noLabel?: boolean;
  isRequired?: boolean;
  onSearch?: (value: string) => void;
  isLoading?: boolean;
}

export default function Autocomplete<T extends FieldValues>({
  name,
  options,
  placeholder,
  rules,
  label,
  noLabel,
  isRequired,
  onSearch,
  isLoading,
}: AutocompleteProps<T>) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <CustomAutocomplete
          options={options}
          placeholder={isLoading ? "Loading..." : placeholder}
          value={value}
          onChange={onChange}
          error={error}
          label={label || startCase(name)}
          isRequired={isRequired}
          noLabel={noLabel}
          onSearch={onSearch}
          isLoading={isLoading}
        />
      )}
    />
  );
}

interface CustomAutocompleteProps {
  options: Option[];
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: FieldError;
  noLabel?: boolean;
  isRequired?: boolean;
  onSearch?: (value: string) => void;
  isLoading?: boolean;
}

function CustomAutocomplete({
  label,
  options,
  placeholder,
  value,
  onChange,
  error,
  noLabel = false,
  isRequired = false,
  onSearch,
  isLoading = false,
}: CustomAutocompleteProps) {
  const rootRef = useRef<HTMLDivElement>(null);
  const inputWrapperRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState<{ top?: number; bottom?: number; width?: number }>({});

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      onSearch?.(value);
    }, 300),
    [onSearch]
  );

  const updatePosition = useCallback(() => {
    if (!inputWrapperRef.current) return;

    const rect = inputWrapperRef.current.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const spaceBelow = windowHeight - rect.bottom;
    const spaceAbove = rect.top;
    const GAP = 8;

    if (spaceBelow < 200 && spaceAbove > spaceBelow) {
      setPosition({
        bottom: windowHeight - rect.top + GAP,
        width: rect.width,
      });
    } else {
      setPosition({
        top: rect.top + rect.height + GAP,
        width: rect.width,
      });
    }
  }, []);

  useEffect(() => {
    updatePosition();
    window.addEventListener("scroll", updatePosition, true);
    window.addEventListener("resize", updatePosition);

    return () => {
      window.removeEventListener("scroll", updatePosition, true);
      window.removeEventListener("resize", updatePosition);
    };
  }, [updatePosition]);

  const { getRootProps, getInputProps, getListboxProps, getOptionProps, groupedOptions, focused } = useAutocomplete({
    options,
    value: options?.find((opt) => opt.value === value) || null,
    onInputChange: (_event, value) => {
      // TODO: Remove this once the search is implemented
      return;
      /* eslint-disable  no-unreachable */
      debouncedSearch(value);
    },
    onChange: (_event, newValue) => {
      const selectedValue = (newValue as Option)?.value || "";
      onChange?.(selectedValue);
    },
    onOpen: updatePosition,
    getOptionLabel: (option) => {
      if (typeof option === "string") return option;
      return (option as Option).label;
    },
    isOptionEqualToValue: (option: Option, value: string | Option) =>
      typeof value === "string" ? option.value === value : option.value === value.value,
  });

  return (
    <Root ref={rootRef}>
      {!noLabel && (
        <InputLabel
          sx={{
            mb: 1,
            fontSize: "14px",
            fontWeight: "500",
            color: "#515C5F",
          }}
        >
          {label}&nbsp;
          {isRequired && <span style={{ color: "#D32F2F" }}>*</span>}
        </InputLabel>
      )}

      <InputWrapper ref={inputWrapperRef} {...getRootProps()} className={clsx({ focused, error: !!error })}>
        <Input {...getInputProps()} placeholder={placeholder ?? "Select..."} prefix="$" />
        <IconWrapper>
          <UnfoldMoreRoundedIcon sx={{ fontSize: 20, color: "gray" }} />
        </IconWrapper>
      </InputWrapper>

      {groupedOptions.length > 0 && (
        <Listbox {...getListboxProps()} style={position}>
          {(groupedOptions as Option[]).map((option, index) => (
            <OptionItem {...getOptionProps({ option, index })} key={option.value}>
              {option.label}
            </OptionItem>
          ))}
          {isLoading && (
            <OptionItem sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
              <CircularProgress size={14} />
            </OptionItem>
          )}
        </Listbox>
      )}

      {error && (
        <InputLabel error sx={{ mt: 0.5, fontSize: "12px", whiteSpace: "normal" }}>
          {error.message}
        </InputLabel>
      )}
    </Root>
  );
}

const Root = styled("div")`
  position: relative;
  width: 100%;
`;

const InputWrapper = styled("div")(
  () => `
  font-size: 14px;
  border: 1px solid #E8EBEC;
  background-color: #FFF;
  border-radius: 8px;
  display: flex;
  flex-wrap: wrap;
  height: 43px;
  position: relative;
`
);

const Input = styled("input")`
  font-size: 14px;
  padding: 12px;
  color: #212d30;
  background: none;
  border: 0;
  outline: 0;
  flex-grow: 1;
  box-sizing: content-box;
  &::placeholder {
    color: #b6c1c4;
  }
`;

const IconWrapper = styled("div")`
  display: flex;
  align-items: center;
  padding-right: 12px;
  pointer-events: none;
`;

const Listbox = styled("ul")`
  font-size: 14px;
  box-sizing: border-box;
  padding: 5px;
  margin: 0;
  max-height: 200px;
  border-radius: 8px;
  overflow: auto;
  background: #fff;
  border: 1px solid #e8ebec;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: fixed;
  width: auto;
  z-index: 1400;
  list-style: none;
`;

const OptionItem = styled("li")`
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  &.Mui-focused,
  &:hover {
    background-color: #e5eaf2;
    color: #1c2025;
  }
`;
