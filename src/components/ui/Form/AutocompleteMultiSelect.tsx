import React, { useEffect, useMemo, useRef, useState } from "react";
import { Controller, FieldValues, Path, useFormContext } from "react-hook-form";

import { useAutocomplete } from "@mui/base/useAutocomplete";
import { Box, Chip, CircularProgress, InputLabel, Typography } from "@mui/material";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import { styled } from "@mui/system";

import { debounce, startCase } from "lodash";

interface Option {
  label: string;
  value: string;
  disabled?: boolean;
}

interface AutocompleteMultiSelectProps<T extends FieldValues> {
  name: Path<T>;
  options: Option[];
  placeholder?: string;
  rules?: object;
  label?: string;
  noLabel?: boolean;
  isRequired?: boolean;
  onSearch?: (value: string) => void;
  isLoading?: boolean;
  isEndOfListScroll?: () => void;
}

const Listbox = styled("ul")(() => ({
  width: "100%",
  margin: "2px 0 0",
  padding: "5px 0",
  position: "absolute",
  listStyle: "none",
  backgroundColor: "#fff",
  overflow: "auto",
  maxHeight: 200,
  borderRadius: "4px",
  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
  zIndex: 1,
  "& li": {
    padding: "8px 12px",
    "&:hover": {
      backgroundColor: "#f5f5f5",
      cursor: "pointer",
    },
    "&.Mui-focused": {
      backgroundColor: "#e8f0fe",
    },
  },
}));

export default function AutocompleteMultiSelect<T extends FieldValues>({
  name,
  options,
  placeholder,
  rules,
  label,
  noLabel,
  isRequired,
  onSearch,
  isLoading,
  isEndOfListScroll,
}: AutocompleteMultiSelectProps<T>) {
  const { control, watch, setValue, trigger } = useFormContext<T>();
  const selectedValues = watch(name) || [];

  const [inputVal, setInputVal] = useState<string>("");
  const [open, setOpen] = useState<boolean>(false);
  const [normalizedSelectedValues, setNormalizedSelectedValues] = useState<Option[]>([]);
  const [isFetchingMore, setIsFetchingMore] = useState<boolean>(false);

  const listboxRef = useRef<HTMLUListElement | null>(null);
  const scrollTopRef = useRef<number>(0);

  // Normalize selected values to ensure they match the expected format
  useEffect(() => {
    if (Array.isArray(selectedValues)) {
      const normalized = selectedValues.map((value) => {
        // Handle case where value is already an Option
        if (value && typeof value === "object" && "label" in value && "value" in value) {
          return value;
        }

        // Handle string values that need to be converted to Option objects
        if (typeof value === "string") {
          const matchingOption = options.find((opt) => opt.value === value);
          if (matchingOption) {
            return matchingOption;
          }
          return { label: value, value };
        }

        return value;
      });

      setNormalizedSelectedValues(normalized);
    } else {
      setNormalizedSelectedValues([]);
    }
  }, [selectedValues, options]);

  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        onSearch?.(value);
      }, 300),
    [onSearch]
  );

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // This useEffect causes duplicate API calls - let's replace it with a ref to track if initial load happened
  const initialLoadDoneRef = useRef(false);

  useEffect(() => {
    // Only run once and only if there are selected values but no options
    if (
      !initialLoadDoneRef.current &&
      onSearch &&
      Array.isArray(selectedValues) &&
      selectedValues.length > 0 &&
      options.length === 0
    ) {
      initialLoadDoneRef.current = true;
      onSearch("");
    }
  }, [onSearch, selectedValues, options.length]);

  // Reset fetching state when loading changes
  useEffect(() => {
    if (!isLoading) {
      setIsFetchingMore(false);
    }
  }, [isLoading]);

  const handleScroll = (event: React.UIEvent<HTMLUListElement>) => {
    const listboxNode = event.currentTarget;
    scrollTopRef.current = listboxNode.scrollTop;
    if (listboxNode.scrollTop + listboxNode.clientHeight >= listboxNode.scrollHeight - 50) {
      if (!isFetchingMore && !isLoading) {
        setIsFetchingMore(true);
        isEndOfListScroll?.();
      }
    }
  };

  useEffect(() => {
    if (listboxRef.current) {
      listboxRef.current.scrollTop = scrollTopRef.current;
    }
  }, [options]);

  const { getRootProps, getInputProps, getTagProps, getListboxProps, getOptionProps, groupedOptions } = useAutocomplete(
    {
      multiple: true,
      options,
      open,
      value: normalizedSelectedValues,
      inputValue: inputVal,
      filterOptions: (options) => options,
      onInputChange: (_event, newValue, reason) => {
        if (reason === "input") {
          setInputVal(newValue);
          debouncedSearch(newValue);
          setIsFetchingMore(false); // Reset on new search
        }
      },
      onChange: (_, newValue) => {
        setValue(name, newValue as T[typeof name]);
        trigger(name);
        setInputVal("");
        onSearch?.("");
      },
      onOpen: () => {
        setOpen(true);
        // Only load options if they're not already loaded
        if (onSearch && options.length === 0 && !initialLoadDoneRef.current) {
          initialLoadDoneRef.current = true;
          onSearch("");
        }
      },
      onClose: () => {},
      getOptionDisabled: (option) => option.disabled || false,
      getOptionLabel: (option) => (typeof option === "string" ? option : option.label),
      isOptionEqualToValue: (option, value) =>
        (typeof option === "string" ? option : option.value) === (typeof value === "string" ? value : value.value),
      disableCloseOnSelect: true,
    }
  );

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ fieldState }) => (
        <ClickAwayListener onClickAway={() => setOpen(false)}>
          <Box sx={{ position: "relative", width: "100%" }}>
            {!noLabel && (
              <InputLabel
                sx={{
                  fontSize: "14px",
                  fontWeight: "500",
                  color: "#515C5F",
                  marginBottom: 1,
                }}
              >
                {label || startCase(name)}
                {isRequired && <span style={{ color: "#D32F2F" }}> *</span>}
              </InputLabel>
            )}
            <Box
              {...getRootProps()}
              sx={{
                display: "flex",
                flexWrap: "wrap",
                border: `1px solid #E8EBEC`,
                borderRadius: "8px",
                padding: "3px 8px",
                minHeight: "43px",
                backgroundColor: "#fff",
              }}
            >
              {Array.isArray(normalizedSelectedValues) &&
                normalizedSelectedValues.map((option: Option, index: number) => (
                  <Chip
                    label={option.label}
                    {...getTagProps({ index })}
                    size="small"
                    key={option.value}
                    sx={{
                      margin: "4px 4px 4px 0",
                      backgroundColor: "#F2F7FF",
                      color: "#2D7AE5",
                      "& .MuiChip-deleteIcon": {
                        color: "#2D7AE5",
                        "&:hover": {
                          color: "#1A5BB5",
                        },
                      },
                    }}
                  />
                ))}
              <input
                {...getInputProps()}
                placeholder={normalizedSelectedValues.length === 0 ? placeholder || "Please Select" : ""}
                style={{
                  flex: 1,
                  border: "none",
                  outline: "none",
                  padding: "8px 4px",
                  fontSize: "14px",
                  minWidth: "50px",
                }}
              />
              {isLoading && <CircularProgress size={20} sx={{ margin: "8px 4px" }} />}
            </Box>

            {fieldState.error && (
              <Typography color="error" variant="caption" sx={{ mt: 0.5, display: "block" }}>
                {fieldState.error.message}
              </Typography>
            )}

            {open && (
              <Listbox {...getListboxProps()} ref={listboxRef} onScroll={handleScroll}>
                {groupedOptions.length === 0 ? (
                  <li style={{ padding: "8px 12px", color: "#888", cursor: "default" }} aria-disabled="true">
                    {isLoading ? "Loading..." : "No Data"}
                  </li>
                ) : (
                  (groupedOptions as Option[]).map((option, index) => {
                    const isSelected = normalizedSelectedValues.some(
                      (selectedOption: Option) => selectedOption.value === option.value
                    );

                    return (
                      <li
                        {...getOptionProps({ option, index })}
                        key={option.value}
                        style={{
                          backgroundColor: isSelected ? "#F2F7FF" : undefined,
                          color: isSelected ? "#2D7AE5" : undefined,
                        }}
                      >
                        {option.label}
                      </li>
                    );
                  })
                )}
              </Listbox>
            )}
          </Box>
        </ClickAwayListener>
      )}
    />
  );
}
