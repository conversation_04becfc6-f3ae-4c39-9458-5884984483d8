import { Controller, useFormContext } from "react-hook-form";

import { InputLabel, TextareaAutosize, TextareaAutosizeProps } from "@mui/material";

import { startCase } from "lodash";

interface TextareaProps extends TextareaAutosizeProps {
  name: string;
  label?: string;
  isRequired?: boolean;
  placeholder?: string;
}

export const Textarea = ({ name, label, isRequired, placeholder, ...props }: TextareaProps) => {
  const { style, ...textAreaProps } = props;
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <>
          <InputLabel sx={{ mb: 1, fontSize: "14px", fontWeight: "500", color: "#515C5F" }}>
            {label || startCase(name)}&nbsp;
            {isRequired && <span style={{ color: "#D32F2F" }}>*</span>}
          </InputLabel>
          <TextareaAutosize
            minRows={2}
            placeholder={placeholder || `Enter ${startCase(name)}`}
            style={{
              padding: "12px",
              fontSize: "14px",
              border: "1px solid #E8EBEC",
              borderRadius: "8px",
              background: "#FFFFFF",
              boxSizing: "border-box",
              outline: "none",
              fontFamily: "Roboto, sans-serif",
              resize: "none",
              ...style,
            }}
            {...field}
            {...textAreaProps}
          />
          {error && (
            <InputLabel error sx={{ mt: 0.5, fontSize: "12px", whiteSpace: "normal" }}>
              {error.message}
            </InputLabel>
          )}
        </>
      )}
    />
  );
};
