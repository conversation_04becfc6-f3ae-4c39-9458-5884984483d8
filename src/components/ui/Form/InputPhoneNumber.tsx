import { Controller, useFormContext } from "react-hook-form";

import { Grid2 as Grid, InputBase, InputBaseProps, InputLabel } from "@mui/material";

import Select from "./Select";

interface InputProps extends Omit<InputBaseProps, "name"> {
  label?: string;
  isRequired?: boolean;
  namePrefix?: string;
  namePhone?: string;
}

const formatPhoneNumber = (value: string) => {
  // Remove all non-digits
  const cleaned = value.replace(/\D/g, "");

  // Format as the user types
  if (cleaned.length <= 3) {
    return cleaned;
  } else if (cleaned.length <= 6) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3)}`;
  } else {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
  }
};

const getUnformattedValue = (value: string) => value.replace(/-/g, "");

export const InputPhoneNumber = ({ label, isRequired, namePrefix, namePhone, ...props }: InputProps) => {
  const { control } = useFormContext();

  return (
    <>
      <InputLabel
        sx={{
          mb: 1,
          fontSize: "14px",
          fontWeight: "500",
          color: "#515C5F",
        }}
      >
        {label || "Phone Number"}&nbsp;
        {isRequired && <span style={{ color: "#D32F2F" }}>*</span>}
      </InputLabel>
      <Grid container columnGap={1} flexWrap="nowrap">
        <Grid size={3}>
          <Select name={namePrefix || "prefix"} options={[{ label: "+1", value: "+1" }]} noLabel defaultValue="+1" />
        </Grid>
        <Grid size={9}>
          <Controller
            name={namePhone || "phone"}
            control={control}
            defaultValue=""
            render={({ field, fieldState: { error } }) => (
              <>
                <InputBase
                  {...field}
                  {...props}
                  error={!!error}
                  fullWidth
                  onChange={(e) => {
                    const formattedValue = formatPhoneNumber(e.target.value);
                    // Store unformatted value
                    field.onChange(getUnformattedValue(formattedValue));
                  }}
                  // Display formatted but store unformatted
                  value={formatPhoneNumber(field.value)}
                  inputProps={{
                    type: "tel",
                    maxLength: 12, // Adjusted for the formatted length
                    placeholder: props.placeholder || `Enter Phone Number`,
                    sx: {
                      padding: "12px",
                      fontSize: "14px",
                      border: "1px solid #E8EBEC",
                      borderRadius: "8px",
                      height: "100%",
                      boxSizing: "border-box",
                      "&::placeholder": {
                        color: "#B6C1C4",
                        opacity: 1,
                      },
                    },
                  }}
                />
                {error && (
                  <InputLabel error sx={{ mt: 0.5, fontSize: "12px", whiteSpace: "normal" }}>
                    {error.message}
                  </InputLabel>
                )}
              </>
            )}
          />
        </Grid>
      </Grid>
    </>
  );
};
