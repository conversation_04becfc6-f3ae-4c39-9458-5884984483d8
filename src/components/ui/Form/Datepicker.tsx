import { forwardRef } from "react";
import { Controller, FieldValues, useFormContext } from "react-hook-form";

import { InputLabel } from "@mui/material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { DatePicker, DatePickerProps } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";

import { enIN } from "date-fns/locale";
import { startCase } from "lodash";

interface DatepickerProps {
  name: string;
  label?: string;
  isRequired?: boolean;
  disabled?: boolean;
  minDate?: Date;
  maxDate?: Date;
  placeholder?: string;
}

export const Datepicker = ({
  name,
  label,
  isRequired = false,
  disabled = false,
  minDate,
  maxDate,
  placeholder,
}: DatepickerProps) => {
  const { control, setError, clearErrors } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={null}
      render={({ field, fieldState: { error } }) => (
        <>
          {label && (
            <InputLabel
              sx={{
                mb: 1,
                fontSize: "14px",
                fontWeight: "500",
                color: "#515C5F",
              }}
            >
              {label || startCase(name)}&nbsp;
              {isRequired && <span style={{ color: "#D32F2F" }}>*</span>}
            </InputLabel>
          )}

          <CustomDatepicker
            {...field}
            disabled={disabled}
            minDate={minDate}
            maxDate={maxDate}
            error={Boolean(error)}
            onChange={(value) => {
              if (value && value.toString() === "Invalid Date") {
                setError(name, { message: "Invalid date" });
              } else {
                clearErrors(name);
              }

              field.onChange(value);
            }}
            placeholder={placeholder || (name ? startCase(`Select ${name}`) : "Filter by Date")}
          />

          {error && (
            <InputLabel error sx={{ mt: 0.5, fontSize: "12px", whiteSpace: "normal" }}>
              {error.message}
            </InputLabel>
          )}
        </>
      )}
    />
  );
};

interface CustomDatepickerProps extends DatePickerProps<Date> {
  field?: FieldValues;
  onChange?: (value: Date | null) => void;
  disabled?: boolean;
  minDate?: Date;
  maxDate?: Date;
  error?: boolean;
  placeholder?: string;
}

export const CustomDatepicker = forwardRef<HTMLDivElement, CustomDatepickerProps>(
  (
    {
      field = {},
      onChange,
      disabled,
      minDate,
      maxDate,
      format = "dd/MM/yyyy",
      error,
      placeholder,
      ...props
    }: CustomDatepickerProps,
    ref
  ) => {
    const { slotProps, ...restProps } = props;

    return (
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enIN}>
        <DatePicker
          {...field}
          format={format}
          disabled={disabled}
          minDate={minDate}
          maxDate={maxDate}
          onChange={onChange}
          ref={ref}
          slotProps={{
            ...slotProps,
            textField: {
              placeholder: placeholder,
              error: error,
              fullWidth: true,
              sx: {
                "& .MuiInputBase-root": {
                  height: "43px",
                  background: "#FFFFFF",
                  border: "1px solid #E8EBEC",
                  borderRadius: "8px",
                },
                "& .MuiInputBase-input": {
                  padding: "0 12px",
                  fontSize: "14px",
                  color: "#515C5F",
                },
                "& .MuiInputAdornment-root button": {
                  color: "#515C5F ",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none",
                },
              },
            },
          }}
          {...restProps}
        />
      </LocalizationProvider>
    );
  }
);

CustomDatepicker.displayName = "CustomDatepicker";
