import React from "react";

import { ButtonBase, ButtonBaseProps } from "@mui/material";

interface ChipButtonProps extends ButtonBaseProps {
  label: string;
  icon?: React.ReactNode;
}

const ChipButton = ({ label, icon, ...props }: ChipButtonProps) => {
  const { sx, ...rest } = props;

  return (
    <ButtonBase
      sx={{
        border: "1px solid #CDD7DA",
        borderRadius: "8px",
        fontWeight: 500,
        fontSize: "0.75rem",
        color: "#364144",
        background: "#FFFFFF",
        padding: "2px 8px",
        minHeight: "22px",
        display: "flex",
        alignItems: "center",
        gap: "8px",
        "&:hover": {
          opacity: 0.8,
        },
        ...sx,
      }}
      {...rest}
    >
      {icon}
      {label}
    </ButtonBase>
  );
};

export default ChipButton;
