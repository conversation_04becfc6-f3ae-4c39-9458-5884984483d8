import { LinearProgress as BaseLinearProgress, LinearProgressProps, linearProgressClasses } from "@mui/material";
import { styled } from "@mui/material/styles";

interface LineProgressProps extends LinearProgressProps {
  value: number;
}

const LineProgress = ({ value, ...props }: LineProgressProps) => {
  return <LinearProgress value={value} {...props} />;
};

const LinearProgress = styled(BaseLinearProgress)(() => ({
  height: 4,
  borderRadius: 5,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: "#E8EBEC",
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 100,
    backgroundColor: "#049B22",
  },
}));

export default LineProgress;
