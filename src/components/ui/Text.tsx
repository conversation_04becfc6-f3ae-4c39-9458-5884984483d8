import React from "react";

import { Skeleton, Typography, TypographyProps } from "@mui/material";

interface TextProps extends TypographyProps {
  children: React.ReactNode;
  isLoading?: boolean;
}

const Text: React.FC<TextProps> = ({ children, isLoading, ...props }) => {
  return isLoading ? (
    <Skeleton variant="text" sx={{ fontSize: props.fontSize || "0.875rem", width: "100px" }} />
  ) : (
    <Typography {...props}>{children}</Typography>
  );
};

export default Text;
