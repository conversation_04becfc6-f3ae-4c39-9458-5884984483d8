import { PropsWithChildren } from "react";

import { Box, LinearProgress } from "@mui/material";
import type { BoxProps } from "@mui/material";

interface DrawerBodyProps extends BoxProps {
  offset?: number;
  isLoading?: boolean;
}

const DrawerBody = ({ offset, children, isLoading, ...props }: PropsWithChildren<DrawerBodyProps>) => {
  const { sx, ...restProps } = props;

  return (
    <Box
      sx={{
        flexGrow: 1,
        maxHeight: `calc(100% - ${offset ?? 0}px)`,
        overflow: "auto",
        ...sx,
      }}
      {...restProps}
      padding={isLoading ? 0 : Number(restProps.padding)}
    >
      {isLoading ? <LinearProgress /> : children}
    </Box>
  );
};

export default DrawerBody;
