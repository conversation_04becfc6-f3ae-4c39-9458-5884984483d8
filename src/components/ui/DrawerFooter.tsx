import React from "react";
import { PropsWithChildren } from "react";

import { Box, Grid2 as Grid } from "@mui/material";

import { theme } from "@/utils/theme";

interface DrawerFooterProps extends PropsWithChildren {
  footerRef?: React.RefObject<HTMLDivElement>;
}

const DrawerFooter = ({ footerRef, children }: DrawerFooterProps) => {
  return (
    <Box
      ref={footerRef}
      sx={{
        width: "100%",
        backgroundColor: theme.palette.background.paper,
        borderTop: `1px solid ${theme.palette.divider}`,
        position: "absolute",
        bottom: 0,
        left: 0,
        padding: "20px 24px",
        height: "80px",
      }}
    >
      <Grid container justifyContent="flex-end">
        {children}
      </Grid>
    </Box>
  );
};

export default DrawerFooter;
