import React from "react";

import { Box } from "@mui/material";

import { FIXED_HEADER_HEIGHT } from "@/constants/styles";

const FillContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "stretch",
        justifyContent: "stretch",
        width: "100%",
        height: `calc(100vh - ${FIXED_HEADER_HEIGHT}px)`,
        padding: 2,
        overflow: "hidden",
        "& > *": {
          flex: 1,
        },
      }}
    >
      {children}
    </Box>
  );
};

export default FillContainer;
