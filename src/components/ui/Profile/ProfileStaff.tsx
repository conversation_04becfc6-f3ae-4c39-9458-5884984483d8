import { Ava<PERSON>, Box, Grid2 as Grid, <PERSON>arProgress, Stack, Typography } from "@mui/material";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import { Roles } from "@/constants/roles";
import { UserControllerService } from "@/sdk/requests";
import { User } from "@/sdk/requests";
import { titleCase } from "@/utils/format/string";
import { theme } from "@/utils/theme";

interface ProfileStaffProps {
  staffId: string;
  xTenantId: string;
}

const ProfileStaff = (props: ProfileStaffProps) => {
  const { staffId, xTenantId } = props;

  const { data: profile, isLoading: loadingProfile } = useQuery({
    queryKey: ["profile", staffId],
    queryFn: async () => {
      const response = (await UserControllerService.getUser({
        userId: String(staffId),
        xTenantId: xTenantId,
      })) as AxiosResponse<User>;

      return response.data as User;
    },
    enabled: !!staffId,
  });

  if (loadingProfile) {
    return <LinearProgress />;
  }

  return (
    <Stack spacing={3} sx={{ padding: 2 }}>
      <Grid container spacing={2}>
        <Grid size={2}>
          <Box sx={{ width: "100%", aspectRatio: "1/1", background: "grey.200" }}>
            <Avatar
              src={profile?.avatar || ""}
              alt={profile?.firstName || ""}
              sx={{
                width: "100%",
                height: "100%",
                border: "2px solid",
                borderColor: "grey.200",
              }}
            />
          </Box>
        </Grid>
        <Grid container size={10}>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                First Name
              </Typography>
              <Typography variant="medium">{profile?.firstName}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Last Name
              </Typography>
              <Typography variant="medium">{profile?.lastName}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Email
              </Typography>
              <Typography variant="medium">{profile?.email}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Phone
              </Typography>
              <Typography variant="medium">{profile?.phone}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Role
              </Typography>
              <Typography variant="medium">{titleCase(profile?.role)}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Gender
              </Typography>
              <Typography variant="medium">{titleCase(profile?.gender)}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Status
              </Typography>
              <Typography variant="medium">{profile?.active ? "Active" : "Inactive"}</Typography>
            </Stack>
          </Grid>
          {profile?.role === Roles.SITE_ADMIN && (
            <Grid size={6}>
              <Stack spacing={0.75}>
                <Typography variant="medium" color="#515C5F">
                  Location
                </Typography>
                <Typography variant="medium">{profile?.locationName}</Typography>
              </Stack>
            </Grid>
          )}
        </Grid>
      </Grid>
      <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
        <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
          <Typography variant="medium">Address</Typography>
        </Box>
        <Grid container spacing={2} sx={{ padding: 2 }}>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Line 1
              </Typography>
              <Typography variant="medium">{profile?.address?.line1}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Line 2
              </Typography>
              <Typography variant="medium">{profile?.address?.line2 || "-"}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                State
              </Typography>
              <Typography variant="medium">{profile?.address?.state}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                City
              </Typography>
              <Typography variant="medium">{profile?.address?.city}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Country
              </Typography>
              <Typography variant="medium">{profile?.address?.country}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Zip Code
              </Typography>
              <Typography variant="medium">{profile?.address?.zipcode}</Typography>
            </Stack>
          </Grid>
        </Grid>
      </Box>
    </Stack>
  );
};

export default ProfileStaff;
