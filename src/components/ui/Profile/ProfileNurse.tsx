import { Avatar, Box, Grid2 as Grid, LinearProgress, Stack, Typography } from "@mui/material";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import { Provider, ProviderControllerService } from "@/sdk/requests";
import { formatDateOnly } from "@/utils/format/date";
import { titleCase } from "@/utils/format/string";
import { theme } from "@/utils/theme";

interface ProfileNurseProps {
  providerId: string;
  xTenantId?: string;
}

const ProfileNurse = (props: ProfileNurseProps) => {
  const { providerId, xTenantId } = props;

  const { data: profile, isLoading: loadingProfile } = useQuery({
    queryKey: ["provider", providerId],
    queryFn: async () => {
      const response = (await ProviderControllerService.getProviderById({
        providerUuid: providerId,
        xTenantId: xTenantId,
      })) as AxiosResponse<Provider>;

      return response.data as Provider;
    },
    enabled: !!providerId,
  });

  if (loadingProfile) {
    return <LinearProgress />;
  }

  return (
    <Stack spacing={3} sx={{ padding: 2 }}>
      <Grid container spacing={2}>
        <Grid size={2}>
          <Box sx={{ width: "100%", aspectRatio: "1/1", background: "grey.200" }}>
            <Avatar
              src={profile?.avatar || ""}
              alt={profile?.firstName || ""}
              sx={{
                width: "100%",
                height: "100%",
                border: "2px solid",
                borderColor: "grey.200",
              }}
            />
          </Box>
        </Grid>
        <Grid container size={10}>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                First Name
              </Typography>
              <Typography variant="medium">{profile?.firstName}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Last Name
              </Typography>
              <Typography variant="medium">{profile?.lastName}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Email
              </Typography>
              <Typography variant="medium">{profile?.email}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Phone
              </Typography>
              <Typography variant="medium">{profile?.phone}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Role
              </Typography>
              <Typography variant="medium">{titleCase(profile?.role)}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Gender
              </Typography>
              <Typography variant="medium">{titleCase(profile?.gender)}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Status
              </Typography>
              <Typography variant="medium">{profile?.active ? "Active" : "Inactive"}</Typography>
            </Stack>
          </Grid>
        </Grid>
      </Grid>
      <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
        <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
          <Typography variant="medium">Address</Typography>
        </Box>
        <Grid container spacing={2} sx={{ padding: 2 }}>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Line 1
              </Typography>
              <Typography variant="medium">{profile?.address?.line1}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Line 2
              </Typography>
              <Typography variant="medium">{profile?.address?.line2 || "-"}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                State
              </Typography>
              <Typography variant="medium">{profile?.address?.state}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                City
              </Typography>
              <Typography variant="medium">{profile?.address?.city}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Country
              </Typography>
              <Typography variant="medium">{profile?.address?.country}</Typography>
            </Stack>
          </Grid>
          <Grid size={6}>
            <Stack spacing={0.75}>
              <Typography variant="medium" color="#515C5F">
                Zip Code
              </Typography>
              <Typography variant="medium">{profile?.address?.zipcode}</Typography>
            </Stack>
          </Grid>
        </Grid>
      </Box>
      <Stack spacing={1.5}>
        <Typography variant="medium">License Details</Typography>
        <Box sx={{ backgroundColor: "#EEFBFF", padding: 1.5 }}>
          {Number(profile?.providerLicenseDetails?.length) > 0 ? (
            <Stack spacing={1}>
              <Grid container spacing={2}>
                <Grid size={4}>
                  <Typography variant="medium" color="#515C5F">
                    License Number
                  </Typography>
                </Grid>
                <Grid size={4}>
                  <Typography variant="medium" color="#515C5F">
                    Licensed State
                  </Typography>
                </Grid>
                <Grid size={4}>
                  <Typography variant="medium" color="#515C5F">
                    License Expiry Date
                  </Typography>
                </Grid>
              </Grid>
              {profile?.providerLicenseDetails?.map((license) => (
                <Grid container spacing={2}>
                  <Grid size={4}>
                    <Stack>
                      <Typography variant="medium">{license.licenseNumber}</Typography>
                    </Stack>
                  </Grid>
                  <Grid size={4}>
                    <Stack>
                      <Typography variant="medium">{license.licensedStates?.[0]?.state}</Typography>
                    </Stack>
                  </Grid>
                  <Grid size={4}>
                    <Stack>
                      <Typography variant="medium">{formatDateOnly(license.expiryDate)}</Typography>
                    </Stack>
                  </Grid>
                </Grid>
              ))}
            </Stack>
          ) : (
            <Typography variant="medium" sx={{ textAlign: "center" }}>
              No license details found
            </Typography>
          )}
        </Box>
      </Stack>
    </Stack>
  );
};

export default ProfileNurse;
