import React from "react";

import { Close } from "@mui/icons-material";
import { Box, Dialog, DialogActions, DialogContent, DialogProps, DialogTitle, Grid2 as Grid } from "@mui/material";

import IconButton from "./Atom/IconButton";

interface MainDialogProps extends DialogProps {
  title: string;
  actionButtons?: React.ReactNode;
  handleClose: () => void;
  isCloseable?: boolean;
}

const MainDialog = ({ title, open, handleClose, actionButtons, children, isCloseable = false }: MainDialogProps) => {
  return (
    <Dialog
      open={open}
      onClose={isCloseable ? handleClose : () => {}}
      fullWidth
      maxWidth="sm"
      sx={{ borderRadius: "12px" }}
    >
      <Grid container justifyContent="space-between" alignItems="center">
        <DialogTitle>{title}</DialogTitle>
        <Box sx={{ padding: "16px 24px" }}>
          <IconButton onClick={handleClose} icon={<Close sx={{ fontSize: "28  px" }} />} />
        </Box>
      </Grid>
      <DialogContent dividers>{children}</DialogContent>
      <DialogActions sx={{ padding: "16px 24px" }}>{actionButtons}</DialogActions>
    </Dialog>
  );
};

export default MainDialog;
