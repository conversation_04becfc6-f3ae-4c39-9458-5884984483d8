import { ReactNode } from "react";
import { Link } from "react-router-dom";

import MailOutlineIcon from "@mui/icons-material/MailOutline";
import { Box, Grid2 as Grid, Typography, useMediaQuery, useTheme } from "@mui/material";

import LoginIllustrationSVG from "@/assets/image_svg/auth/Login-Image.svg";
import LogoSVG from "@/assets/image_svg/logo/logo.svg";

interface AuthLayoutProps {
  children: ReactNode;
  illustrationSrc?: string;
  logoSrc?: string;
}

export default function AuthLayout({ children, illustrationSrc = LoginIllustrationSVG }: AuthLayoutProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container>
        <Grid
          size={isMobile ? 12 : 6}
          bgcolor={isMobile ? theme.palette.background.paper : theme.palette.secondary.main}
        >
          <Grid
            display={"flex"}
            alignItems={"center"}
            justifyContent={"space-between"}
            flexDirection={"column"}
            padding={"32px"}
            minHeight={"100vh"}
            maxWidth={"640px"}
            margin={"auto"}
            gap={"16px"}
          >
            <Link to="/auth/login">
              <Box component={"img"} src={LogoSVG} />
            </Link>

            {isMobile ? (
              <Box
                sx={{
                  maxWidth: 460,
                  width: "100%",
                  boxShadow: "0px 0px 16px 0px #021D2614",
                  padding: "48px",
                  borderRadius: "8px",
                  backgroundColor: theme.palette.background.paper,
                }}
              >
                {children}
              </Box>
            ) : (
              <Box component={"img"} src={illustrationSrc} maxWidth={"100%"} />
            )}
            <Grid
              display={"flex"}
              alignItems={"flex-end"}
              justifyContent={"space-between"}
              width={"100%"}
              flexWrap={"wrap"}
              gap={"8px"}
            >
              <Typography variant="bodySmall" color="primary" whiteSpace={"nowrap"}>
                © eAmata 2024
              </Typography>
              <Grid display={"flex"} alignItems={"center"} gap={"8px"}>
                <MailOutlineIcon fontSize="small" color={"primary"} />
                <Typography variant="bodySmall" color="primary">
                  <EMAIL>
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid size={6} display={"flex"} alignItems={"center"} justifyContent={"center"}>
          {!isMobile && (
            <Box
              sx={{
                maxWidth: 460,
                width: "100%",
                boxShadow: "0px 0px 16px 0px #021D2614",
                padding: "48px",
                borderRadius: "8px",
                backgroundColor: theme.palette.background.paper,
              }}
            >
              {children}
            </Box>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}
