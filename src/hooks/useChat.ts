import { useCallback, useEffect, useState } from "react";

import { chatService, type ChatSession, type SendMessageRequest } from "@/services/chat/chatService";

import type { ChatMessage } from "@/pages/provider-portal/patients/ChatBot";

interface UseChatReturn {
  // Chat sessions
  recentChats: ChatSession[];
  loadingChats: boolean;
  
  // Current chat
  currentChat: ChatSession | null;
  messages: ChatMessage[];
  loadingMessages: boolean;
  
  // Actions
  loadRecentChats: () => Promise<void>;
  selectChat: (chat: ChatSession) => Promise<void>;
  sendMessage: (content: string) => Promise<void>;
  markAsRead: (chatId: number) => Promise<void>;
  
  // Error handling
  error: string | null;
  clearError: () => void;
}

/**
 * Custom hook for managing chat state and operations
 */
export const useChat = (): UseChatReturn => {
  const [recentChats, setRecentChats] = useState<ChatSession[]>([]);
  const [loadingChats, setLoadingChats] = useState(false);
  
  const [currentChat, setCurrentChat] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const loadRecentChats = useCallback(async () => {
    try {
      setLoadingChats(true);
      setError(null);
      const chats = await chatService.getRecentChats();
      setRecentChats(chats);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load chats");
    } finally {
      setLoadingChats(false);
    }
  }, []);

  const selectChat = useCallback(async (chat: ChatSession) => {
    try {
      setLoadingMessages(true);
      setError(null);
      setCurrentChat(chat);
      
      const chatHistory = await chatService.getChatHistory(chat.id);
      setMessages(chatHistory.messages);
      
      // Mark as read when opening chat
      if (chat.unreadCount > 0) {
        await chatService.markAsRead(chat.id);
        // Update the chat in recent chats list
        setRecentChats(prev => 
          prev.map(c => c.id === chat.id ? { ...c, unreadCount: 0 } : c)
        );
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load chat messages");
    } finally {
      setLoadingMessages(false);
    }
  }, []);

  const sendMessage = useCallback(async (content: string) => {
    if (!currentChat || !content.trim()) return;

    try {
      setError(null);
      
      const request: SendMessageRequest = {
        content: content.trim(),
        chatId: currentChat.id,
        senderId: "current_provider_id", // This should come from auth context
        senderType: "Provider",
      };

      const newMessage = await chatService.sendMessage(request);
      
      // Add message to current chat
      setMessages(prev => [...prev, newMessage]);
      
      // Update last message in recent chats
      setRecentChats(prev =>
        prev.map(chat =>
          chat.id === currentChat.id
            ? {
                ...chat,
                lastMessage: content.length > 50 ? content.substring(0, 50) + "..." : content,
                timestamp: newMessage.timestamp,
              }
            : chat
        )
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to send message");
    }
  }, [currentChat]);

  const markAsRead = useCallback(async (chatId: number) => {
    try {
      await chatService.markAsRead(chatId);
      setRecentChats(prev =>
        prev.map(chat =>
          chat.id === chatId ? { ...chat, unreadCount: 0 } : chat
        )
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to mark as read");
    }
  }, []);

  // Load recent chats on mount
  useEffect(() => {
    loadRecentChats();
  }, [loadRecentChats]);

  return {
    recentChats,
    loadingChats,
    currentChat,
    messages,
    loadingMessages,
    loadRecentChats,
    selectChat,
    sendMessage,
    markAsRead,
    error,
    clearError,
  };
};
