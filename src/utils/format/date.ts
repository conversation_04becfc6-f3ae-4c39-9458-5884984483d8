import { differenceInYears, format, isToday, isYesterday } from "date-fns";

export const birthDate = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  const utcDate = new Date(parsedDate.getTime() + parsedDate.getTimezoneOffset() * 60000);
  const formattedDate = format(utcDate, "MMM dd, yyyy");
  const age = differenceInYears(new Date(), utcDate);

  return `${formattedDate} ( ${age} Yrs )`;
};

export const formatDate = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  return format(parsedDate, "dd/MM/yyyy, hh:mm a");
};

export const formatDateOnly = (date: Date | string | number | undefined, nilReturn: string = ""): string => {
  if (!date) return nilReturn;

  const parsedDate = new Date(date);
  return format(parsedDate, "dd/MM/yyyy");
};

export const formatTime = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  return format(parsedDate, "hh:mm a");
};

export const formatDateWithToday = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);

  if (isToday(parsedDate)) {
    return `Today, ${format(parsedDate, "dd MMM yyyy")}`;
  }

  if (isYesterday(parsedDate)) {
    return `Yesterday, ${format(parsedDate, "dd MMM yyyy")}`;
  }

  return format(parsedDate, "EEEE, dd MMM yyyy");
};
export const DateNewFormat = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  return format(parsedDate, "dd MMM yyyy");
};

export const formatDateNewFormat = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  return format(parsedDate, "dd MMM yyyy, hh:mm a");
};

export const formatTimeNewFormat = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  return format(parsedDate, "hh:mm a");
};

export const convertIstMmDdYyyyToUtc = (istDateStr: string): string => {
  if (!istDateStr) return "";
  const [month, day, year] = istDateStr.split("-").map(Number);
  const targetUtcMidnight = Date.UTC(year, month - 1, day);
  const istOffsetMillis = 5.5 * 60 * 60 * 1000;
  const finalUtcMillis = targetUtcMidnight - istOffsetMillis;
  return new Date(finalUtcMillis).toISOString();
};

export const convertSecondsToHMS = (totalSeconds: number): string => {
  if (!totalSeconds || totalSeconds < 0) return "0S";

  const h = Math.floor(totalSeconds / 3600);
  const m = Math.floor((totalSeconds % 3600) / 60);
  const s = totalSeconds % 60;

  return [h > 0 && `${h}h`, m > 0 && `${m}m`, s > 0 && `${s}s`].filter(Boolean).join(" ");
};
