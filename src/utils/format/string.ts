export const titleCase = (str?: string | null): string => {
  if (!str) return "";

  const normalized = str.toLowerCase().replace(/_/g, " ");

  return normalized
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export const getNumbersOnly = (inputString: string) => {
  if (!inputString) return "";

  return inputString.replace(/^\+1/, "").replace(/[^0-9]/g, "");
};
