import React from "react";

import { alpha, createTheme } from "@mui/material";
import { TypographyOptions } from "@mui/material/styles/createTypography";

declare module "@mui/material/Typography" {
  interface TypographyPropsVariantOverrides {
    bodySmall: true;
    bodyMedium: true;
    xl: true;
    large: true;
    medium: true;
    small: true;
    description: true;
    smallBold: true;
    iconText: true;
  }
}
interface ExtendedTypographyOptions extends TypographyOptions {
  bodySmall: React.CSSProperties;
  bodyMedium: React.CSSProperties;
  xl: React.CSSProperties;
  large: React.CSSProperties;
  medium: React.CSSProperties;
  small: React.CSSProperties;
  description: React.CSSProperties;
  smallBold: React.CSSProperties;
  iconText: React.CSSProperties;
}

// Typescript module augmentation
declare module "@mui/material/styles" {
  interface BreakpointOverrides {
    xxs: true;
    xs: true;
    xs1: true;
    sm: true;
    sm1: true;
    md: true;
    md1: true;
    lg: true;
    lg1: true;
    xl: true;
    xl1: true;
    xl2: true;
    xl3: true;
    xxl: true;
  }
}

const palette = {
  primary: {
    main: "#006E8F",
    light: "#00506B", //hover shade
  },
  secondary: {
    main: "#EAF3FF",

    light: "#F2F7F9",
  },

  background: {
    default: "#F3F4F4",
  },
  common: { white: "#FFFFFF", black: "000000" },
};

export const theme = createTheme({
  breakpoints: {
    values: {
      xxs: 360, //
      xs: 375, //
      xs1: 390, //
      sm: 744, //
      sm1: 834, //
      md: 1024, //
      md1: 1133, //
      lg: 1194, //
      lg1: 1280, // //
      xl: 1366, // //
      xl1: 1440, // //
      xl3: 1430,
      xl2: 1650,
      xxl: 1920, // //
    },
  },
  palette: {
    primary: {
      main: palette.primary.main,
      light: palette.primary.light,
    },
    secondary: {
      main: palette.secondary.main,
      light: palette.secondary.light,
    },
    background: {
      default: palette.background.default,
    },
  },
  typography: {
    bodyMedium: {
      fontSize: "16px",
    },
    bodySmall: {
      fontSize: "14px",
    },
    xl: {
      fontSize: "1.25rem",
      fontWeight: 700,
    },
    large: {
      fontSize: "1.125rem",
      fontWeight: 500,
    },
    medium: {
      fontSize: "0.875rem",
      fontWeight: 500,
    },
    small: {
      fontSize: "0.625rem",
      color: "#515C5F",
    },
    description: {
      fontSize: "0.75rem",
      color: "#7E8C8E",
    },
    smallBold: {
      fontSize: "0.625rem",
      fontWeight: 500,
      color: "#27313F",
    },
    iconText: {
      fontSize: "0.688rem",
      color: "#27313F",
      letterSpacing: "0.005em",
      lineHeight: "16.5px",
    },
  } as ExtendedTypographyOptions,
  components: {
    MuiGrid2: {
      styleOverrides: {
        root: {
          "&.bordered-box": {
            border: "1px solid #EAECF0",
            borderRadius: "4px",
            boxShadow: "0px 2px 4px -2px rgba(16, 24, 40, 0.06), 0px 4px 8px -2px rgba(16, 24, 40, 0.1)",
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          fontWeight: 500,
          "&.MuiButton-sizeMedium": {
            height: "40px",
          },
          "&.MuiButton-containedPrimary": {
            borderRadius: "8px",
            boxShadow: "none",
            "&:hover": {
              backgroundColor: palette.primary.light,
            },
          },

          "&.MuiButton-outlined": {
            borderRadius: "8px",
            border: "1px solid #CBD4E1",
            boxShadow: "none",
            color: "#3B4554",
            "&:hover": {
              backgroundColor: alpha(palette.primary.light, 0.3),
            },
          },
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          fontSize: "14px",
          textTransform: "none",
          fontFamily: "Roboto, sans-serif",
          letterSpacing: "normal",
          "&.Mui-selected": {
            backgroundColor: "#EEFBFF",
            color: "#006D8F",
            borderBottom: "2px solid #006D8F",
          },
        },
      },
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          borderColor: "#E8EBEC",
        },
      },
    },
  },
});
