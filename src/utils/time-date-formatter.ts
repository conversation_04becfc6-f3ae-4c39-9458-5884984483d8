import {
  addMinutes,
  differenceInDays,
  differenceInHours,
  differenceInMonths,
  differenceInWeeks,
  differenceInYears,
  parseISO,
} from "date-fns";
import { format, fromZonedTime, toZonedTime } from "date-fns-tz";

import { LocalTime } from "../sdk/requests";

export const getTimeZone = () => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

export const convertToUtcDateTime = (date: Date, tz: string) => {
  const utcDate = fromZonedTime(addMinutes(date, 2), tz);

  return utcDate.toISOString().replace(".000", "");
};

// export const convertToUtcDateTime = (date: Date, tz: string) => {
//   const utcDate = fromZonedTime(date, tz);

//   return utcDate.toISOString().replace(".000", "");
// };

/**Input 2023-12-17T18:30:00Z  Output 18:30:00*/
export const getTimeString = (originalDateTimeString: string) => {
  const originalDate = new Date(originalDateTimeString);

  const formattedTimeString =
    originalDate.getUTCHours().toString().padStart(2, "0") +
    ":" +
    originalDate.getUTCMinutes().toString().padStart(2, "0") +
    ":" +
    originalDate.getUTCSeconds().toString().padStart(2, "0");

  return formattedTimeString;
};

/** Required value format: 14:59:00 */
/** For displaying Local time only along with AM/PM suffix */
export const getTimeInAMPM = (value: string | undefined | null, tz: string) => {
  if (!value) {
    return;
  }
  const dateInGivenTimezone = convertInSpecificTimeZone(value, tz);

  const localTime = format(dateInGivenTimezone, "HH:mm:ss");

  return getTimeInAMPMInSameTz(localTime);
};

export const getTimeInAMPMInSameTz = (time: string) => {
  if (!time) {
    return "-";
  }
  const [localHours, localMinutes] = time.split(":");
  const localHoursAmPmFormat = +localHours % 12;
  const isPM = +localHours > 11;

  return `${(localHoursAmPmFormat <= 9 && localHoursAmPmFormat > 0 && "0") || ""}${
    localHoursAmPmFormat === 0 ? "12" : localHoursAmPmFormat
  }:${localMinutes} ${isPM ? "PM" : "AM"}`;
};

export const convertInSpecificTimeZone = (date: string, tz: string) => {
  // Check if the date string has milliseconds
  let updatedDate = date;
  if (!date) {
    return "";
  }
  if (!date.includes(".")) {
    updatedDate = date.replace("Z", ".000Z");
  }

  //TODO:how to consider day light saving
  // console.log(
  // 	"timezone offset",
  // 	(getTimezoneOffset(tz, new Date(updatedDate)) / 1000) * 60 * 60
  // ); //19800000

  // const dayLightSavingOffset =
  // 	(getTimezoneOffset(tz, new Date(updatedDate)) / 1000) * 60 * 60;

  const dateInGivenTimezone = toZonedTime(updatedDate, tz);
  return dateInGivenTimezone;
};

export const convertDateToTimezone = (date: Date | string, tz: string): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  const dateInGivenTimezone = toZonedTime(dateObj, tz);

  return format(dateInGivenTimezone, "yyyy-MM-dd'T'HH:mm:ssXXX", {
    timeZone: tz,
  });
};

// export const convertDateToTimezone = (
//   date: Date | string,
//   tz: string,
// ): string => {
//   // console.log("convertDateToTimezone date before parsing:", date);
//   // console.log("tz", tz);

//   // Parse the date as UTC if it’s a string, using `Date.parse()` for consistent UTC interpretation
//   const dateObj =
//     typeof date === "string"
//       ? new Date(Date.parse(date + "Z"))
//       : new Date(date.toISOString()); // Converts Date objects to UTC

//   // console.log("Date parsed in UTC:", dateObj);

//   // Convert the UTC date to the specified timezone
//   const dateInGivenTimezone = toZonedTime(dateObj, tz);
//   //console.log("Date in target time zone:", dateInGivenTimezone);

//   // Format the date in the target time zone with time zone offset
//   const formattedDate = format(
//     dateInGivenTimezone,
//     "yyyy-MM-dd'T'HH:mm:ssXXX",
//     { timeZone: tz },
//   );
//   // console.log("Formatted date after timezone conversion:", formattedDate);

//   return formattedDate;
// };

//use this very carefully, using this for patient overview
export const toLocalDateString = (date?: string) => {
  if (date?.length != 20) return "-";
  const utcDateString = date;
  const utcDate = new Date(utcDateString);
  const year = utcDate.getFullYear();
  const month = String(utcDate.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(utcDate.getDate()).padStart(2, "0");

  return `${month}-${day}-${year}`;
};

export const formatDateWithAgeNew = (
  birthDate: string | undefined,
  birthtime: LocalTime | undefined,
  gender: string | undefined
): string => {
  if (!birthDate) return "N/A";

  try {
    const formattedGender = formatGender(gender);

    const timeParts = birthtime?.toString().split(":");
    const hours = timeParts && timeParts.length > 0 ? timeParts[0] : "00"; // Default to "00" if not provided
    const minutes = timeParts && timeParts.length > 1 ? timeParts[1] : "00";

    const datePart = birthDate.split("T")[0];

    const dateTimeStr = `${datePart}T${hours}:${minutes}:00`; // Append seconds as "00"

    const dateObj = new Date(dateTimeStr);

    const formattedDate = format(dateObj, "MM-dd-yyyy");
    const today = new Date();

    const years = differenceInYears(today, dateObj);

    const months = differenceInMonths(today, dateObj) % 12;
    // Mod 12 to avoid showing more than 12 months
    const weeks = differenceInWeeks(today, dateObj) % 4; // Mod 4 to show weeks within a month
    weeks;
    const days = differenceInDays(today, dateObj) % 7; // Mod 7 to show days within a week
    const totalWeeks = differenceInWeeks(today, dateObj);

    const totalDays = differenceInDays(today, dateObj);
    const totalHours = differenceInHours(today, dateObj);
    const extraHours = totalHours % 24;

    if (years >= 2.5) {
      return `${formattedDate} (${years} y${months > 0 ? `, ${months} m` : ""}) (${formattedGender})`;
    }

    if (totalWeeks >= 4 && years < 2.5) {
      return `${formattedDate} (${years} y${months > 0 ? `, ${months} m` : ""}) (${formattedGender})`;
    }

    if (totalWeeks >= 2 && totalWeeks < 4) {
      return `${formattedDate} (${totalWeeks} w${days > 0 ? `, ${days} d` : ""}) (${formattedGender})`;
    }

    if (totalWeeks >= 1 && totalWeeks < 2) {
      return `${formattedDate} (1 w${days > 0 ? `, ${days} d` : ""} / ${extraHours} hr,)  (${formattedGender})`;
    }

    if (totalDays < 7) {
      if (totalDays === 1) {
        return `${formattedDate} (${totalDays} day, ${extraHours} hr) (${formattedGender})`;
      } else {
        return `${formattedDate} (${totalDays} d, ${extraHours} hr) (${formattedGender})`;
      }
    }

    return "";
  } catch {
    return "Invalid Date";
  }
};

function convertToDate(dateStr: string): Date {
  const [month, day, year] = dateStr.split("-");
  return new Date(`${year}-${month}-${day}`); // Convert to yyyy-MM-dd format
}
export const formatDateWithAgeNewPremature = (
  birthDate: string | undefined,
  birthtime: LocalTime | undefined,
  gender: string | undefined,
  prematureDays: string | undefined
): string => {
  if (!birthDate) return "N/A";

  try {
    const formattedGender = formatGender(gender);

    // Convert prematureDays to number
    const prematureDaysNum = Number(prematureDays) || 0; // Default to 0 if undefined or NaN

    const timeParts = birthtime?.toString().split(":");
    const hours = timeParts && timeParts.length > 0 ? timeParts[0] : "00"; // Default to "00" if not provided
    const minutes = timeParts && timeParts.length > 1 ? timeParts[1] : "00";

    const datePart = birthDate.split("T")[0];

    const dateTimeStr = `${datePart}T${hours}:${minutes}:00`; // Append seconds as "00"

    const dateObj = new Date(dateTimeStr);

    // Adjust birth date based on prematureDays
    if (prematureDaysNum > 0) {
      dateObj.setDate(dateObj.getDate() + prematureDaysNum);
    }

    const formattedDate = format(dateObj, "MM-dd-yyyy");
    const today = new Date();
    const formattedDateToday = format(today, "MM-dd-yyyy");

    const years = differenceInYears(today, dateObj);
    const months = differenceInMonths(today, dateObj) % 12; // Mod 12 to avoid showing more than 12 months
    const weeks = differenceInWeeks(today, dateObj) % 4; // Mod 4 to show weeks within a month
    const days = differenceInDays(today, dateObj) % 7; // Mod 7 to show days within a week
    const totalWeeks = differenceInWeeks(today, dateObj);
    const totalDays = differenceInDays(today, dateObj);
    const totalHours = differenceInHours(today, dateObj);
    const extraHours = totalHours % 24;
    weeks;

    const dateObj1 = convertToDate(formattedDate);
    const dateObj2 = convertToDate(formattedDateToday);

    if (dateObj1 > dateObj2) {
      // console.log("Inside the if");
      return `${formattedDate} (${formattedGender})`;
    } else {
      // console.log("dsm,nd")
      if (years >= 2.5) {
        return `${formattedDate} (${years} y${months > 0 ? `, ${months} m` : ""}) (${formattedGender})`;
      }

      if (totalWeeks >= 4 && years < 2.5) {
        return `${formattedDate} (${years} y${months > 0 ? `, ${months} m` : ""}) (${formattedGender})`;
      }

      if (totalWeeks >= 2 && totalWeeks < 4) {
        return `${formattedDate} (${totalWeeks} w${days > 0 ? `, ${days} d` : ""}) (${formattedGender})`;
      }

      if (totalWeeks >= 1 && totalWeeks < 2) {
        return `${formattedDate} (1 w${days > 0 ? `, ${days} d` : ""} / ${extraHours} hr,)  (${formattedGender})`;
      }

      if (totalDays < 7) {
        if (totalDays === 1) {
          return `${formattedDate} (${totalDays} day, ${extraHours} hr) (${formattedGender})`;
        } else {
          return `${formattedDate} (${totalDays} d, ${extraHours} hr) (${formattedGender})`;
        }
      }
    }

    return "";
  } catch {
    return "Invalid Date";
  }
};

export const formatDateWithAgeNewWithoutdate = (
  birthDate: string | undefined,
  birthtime: LocalTime | undefined,
  gender: string | undefined
): string => {
  if (!birthDate) return "N/A";

  try {
    const formattedGender = formatGender(gender);

    const timeParts = birthtime?.toString().split(":");
    const hours = timeParts && timeParts.length > 0 ? timeParts[0] : "00"; // Default to "00" if not provided
    const minutes = timeParts && timeParts.length > 1 ? timeParts[1] : "00";

    const datePart = birthDate.split("T")[0];

    const dateTimeStr = `${datePart}T${hours}:${minutes}:00`; // Append seconds as "00"

    const dateObj = new Date(dateTimeStr);

    const formattedDate = format(dateObj, "MM-dd-yyyy");
    const today = new Date();
    formattedDate;
    const years = differenceInYears(today, dateObj);
    const months = differenceInMonths(today, dateObj) % 12; // Mod 12 to avoid showing more than 12 months
    const weeks = differenceInWeeks(today, dateObj) % 4; // Mod 4 to show weeks within a month
    weeks;
    const days = differenceInDays(today, dateObj) % 7; // Mod 7 to show days within a week
    const totalWeeks = differenceInWeeks(today, dateObj);
    const totalDays = differenceInDays(today, dateObj);
    const totalHours = differenceInHours(today, dateObj);
    const extraHours = totalHours % 24;

    if (years >= 2.5) {
      return `(${years} y${months > 0 ? `, ${months} m` : ""})  (${formattedGender})`;
    }

    if (totalWeeks >= 4 && years < 2.5) {
      //const remainingWeeks = totalWeeks % 4;
      return `(${years} y${months > 0 ? `, ${months} m` : ""})  (${formattedGender})`;
    }

    if (totalWeeks >= 2 && totalWeeks < 4) {
      return `${totalWeeks} w${days > 0 ? `, ${days} d` : ""}) (${formattedGender})`;
    }

    if (totalWeeks >= 1 && totalWeeks < 2) {
      return `(1 w${days > 0 ? `, ${days} d` : ""} / ${extraHours} hr)  (${formattedGender})`;
    }

    if (totalDays < 7) {
      if (totalDays === 1) {
        return `(${totalDays} day, ${extraHours} hr) (${formattedGender})`;
      } else {
        return `(${totalDays} d, ${extraHours} hr) (${formattedGender})`;
      }
    }

    return "";
  } catch {
    return "Invalid Date";
  }
};

export const formatDateWithAgeNewWithoutdateandGen = (
  birthDate: string | undefined,
  birthtime?: LocalTime | undefined
  //gender: string | undefined,
): string => {
  if (!birthDate) return "N/A";

  try {
    const timeParts = birthtime?.toString().split(":");
    const hours = timeParts && timeParts.length > 0 ? timeParts[0] : "00"; // Default to "00" if not provided
    const minutes = timeParts && timeParts.length > 1 ? timeParts[1] : "00";

    const datePart = birthDate.split("T")[0];

    const dateTimeStr = `${datePart}T${hours}:${minutes}:00`; // Append seconds as "00"

    const dateObj = new Date(dateTimeStr);

    // const formattedDate = format(dateObj, "MM-dd-yyyy");
    const today = new Date();
    // formattedDate;
    const years = differenceInYears(today, dateObj);
    const months = differenceInMonths(today, dateObj) % 12; // Mod 12 to avoid showing more than 12 months
    const weeks = differenceInWeeks(today, dateObj) % 4; // Mod 4 to show weeks within a month
    weeks;
    const days = differenceInDays(today, dateObj) % 7; // Mod 7 to show days within a week
    const totalWeeks = differenceInWeeks(today, dateObj);
    const totalDays = differenceInDays(today, dateObj);
    const totalHours = differenceInHours(today, dateObj);
    const extraHours = totalHours % 24;

    if (years >= 2.5) {
      return `${years} y${months > 0 ? `, ${months} m` : ""} `;
    }

    if (totalWeeks >= 4 && years < 2.5) {
      const remainingWeeks = totalWeeks % 4;
      return `${Math.floor(totalWeeks / 4)} m${remainingWeeks > 0 ? `, ${remainingWeeks} w` : ""}${days > 0 ? `, ${days} d` : ""} `;
    }

    if (totalWeeks >= 2 && totalWeeks < 4) {
      return `${totalWeeks} w${days > 0 ? `, ${days} d` : ""}`;
    }

    if (totalWeeks >= 1 && totalWeeks < 2) {
      return `1 w${days > 0 ? `, ${days} d` : ""} / ${extraHours} hr`;
    }

    if (totalDays < 7) {
      if (totalDays === 1) {
        return `${totalDays} day, ${extraHours} hr`;
      } else {
        return `${totalDays} d, ${extraHours} hr `;
      }
    }

    return "";
  } catch {
    return "Invalid Date";
  }
};

const formatGender = (gender: string | undefined): string => {
  if (!gender) return "";
  const genderMap: { [key: string]: string } = {
    MALE: "M",
    FEMALE: "F",
    OTHER: "O",
  };
  return genderMap[gender.toUpperCase()] || "";
};

export const formatDatee = (dateString?: string) => {
  if (!dateString) return "-";

  const date = parseISO(dateString);
  return format(date, "MM-dd-yyyy");
};

export const formatDateTime = (dateString?: string) => {
  if (!dateString) return "-";

  const date = parseISO(dateString);
  const formattedDate = format(date, "dd MMM yyyy");
  const formattedTime = format(date, "hh:mm a");
  return `${formattedDate} ${formattedTime}`;
};

export const formatPhoneNumber = (phone: string | undefined): string => {
  if (!phone) return "";
  return phone.replace(/(\d{3})(\d{3})(\d{4})/, "$1-$2-$3");
};

export const formatDate = (dateString?: string) => {
  if (!dateString) return "-";

  const date = parseISO(dateString);
  return format(date, "MM-dd-yyyy");
};
