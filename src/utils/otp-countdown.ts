const RESEND_OTP_TIMEOUT_SECONDS = 15 * 60;

export const calculateTimeRemaining = (timestamp: string | null): number => {
  if (!timestamp) return 0;

  const diff = RESEND_OTP_TIMEOUT_SECONDS - Math.floor((Date.now() - parseInt(timestamp)) / 1000);
  return diff > 0 ? diff : 0;
};

export const formatCountdown = (seconds: number): { minutes: number; seconds: number } => ({
  minutes: Math.floor(seconds / 60),
  seconds: seconds % 60,
});
